<?php

use App\Transformers\ServiceModelDataTransformer;
use Izam\Daftra\Invoice\Events\InvoiceCreatedEvent;
use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use Izam\Entity\Helper\EntityHasLegacyCustomFields;

App::import('Vendor', 'AutoNumber');
App::import('Vendor', 'settings');
/**
 * @property PurchaseOrder $PurchaseOrder Po
 * @property Invoice $Invoice
 * @property WorkOrder $WorkOrder
 * @property Store $Store
 */
class ResellersController extends AppController {

	var $name = 'Resellers';
   /* append js_lang_labels */
    var $invoice_js_labels = array(
        'Discount',
        'Paid',
        'Unpaid',
        'Balance Due:',
        'Next Payment',
        'Partial Paid',
        'Invalid date. Date must match date format',
        'Client Details',
        'You can add up to 10 documents only',
        'Documents limit',
        'Please select invoice template to load',
        'Invoice Template',
        'Stock Level After',
        'Please Save the client details first by clicking below on Save Client button',
        'You need to enter deposit the amount first',
        'Shipping',
        'Please select the invoices first by clicking on the small left box',
        'Are you sure ?',
        'Changes you made may not be saved',
        'The card number is not a valid credit card number.',
        'Credit card number is not valid',
        'CVV is not valid',
        'Postal code is not valid',
        'Please enter document title',
        'Refunds',
        'Refunded',
        'Full Name / Business Name ',
        'Full Name',
        'Purchases',
        'Sales',
        'Total',
        'Profit',
    );

	function beforeFilter() {

        parent::beforeFilter();
	if (!empty($_GET['work_order_id']) && strpos($this->params['action'], 'owner_add') !== false) {

            $this->loadModel('WorkOrder');
            $work_order = $this->WorkOrder->find('first', ['conditions' => ['WorkOrder.id' => $_GET['work_order_id']]]);
            if (!empty($work_order) && empty($_GET['client_id']) && !empty($work_order['WorkOrder']['client_id'])) {
                $_GET['client_id'] = $this->params['url']['client_id'] = $work_order['WorkOrder']['client_id'];
            }
        }
	}
	function owner_index() {
		$site = getAuthOwner();
		if ($site['staff_id'] != 0) {
			if (!check_permission(Invoices_View_All_Invoices) && !check_permission(Invoices_View_Invoices_Details)) {
				if(IS_REST) $this->cakeError('error403');
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
				$this->redirect('/');
			}
		}
		$this->loadModel('Invoice');
		$this->loadModel('Reseller');
		$conditions2 = parent::_filter_params();

		foreach($conditions2 as $key=>$value){
		    $key=str_replace('Reseller','Invoice',$key);
            $conditions[$key]=$value;
        }

		$conditions['Invoice.type']=Invoice::Resellers;
		  if (!check_permission(Invoices_View_All_Invoices)) {
            $conditions['Invoice.staff_id'] = $site['staff_id'];
        }
		$this->paginate['Invoice']['order'] = 'Invoice.id DESC';
        $rows = $this->paginate('Invoice', $conditions);
	$this->loadModel('Staff');
        $this->set('staffs',$this->Staff->getList());
	$this->set('rows',$rows);
	}


	function owner_add() {
        $this->set('client_type',settings::getValue(ClientsPlugin, "client_type"));
		$site = getAuthOwner();
		 if (IS_REST) {
            if (!userHavePermissionToAddInvoice())
                $this->cakeError('error403');
        } else {
            if (!userHavePermissionToAddInvoice()) {
                if (IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }

		$this->loadModel('Invoice');
         $this->loadModel('Site');
        $check = $this->Site->check_add_invoice_limit();
        if (!$check['status']) {
            $this->_flashLimitMessage($check['message'], __('invoices', true));
            $this->redirect(array('action' => 'index'));
        }
		$this->set('title_for_layout',  __('New PNR', true));
		$this->set('page_title', __('New PNR', true));

		$this->loadModel('PurchaseOrder');

		$this->loadModel('Store');

		        if (!empty($this->data)) {
                    if (!check_permission(INVOICES_EDIT_DATE)) {
                        $this->data['Invoice']['date'] = format_date(date("Y-m-d"));
                        $this->data['Invoice']['issue_date'] = format_date(date("Y-m-d"));
                    }
					$this->data['Invoice']['store_id']=$this->Store->getPrimaryStore();
					$maindata=$this->data;
					$invoicedata=$this->data;
					$products = [];
                    $willSave = true;
					foreach ($invoicedata['InvoiceItem'] as $invoiceItem) {
					    $products[] = $invoiceItem['product_id'];
                    }
					$this->loadModel('Product');
					$oldRecursive = $this->Product->recursive;
					$this->Product->recursive = -1;
                    $productsData = $this->Product->find('all', ['conditions' => ['Product.id' => $products]]);
                    foreach ($productsData as $product) {
                        if ($product['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY) {
                            $this->flashMessage(__('Could not save the Pnr, You cannot choose any product with tracking type in the PNR', true));
                            $willSave = false;
                            break;
                        }
                    }
                    $this->Product->recursive = $oldRecursive;
                    if($site['id']=="2062332" && $site['id']=="2037835"){
					$this->Invoice->InvoiceItem->validate['item']['isUnique']=array('rule' =>  array('isUniqueItem', Invoice::Resellers), 'message' => __('item name already exists', true));
					foreach($invoicedata['InvoiceItem'] as $row){
					$itcount[$row['item']]=(int)$itcount[$row['item']]+1;

					}

						foreach($itcount as $r){
							if($r>1){

						$this->Invoice->InvoiceItem->validate['item']['itemerror']=array('rule' =>  array('ItemError', Invoice::Resellers), 'message' => __('item name is duplicated', true));
							}
						}
					}
					if ($willSave) {

                            $suppliers_list = [];
                            foreach ($maindata['InvoiceItem'] as $poitem) {
                                $suppliers_list[$poitem['col_4']][] = $poitem;
                            }  

                            $SupplierModel = GetObjectOrLoadModel('Supplier');
                            $supplierList = $SupplierModel->find('all', ['recursive' => -1, 'conditions' => ['Supplier.id' => array_keys($suppliers_list)]]);
                            foreach($supplierList as $suppler) {
                                if ($suppler && $suppler['Supplier']['suspend'] == "1") {
                                    if (IS_REST) {
                                        $this->cakeError("error400", ["message" => __('You cannot create a new transaction for a suspended supplier', true)]);
                                    }
                                    $this->flashMessage(__('You cannot create a new transaction for a suspended supplier', true));
                                    $this->Invoice->validationErrors['supplier'] = __('You cannot create a new transaction for a suspended supplier', true);
                                }
                            }
                            $this->data['Invoice']['date'] = $this->Invoice->formatDate($this->data['Invoice']['date']);
                            $this->validateOpenDayWithValidationError($this->data, 'Invoice','date');
                            $this->data['Invoice']['date'] = format_date($this->data['Invoice']['date']);
                            if(empty($this->Invoice->validationErrors))
                            {
                                $main_invoice=$this->Invoice->addInvoice($maindata,Invoice::Resellers);
                            }else{
                                $main_invoice['status'] = false;
                            }
                        if ($main_invoice['status']) {
                            //print_pre($main_invoice);
                            //die();
                            foreach ($invoicedata['InvoiceItem'] as &$item) {
                                unset($item['tax2']);
                                unset($item['col_3']);
                                unset($item['col_4']);
                            }
                            $podata['PurchaseOrder']['is_offline'] = 1;
                            $invoicedata['Invoice']['source_type'] = Invoice::Resellers;
                            $invoicedata['Invoice']['source_id'] = $main_invoice['data']['Invoice']['id'];
                            $invoicedata['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
                            if ($site['id'] == "2062332" && $site['id'] == "2037835") {
                                $this->Invoice->InvoiceItem->validate['item']['isUnique'] = ['rule' => ['isUniqueItem', Invoice::Invoice], 'message' => __('item name already exists', true)];
                            }

                            $client_invoice = $this->Invoice->addInvoice($invoicedata);

                            if ( !$client_invoice['data']['Invoice']['draft']) {
                                dispatch_event_action(new InvoiceCreatedEvent(ServiceModelDataTransformer::transform($client_invoice['data'], 'Invoice')));
                            }

                            $resulti = $this->Invoice->Client->pay_invoice_from_credit($client_invoice['data']['Invoice']['id']);
                            $re_read_invoice = $this->Invoice->getInvoice($client_invoice['data']['Invoice']['id']);
                            $arry = ['primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => empty($re_read_invoice['Invoice']['draft']) ? $re_read_invoice['Invoice']['payment_status'] : -1, 'param3' => $re_read_invoice['Invoice']['summary_paid'], 'param4' => $re_read_invoice['Invoice']['no'], 'param5' => $re_read_invoice['Client']['business_name'], 'param6' => $re_read_invoice['Client']['client_number']];
                            $this->add_actionline(ACTION_ADD_INVOICE, $arry);
                            if (!empty($this->data['Payment']['is_paid']) || !empty($this->data['Deposit']['is_paid'])) {
                                if (!empty($this->data['Payment']['is_paid'])) {
                                    $k = 'Payment';
                                    $amount = $client_invoice['data']['Invoice']['summary_total'];
                                    $treasury_id = $this->data['Payment']['treasury_id'];
                                } else {
                                    $k = 'Deposit';
                                    $amount = $this->data['Invoice']['deposit'];
                                    $treasury_id = $this->data['Deposit']['treasury_id'];
                                }
                                $owner = getAuthOwner();
                                $payment['InvoicePayment'] = ['currency_code' => $client_invoice['data']['Invoice']['currency_code'], 'treasury_id' => $treasury_id, 'invoice_id' => $client_invoice['data']['Invoice']['id'], 'amount' => $amount, 'status' => 1, 'date' => $this->data['Invoice']['date'], 'payment_method' => $this->data[$k]['payment_method'], 'transaction_id' => $this->data[$k]['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id']];
                                $resulti = $this->Invoice->ownerAddPayment($payment);
                            }
      
                            foreach ($suppliers_list as $supplier_id => $supplier_item) {
                                if ($supplier_id == 0) {
                                    continue;
                                }
                                $this->PurchaseOrder->create(false);
                                $podata['PurchaseOrder'] = $maindata['Invoice'];
                                $podata['PurchaseOrder']['discount'] = null;
                                $podata['PurchaseOrder']['is_offline'] = 1;
                                $podata['PurchaseOrder']['discount_amount'] = null;
                                $podata['PurchaseOrder']['shipping_amount'] = null;
                                $podata['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_INVOICE;
                                $podata['PurchaseOrder']['supplier_id'] = $supplier_id;
                                $podata['PurchaseOrder']['summary_refund'] = 0;
                                $podata['PurchaseOrder']['source_type'] = Invoice::Resellers;
                                $podata['PurchaseOrder']['source_id'] = $main_invoice['data']['Invoice']['id'];
                                unset($podata['PurchaseOrder']['client_id']);
                                $podata['PurchaseOrderItem']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_INVOICE);
                                foreach ($supplier_item as &$sitem) {
                                    $sitem['tax1'] = $sitem['tax2'];
                                    $sitem['unit_price'] = $sitem['col_3'];
                                    unset($sitem['tax2']);
                                    unset($item['col_3']);
                                    unset($item['col_4']);
                                }
                                $podata['PurchaseOrderItem'] = $supplier_item;
                                //print_pre($podata);
                                unset($this->PurchaseOrder->validate['supplier_email']);
                                $res = $this->PurchaseOrder->addPurchaseOrder($podata);
                                $this->PurchaseOrder->recursive = 1;
                                $re_read_po = $this->PurchaseOrder->read(null, $res['data']['PurchaseOrder']['id']);
                                $this->PurchaseOrder->update_journals($re_read_po);
                                $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number'], 'param7' => $re_read_po['PurchaseOrder']['payment_status']];
                                $this->add_actionline(ACTION_ADD_PO, $arry);
                                $resulti = $this->PurchaseOrder->Supplier->pay_po_from_credit($res['data']['PurchaseOrder']['id']);
                                //print_pre($suppliers);
                                //	print_pre($res);
                                //die();
                            }
                            $this->flashMessage(__('Pnr has been saved', true), 'Sucmessage', 'secondaryMessage');
                            if (!empty($main_invoice['data']['Invoice']['work_order_id'])) {
                                $this->redirect(['controller' => 'work_orders', 'action' => 'view', $main_invoice['data']['Invoice']['work_order_id']]);
                            } else {
                                $this->redirect(['action' => 'view', $main_invoice['data']['Invoice']['id']]);
                            }
                        } else {
                            $this->add_stats(STATS_INVOICE_VALIDATION_ERROR, [serialize($this->Invoice->validationErrors), serialize($this->Invoice->data)]);
                            if (IS_REST) $this->cakeError("error400", ["message" => __('Could not save the Pnr, Please fix errors below', true), "validation_errors" => $this->Invoice->validationErrors]);
                            $this->flashMessage(__('Could not save the Pnr, Please fix errors below', true));
                            foreach($this->Invoice->validationErrors as $error){
                                if(is_array($error))
                                {
                                    //because if error is array that means its an invoice item error
                                    //and invoice item errors will show in each of its rows as
                                    //it dosent make sense to show them in the head of page area
                                    continue ;
                                }
                                $multi_errors[]=$error;
                            }
                            if(!empty($multi_errors)) {
        
                                CustomValidationFlash($multi_errors);
                            }
                        }

                    }
                } else {
                    if (!empty ($_GET['work_order_id'])){
			$this->data['Invoice']['work_order_id']=$_GET['work_order_id'];
		}
		 if (!empty($this->params['url']['client_id'])) {

                $this->data['Invoice']['client_id'] = $this->params['url']['client_id'];
            }
				$this->data['Invoice']['hidden_no'] = $this->data['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
				$this->js_lang_labels = array_merge($this->js_lang_labels, $this->invoice_js_labels);

				$this->set('isInvoice', true);
				$formats = getDateFormats(null);
                $this->data['Invoice']['issue_date'] = format_date(date("Y-m-d"));
				}
        $this->_settings();
	}

	function owner_edit($id=null) {
		$owner= getAuthOwner();
		$site= getAuthOwner();
		$this->loadModel('PurchaseOrder');
		$this->loadModel('Invoice');

        $this->Invoice->bindAttachmentRelation('invoice');

		$invoice=$this->Invoice->getInvoice($id,array('Invoice.type'=>Invoice::Resellers));


		 if (!$invoice) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('PNR not found', true)));
            $this->flashMessage(__('PNR not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

		 if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(Invoices_Edit_All_Invoices) && $invoice['Invoice']['staff_id'] != $staff) || !check_permission(Invoices_Edit_his_own_Invoices)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this pnr", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));

            }
        }
		$this->validate_open_day($invoice['Invoice']['date']);
		if (!empty($this->data)) {
            $PO_IDS=array();
			$maindata=$this->data;

			$invoicedata=$this->data;
            $products = [];
            $willSave = true;
            foreach ($invoicedata['InvoiceItem'] as $invoiceItem) {
                $products[] = $invoiceItem['product_id'];
            }
            $this->loadModel('Product');
            $oldRecursive = $this->Product->recursive;
            $this->Product->recursive = -1;
            $productsData = $this->Product->find('all', ['conditions' => ['Product.id' => $products]]);
            foreach ($productsData as $product) {
                if ($product['Product']['tracking_type'] != \App\Utils\TrackStockUtil::QUANTITY_ONLY) {
                    $this->flashMessage(__('Could not save the Pnr, You cannot choose any product with tracking type in the PNR', true));
                    $willSave = false;
                    break;
                }
            }
            $this->Product->recursive = $oldRecursive;
			if($site['id']=="2062332" && $site['id']=="2037835"){
			$this->Invoice->InvoiceItem->validate['item']['isUnique']=array('rule' =>  array('isUniqueItem', Invoice::Resellers), 'message' => __('item name already exists', true));
				foreach($invoicedata['InvoiceItem'] as $row){
					$itcount[$row['item']]=(int)$itcount[$row['item']]+1;

					}
						foreach($itcount as $r){
							if($r>1){

						$this->Invoice->InvoiceItem->validate['item']['itemerror']=array('rule' =>  array('ItemError', Invoice::Resellers), 'message' => __('item name is duplicated', true));
							}
						}
			}
			if ($willSave) {
                $suppliers_list = [];
                foreach ($maindata['InvoiceItem'] as $poitem) {
                    $suppliers_list[$poitem['col_4']][] = $poitem;
                }
                $SupplierModel = GetObjectOrLoadModel('Supplier');
                $supplierList = $SupplierModel->find('all', ['recursive' => -1, 'conditions' => ['Supplier.id' => array_keys($suppliers_list)]]);
                foreach($supplierList as $suppler) {
                    if ($suppler && $suppler['Supplier']['suspend'] == "1") {
                        if (IS_REST) {
                            $this->cakeError("error400", ["message" => __('You cannot create a new transaction for a suspended supplier', true)]);
                        }
                        $this->flashMessage(__('You cannot create a new transaction for a suspended supplier', true));
                        $this->Invoice->validationErrors['supplier'] = __('You cannot create a new transaction for a suspended supplier', true);
                    }
                }

                $this->data['Invoice']['date'] = $this->Invoice->formatDate($this->data['Invoice']['date']);
                $this->validateOpenDayWithValidationError($this->data, 'Invocie','date');
                $this->data['Invoice']['date'] = format_date($this->data['Invoice']['date']);
                if(empty($this->Invoice->validationErrors))
                {
                    $main_invoice=$this->Invoice->updateInvoice($maindata,Invoice::Resellers);
                }else{
                    $main_invoice['status'] = false;
                }
                if ($main_invoice['status']) {
                    unset($maindata['Invoice']['id']);
                    foreach ($invoicedata['InvoiceItem'] as &$item) {
                        unset($item['tax2']);
                        unset($item['col_3']);
                        unset($item['col_4']);
                    }
                    $invoicedata['Invoice']['source_type'] = Invoice::Resellers;
                    $invoicedata['Invoice']['source_id'] = $main_invoice['data']['Invoice']['id'];
                    unset($invoicedata['Invoice']['no']);
                    $client_invoice = $this->Invoice->find('first', ['conditions' => ['Invoice.source_id' => $id, 'Invoice.source_type' => Invoice::Resellers]]);
                    $invoicedata['Invoice']['id'] = $client_invoice['Invoice']['id'];
                    $invoicedata['Invoice']['no'] = $client_invoice['Invoice']['no'];

                    if (empty($invoicedata['Invoice']['id'])) {
                        $client_invoice = $this->Invoice->addInvoice($invoicedata);
                    } else {
                        $client_invoice = $this->Invoice->updateInvoice($invoicedata);
                    }
                    $re_read_invoice = $this->Invoice->getInvoice($client_invoice['data']['Invoice']['id']);
                    $array = ['primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => empty($re_read_invoice['Invoice']['draft']) ? $re_read_invoice['Invoice']['payment_status'] : -1, 'param3' => $re_read_invoice['Invoice']['summary_paid'], 'param4' => $re_read_invoice['Invoice']['no'], 'param5' => $re_read_invoice['Client']['business_name'], 'param6' => $re_read_invoice['Client']['client_number']];
                    $this->add_actionline(ACTION_UPDATE_INVOICE, $array);

                    $JournalAccountRoute['entity_type'] = 'sales';
                    $JournalAccountRoute['entity_id'] = $invoicedata['Invoice']['id'];
                    $JournalAccountRoute['auto_account_type'] = 'fixed';
                    $JournalAccountRoute['account_type'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT;
                    $JournalAccountRoute['account_id'] = '';
                    RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_INVOICE)
                        ->save($JournalAccountRoute, $invoicedata['Invoice']['id']);

                    foreach ($suppliers_list as $supplier_id => $supplier_item) {
                        if ($supplier_id == 0) {
                            continue;
                        }
                        $podata = [];
                        $supplierpo = $this->PurchaseOrder->find('first', ['conditions' => ['PurchaseOrder.supplier_id' => $supplier_id, 'PurchaseOrder.source_id' => $id, 'PurchaseOrder.source_type' => Invoice::Resellers]]);
                        //echo "-----------------------------------";
//				print_pre($supplierpo);
                        //$this->PurchaseOrder->create(false);
                        $podata['PurchaseOrder'] = $maindata['Invoice'];
                        if ($supplierpo) {
                            $podata['PurchaseOrder']['id'] = $supplierpo['PurchaseOrder']['id'];
                            $podata['PurchaseOrder']['no'] = $supplierpo['PurchaseOrder']['no'];
                            $podata['PurchaseOrder']['supplier_id'] = $supplier_id;
                        }
                        unset($podata['PurchaseOrder']['client_id']);
                        foreach ($supplier_item as &$sitem) {
                            $sitem['unit_price'] = (double) $sitem['col_3'];
                            $sitem['tax1'] = $sitem['tax2'];
                            unset($sitem['tax2']);
                            unset($item['col_3']);
                            unset($item['col_4']);
                        }
                        $podata['PurchaseOrderItem'] = $supplier_item;
                        $podata['PurchaseOrder']['discount'] = null;
                        $podata['PurchaseOrder']['discount_amount'] = null;
                        $podata['PurchaseOrder']['shipping_amount'] = null;
                        unset($this->PurchaseOrder->validate['supplier_email']);
                        //print_pre($podata);
                        if ($supplierpo) {

                            $res = $this->PurchaseOrder->updatePurchaseOrder($podata);
                            $PO_IDS[] = $res['data']['PurchaseOrder']['id'];
                            $re_read_po = $this->PurchaseOrder->read(null, $res['data']['PurchaseOrder']['id']);
                            $this->PurchaseOrder->update_journals($re_read_po);
                            $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number'], 'param7' => $re_read_po['PurchaseOrder']['payment_status']];
                            $this->add_actionline(ACTION_UPDATE_PO, $arry);
                        } else {
                            $podata['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_INVOICE;
                            $podata['PurchaseOrder']['supplier_id'] = $supplier_id;
                            $podata['PurchaseOrder']['summary_refund'] = 0;
                            $podata['PurchaseOrder']['source_type'] = Invoice::Resellers;
                            $podata['PurchaseOrder']['source_id'] = $main_invoice['data']['Invoice']['id'];
                            unset($this->PurchaseOrder->id);
                            unset($podata['PurchaseOrder']['no']);
                            $this->PurchaseOrder->create();
                            $res = $this->PurchaseOrder->addPurchaseOrder($podata);
                            $PO_IDS[] = $res['data']['PurchaseOrder']['id'];
                            $re_read_po = $this->PurchaseOrder->read(null, $res['data']['PurchaseOrder']['id']);
                            $this->PurchaseOrder->update_journals($re_read_po);
                            $arry = ['primary_id' => $re_read_po['PurchaseOrder']['id'], 'secondary_id' => $re_read_po['PurchaseOrder']['supplier_id'], 'param1' => $re_read_po['PurchaseOrder']['summary_total'], 'param2' => $re_read_po['PurchaseOrder']['is_received'], 'param3' => $re_read_po['PurchaseOrder']['summary_paid'], 'param4' => $re_read_po['PurchaseOrder']['no'], 'param5' => $re_read_po['Supplier']['business_name'], 'param6' => $re_read_po['Supplier']['supplier_number'], 'param7' => $re_read_po['PurchaseOrder']['payment_status']];
                            $this->add_actionline(ACTION_ADD_PO, $arry);
                        }
                    }
                    $this->flashMessage(__('Pnr has been saved', true), 'Sucmessage', 'secondaryMessage');
                    foreach ($this->data['InvoiceItem'] as $row) {
                        $supplier_ids[$row['col_4']] = $row['col_4'];
                    }
                    if (count($PO_IDS) == 1) {
                        $PO_IDS = $PO_IDS[0];
                    }

                    $pos = $this->PurchaseOrder->find('list', ['conditions' => ['id not' => $PO_IDS, 'source_type' => Invoice::Resellers, 'source_id' => $id]]);
                    foreach ($pos as $p) {
                        $this->PurchaseOrder->delete($p);
                        $this->PurchaseOrder->delete_auto_journals($p);
                    }
                    if (!empty($main_invoice['data']['Invoice']['work_order_id'])) {
                        $this->redirect(['action' => 'view', $main_invoice['data']['Invoice']['id']]);
                    } else {
                        $this->redirect(['action' => 'view', $main_invoice['data']['Invoice']['id']]);
                    }
                }
            }
        } else {
            $this->data = $invoice;
        }
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->invoice_js_labels);
		$this->_settings();
		$this->render('owner_add');
	}
	function owner_delete($id=null) {
		$owner = getAuthOwner();
        if (!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to delete pnr", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

		$this->loadModel('StockTransaction');
		$this->loadModel('PurchaseOrder');
		$this->loadModel('Invoice');
		$invoice=$this->Invoice->getInvoice($id,array('Invoice.type'=>Invoice::Resellers));
		$client_invoice=$this->Invoice->getInvoice(null,array('Invoice.source_id'=>$id,'Invoice.source_type'=>Invoice::Resellers));
		$this->loadModel('PurchaseOrder');
		$purchase_orders=$this->PurchaseOrder->find('list',array('fields'=>'PurchaseOrder.id,PurchaseOrder.no','conditions'=>array('PurchaseOrder.source_id'=>$id,'PurchaseOrder.source_type'=>Invoice::Resellers)));


		 if (!$invoice) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('PNR not found', true)));
            $this->flashMessage(__('PNR not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $this->validate_open_day($invoice['Invoice']['date']);

		if ($_POST['submit_btn'] == 'yes') {
		 $this->Invoice->delete($id);
		$this->Invoice->delete_related_items($id);
		//Delete Invoice
		 $this->Invoice->delete($client_invoice['Invoice']['id']);
		$this->Invoice->delete_related_items($client_invoice['Invoice']['id']);
		$this->add_actionline(ACTION_DELETE_INVOICE, array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'], 'param1' => $invoice['Invoice']['summary_total'], 'param2' => empty($invoice['Invoice']['draft']) ? $invoice['Invoice']['payment_status'] : -1, 'param3' => $invoice['Invoice']['summary_paid'], 'param4' => $invoice['Invoice']['no'], 'param5' => $invoice['Client']['business_name'], 'param6' => $invoice['Client']['client_number']));
		if (ifPluginActive(InventoryPlugin)) {
		StockTransaction::updateForInvoice($client_invoice);
		}

		foreach($purchase_orders as $po_id=>$po){
		$purchase_order=$this->PurchaseOrder->getPurchaseOrder($po_id);
		 $this->PurchaseOrder->delete($po_id);
		$this->PurchaseOrder->delete_related_items($po_id);
		if (ifPluginActive(InventoryPlugin)) {
		StockTransaction::updateForPr($purchase_order);
		}
		$this->add_actionline(ACTION_DELETE_PO, array('primary_id' => $purchase_order['PurchaseOrder']['id'], 'secondary_id' => $purchase_order['PurchaseOrderItem']['product_id'], 'param1' => $purchase_order['PurchaseOrder']['summary_total'], 'param2' => $purchase_order['PurchaseOrder']['payment_status'], 'param3' => $purchase_order['PurchaseOrder']['summary_paid'], 'param4' => $purchase_order['PurchaseOrder']['no'], 'param5' => $purchase_order['Supplier']['business_name'], 'param6' => $purchase_order['Supplier']['supplier_number']));
		}
		$this->flashMessage(__('Pnr has been deleted', true), 'Sucmessage', 'secondaryMessage');
		$this->redirect(array('action' => 'index'));
		}

		$this->set('client_invoice',$client_invoice);

		$this->set('invoice',$invoice);
		$this->set('purchase_orders',$purchase_orders);

		$this->js_lang_labels = array_merge($this->js_lang_labels, $this->invoice_js_labels);

	}

	function owner_view($id = null) {
		 $this->set('title_for_layout',  __('View PNR', true));
			$this->loadModel('Product');
			$this->loadModel('Supplier');
			$this->loadModel('Post');
			$this->loadModel('Invoice');
	$row=$this->Invoice->find('first',array('conditions'=>array('Invoice.id'=>$id)));
	 if (!$row) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('PNR not found', true)));
            $this->flashMessage(__('PNR not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
	$client_invoice=$this->Invoice->find('first',array('conditions'=>array('Invoice.source_id'=>$id,'Invoice.source_type'=>Invoice::Resellers)));

	$this->set('client_invoice',$client_invoice);

	foreach($row['InvoiceItem'] as &$sitem){
		$product=$this->Product->find('first',array('recursive'=>-1,'conditions'=>array('Product.id'=>$sitem['product_id'])));
		$sitem['Product']=$product['Product'];
		$supplier=$this->Supplier->find('first',array('recursive'=>-1,'conditions'=>array('Supplier.id'=>$sitem['col_4'])));
		$sitem['Supplier']=$supplier['Supplier'];
		}
    $row=   $this->cal_profit($row);
   
	$this->set('invoice',$row);
	$this->loadModel ( 'PurchaseOrder');
		$po=$this->PurchaseOrder->find('all',array('recursive'=>-1,'fields'=>array('currency_code','summary_total','summary_subtotal'),'conditions'=>array('PurchaseOrder.source_id'=>$row['Invoice']['id'],'PurchaseOrder.source_type'=>Invoice::Resellers)));

		$this->set('po_currency_code',$po[0]['PurchaseOrder']['currency_code']);

		$total_po=0;
		foreach($po as $rpo){
		$total_po=$total_po+$rpo['PurchaseOrder']['summary_total'];
		}
        $purchaseOrderUrl = "/owner/purchase_invoices/index?source_type=" . Invoice::Resellers . "&source_id=" . $row['Invoice']['id'];
        if (!EntityHasLegacyCustomFields::check('purchase_orders')) {
            $purchaseOrderUrl = '/v2/owner/entity/purchase_order/list?' . http_build_query(['filter' => ['source_type' => Invoice::Resellers, 'source_id' => $row['Invoice']['id']], 'iframe' => 1]);
        }
        $this->set('purchase_order_url', $purchaseOrderUrl);
        $this->set('total_profit',$row['Invoice']['total_profit']);
		$this->set('total_po',$total_po);
		$this->set('po_currency_code',$po[0]['PurchaseOrder']['currency_code']);

	}

    private function cal_profit($invoice)
    {
        $this->loadModel('Tax');
        $total_profit = 0;
        foreach ($invoice['InvoiceItem'] as &$invoice_item) {
            $origan_puy_price = (float)$invoice_item['col_3'];
            $origan_sell_price = (float)$invoice_item['unit_price'];
            if ($invoice_item['tax1']) {
                $tax1  = $this->Tax->find('first', ['recursive' => -1, 'conditions' => ['Tax.id' => $invoice_item['tax1']]]);
                if ($tax1['Tax']['included'] == 1) {
                    $origan_sell_price  =  $origan_sell_price / (($tax1['Tax']['value']  / 100) + 1);
                }
            }
            if ($invoice_item['tax2']) {
                $tax2  = $this->Tax->find('first', ['recursive' => -1, 'conditions' => ['Tax.id' => $invoice_item['tax2']]]);
                if ($tax2['Tax']['included'] == 1) {
                    $origan_puy_price  =  $origan_puy_price / (($tax2['Tax']['value']  / 100) + 1);
                }
            }
            $invoice_item['profit'] = $invoice_item['quantity'] * ($origan_sell_price - $origan_puy_price);
            $total_profit += $invoice_item['profit'];
        }
        $invoice['Invoice']['total_profit'] = $total_profit;
        return $invoice;
    }

	function _settings() {
	    $this->loadModel('SitePaymentGateway');
		$invoicing_method = settings::getValue(InvoicesPlugin, 'invoicing_method');
        $this->set('default_invoice_method', $invoicing_method);
        $hide_invoice_method = ($invoicing_method == settings::OPTION_INVOICING_METHOD_PRINT || $invoicing_method == settings::OPTION_INVOICING_METHOD_EMAIL);
		if($hide_invoice_method==false){
		   $invoicing_method=settings::getValue(InvoicesPlugin, "preferred_invoicing_method");
		}


        $this->set('hide_invoice_method', $hide_invoice_method);
        if (empty($this->data['Invoice']['is_offline'])) {
            if ($invoicing_method == settings::OPTION_INVOICING_METHOD_PRINT)
                $this->data['Invoice']['is_offline'] = 1;
            else if ($this->Cookie->read('last_is_offline') !== null && !$hide_invoice_method)
                $this->data['Invoice']['is_offline'] = $this->Cookie->read('last_is_offline');
            else
                $this->data['Invoice']['is_offline'] = 0;
        }

        $this->loadModel('Site');
        $addClientLimit = $this->Site->check_add_client_limit();
        $this->set(compact('addClientLimit'));
		$this->loadModel('SitePaymentGateway');
        $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id')));
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

		$this->loadModel('InvoiceTax');
		$this->set('jsTaxes', $this->InvoiceTax->getJSONList());
		$this->set('taxes', $this->InvoiceTax->getInvoiceTaxList());

		$this->set('languages', array());
		 $this->loadModel('Term');
        $this->set('termsConditions', $this->Term->getTermList());
		$this->loadModel('Client');
		   $ajax_clients = false;
        $clients_count = $this->Client->getClientCount();
        if ($clients_count < AJAX_CLIENT_COUNT) {
            $this->set('clients', $this->Invoice->Client->getClientsList());
        } else {
            if (!empty($this->data['Invoice']['client_id']))
                $this->set('clients', $this->Invoice->Client->getClientsList(0, array('Client.id' => $this->data['Invoice']['client_id'])));
            $ajax_clients = true;
        }
        $this->set('ajax_clients', $ajax_clients);


		$this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList());
		$this->loadModel('Supplier');
        $this->set('suppliers', $this->Supplier->getSuppliersList());

        $this->loadModel('Country');
        $this->set('countries', $this->Country->getCountryList());
		$this->loadModel ( 'Treasury');
            $this->set ( 'treasuries' , $this->Treasury->get_list () ) ;

			$this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;

		$this->loadModel('Document');

        $this->set('documents', $this->Document->getInvoiceDocumentList());
        $this->set('documents_list', $this->Document->getDocumentList());

        $this->loadModel('Language');
        $languages = $this->Language->getLanguageList();
        $this->set('languages', $languages);
		$this->set('invoice_methods', Invoice::getMethods());

        $this->set('statuses', Invoice::getPaymentStatuses());
        $this->set('dateFormats', getDateFormats(true));

		$this->loadModel('SitePaymentGateway');
        $this->set('payment_treasury',$this->SitePaymentGateway->PaymentTreasury());

		//Ajax_product_search
        $aps = '1';
        $this->loadModel('Product');
        $count = $this->Product->find('count');
        if ($count <= 10) {
            $aps = '0';
            $products = $this->Product->getInvoiceProductList();
            $this->set('products', $products);
        } else {

            $this->loadModel('InvoiceItem');
            $this->InvoiceItem->recursive = -1;
            $usd_product = $this->InvoiceItem->find('all', array('order'=>'count(InvoiceItem.product_id) desc', 'fields' => array('count(InvoiceItem.product_id) as total', 'InvoiceItem.product_id'), 'conditions' => array('InvoiceItem.product_id !=' => NULL, 'InvoiceItem.product_id !=' => 0), 'group' => 'InvoiceItem.product_id'));
            foreach ($usd_product as $product) {
                $TopProductIds[] = $product['InvoiceItem']['product_id'];
            }
            $products = $this->Product->getInvoiceProductList($TopProductIds);
            $this->set('products', $products);
        }
		$this->set('aps', $aps);
	}

}
