<?php

class ClientsController extends AppController {

	var $name = 'Clients';
	/**
	 * @var Client
	 */
	var $Client;
	var $helpers = array('Html', 'Form');
	var $components = array('Lmail');


	function client_logout() {
		$this->Session->delete('CLIENT');
		$this->redirect('/');
	}

	//---------------------------
	function client_dashboard() {

	}

	//--------------------------------
	function owner_index() {
		$this->Client->recursive = 0;

		$owner_id = getAuthOwner('id');
		$conditions = array();
		$conditions['Client.site_id'] = $owner_id;
		$filter_conditions = $this->_filter_params();
		$conditions[] = $filter_conditions;
		$this->paginate['Client']['limit'] = 4;
		$this->set('clients', $this->paginate('Client', $conditions));

		$this->loadModel('Country');
		$this->loadModel('Currency');

		$this->set('countryCodes', $this->Country->getCountryList());
		$this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());
	}

	//---------------------------
	function _filter_params() {
		$conditions = parent::_filter_params();
		if (!empty($conditions['Client.name LIKE'])) {
			$conditions[] = "CONCAT (Client.first_name,Client.last_name,Client.business_name,Client.email) LIKE '{$conditions['Client.name LIKE']}'";
		}
		unset($conditions['Client.name LIKE']);

		if (!empty($conditions['Client.address LIKE'])) {
			$conditions[] = "CONCAT (Client.address1,Client.address2,Client.city,Client.state) LIKE '{$conditions['Client.address LIKE']}'";
		}
		unset($conditions['Client.address LIKE']);

		return $conditions;
	}

	//---------------------
	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf(__('Invalid %s', true), __('client', true)), true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('client', $this->__authenticate_site_item($id));

		$this->loadModel('Country');
		$this->loadModel('Language');
		$this->loadModel('Currency');

		$this->set('countries', $this->Country->getCountryList());
		$this->set('languages', $this->Language->getLanguageList());
		$this->set('currencies', $this->Currency->getCurrencyList());
	}

	//---------------------
	function owner_add() {
		$site = getAuthOwner();
		$result = $this->Client->Site->check_add_client_limitation($site['id']);
		if (empty($result['status'])) {
			$this->flashMessage($result['message']);
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($this->data)) {
			$result = $this->Client->saveClient($this->data);
			$client_id= $this->Client->getLastInsertID();
			if ($result['status']) {
				if (!empty($result['data']['send_notify']) && empty($result['data']['suspend'])) {
					//FIXME: Contact details are not sent correctly. Email template file should be set in APP/locale/eng/LC_MESSAGES/emails/ and it should contian proper details for client details.
					$this->set('user',$result['data']);
					if ($this->_send_email_to_user($result['data']['email'],$client_id)) {
						$this->data = array();
						$this->flashMessage(__('Your message has been sent.', true), 'Sucmessage');
					} else {
						$this->flashMessage(__('can\'t send email', true));
					}
				}
				$this->flashMessage(sprintf(__('The %s has been saved', true), __('client', true)), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('client', true)));
			}
		} else {
			$this->data['Client']['client_number'] = $this->Client->get_next_clientno();
		}

		$this->loadModel('Country');
		$this->loadModel('Language');
		$this->loadModel('Currency');

		$this->set('countryCodes', $this->Country->getCountryList());
		$this->set('languageCodes', $this->Language->getLanguageList());
		$this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());

		$this->set('default_language',$this->Client->Site->get_site_language());
		$this->set('default_currency',$this->Client->Site->get_site_currency());
		$this->set('default_country',$this->Client->Site->get_site_country());
	}
	//--------------------------
	function _send_email_to_user($to,$client_id) {
		$site = getAuthOwner();

		$this->Lmail->to = $to;
		$this->Lmail->sendAs = 'html';
		$this->Lmail->layout = 'contact';
		$this->Lmail->template = 'contact';
		$this->Lmail->from = $site['business_name'] . '<' . $site['email'] . '>';
		$this->Lmail->subject = "{$site['business_name']}: " . __('Your Login Details', true);
		$this->Lmail->language = $this->Client->get_client_language($client_id);
		if($this->Lmail->send()) {
			return true;
		}
		return false;
	}
	//---------------------
	function owner_send_login_details($id=null) {
		$referral_url=array('action' => 'index');
		$id=empty ($id)?$_POST['id']:$id;

		if(empty ($id)) {
			$this->flashMessage(sprintf(__('Invalid %s.', 'client', true)));
			$this->redirect($referral_url);
		}
		$client = $this->__authenticate_site_item($id);
		if (!empty($_POST['submit_btn']) ) {
			if($_POST['submit_btn'] == 'yes') {
				$new_password=substr(base64_encode(HashPassword(time() . mt_rand())), 0, 7);
				$client['Client']['password_view']=$client['Client']['password']=$new_password;
				if($this->Client->save($client)) {
					$this->set('user',$client['Client']);
					if($this->_send_email_to_user($client['Client']['email'], $id)) {
						$this->flashMessage(__('The login details has been sent',true),'Sucmessage');
					}else {
						$this->flashMessage(__('The login details could not be sent',true));
					}
				}else {
					$this->flashMessage(__('The login details could not be sent',true));
				}
			}
			$this->redirect($referral_url);
		}
		$this->set('client',$client);
	}
	//---------------------
	function owner_edit($id = null) {
		$referral_url=$this->referer(array('action' => 'index'));

		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf(__('Invalid %s.', 'client', true)));
			$this->redirect($referral_url);
		}
		$client = $this->__authenticate_site_item($id);
		if (!empty($this->data)) {
			$result = $this->Client->saveClient($this->data);

			if ($result['status']) {
				$this->flashMessage(sprintf(__('The %s  has been saved', true), __('client', true)), 'Sucmessage');
				$this->redirect($referral_url);
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('client', true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $client;
			unset($this->data['Client']['password']);
		}

		$this->loadModel('Country');
		$this->loadModel('Language');
		$this->loadModel('Currency');

		$this->set('countryCodes', $this->Country->getCountryList());
		$this->set('languageCodes', $this->Language->getLanguageList());
		$this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());

		$this->render('owner_add');
	}

	//---------------------
	function owner_delete($id = null) {
		$referral_url=$this->referer(array('action' => 'index'));
		//If request type is post
		if (empty($id) && !empty($_POST['ids'])) {
			$id = $_POST['ids'];
		}
		if (!$id && empty($_POST)) {
			$this->flashMessage(sprintf(__('Invalid id for %s', true), __('client', true)));
			$this->redirect($referral_url);
		}
		$site_id = getAuthOwner('id');
		$clients = $this->Client->find('all', array('conditions' => array('Client.id' => $id, 'Client.site_id' => $site_id)));
		if (empty($clients)) {
			$this->flashMessage(sprintf(__('%s not found', true), __('client', true)));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			$id = $_POST['ids'];
			if ($_POST['submit_btn'] == 'yes' && $this->Client->deleteAll(array('Client.id' => $id, 'Client.site_id' => $site_id))) {
				$this->flashMessage(sprintf(__('%s deleted', true), __('client', true)), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->redirect(array('action' => 'index'));
			}
		}
		$this->set('clients', $clients);
	}

	//---------------------------
	function owner_suspend_users($suspend=0,$id=false) {
		$referral_url=array('action' => 'index');
		if(!empty($id)) {
			$_POST['ids']=array();
			$_POST['ids'][]=$id;
		}
		if (empty($_POST['ids']) || !is_array($_POST['ids'])) {
			$this->flashMessage(__('Invalid ids for Clients', true));
			$this->redirect($referral_url);
		}
		$site_id = getAuthOwner('id');
		$clients = $this->Client->find('all', array('conditions' => array('Client.id' => $_POST['ids'], 'Client.site_id' => $site_id)));
		if (empty($clients)) {
			$this->flashMessage(sprintf(__('%s not found', true), __('client', true)));
			$this->redirect(array('action' => 'index'));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			$id = $_POST['ids'];
			if ($_POST['submit_btn'] == 'yes' && $this->Client->updateAll(array('Client.suspend' => $suspend), array('Client.site_id'=>$site_id,'Client.id' => $_POST['ids']))) {
				$this->flashMessage(__('Client status updated', true), 'Sucmessage');
				$this->redirect($referral_url);
			} else {
				$this->flashMessage(__('Unknown error', true));
				$this->redirect($referral_url);
			}
		}
		$this->set('clients', $clients);
	}

	//-----------------------------
	function owner_get_user_data($id = 0) {
		$this->autoRender = false;
		$site_id = getAuthOwner('id');
		Configure::write('debug', 0);
		$client = $this->Client->find(array('Client.id' => $id, 'Client.site_id' => $site_id));
		if ($client) {
			unset($client['Client']['password'], $client['Client']['created'], $client['Client']['modified']);
			$result = array('error' => false, 'client' => $client['Client']);
		} else {
			$result = array('error' => true, 'client' => false);
		}
		die(json_encode($result));
	}
	//--------------------------------
	function login() {
		$this->_is_user();
		if (!empty($this->data)) {
			$user = array();
			$result = $this->Client->login_system($this->data['Client']);
			if ($result['success']) {
				$session_name = strtoupper($result['type']);
				$result['user']['type']=($result['type']=='owner')?'manager':$result['type'];
				$this->Session->write($session_name, $result['user']);
				Cache::delete(getNotificationCacheKey());

				$previous_page = $this->Session->read("LOGIN_REDIRECT");
				if (empty($previous_page)) {
					$previous_page = "/{$result['type']}";
				}
				$this->redirect($previous_page);
			} else {
			    $this->flashMessage(__('Invalid email or password', true), 'Errormessage', 'Loginpage');
			}
		}
		$this->layout = false;
		$this->set('title_for_layout',  __("Client Login", true));
	}



}

?>
