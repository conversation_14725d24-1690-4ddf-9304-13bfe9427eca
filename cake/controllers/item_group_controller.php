<?php

use App\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PermissionUtil as PermissionUtilAlias;
use App\Helpers\CurrencyHelper;

class ItemGroupController extends AppController
{
    public $ProductAttributeOption;

    public function owner_sku_barcode()
    {
        $type = $this->params['url']['type'];
        $skus = [];


        $rows = $this->params['url']['rowsCount'];
        $lastSku = $this->params['url']['lastNonEmptyValue'];

        $values = match ($type) {
            'sku' => $this->generateSkus($rows,$lastSku),
            'barcode' => $this->getAutoBarcode(3, $rows),
            default => []
        };

        die(json_encode(['values' => $values], JSON_UNESCAPED_SLASHES));
    }

    public function generateSingleBarCode($item_type , &$increment = 0)
    {
        $first_block = $item_type;
        $site_id = substr(getCurrentSite('id'), -4);
        $second_block = str_pad($site_id, 4, '0', STR_PAD_LEFT);
        $type_configurations = OIBarcode::getBarcodeTypes($item_type);
        $item_model = ClassRegistry::init($type_configurations['model']);
        $item_id = $this->getLastItemId($item_type) + $increment;
        $third_block = str_pad($item_id + $increment, 7, '0', STR_PAD_LEFT);

        $_12digitcode = $first_block . $second_block . $third_block;
        $code = $first_block . $second_block . $third_block . OIBarcode::ean_checkdigit($_12digitcode);
        while ($item_model->find('first', ['recursive' => -1, 'conditions' => ['barcode' => $code]])) {
            $increment ++;
            $code = $this->generateSingleBarCode($item_type , $increment);
        }

        return $code;
    }

    private function getLastItemId($item_type)
    {
        $item_model = ClassRegistry::init(OIBarcode::getBarcodeTypes($item_type)['model']);
        $lastId = $item_model->find('first', ['fields' => ['MAX(Product.id) AS max_id']]);
        if($lastId) {
            $lastId = $lastId[0]['max_id'];
        } else {
            $lastId = 0;
        }
        return $lastId;
    }


    public function getAutoBarcode($item_type, $count = 1)
    {
        $codes = [];
        $i = 0;
        while (count($codes) < $count ) {
            $code =  $this->generateSingleBarCode($item_type , $i);
            if (!in_array($code , $codes)){
                $codes[] = $code;
            }
            $i ++;
        }
        return $codes;
    }

    public function generateSkus($count = 1 ,$requestLastSku=null)
    {
        if ($requestLastSku){
            for ($i = 0; $i <  $count ; $i++) {
                $skus[] = ++$requestLastSku;
            }
        }
        else{
            for ($i = 0; $i <  $count ; $i++) {
                $sku = \AutoNumber::update_auto_serial(\AutoNumber::TYPE_PRODUCT);
                $skus[] = $sku;
            }
        }
       return $skus;
    }
    public function owner_get_attribute_options()
    {
        $attribute = $this->params['url']['attribute'];

        $optionsQuery = trim($_GET['q']);

        $this->loadModel('ProductAttributeOption');
        $this->ProductAttributeOption->recursive = -1;

        $conditions = [
            'ProductAttributeOption.attribute' =>  $attribute,
        ];
        if ($optionsQuery){
            $conditions[] =   "ProductAttributeOption.option LIKE '%$optionsQuery%'";
        }
        $attributeOptions = $this->ProductAttributeOption->find('all', ['conditions' => $conditions] );

        $optionsArray= [];
        foreach ($attributeOptions as $option){
            $optionsArray[] = ['name'=>$option['ProductAttributeOption']['option'] , 'id'=>$option['ProductAttributeOption']['option']];
        }

        die(json_encode( $optionsArray, JSON_UNESCAPED_SLASHES));
    }


    public function owner_get_attributes()
    {
        $query = trim($_GET['q']);

        $this->loadModel('ProductAttributeOption');
        $this->ProductAttributeOption->recursive = -1;
        $attributeOptions = $this->ProductAttributeOption->find('all', ['conditions' => [
            "ProductAttributeOption.attribute LIKE '%$query%'",
        ]] );
        $optionsArray= [];
        foreach ($attributeOptions as $option){
            $optionsArray[] = ['name'=>$option['ProductAttributeOption']['attribute'] , 'id'=>$option['ProductAttributeOption']['attribute']];
        }

        die(json_encode([$optionsArray], JSON_UNESCAPED_SLASHES));
    }

    public function owner_get_options()
    {
        $optionsQuery = trim($_GET['q']);

        $this->loadModel('ProductAttributeOption');
        $this->ProductAttributeOption->recursive = -1;

        if ($optionsQuery){
            $conditions[] =   "ProductAttributeOption.option LIKE '%$optionsQuery%'";
        }
        $attributeOptions = $this->ProductAttributeOption->find('all', ['conditions' => $conditions] );

        $optionsArray= [];
        foreach ($attributeOptions as $option){
            $value = $option['ProductAttributeOption']['attribute'] ." : " . $option['ProductAttributeOption']['option'];
            $optionsArray[] = ['name'=> $value ,'id'=>$value];
        }

        die(json_encode( $optionsArray, JSON_UNESCAPED_SLASHES));
    }

}