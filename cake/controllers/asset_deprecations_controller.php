<?php

App::import('Vendor', 'settings');

class AssetDeprecationsController extends AppController {

    var $name = 'AssetDeprecations';
//    var $validate_schema;

   
    function beforeFilter() {
        parent::beforeFilter();
        
    }
    function __settings ( ) {
        $this->loadModel('Asset');
        $assets = $this->Asset->find ( 'list');
        $deprecation_methods = AssetDeprecation::getDeprecationMethods () ;
        $this->set ( 'assets' , $assets );
        $this->set ( 'deprecation_methods' , $deprecation_methods );
    }
    function owner_index ( ) {
        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        $conditions = []  ; 
        if ( !empty ( $this->params['url']['asset_id']))
        {
            $conditions['asset_id'] = intval ($this->params['url']['asset_id'] ) ;
        }
        $this->paginate['order'] = 'AssetDeprecation.date DESC';
//        $asset_deprecations = $this->AssetDeprecation->find ( 'all' , ['conditions'=> $conditions]) ;
        $asset_deprecations = $this->paginate ( 'AssetDeprecation' ,  $conditions) ;
        $this->set ( 'asset_deprecations' , $asset_deprecations ) ;
        
    }
    /** 
     * Form for adding an asset Depreciation
     */
    function owner_add ( $asset_id = null  )  
    {
        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel('Asset');
        $asset = $this->Asset->findById($asset_id ?? intval($this->data['AssetDeprecation']['asset_id']));
        if (!empty($this->data)){
            $this->data['AssetDeprecation']['asset_id'] = !empty($asset_id) ? $asset_id : intval($this->data['AssetDeprecation']['asset_id']);
            $depreciationResult = $this->Asset->addDepreciation($this->data, $asset, false);
            if($depreciationResult['status']){
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Depreciation', true)), 'Sucmessage');
                $this->redirect (Router::url(['controller' => 'assets',  'action' => 'view' , $this->data['AssetDeprecation']['asset_id'] ]) ) ;
            } else {
                $errors = $depreciationResult['errors'] ?? null;
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Depreciation', true)));
            }
        }
        if(!empty($asset_id)){
            $this->data['AssetDeprecation']['asset_id'] = intval($asset_id); 
        }
        $this->__settings();
        $this->set('asset', $asset);
    }
    /**
     * Form for editing an asset Depreciation
     */
    function owner_edit ( $id ) 
    {
        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->__settings () ;
        $asset_deprecation = $this->AssetDeprecation->findById ( $id ) ;
        $this->loadModel('Asset') ;
        \App\Helpers\BranchHelper::redirectIfRecordNotInCurrentBranch($asset_deprecation['Asset']['branch_id']);
        $asset_id = $asset_deprecation['AssetDeprecation']['asset_id'];
        $this->validate_open_day($asset_deprecation['AssetDeprecation']['date']);

        if ( !empty ( $this->data ) ){
            $last_deprecation_date = max([strtotime($this->data['AssetDeprecation']['date']) , strtotime($asset_deprecation['Asset']['last_deprecation_date']) ] );
            if ( $asset_deprecation['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS)
            {
                //we take the cost for the total units to be deprecated 
                $total_units = $this->data['AssetDeprecation']['cost'] ; 
                $this->data['AssetDeprecation']['cost'] =(float)$this->data['AssetDeprecation']['cost'] * (float)$asset_deprecation['Asset']['cost_per_unit'];
                $asset_deprecation['Asset']['deprecation_total_units'] = $asset_deprecation['Asset']['deprecation_total_units'] + $total_units - ($asset_deprecation['AssetDeprecation']['cost'] / $asset_deprecation['Asset']['cost_per_unit']);
            }
            $this->Asset->id = $this->data['AssetDeprecation']['asset_id'] ;
            $this->Asset->save ( ['Asset' =>[
                'asset_current_value' => ((float)$asset_deprecation['Asset']['asset_current_value'] + (float)$asset_deprecation['AssetDeprecation']['cost'] - (float)$this->data['AssetDeprecation']['cost'] ) ,
                'last_deprecation_date' => date('Y-m-d H:i:s' , $last_deprecation_date ),
                'total_deprecation' => $asset_deprecation['Asset']['total_deprecation'] - $asset_deprecation['AssetDeprecation']['cost'] + (float)$this->data['AssetDeprecation']['cost'],
                'deprecation_total_units' => $asset_deprecation['Asset']['deprecation_total_units']
            ] ]);
           if ( $asset_deprecation_id = $this->AssetDeprecation->update ( $id , $this->data , $validation_errors )) {
               $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Depreciation', true)), 'Sucmessage');
               $this->redirect (Router::url(['controller' => 'assets',  'action' => 'view' , $this->data['AssetDeprecation']['asset_id'] ]) ) ;
           } else {
               $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Depreciation', true)));
           }
       }else {
           $this->data = $this->AssetDeprecation->find ('first' ,['conditions' => ['AssetDeprecation.id' => $id] ] ) ;
            if($this->data['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS){
                $this->data['AssetDeprecation']['cost'] =(float)$this->data['AssetDeprecation']['cost'] / (float)$this->data['Asset']['cost_per_unit'];
            }
           $this->data['AssetDeprecation']['date'] =format_date ($this->data['AssetDeprecation']['date'] ) ;
       }
        $this->set('asset_deprecation', $asset_deprecation);
        $this->set('asset', $this->Asset->findById($asset_deprecation['AssetDeprecation']['asset_id']));
        $this->render('owner_add');
    }
    function owner_delete (  $id = null ) {
        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if ( !empty ($_POST['ids'] )){
                $ids = $_POST['ids'];
                $id = $_POST['ids'][0];
        }else {
                $ids = [$id];
                $_POST['ids'][] = $id;
        }
        $reread_deprecation = $this->AssetDeprecation->find('first', ['conditions' => ['AssetDeprecation.id' => $id]]);
        $this->validate_open_day($reread_deprecation['AssetDeprecation']['date']);

        if (empty ($reread_deprecation)) {
            $this->flashMessage(sprintf ( __('%s not found', TRUE) , __("Asset Depreciation" , true)) );
            $this->redirect(array('action' => 'index'));
        }
        $asset_id = $reread_deprecation['AssetDeprecation']['asset_id'];
        if ($_POST['submit_btn'] == 'yes' && !empty ($ids) ) {
            $this->loadModel('Asset') ;
            foreach ( $ids as $i ){
                $asset_deprecation = $this->AssetDeprecation->findById ( $i ) ;
                $this->AssetDeprecation->delete($i ) ;
                $this->Asset->id = $asset_deprecation['Asset']['id'];
                $this->Asset->save ( ['Asset' =>[
                    'id' => $this->Asset->id,
                    'total_deprecation' => ($asset_deprecation['Asset']['total_deprecation'] - $asset_deprecation['AssetDeprecation']['cost'] ) ,
                    'deprecation_total_units' => ($asset_deprecation['Asset']['deprecation_total_units'] - ($asset_deprecation['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_UNITS ? ($asset_deprecation['AssetDeprecation']['cost'] / $asset_deprecation['Asset']['cost_per_unit']) : $asset_deprecation['Asset']['deprecation_total_units']) ) ,
                ] ]);
                
            }
//            if ( !empty ( $reread_asset ) ) {
//                $this->Asset->deleteAll($id , true ) ;
//                
//            }

            $this->Asset->updateAssetLastDeprecationDate($asset_id);
            $this->flashMessage(sprintf (__('%s has been deleted', TRUE) , (count ($ids ) > 1? __("Asset Deprecations",true) :__("Asset Depreciation" , true ) )  ) , 'Sucmessage' ,'secondaryMessage');
            if ( count( $ids ) > 1  ) {
                $this->redirect(['action' => 'index']);
            }else {
                $this->redirect ( ['controller' => 'assets' , 'action' => 'view' , $asset_id ]) ;
            }
            
        } else if($_POST['submit_btn'] == 'no' ){
            if (!empty($ids) && count($ids) > 1) {
                $this->redirect(['action' => 'index']);
            } else {
                $this->redirect('/v2/owner/asset_deprecations/view/'. $ids[0]);
            }
        }
        
        
        
    }
}
