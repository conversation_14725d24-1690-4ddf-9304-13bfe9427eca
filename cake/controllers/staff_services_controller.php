<?php

use App\Helpers\UsersHelper;
use Izam\Daftra\Common\Utils\PermissionUtil;

class StaffServicesController extends AppController {

	var $name = 'StaffServices';

	/**
	 * @var StaffService
	 */
	var $StaffService;
	var $helpers = array('Html', 'Form');

	function owner_index() {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$this->StaffService->recursive = 0;
		$conditions = $this->_filter_params();
		if (isset($conditions['StaffService.service_id'])) {
		    $staffIDs = $this->StaffService->find('list',['conditions' => $conditions, 'fields' => ['staff_id', 'staff_id']]);
		    $conditions['StaffService.staff_id'] = array_keys($staffIDs);
		    unset($conditions['StaffService.service_id']);
        }
		$data = $this->StaffService->find('all', ['conditions' => $conditions, 'order' => 'Staff.name']);
		$data = Set::combine($data, '{n}.StaffService.service_id', '{n}.Service', '{n}.StaffService.staff_id');
		$this->set('staff_services', $data);
		$this->set('staffList', $this->StaffService->Staff->getList());
	}

	function owner_add() {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		if (!empty($this->data)) {
            $this->commonSave();
		}
        $this->set('staffList', UsersHelper::getInstance()->getList(false,[],false,true));
	}

	function owner_edit($id = null) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', true), __('Staff Service',true)));
			$this->redirect(array('action'=>'index'));
		}
        $data = $this->StaffService->find('all', ['conditions' => ['StaffService.staff_id' => $id]]);
        $data = Set::combine($data, '{n}.StaffService.service_id', '{n}.Service', '{n}.StaffService.staff_id');
		if (!empty($this->data)) {
            $this->commonSave();
		}
        $services = [];
		if (empty($this->data)) {
            $this->data['StaffService']['staff_id'] = $id;
			foreach ($data[$id] as $serviceID => $service) {
			    $services[$serviceID] = $service['name'];
            }
            $this->data['StaffService']['service_id'] = array_keys($services);
		}
		$this->set('services' , $services);
        $this->set('staffList', $this->StaffService->Staff->getList(false));
	}

	function owner_delete($id = null) {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', true), __('Staff Service',true)));
            $this->redirect(array('action'=>'index'));
        }
        $this->StaffService->deleteAll(['StaffService.staff_id' => $id]);
        $this->flashMessage(sprintf(__('%s deleted', true), __('Staff Service',true)), 'Sucmessage');
        $this->redirect(array('action'=>'index'));
	}

	function commonSave() {
        $staffID = $this->data['StaffService']['staff_id'];
        $services = $this->data['StaffService']['service_id'];
        $this->StaffService->deleteAll(['StaffService.staff_id' => $staffID]);
        foreach ($services as $service) {
            $this->StaffService->create();
            $this->StaffService->save([
                'StaffService' => [
                    'staff_id' => $staffID,
                    'service_id' => $service
                ]
            ]);
        }
        $this->flashMessage(sprintf (__('The %s has been saved', true), __('Staff Service',true)), 'Sucmessage');
        $this->redirect(array('action'=>'index'));
    }
}
?>
