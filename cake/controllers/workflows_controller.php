<?php

class WorkflowsController extends AppController
{
    function client_index($id = null)
    {
        $conditions = parent::_filter_params();
        $conditions['client_id'] = getAuthClient('id');
        $conditions['workflow_type_id'] = $id;

        $this->set('work_orders', $this->paginate('WorkOrder' , $conditions));
    }

    function owner_preview($id = null)
    {
        $this->loadModel('Client');
        $this->loadModel('Workflow');
        $work_order = $this->WorkOrder->findById ( $id) ;

        if ( empty ( $work_order ) ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Work Order",true)) );
            $this->redirect ( 'index'); die ;
        }
        $additional_styles = '<style type="text/css">'.'table { overflow: visible !important; }
thead { display: table-header-group }
tfoot { display: table-row-group }
tr { page-break-inside: avoid }' . file_get_contents(WWW_ROOT . 'css/common.css');
        $additional_styles .= PHP_EOL . file_get_contents(WWW_ROOT . 'css/work-order-template.css');
        if (CurrentSiteLang() == 'ara') {
            $additional_styles .= PHP_EOL . file_get_contents(WWW_ROOT . 'css/work-order-template-ar.css');
        }

        $additional_styles .= '</style>';
        $view_work_order = $this->Workflow->generate_work_order_html ( $id ,$additional_styles) ;
        $this->layout = '' ;
        $this->set ( 'style' ,$additional_styles ) ;
        $this->set ( 'view_work_order' ,$view_work_order );
        $this->set ( 'work_order' , $work_order ) ;
    }

    function client_preview($id = null)
    {
        $work_order = $this->WorkOrder->find('first' , ['fields' => 'id', 'recursive' => -1 ,'conditions' => [  'id' => $id ,'client_id' => getAuthClient('id')] ]);

        if (empty($work_order )) {
            $this->flashMessage(sprintf(__("%s is not found",true),__('Workflow',true)) ) ;
            $this->redirect(['controller' => "work_orders" ]);
        }

        $this->owner_preview($id);

        $this->render('owner_preview');
    }

    function client_view($id )
    {
        $work_order = $this->WorkOrder->find('first', [
            'recursive' => -1 ,'conditions' => ['id' => $id ,'client_id' => getAuthClient('id')]
        ]);

        if (empty($work_order)) {
            $this->flashMessage(sprintf(__("%s is not found",true),__('Workflow', true)) ) ;
            $this->redirect(['controller' => 'work_orders']);
        }

        if ($this->params['url']['ext'] == 'jpeg') {
            return $this->owner_view($id);
        }

        $this->set('postsCount', $this->Workflow->getClientPostsCount($id, getAuthClient('id')));
        
        $this->loadModel('PrintableTemplate');
        $this->set('default_template', $this->PrintableTemplate->getDefaultTemplateForType('work_order'));
        $this->set('work_order', $work_order);
    }
}