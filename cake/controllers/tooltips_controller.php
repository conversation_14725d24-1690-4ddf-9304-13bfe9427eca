<?php

use Izam\Daftra\Portal\Services\TooltipService;

/**
 * @property Tooltip $Tooltip
 */
class TooltipsController extends AppController {

    var $name = 'Tooltips';
    var $helpers = array('Html', 'Form','Fck' , 'Mixed');

    function admin_index() {
        $conditions=$this->_filter_params();
        $tooltips=array();


		if(!empty ($this->data['Tooltip']['language_id']) && !empty ($this->data['Tooltip']['tooltip_name'])){
			$this->redirect("/admin/tooltips/edit/{$this->data['Tooltip']['tooltip_name']}/{$this->data['Tooltip']['language_id']}");
		}

        foreach($this->Tooltip->tooltipss as $tooltip) {
            $tooltips[]=$tooltip['title'];
        }
        $list_tooltips= $this->paginate('Tooltip',$conditions);

        $this->set('list_tooltips', $list_tooltips);
        $this->loadModel('Language');
		$this->set('languages',$this->Language->getLanguageList());
		$this->set('tooltips',$tooltips);
    }

//----------------------
    function admin_edit($name= null,$language_id= null) {

        if ((empty ($name) || empty ($language_id)) ) {
            $this->flashMessage(__('Invalid tooltip', true));
            $this->redirect(array('action'=>'index'));
        }

        if (!empty($this->data)) {
			$this->data['Tooltip']['language_id']=$language_id;
            if (TooltipService::updateById($this->data['Tooltip']['id'], $this->data['Tooltip'])) {
                $this->flashMessage(__('Tooltip has been saved', true), 'Sucmessage');
                $this->redirect(array('action'=>'index'));
            } else {
                $this->flashMessage(__('Tooltip could not be saved. Please, try again.', true));
            }
        } else {
			$tooltip = $this->Tooltip->find('first',array('conditions'=>array('Tooltip.name' => $name, 'Tooltip.language_id'=>$language_id)));
			if ($tooltip){
				$this->data = $tooltip;
			} else {
				$this->data['Tooltip']['language_id'] = $language_id;
				$this->data['Tooltip']['name'] = $name;
				$this->data['Tooltip']['content'] = '';
			}
//            $id= $this->Tooltip->field('id', array('Tooltip.name'=>$name,'Tooltip.language_id'=>$language_id));
//            //if this tooltip does not exist in the DB , Add it
//            if(empty ($id)) {
//                if(!$this->Tooltip->save(array('Tooltip'=>array('name'=>$name,'language_id'=>$language_id)))) {
//                    $this->flashMessage(__('System Error, please contact the admin', true));
//                    $this->redirect(array('action'=>'index'));
//                }
//                else {
//                    $id=$this->Tooltip->id;
//                }
//            }
//            $this->data = $this->Tooltip->find('first',array('conditions'=>array('Tooltip.id'=>$id,'Tooltip.language_id'=>$language_id)));
        }
        /*$this->set('advanced_editor',
            isset($this->Tooltip->tooltipss[$name]['advanced_editor'])&&!$this->Tooltip->tooltipss[$name]['advanced_editor']?false:true );
            $this->set('tooltip_properties',$this->Tooltip->tooltipss[$name]);*/
		$this->set('language_id',$language_id);
		$this->set('name',$name);
        $this->render('admin_add');
    }
//------------------------------
    function admin_delete($id = null) {
        if (!$id) {
            $this->flashMessage(__('Invalid tooltip', true));
            $this->redirect(array('action'=>'index'));
        }
        if ($this->Tooltip->del($id)) {
            $this->flashMessage(__('tooltip has been deleted', true), 'Sucmessage');
            $this->redirect(array('action'=>'index'));
        }else{
            $this->flashMessage(__('Could not delete tooltip', true));
            $this->redirect(array('action'=>'index'));
        }
    }
	//---------------------------------
    function admin_delete_multi() {
        if (empty($_POST['ids'])||!is_array($_POST['ids'])) {
            $this->flashMessage(__('Invalid tooltips', true));
            $this->redirect(array('action'=>'index'));
        }
        if ($this->Tooltip->deleteAll(array('Tooltip.id'=>$_POST['ids']))) {
            $this->flashMessage(__('Tooltips have been deleted', true), 'Sucmessage');
            $this->redirect(array('action'=>'index'));
        }
        else {
            $this->flashMessage(__('Could not delete tooltips', true));
            $this->redirect(array('action'=>'index'));
        }
    }
	//---------------------------
	function admin_get_tooltips($language_id= null){
		Configure::write('debug',0);
		$this->layout=$this->autoRender=false;

		$tooltips= $this->Tooltip->find('list',array('conditions'=>array('language_id'=>$language_id),'order'=>'Tooltip.name'));

		$org_tooltips=array();
		foreach($this->Tooltip->tooltipss as $tooltip){
			if(!in_array($tooltip['name'], $tooltips)){
				$org_tooltips[]=array('name'=>$tooltip['name'],'title'=>$this->Tooltip->getTooltipTitle($tooltip['name']));
			}
		}
		echo json_encode($org_tooltips);
	}
	
	function tooltip($name =null, $lang = 0) {
        $site= getCurrentSite();
        $site_lang=$site['language_code']?$site['language_code']: 41;
		$this->layout = 'ajax';
        $this->set('tooltip', $this->Tooltip->field('content', array('Tooltip.name' => $name, 'Tooltip.language_id' => array($site_lang,intval($lang), 41)), 'FIELD(`language_id`,'.$site_lang.',' . intval($lang) . ',41)'));
    }
        
}