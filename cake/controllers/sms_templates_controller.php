<?php
class SmsTemplatesController extends AppController {

	var $name = 'SmsTemplates';

	/**
	 * @var SmsTemplate
	 */
	var $SmsTemplate;
	var $helpers = array('Html', 'Form');
	var $uses = ["SmsTemplate", "EmailTemplate"];

	function addBreadCrumbs($model, $url, $data = [])
	{
		parent::addBreadCrumbs($model, $url, $data);

		if (array_key_exists('_PageBreadCrumbs', $this->viewVars)) {
			$parentBC = $this->viewVars['_PageBreadCrumbs'];
			$parentBC = array_map(fn($item) => ['title' => str_replace('Sms', 'SMS', $item['title'])] + $item, $parentBC);
			$this->set('_PageBreadCrumbs', $parentBC);
		}
	}

	function owner_index() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', true));
                $this->redirect('/');
            }
        }
		$this->SmsTemplate->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('smsTemplates', $this->paginate('SmsTemplate', $conditions));
	}

	function owner_add($type = 'invoice') {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
		App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
        $this->set('PlaceHolders', $this->EmailTemplate->getPlaceHoldersList($type));
        $this->set('types', $this->EmailTemplate->getTypesList());
		if (!empty($this->data)) {
			$this->SmsTemplate->create();
			if ($this->SmsTemplate->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('SMS template',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('SMS template',true)));
			}
		} else {
            $this->data = $this->EmailTemplate->getDefaultTemplate($type);
            $this->data['SmsTemplate']['type'] = $type;
            unset($this->data['SmsTemplate']['id']);
        }
	}

	function owner_view($id = null)
    {
        $this->redirect(array('action' => 'edit', $id));
    }

	function owner_edit($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'SMS template',true)));
			$this->redirect(array('action'=>'index'));
		}
		App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
		$smsTemplate = $this->SmsTemplate->read(null, $id);
		$type = $smsTemplate['SmsTemplate']['type'];
        $this->set('PlaceHolders', $this->EmailTemplate->getPlaceHoldersList($type));
        $this->set('types', $this->EmailTemplate->getTypesList());
		if (!empty($this->data)) {
			if ($this->SmsTemplate->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('SMS template',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('SMS template',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $smsTemplate;
		}
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('SMS template',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('SMS Template', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('SMS Templates', true);
		 } 
		$smsTemplates = $this->SmsTemplate->find('all',array('conditions'=>array('SmsTemplate.id'=>$id)));
		if (empty($smsTemplates)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		$this->loadModel('AutoReminderRule');
		foreach($smsTemplates as $template){

		$count=$this->AutoReminderRule->find('count',array('conditions'=>['AutoReminderRule.channel'=>1,'AutoReminderRule.channel_template_id'=>$template['SmsTemplate']['id']]));
		if($count>0){
            $this->flashMessage(sprintf(__('%s with id #%s can not be deleted because it\'s inuse in auto reminder rule', true), $module_name,$template['SmsTemplate']['id']));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        }
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->SmsTemplate->deleteAll(array('SmsTemplate.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('smsTemplates',$smsTemplates);
	}
}
?>
