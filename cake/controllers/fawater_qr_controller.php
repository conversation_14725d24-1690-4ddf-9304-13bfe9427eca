<?php


class FawaterQrController extends Controller
{
    var $uses = array();
    public function  owner_qr()
    {
        $qr_data = explode('"', base64_decode($_GET['d64']));
        $data =  self::toTLV($qr_data);;
        return redirect("/qr/?d64=$data");
    }

    private static function toTLV($tags)
    {
        $data = '';

        foreach ($tags as $b => $value) {
            $value = (string) $value;
            $data .= self::toHex($b) . self::toHex(strlen($value)) . ($value);
        }

        return base64_encode(base64_encode($data));
    }

    private static function toHex($value)
    {
        return pack('H*', sprintf('%02X', $value));
    }
}
