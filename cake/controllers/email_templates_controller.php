<?php

App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));

/**
 * @property $SystemEmail SystemEmail
 */
class EmailTemplatesController extends AppController {

    var $name = 'EmailTemplates';

    /**
     * @var EmailTemplate
     */
    var $EmailTemplate;
    var $helpers = array('Html', 'Form', 'Fck');
    var $titleAlias = 'templates';

    function get_place_holders($type = 'invoice') {
        $this->layout = '';
        $templates_types = EmailTemplate::getTypes($type);
        $this->set('placeHolders', $templates_types['placeholders']);
    }

    function owner_test_mail() {
        if (!empty($_POST)) {
            $this->Email->to = $_POST['to'];
            $this->Email->subject = $_POST['subject'];
            $this->Email->from = $_POST['from'];
            $this->Email->sendAs = 'html';
            $this->Email->send($_POST['msg']);
        }
    }

    function owner_index() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', true));
                $this->redirect('/');
            }
        }
        $this->EmailTemplate->recursive = 0;
        $conditions = $this->_filter_params();
        $conditions['EmailTemplate.site_id'] = getAuthOwner('id');

        $this->paginate['EmailTemplate'] = array('conditions' => $conditions,
            'order' => array('EmailTemplate.title asc'),
        );


        $this->set('emailTemplates', $this->paginate());
        $this->set('content', $this->get_snippet('email-templates'));

        $this->loadModel('SystemEmail');
        $sysEmails = $this->SystemEmail->getSiteEmails();

        debug ( $sysEmails  ) ;
        foreach ($sysEmails as $i => $temp) {
            if (isset($temp['SystemEmail']['editable']) && $temp['SystemEmail']['editable'] == false)
                unset($sysEmails[$i]);
            if (isset($temp['SystemEmail']['plugin']) && !ifPluginActive($temp['SystemEmail']['plugin']))
                unset($sysEmails[$i]);
        }

        $this->set('systemEmails', $sysEmails);
        $this->set('panels', $this->_getpanels());
        $this->set('canAdd', count($this->EmailTemplate->getTypesList()));
        $this->set('title_for_layout',  __('Email Templates', true));
    }

    function owner_view($id = null) {
        $this->redirect(['action'=>'edit',$id]);
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('email template', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $this->set('emailTemplate', $this->EmailTemplate->read(null, $id));
    }

    function owner_add($type = 'invoice') {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }


        if (!empty($this->data)) {
            $this->EmailTemplate->create();
            $this->data['EmailTemplate']['site_id'] = getAuthOwner('id');
            if ($this->EmailTemplate->save($this->data)) {

                $this->add_actionline(ACTION_ADD_EMAIL_TEMPLATE, array('primary_id' => $this->EmailTemplate->id, 'param3' => $this->data['EmailTemplate']['title'], 'param4' => $this->data['EmailTemplate']['type']));
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Email template', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Email template', true)));
            }
            $type = $this->data['EmailTemplate']['type'];
        } else {
            $this->data = $this->EmailTemplate->getDefaultTemplate($type);
            $this->data['EmailTemplate']['type'] = $type;
            unset($this->data['EmailTemplate']['id']);
        }
        //$this->set('file_settings', $this->EmailTemplate->getFileSettings());
        $this->set('placeholders', $this->EmailTemplate->getPlaceHoldersList($type));
        $this->set('types', $this->EmailTemplate->getTypesList());
        $this->set('addEmailTemplateText', $this->get_snippet('add-email-template'));
        $this->set('title_for_layout',  __('Add Email Template', true));
    }

    function owner_edit($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }

        $site_id = getAuthOwner('id');
        $emailTemplate = $this->EmailTemplate->find(array('EmailTemplate.id' => $id));
        if (!$emailTemplate) {
            $this->flashMessage(sprintf(__('%s not found.', true),  __('Email template', true)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }


        if (!empty($this->data)) {
            $this->data['EmailTemplate']['id'] = $id;
            $this->data['EmailTemplate']['site_id'] = $site_id;
            if ($this->EmailTemplate->save($this->data)) {
                $this->add_actionline(ACTION_UPDATE_EMAIL_TEMPLATE, array('primary_id' => $id, 'param3' => $this->data['EmailTemplate']['title'], 'param4' => $this->data['EmailTemplate']['type']));
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Email template', true)), 'Sucmessage');
                if (!empty($_GET['box'])) {
                    $this->render('blank');
                    return;
                } else {
                    $this->redirect(array('action' => 'index'));
                }
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Email template', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $emailTemplate;
        }
        $type = $this->data['EmailTemplate']['type'];

        //$this->set('file_settings', $this->EmailTemplate->getFileSettings());
        $this->set('placeholders', $this->EmailTemplate->getPlaceHoldersList($type));
        $this->set('types', $this->EmailTemplate->getTypesList());
        //    $this->set('placeHoldersContent', $this->requestAction('/email_templates/get_place_holders/' . $this->data['EmailTemplate']['type'], array('return')));
        $this->set('title_for_layout',  __('Edit Email Template', true));


        $this->render('owner_add');
    }

    //----------------------------
    function owner_delete($id = false) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->_record_referer_path();

        //If request type is post
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }


        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('email template', true)));
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }
        $module_name = __('Email template', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $module_name = __('Email templates', true);
            $verb = __('have', true);
        }

        $site_id = getAuthOwner('id');
        $templates = $this->EmailTemplate->find('all', array('conditions' => array('EmailTemplate.id' => $id, 'EmailTemplate.site_id' => $site_id)));
        if (empty($templates)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }
        $this->loadModel('AutoReminderRule');
        foreach($templates as $template){

            $count=$this->AutoReminderRule->find('count',array('conditions'=>['AutoReminderRule.channel'=>2,'AutoReminderRule.channel_template_id'=>$template['EmailTemplate']['id']]));
            if($count>0){
                $this->flashMessage(sprintf(__('%s with id #%s can not be deleted because it\'s inuse in auto reminder rule', true), $module_name,$template['EmailTemplate']['id']));
                $this->redirect($this->referer(array('action' => 'index'), true));
            }
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && $this->EmailTemplate->deleteAll(array('EmailTemplate.id' => $id, 'EmailTemplate.site_id' => $site_id))) {
                foreach ($templates as $template)
                    $this->add_actionline(ACTION_DELETE_EMAIL_TEMPLATE, array('primary_id' => $template['EmailTemplate']['id'], 'param3' => $template['EmailTemplate']['title'], 'param4' => $template['EmailTemplate']['type']));
                $this->flashMessage(sprintf(__('%s %s deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
            }
            $referer_url = $this->_get_referer_path();
            $this->redirect($referer_url);
        }

        $this->set('title_for_layout',  __('Delete Email Template', true));
        $this->set('templates', $templates);
        $this->set('module_name', $module_name);

        $this->set('title_for_layout',  __('Delete Email Template', true));
    }

    function owner_edit_system_email($type) {
        
        $this->loadModel('SystemEmail');
        $this->loadModel('DefaultSystemEmail');

        $type = low($type);

        $defaultEmails = DefaultSystemEmail::getDefaultSpecs();
        if (!isset($defaultEmails[$type])) {
            $this->flashMessage(__('Email not found', true));
            $this->redirect(array('action' => 'index', '#' => 'tab:SystemEmails'));
        }

        $site_id = getAuthOwner('id');

        $this->SystemEmail->applyBranch['onFind'] = false;
        $parentEmail = $this->SystemEmail->find(array('SystemEmail.site_id' => $site_id, 'SystemEmail.type' => $type));
        if ($parentEmail) {
            $parentEmail = $parentEmail['SystemEmail'];
        } else {
            $parentEmail = $this->DefaultSystemEmail->find(array('DefaultSystemEmail.type' => $type, 'DefaultSystemEmail.language_id' => getCurrentSite('language_code')));
            $parentEmail = $parentEmail['DefaultSystemEmail'];
        }

        $specs = $defaultEmails[$type];
        $this->set(compact('parentEmail', 'type', 'specs'));

        if (!empty($this->data)) {
            $this->data['SystemEmail']['site_id'] = $site_id;
            $this->data['SystemEmail']['type'] = $type;
            if ($this->SystemEmail->saveEmail($this->data)) {
                $this->flashMessage(__('Email template has been saved', true), 'Sucmessage');
                $this->redirect(array('action' => 'index', '#' => 'tab:SystemEmails'));
            } else {
                $this->flashMessage(__('Could not save the email message', true));
            }
        } else {
            $this->data['SystemEmail'] = $parentEmail;
        }
    }

    function owner_reset_system_email($type) {
        $this->loadModel('DefaultSystemEmail');
        $specs = DefaultSystemEmail::getDefaultSpecs();
        $type = low($type);

        if (!isset($specs[$type])) {
            die(json_encode(array('error' => true)));
        }
        $this->loadModel('SystemEmail');
        $systemEmail = $this->SystemEmail->find('first',['conditions' => ['SystemEmail.type' => $type], 'fields' => ['id']]);
        $this->SystemEmail->delete($systemEmail['SystemEmail']['id'], true);

        $this->loadModel('EmailTemplate');
        $emailTemplate = $this->EmailTemplate->find('first',['conditions' => ['EmailTemplate.type' => $type], 'fields' => ['id']]);
        $this->EmailTemplate->delete($emailTemplate['EmailTemplate']['id'], true);
        
        $email = $this->DefaultSystemEmail->find('all', ['conditions' => ['DefaultSystemEmail.type' => $type]]);
        $filteredEmails = array_values(array_filter($email, fn($email) => $email['DefaultSystemEmail']['language_id'] == getCurrentSite('language_code')));
        if ($filteredEmails) {
            $email = $filteredEmails[0];
        } else {
            $email = $dbEmail[0];
        }
        if ($email) {
            $response = array('error' => false, 'subject' => $email['DefaultSystemEmail']['subject'], 'message' => $email['DefaultSystemEmail']['message']);
        } else {
            $response = array('error' => false, 'subject' => '', 'message' => '', 'empty' => true);
        }

        die(json_encode($response));
    }

    function owner_placeholder($item="", $all = 0) {
        $this->set('all', $all);
		if($item)
        $lfunction = $item . '_place_holder_list';
        $wfunction = $item . '_place_holder';
        $class = new PlaceHolder();
        if ($all == 0) {
            if (method_exists($class, $lfunction)) {

                $site = $this->config['txt.domain'];
                $this->set('site' ,$site);
                $this->set('place_holders' ,PlaceHolder::$lfunction($site));

            } else {
                $this->flashMessage(sprintf(__('no place holders for %s', $item, true), $item));
            }
        } else {
            if (method_exists($class, $wfunction)) {
                $this->set('place_holders', array_keys(PlaceHolder::$wfunction()));
            } else {
                $this->flashMessage(sprintf(__('no place holders for %s', $item, true), $item));
            }
        }
		   $this->set('title_for_layout',  __("Full Variables Guide",true));
    }
    function owner_testplaceholder($item, $all = 0) {
        $this->set('all', $all);
        $lfunction = $item . '_place_holder_list';
        $wfunction = $item . '_place_holder';
        $class = new PlaceHolder();
        if ($all == 0) {
            if (method_exists($class, $lfunction)) {
                $this->set('place_holders', PlaceHolder::$lfunction());
            } else {
                $this->flashMessage(sprintf(__('no place holders for %s', $item, true), $item));
            }
        } else {
            if (method_exists($class, $wfunction)) {
                $this->set('place_holders', array_keys(PlaceHolder::$wfunction()));
            } else {
                $this->flashMessage(sprintf(__('no place holders for %s', $item, true), $item));
            }
        }
    }

    function _getpanels() {
        return array(
            'invoice' => __('Invoices & Estimates', true),
            'sales_order' => __('Sales Order', true),
            'client' => __('Clients', true),
            'staff' => __('Staff Members', true),
            'payment' => __('Payments', true),
            'purchaseorders' => __('Purchase Invoices', true),
            'refund' => __('Refund Receipts', true),
            'creditnote' => __('Credit notes', true),
            'quoterequests' => __('Quote request', true),
            'work_orders' => __('Work Orders', true),
            'shop_front' => __('Shop Front', true),
            'appointment' => __('Appointment', true),
            'booking' => __('Booking', true),

        );
    }

}
