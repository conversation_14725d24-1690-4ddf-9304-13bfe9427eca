<?php

use Izam\Daftra\Portal\Models\SitesGoogleContact as PortalSitesGoogleContact;

App::import('Vendor','GoogleContactsApi', array('file' => 'GoogleContactsApi/GoogleContactsAPI.php'));

class SitesGoogleContactsController extends AppController {

	var $name = 'SitesGoogleContacts';

	/**
	 * @var SitesGoogleContact
	 */
	var $SitesGoogleContact;
	var $helpers = array('Html', 'Form');

    /*
     * get all the contacts on the google contacts account
     */
    function get_all_contacts_on_google_api()
    {
        die(debug(GoogleContactsApi::getAllContacts()));
    }

    /**
     * get all the contacts in the sites_google_contacts table portal database
     */
    function get_all_contacts_from_portal()
    {
        ini_set('memory_limit','2G');
        $this->loadModel('SitesGoogleContact');
        $sites = $this->SitesGoogleContact->find('all');
        die(debug($sites));
    }

    /**
     * this method will start inserting contacts after the id passed
     * @param $id => id that will start getting from
     */
    function copy_contacts_starting_from_id($id = null)
    {
        //get all the ids afterr ID
        $this->loadModel('Site');
        $sites = $this->Site->find('all');

        $contacts = [];
        foreach($sites as $site)
        {
            if( $site['Site']['id'] >= $id)
                $contacts[] = $site;
        }

//        dd($contacts);
        //insert all the users according to this RULE NotEmpty(bussiness_name) && is_Number(phone_1 | phone_2)
        $this->insert_sites_contacts($contacts);
        die(debug("Done"));
    }

    /**
     * @param $sites => the sites that will be inserted into the database
     */
    function insert_sites_contacts($sites)
    {
        foreach($sites as $site)
        {
            $messageData = [
                'name'=> $site['Site']['business_name'],
                'phone_number'=> (empty($site['Site']['phone1']) ? $site['Site']['phone2'] : $site['Site']['phone1']) ,
                'note'=> $site['Site']['id'],
                'email_address'=> $site['Site']['email']
            ];

            if(
                empty($messageData['name']) ||
                empty($messageData['phone_number']) || preg_match("/([a-zA-Z]+)/", $messageData['phone_number']) ||
                empty($messageData['note']) ||
                empty($messageData['email_address']))
                continue;

            $t = GoogleContactsApi::createContact($messageData);

            //if there is error continue
            if(array_key_exists('error', $t))
                die(debug("Error Occured"));

            $data['SitesGoogleContact'] = [
                'site_id' => $site['Site']['id'],
                'google_contacts_self_url' => $t->selfURL,
            ];

//            die(debug($data));

            if (PortalSitesGoogleContact::create($data['SitesGoogleContact'])) {
                $this->flashMessage(sprintf (__('The %s has been added on your google contacts Account', true), __('Contacts',true)), 'Sucmessage');
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Contact',true)));
            }
        }
    }

    /**
     * will loop on the sites in portal and insert their contacts in GoogleContacts
     */
    function copy_contacts_from_portal()
    {
        $this->loadModel('Site');
        $sites = $this->Site->find('all');

        foreach($sites as $site)
        {
            $messageData = [
                'name'=> $site['Site']['business_name'],
                'phone_number'=> (empty($site['Site']['phone1']) ? $site['Site']['phone2'] : $site['Site']['phone1']) ,
                'note'=> $site['Site']['id'],
                'email_address'=> $site['Site']['email']
            ];

            if(
                empty($messageData['name']) ||
                empty($messageData['phone_number']) ||
                empty($messageData['note']) ||
                empty($messageData['email_address']))
                    continue;

            $t = GoogleContactsApi::createContact($messageData);

            //if there is error continue
            if(array_key_exists('error', $t))
                die(debug("Error Occured"));

            $data['SitesGoogleContact'] = [
                'site_id' => $site['Site']['id'],
                'google_contacts_self_url' => $t->selfURL,
            ];

//            die(debug($data));

            if (PortalSitesGoogleContact::create($data['SitesGoogleContact'])) {
                $this->flashMessage(sprintf (__('The %s has been added on your google contacts Account', true), __('Contacts',true)), 'Sucmessage');
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Contact',true)));
            }
        }

        $allContacts = GoogleContactsApi::getAllContacts();
        die($allContacts);
    }

    /**
     * Will update all the contacts in google contacts according their occurence in the portal
     */
    function update_all_contacts()
    {
        $this->loadModel('SitesGoogleContact');
        $contacts = $this->SitesGoogleContact->find('all');

        foreach($contacts as $contact)
        {
            $messageData = [
                'self_url'=> $contact['SitesGoogleContact']['google_contacts_self_url'],
                'name'=> $contact['Site']['business_name'],
                'phone_number'=> (empty($contact['Site']['phone1']) ? $contact['Site']['phone2'] : $contact['Site']['phone1']) ,
                'email'=> $contact['Site']['email'],
            ];

            $t = GoogleContactsApi::updateContact($messageData);
        }

        $allContacts = GoogleContactsApi::getAllContacts();
        die("Done");
    }

//    function delete_not_synced()
//    {
//        $this->loadModel('SitesGoogleContact');
//        $contacts = $this->SitesGoogleContact->find('all');
//
//        foreach($contacts as $contact)
//        {
//            if(!isset($contact['Site']['id']))
//            {
//                $messageData = ['self_url'=> $contact['SitesGoogleContact']['google_contacts_self_url']];
//                $deleted = GoogleContactsApi::deleteContact($messageData);
//                $this->SitesGoogleContact->deleteAll(array('SitesGoogleContact.id'=>$contact['SitesGoogleContact']['id']));
//            }
//        }
//        dd("Done");
//    }

    /**
     * loop on the contacts in theportal and delete them from GoogleContacts
     */
    function delete_all()
    {
        $this->loadModel('SitesGoogleContact');
        $contacts = $this->SitesGoogleContact->find('all');
//        die(debug($contacts ));
        foreach($contacts as $contact)
        {
            $messageData =  $contact['SitesGoogleContact']['google_contacts_self_url'];
//            die(debug($contacts ));
//            $deleted = GoogleContactsApi::deleteContact($messageData);
            PortalSitesGoogleContact::where('id', $contact['SitesGoogleContact']['id'])->delete();
        }
        die("Done");
    }

    /**
     * this method will delete all the contacts in google contacts account
     */
    function delete_all_contacts_from_the_google_account()
    {
        $all_contacs = GoogleContactsApi::getAllContacts();

        if(empty($all_contacs ))
            die("there is no contacts to delete");

        foreach($all_contacs as $contact)
        {
            GoogleContactsApi::deleteContact( $contact->selfURL );

        }
        die("Done");
    }

    function dummy_func()
    {
        echo "hello";
        die(debug("Hello There"));
    }

}
?>