<?php

class OrderSourcesController extends AppController
{

    var $name = 'OrderSource';


    function owner_json_find()
    {
        $conditions = parent::_filter_params();

        $value = trim($_GET['q']);
        $applyBranch = true;
        if (isset($_GET['apply_branch'])) {
            $applyBranch = $_GET['apply_branch'] ?? null;
        }

        if (!empty($value)) {

            $value = mysql_escape_string($value);

            $orderSources = $this->OrderSource->search_order_sources($value, $conditions, false, (boolean)$applyBranch);

            $result = array();
            foreach ($orderSources as $orderSource) {

                $result[] = array(
                    'name' => $orderSource['OrderSource']['name'],
                    'text' => $orderSource['OrderSource']['name'],
                    'id' => $orderSource['OrderSource']['id'],
                    'details' => '',
                    'avatar' => \Izam\Daftra\Common\Services\AvatarURLGenerator::generate($orderSource['OrderSource']['name'], $orderSource['OrderSource']['id'], 30, null),
                );
            }

            if (!empty($result))
                array_unshift($result, array(
                    'name' => __('Please Select', true) . ':',
                    'text' => __('Please Select', true) . ':',
                    'id' => '',
                    'details' => ''
                ));
            echo json_encode($result);
            die();
        } else {
            echo json_encode(array());
            die();
        }
    }
}
