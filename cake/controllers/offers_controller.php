<?php

use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Limitation\Utils\LimitationUtil;

App::import('vendor','OfferV',['file'=>'Offer/autoload.php']);
class OffersController extends AppController {

	var $name = 'Offers';

	/**
	 * @var Offer
	 */
	var $Offer;
	var $helpers = array('Html', 'Form');



	function owner_index() {
		if(!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)){
			$this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
			$this->redirect($this->referer());
		}
		$this->loadModel('Post');
		$this->Offer->recursive = 0;
		$conditions = $this->_filter_params();
		$this->paginate['order'] = 'Offer.id desc';
		$this->set('offers', $this->paginate('Offer', $conditions));
		$filtered = $this->params['url'];
		unset($filtered['ext'], $filtered['url']);
		$this->set('filtered', $filtered);
		$this->setDefaultViewData();
		$this->view = 'izam';
		$this->render('offers/index');
	}

	function owner_view($id = null) {
		if(!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)){
			$this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
			$this->redirect($this->referer());
		}
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('offer', true)),true));
			$this->redirect(array('action'=>'index'));
		}

		$this->loadModel('Category');

		$offer = $this->Offer->read(null, $id);
		$this->set('offer', $offer);

		$Offer = \OfferV\OfferFactory::create($offer['Offer']['type'], $offer);
		$offerItemEntitiesList = $Offer->getOfferItemEntitesList([\OfferV\Base::OFFER_ITEM_TYPE_PRODUCT, \OfferV\Base::OFFER_ITEM_TYPE_CATEGORY, \OfferV\Base::OFFER_ITEM_TYPE_ITEM_GROUP]) ;
		$this->set('selected_categories', $Offer->getOfferItemEntitesList([\OfferV\Base::OFFER_ITEM_TYPE_CLIENT_CATEGORY]));
		$this->set('selected_clients', $Offer->getOfferItemEntitesList([\OfferV\Base::OFFER_ITEM_TYPE_CLIENT]));
		$this->set('offerItemEntitiesList', $offerItemEntitiesList);

		$this->setDefaultViewData();
		$this->view = 'izam';
		$this->render('offers/show');
	}

	function owner_add() {

		if(!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)){
			$this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
			$this->redirect($this->referer());
		}

	    $this->handleSiteLimit(
            checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::OFFERS_MANAGE),
            ['action' => 'index']
        );

		if (!empty($this->data)) {
			$offer = \OfferV\OfferFactory::create($this->data['Offer']['type'], $this->data);

            $result = $offer->save();
			if ($result['status']) {
				$newData = getRecordWithEntityStructure(EntityKeyTypesUtil::OFFER_ENTITY_KEY, $offer->Offer->id, 1)->toArray();
				$st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::OFFER_ENTITY_KEY);
				$activityLogRequestCreator = new EntityActivityLogRequestsCreator();
				$requests = $activityLogRequestCreator->create($st, $newData, [], []);
				$activityLogService =  new \App\Services\ActivityLogService();
				foreach ($requests as $requestObj) {
					$activityLogService->addActivity($requestObj);
				}
				
                App::import('Vendor', 'settings');
                $alreadyUpdatedOption = settings::getValue(OffersPlugin, 'set_offers_general_discount_type', false, false);
                if(!$alreadyUpdatedOption) {
                    settings::setValue(OffersPlugin, 'set_offers_general_discount_type', 1, false);
                    settings::setValue(InvoicesPlugin, 'discount_option', settings::OPTION_DISCOUNT_BOTH);
                }
				$this->izamFlashMessage(sprintf(__('The %s has been saved', true), __('offer',true)));
				$this->redirect(array('action'=>'view', $offer->Offer->id));
			} else {
				$this->set('selected_clients', array_filter(array_map(function($client){
					if(!empty($client[0]))
						return $client[0];
				}, $this->data['Client'])));
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('offer',true)));
			}
		}
		setLastUpdatedAt(PluginUtil::OffersPlugin, SettingsUtil::OFFERS_UPDATED_AT);
		$this->set('selected_categories', $this->data['ClientCategory']);
		$this->loadModel('Category');
		$shareAllClients = Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, false);
		if($shareAllClients){
			$this->Category->applyBranch['onFind'] = false;
		}
		$this->loadModel('Client');
		$this->set('categories', $this->Client->getCategoriesListWithIds(true));
	}

	function owner_edit($id = null) {
		if(!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)){
			$this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
			$this->redirect($this->referer());
		}
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'offer',true)));
			$this->redirect(array('action'=>'index'));
		}
		$offerItemEntitiesList = [];
		$offer = $this->Offer->read(null, $id);
		if (!$offer) {
			$this->flashMessage(sprintf(__('Invalid %s.', true),  __('offer',true)));
			$this->redirect(array('action'=>'index'));
		}
		if (!empty($this->data)) {
			
			// in some cases offer's condition_amount input will be disabled so it keeps old value this line added to reset the value . 
			$this->data['Offer']['condition_amount'] = $this->data['Offer']['condition_amount']??0;
		 
			$oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::OFFER_ENTITY_KEY, $id, 3);
		    if ($this->data['Offer']['active'] && $offer['Offer']['active'] == 0) {
                $this->handleSiteLimit(
                    checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::OFFERS_MANAGE),
                    ['action' => 'index']
                );
            }

			$Offer = \OfferV\OfferFactory::create($this->data['Offer']['type'], $this->data);
			$result = $Offer->save();

			if ($result['status']) {

				$oldData = $oldData->toArray();
                $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::OFFER_ENTITY_KEY, $id, 3)->toArray();
                $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::OFFER_ENTITY_KEY);
                $activityLogRequestCreator = new EntityActivityLogRequestsCreator();

                $requests = $activityLogRequestCreator->create($st, $newData, $oldData);
               
                $activityLogService =  new \App\Services\ActivityLogService();
                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }
				setLastUpdatedAt(PluginUtil::OffersPlugin, SettingsUtil::OFFERS_UPDATED_AT);

				$this->izamFlashMessage(sprintf(__('The %s has been saved', true), __('offer',true)));
				$this->redirect(array('action'=>'view', $id));
			} else {
				$this->set('selected_clients', array_filter(array_map(function($client){
					if(!empty($client[0]))
						return $client[0];
				}, $this->data['Client'])));
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('offer',true)));
			}
		}else{
			$Offer = \OfferV\OfferFactory::create($offer['Offer']['type'], $offer);
			$offerItemEntitiesList = $Offer->getOfferItemEntitesList([\OfferV\Base::OFFER_ITEM_TYPE_PRODUCT, \OfferV\Base::OFFER_ITEM_TYPE_CATEGORY, \OfferV\Base::OFFER_ITEM_TYPE_ITEM_GROUP]) ;
		}
		$shareAllClients = Settings::getValue(PluginUtil::BranchesPlugin, 'share_clients', null, false);
		if($shareAllClients){
			$this->loadModel('Category');
			$this->Category->applyBranch['onFind'] = false;
		}
		if (empty($this->data)) {
			$this->data = $offer;
			$this->data['Offer']['valid_from'] = format_date($this->data['Offer']['valid_from']);
			$this->data['Offer']['valid_to'] = format_date($this->data['Offer']['valid_to']);
			$this->set('selected_clients', array_keys($Offer->getOfferItemEntitesList([\OfferV\Base::OFFER_ITEM_TYPE_CLIENT])));
		}
		$this->loadModel('Client');
		$this->set('categories', $this->Client->getCategoriesListWithIds(true));
		$this->loadModel('OfferItem');
		$this->set('selected_categories', $this->data['ClientCategory'] ?? array_keys($Offer->getOfferItemEntitesList([\OfferV\Base::OFFER_ITEM_TYPE_CLIENT_CATEGORY])));
		$this->set('offerItemEntitiesList', $offerItemEntitiesList);
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if(!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)){
			$this->izamFlashMessage(__("You are not allowed to access this page", true), 'danger');
			$this->redirect($this->referer());
		}
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->izamFlashMessage(sprintf (__('Invalid id for %s', true), __('offer',true)), 'danger');
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('offer', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('offers', true);
		 } 
		$offers = $this->Offer->find('all',array('conditions'=>array('Offer.id'=>$id)));
		if (empty($offers)){
			$this->izamFlashMessage(sprintf(__('%s not found', true), $module_name), 'danger');
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if ((!empty($_POST['submit_btn']) && !empty($_POST['ids'])) || (!empty($id) && !empty($_POST)) ) {
			if (($_POST['submit_btn'] == 'yes' || (empty($_POST['submit_btn']) && !empty($id)))  && $this->Offer->deleteAll(array('Offer.id'=>$_POST['ids']??[$id]))) {
				setLastUpdatedAt(PluginUtil::OffersPlugin, SettingsUtil::OFFERS_UPDATED_AT);
				$this->izamFlashMessage(sprintf (__('%s deleted', true), $module_name));
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('offers',$offers);
	}

	function owner_test()
    {
            $data =['Invoice' => ['date' => '11/29/2018'], 'InvoiceItem'=>
            [['product_id' => 586,'unit_price' => 10, 'quantity' => 3]]
            ];
            die(json_encode($this->Offer->applyOffers($data)));
    }

	function owner_ajaxApplyOffer() {
		if (isset($_POST['data'])) {
            $invoiceData = $this->Offer->applyOffers($_POST['data']);
			die(json_encode($invoiceData));
		} else {
			http_response_code(400);
			die(json_encode(['message' => 'Error']));
		}
	}

	function api_ajaxApplyOffer() {
		if (isset($this->data)) {
            $invoiceData = $this->Offer->applyOffers($this->data);
			die(json_encode($invoiceData));
		} else {
			http_response_code(400);
			die(json_encode(['message' => 'Error']));
		}
	}
}
?>
