<?php

use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;
use Carbon\Carbon;
use Izam\Aws\Aws;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Rental\Repositories\UnitTypeRepository;
use Izam\Rental\Services\UnitTypeService;
use Izam\Dynamic\List\Service\EntityDynamicService;
use Izam\Logging\Service\RollbarLogService;

App::import('Controller', 'Invoices'); // mention at top

class ContentsController extends AppController {

    var $name = 'Contents';

    /**
     * @var Content
     */
    var $Content;
    /**
     * @var Invoice
     */
    var $Invoice;
    /**
     * @var $EmailTemplate
     */
    var $EmailTemplate;

    var $helpers = array('Html', 'Form');
    var $homeUrl = '/home' ;
    var $ownerDashboardUrl = '/owner/owners/dashboard';
    var $components = array('Email', 'SysEmails');

    private function send_request($url, $method,$data)
    {
        $this->loadModel('ApiKey');
        $this->api_key = $this->ApiKey->findOrCreate('internalApi');
        $result = $this->send_post($url, $method,$data);
        return $result;
    }



    private function send_post($url,$method,$params)
    {
        $beta = getCurrentSite('beta_version');
        $subDomain = getCurrentSite('subdomain');
        if ($beta == 1 && getenv('APP_ENV') !== 'local') {
            $subDomain= str_replace(Domain_Short_Name,Beta_Domain , $subDomain);
        } else {
            $subDomain= str_replace(Beta_Domain,Domain_Short_Name , $subDomain);
        }
        $url = 'https://'.$subDomain. $url ;
        $request_params['data'] = $params ;
        $request_params['_method'] = $method;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        if ($method == "POST") {
            curl_setopt($ch, CURLOPT_POST, 1);
        }  else  {
            curl_setopt($ch,CURLOPT_CUSTOMREQUEST, $method);
        }

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch,CURLOPT_SSL_VERIFYHOST, false);

        $data = json_encode($params,JSON_UNESCAPED_SLASHES);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json','apikey: '.$this->api_key['ApiKey']['key']]);
        curl_setopt($ch,CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $result = curl_exec($ch);
        if(curl_error($ch))
        {
            echo 'error:' . curl_error($ch);
        }
        return json_decode($result,true);
    }

    private function old_send_post($url,$params)
    {
        $beta = getCurrentSite('beta_version');
        $subDomain = getCurrentSite('subdomain');
        if ($beta == 1 && getenv('APP_ENV') !== 'local') {
            $subDomain= str_replace(Domain_Short_Name,Beta_Domain , $subDomain);
        } else {
            $subDomain= str_replace(Beta_Domain,Domain_Short_Name , $subDomain);
        }
        $url = 'https://'.$subDomain. $url ;

//		die($url);
//		$url .= '?debug=2';
        $request_params['data'] = $params ;
        $request_params['_method'] = 'POST';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch,CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch,CURLOPT_SSL_VERIFYHOST, false);


        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request_params));
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $result = curl_exec($ch);
//						Configure::write('debug', 2);
        if(curl_error($ch))
        {
            echo 'error:' . curl_error($ch);
        }

//			debug($url);
//			debug($params);
//			debug($result);
//			die(debug(json_decode($result)));
        return json_decode($result,true);
    }

    public function client_after_payment_success($invoice_id)
    {
        unset($_SESSION['website_front_'.$invoice_id]);
        $this->loadModel('Invoice');
        $this->loadModel('EmailTemplate');
        $invoice = $this->Invoice->find('first', ['conditions' => ['Invoice.id' => $invoice_id]]);
        $this->updateInvoiceFromDraft($invoice_id);
        $this->SysEmails->invoice($invoice['Invoice'], $invoice['Client'], false,$this->EmailTemplate->getDefaultTemplate('shop-front-notification'), [], true, [], [], true);
        $this->SysEmails->invoice($invoice['Invoice'], $invoice['Client'], false,$this->EmailTemplate->getDefaultTemplate('shop-front-confirmation'), [], false,[], [], true);
        $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'thankyou', '?' => 'invoice_id=' . $invoice_id)));
    }

    private function updateInvoiceFromDraft($invoice_id)
    {
        $this->loadModel('InvoicePayment');
        $invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['invoice_id' => $invoice_id]]);
        $this->loadModel('SitePaymentGateway');
        $paymentGateWay = $this->SitePaymentGateway->find('first', ['conditions' => ['payment_gateway' => $invoicePayment['InvoicePayment']['payment_method']]]);
        if (
            !in_array(
                $invoicePayment['InvoicePayment']['payment_method'],
                ["cash", "bank", "credit", "cheque", "offline", "client_credit", "starting_balance", "cash_on_delivery"]
            ) &&
            $invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED &&
            !$paymentGateWay['SitePaymentGateway']['manually_added']
        ) {
            $result = $this->send_request('/api2/invoices/update_draft/'.$invoice_id.'/0?debug=0', 'GET', []);
        }
    }

    private function call_model_function($model,$action,$args)
    {

        if($action == 'empty')
            return [];
//		debug($model);
//		debug($action);
        $count = count($args);
//		$this->$model = ClassRegistry::init($model);
        $this->loadModel($model);
        if(get_class ($this->$model) == 'AppModel')
        {

            ClassRegistry::flush($model);
            $this->$model = ClassRegistry::init($model);
//				ClassRegistry::flush($model);
        }

        if($count === 0)
        {

            $data = $this->$model->$action();
        }else if($count === 1){
            $data =  $this->$model->$action($args[0]);
        }else if($count === 2){
            debug($args);
            $data =  $this->$model->$action($args[0], $args[1]);
        }else if($count === 3){
            $data =  $this->$model->$action($args[0], $args[1], $args[2]);
        }else if($count === 4){
            $data =  $this->$model->$action($args[0], $args[1], $args[2], $args[3]);
        }

        return $data;

    }

    function test(){
        $this->loadModel('Media');
        die($this->Media->temp_get_menu_links());
//		die(HashPassword('7c4a8d09ca3762af61e59520943dc26494f8941b'));
//		$var = 'test2';
//		$this->$var('a','b');
//		die('22');
//		$response = $this->send_post('https://oidev.daftra.dev/rest/invoices/test2', array(
//			'key1' => 'value1',
//			'key2' => 'value2',
//			'key3' => array('data' => ['key4' => 'value4'] )
//		))	;

    }

    function test2($a,$b){
//		die($a.$b);
    }

    function owner_index() {
        $this->Content->recursive = 0;
        $conditions = $this->_filter_params();
        if(isset($_GET['editable']) && $_GET['editable'] == 1){
            $conditions['Content.is_editable'] = 1;
        }
        $this->set('contents', $this->paginate('Content', $conditions));
    }

    function owner_view($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('content', true)),true));
            $this->redirect(array('plugin' => 'website_front', 'action'=>'index'));
        }
        $this->set('content', $this->Content->read(null, $id));
    }

    function owner_add() {
        if (!empty($this->data)) {
            $this->Content->create();
            if ($this->Content->save($this->data)) {
                $this->flashMessage(sprintf (__('The %s has been saved', true), __('content',true)), 'Sucmessage');
                $this->redirect(array('plugin' => 'website_front', 'action'=>'index'));
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('content',true)));
            }
        }
        $this->set('types_list', $this->Content->get_types_list());

    }

    function owner_add_file() {
        if (!empty($this->data['Content'])) {
            foreach($this->data['Content'] as $k => $content)
            {
                $data['Content']['title'] = $content['content']['name'];
                $name_without_ext = explode('.', $content['content']['name']);
                unset($name_without_ext[count($name_without_ext) - 1]);
                $data['Content']['key'] = implode('.',$name_without_ext);
                if($content['content']['type'] == 'text/css')
                    $data['Content']['type'] = Content::CONTENT_TYPE_CSS;
                else if($content['content']['type'] == 'application/javascript')
                    $data['Content']['type'] = Content::CONTENT_TYPE_JS;

                $data['Content']['content'] = file_get_contents($content['content']['tmp_name']);
                if(empty($data['Content']['content'])){
                    debug('empty content');
                }


                $this->Content->create();
                debug($data['Content']['title']);
                if ($this->Content->save($data)) {
                    debug($data['Content']['title']);

                } else {
                    debug('fail');
                    $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('content',true)));
                }
            }

        }
        $this->set('types_list', $this->Content->get_types_list());

    }


    function css($content_key){
        $content = $this->Content->find('first', [ 'conditions' => ['Content.key' => $content_key, 'Content.type' => Content::CONTENT_TYPE_CSS] ]);
//		die(debug($content));
        if($content['Content']['type'] == Content::CONTENT_TYPE_CSS){
            $this->layout = false ;
            $seconds_to_cache = 3600;
            $ts = gmdate("D, d M Y H:i:s", time() + $seconds_to_cache) . " GMT";
            header("Expires: $ts");
            header("Pragma: cache");
            header("Cache-Control: max-age=$seconds_to_cache");
            header("Content-type: text/css");
            die($content['Content']['content']);
        }
    }

    function font($content_key){
        $content = $this->Content->findByKey($content_key);
        if($content['Content']['type'] == Content::CONTENT_TYPE_FONT){
            $this->layout = false ;
            header("Content-type: application/font-woff2");
            die($content['Content']['content']);
        }
    }

    function js($content_key){
        $content = $this->Content->findByKey($content_key);

        if($content['Content']['type'] == Content::CONTENT_TYPE_JS){
            $this->layout = false ;
            $seconds_to_cache = 3600;
            $ts = gmdate("D, d M Y H:i:s", time() + $seconds_to_cache) . " GMT";
            header("Expires: $ts");
            header("Pragma: cache");
            header("Cache-Control: max-age=$seconds_to_cache");

            header("Content-Type: application/javascript");
            die($content['Content']['content']);
        }
    }


    function get_logout_link()
    {
        if(isset($_SESSION['CLIENT']))
            $logout_link = '/client/clients/logout';
        else if(isset($_SESSION['OWNER']))
            $logout_link = '/owner/sites/logout';
        return $logout_link;
    }

    function formatMenuArr(&$array) {
    	foreach ($array as $key => $value) {
    		if ($value['has_children'] || $value['has_grand_children']) {
    			$array[$key]['children'] = array_values($value['children']);
    			$this->formatMenuArr($array[$key]['children']);
		    }
	    }
    }

    function evaluateMenuItemLink(array $record)
    {
        if (is_null($record))
        	return '#';

        switch ($record['item_type']){
	        case 'content_page':
		        $contentID = $record['item_id'];
		        $content = $this->Content->find('first', ['conditions' => ['id' => $contentID]]);
		        $route = Router::url(['controller' => 'contents', 'action' => 'process_content', $content['Content']['key']]);
		        if ($contentID == 1)
		            return $this->homeUrl;
	        	return strstr($route, '/contents');
	        case 'product':
		        $route = Router::url(["controller" => "contents", "action" => "product_view", $record['item_id']]);
                return strstr($route, '/contents');
	        case 'category':
	            $route = Router::url(["controller" => "contents", "action" => "products_list", '?' => ['cat_id' => $record['item_id']]]);
                return strstr($route, '/contents');
            case 'rental':
                $route = Router::url(["controller" => "contents", "action" => "rental", '?' => ['rental_types' => explode(",", $record['item_id'])]]);
                return strstr($route, '/contents');

            case 'link':
	            /** Implementation Using Meta Record **/
//	            if (isset($record['meta'])){
//	                $meta = json_decode($record['meta'], true);
//	                if (isset($meta['internal']) && $meta['internal']){
//                        return $record['link'];
//                    } else {
//                        return '//'.$record['link'];
//                    }
//                }
	        	return $record['link'];
            case 'home':
                return $this->homeUrl;
            case 'contact_us':
                $route = Router::url(['controller' => 'contents', 'action' => 'process_content', 'contact_us']);
                return strstr($route, '/contents');
            default:
	        	return '#';
        }
    }

    function generateMenuData()
    {
        $this->loadModel('ShopFrontMenu');
        $menus = $this->ShopFrontMenu->find('all',array('order' => 'ShopFrontMenu.display_order ASC'));
        $menuArr = array();
        foreach ($menus as $key => $menu) {
            $record = $menu['ShopFrontMenu'];
            $title = $record['title'];
            $link = $this->evaluateMenuItemLink($record);
            $parent_id = $record['parent'];
            if (is_null($parent_id)) {
                $menuArr[$record['id']] = array(
                    'title' => $title,
                    'link' => $link,
                    'children' => array()
                );
                unset($menus[$key]);
            }
        }

        foreach ($menus as $menu) {
            $record = $menu['ShopFrontMenu'];
            $title = $record['title'];
            $link = $this->evaluateMenuItemLink($record);
            $parent_id = $record['parent'];

            foreach ($menuArr as $key => $item){
                if ($parent_id == $key) {
                    $menuArr[$key]['children'][$record['id']] = [
                        'title' => $title,
                        'link' => $link,
                        'children' => array()
                    ];
                    $menuArr[$key]['has_children'] = true;
                } elseif (in_array($parent_id, array_keys($menuArr[$key]['children']))) {
                    $menuArr[$key]['children'][$parent_id]['children'][$record['id']] = [
                        'title' => $title,
                        'link' => $link,
                        'children' => array()
                    ];
                    $menuArr[$key]['children'][$parent_id]['has_grand_children'] = true;
                }
            }
        }

        $menuArr = array_values($menuArr);
        $this->formatMenuArr($menuArr);
        return $menuArr;
    }

    function getLoggedUserName()
    {
        $owner = getAuthOwner();
        if ($owner) {
            return $owner['business_name'];
        } else {
            $client = getAuthClient();
            return $client['business_name'];
        }
    }

    function get_header_data($data = [])
    {
        $this->loadModel('Cart');
        $this->Category = ClassRegistry::init('Category');
        $header_data = getCurrentSite();

        $this->loadModel('Media');
        $header_data['nav-links'] = Media::get_nav_menu_links();
        $cart_data = Cart::get_cart_items();
        $cart_data = $this->prepareProductImages($cart_data);
        $cart_data['cart_products_json'] = addslashes(json_encode($cart_data['cart_products']));
        $header_data['home_url'] = $this->homeUrl;
        include APP . 'config' . DS . 'currencies.php';
        $header_data['currencies'] = json_encode($currencies);
        $header_data['number_formats'] = json_encode($number_formats);
        $header_data['home_url'] = $this->homeUrl;
        $header_data = array_merge($header_data,$cart_data);
        $header_data['site_logo'] = getCurrentSite('site_logo_full_path');
        $header_data['default_currency'] = getCurrentSite('currency_code');
        $header_data['items'] = $this->Category->get_product_categories()['items'];
        $header_data['is_logged_in'] = $this->is_logged_in();
        $header_data['logout_link'] = $this->get_logout_link();
        $menuArr = $this->generateMenuData();
        $header_data['menuItems'] = $menuArr;
        $header_data = array_merge($data,$header_data);
        $header_data['product_name'] = $this->params['url']['product_name'];
        $header_data['logged_name'] = $this->getLoggedUserName();
        $header_data['Site_Full_name_NoSpace'] = Site_Full_name_NoSpace;
        $this->loadModel('Language');
        $language = $this->Language->findById(getCurrentSite()['language_code'])['Language'];
        $header_data['language'] = $language['code2'];

        $dateFormat = getDateFormats('std');
        $header_data['dateFormat'] = json_encode(["dateFormat" => $dateFormat[getCurrentSiteWithoutCache('date_format')]]);

        //set header categories
        $conditions = array('Category.parent_id' => null,'Category.category_type' => Category::CATEGORY_TYPE_PRODUCT);
        if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
            $conditions['Category.branch_id'] = getBranchForShopFront();
        }
        $categories = $this->Category->find('list',array('conditions' => $conditions));
        $conditions2 = array( 'Category.category_type' => Category::CATEGORY_TYPE_PRODUCT, 'NOT' =>[ 'Category.parent_id' => null]);
        if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
            $conditions2['Category.branch_id'] = getBranchForShopFront();
        }
        $child_categories = $this->Category->find('list',array('fields' => ['Category.id','Category.name','Category.parent_id'],'conditions' => $conditions2));
        foreach($categories as $k => $v)
        {
            $temp = [];
            $temp['id'] = $k;
            $temp['name'] = $v;
            if(isset($child_categories[$k]) && $childs_count = count($child_categories[$k])) {
                foreach($child_categories[$k] as $child_id => $child_name)
                {
                    $temp['childs'][] = ['id' => $child_id, 'name' => $child_name];
                }
            }
            if (!empty($childs_count)) {
                $temp['has_child'] = $childs_count;
            } else {
                $tem['has_child'] = 0;
            }
            $header_data['categories'][] = $temp;
        }
        return $header_data;
    }

    function get_header_content($header_data)
    {
        $header = $this->process_content('header', $header_data,true);
        return $header;
    }

    function get_footer_content($footer_data)
    {
        $footer = $this->process_content('footer', $footer_data,true);
        return $footer;
    }

    function set_header_and_footer($data = []){
        $header_data = $this->get_header_data($data);
        $header = $this->get_header_content($header_data);
        $footer = $this->get_footer_content($header_data);
        $this->set('header',$header);
        $this->set('footer',$footer);
    }

    function set_template_layout($data = []){
        $this->set_header_and_footer($data);
        $this->layout = FALSE;
        die($this->render('content'));
    }

    function unit_type_list()
    {
        $this->loadModel('Content');
        if (empty(settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::RENTAL_PLUGIN, 'rental_enable_client_reservation'))) {
            $this->flashMessage(__("No Online Rental", true), 'alert alert-danger m-t');
            $this->redirect($this->referer());
        }

        App::import('Component', 'ApiRequestsComponent');
        $apiRequests = new ApiRequestsComponent();
        $url = '/v2/api/owner/rental/reservation-order-validation';

        $data =  [
            'le_custom_data_rental_reservation_order' => $_GET['le_custom_data_rental_reservation_order'] ?? []
        ];

        $response  = $apiRequests->request($url, false, 'POST', $data);

        if (!empty($response["error"])) {
            $messages = "";
            foreach ($response["error"]["error"] ?? [] as $key => $error) {
                foreach ($error as $item) {
                    $messages = $key . ": " . $item . "<br/>";
                }
            }
            if (empty($messages)) {
                $messages = $response["error"]["message"];
            }
            $this->flashMessage($messages, 'alert alert-danger m-t');
            $this->redirect("/");
        }

        // validation 
        $service = new UnitTypeService();
        $rental_types = $_GET['rental_types'];
        if (empty($rental_types) || is_null($rental_types[0]) || !$service->isSingleMethod($rental_types)) { 
            $this->flashMessage(__('The Pricing Method should be the same for all the selected unit types', true));
            $this->redirect('/contents/rental?' . http_build_query(['rental_types' => $rental_types]));
            return; 
        }
        if (isset($_GET['start_end_date'])) list($_GET['start_date'], $_GET['end_date']) = explode(' - ', $_GET['start_end_date']);
        if (!isset($_GET['start_date']) || !isset($_GET['end_date'])) {
            $this->flashMessage(__('You must provide start, end date and rental type ids', true));
            $this->redirect('/contents/rental?' . http_build_query(['rental_types' => $rental_types]));
            return;
        }
        // ./ end validation 

        $from = (new Carbon(trim($_GET['start_date'] . ' ' . $_GET['start_time'])))->format('Y-m-d H:i:s');
        $to = (new Carbon(trim($_GET['end_date'] . ' ' . $_GET['end_time'])))->format('Y-m-d H:i:s');

        $fromDate = (new Carbon(trim($_GET['start_date'])))->format('Y-m-d');
        $toDate = (new Carbon(trim($_GET['end_date'])))->format('Y-m-d');
         
        
        $unitTypes = $service->getUnitTypes($rental_types, $_GET['page']);
        
        $unitTypes['data'] = $service->mapBasicViewData($unitTypes['data'], $from, $to); // add available key to unit types using filtered data

        $api = new ApiRequestsComponent();
        $priceRequest = array_map(function ($unitType) {
            return [
                'id' => $unitType['id'],
                'from' => $unitType['from'],
                'to' => $unitType['to'],
            ];
        }, $unitTypes['data']);
        $prices = $api->request('/v2/api/owner/rental/get-unit-types-availability-and-pricing', false, 'POST', ['unit_types' => $priceRequest]);
        
        $unitTypes['data'] = $service->mapAvailability($unitTypes['data'], $from, $to, $prices, ['le_custom_data_rental_reservation_order' => $_GET['le_custom_data_rental_reservation_order' ?? []]]);
        $unitTypes['data'] = $service->sortAvailableUnitTypesOnTop($unitTypes['data']); // add available key to unit types using filtered data
        $unitTypes['data'] = $service->mapPricesFromTo($unitTypes['data'], $prices);
        $template_data['unitTypes'] = $unitTypes;
        
        $template_data['unitsCount'] = (string)$service->getUnitCountInUnitTypes($rental_types);
        
        $pageParams = $_GET;
        unset($pageParams['url']);
        for ($i = 1; $i <= $template_data['unitTypes']['last_page']; $i++) {
            $template_data['pages'][] = [
                'page' => (string)$i,
                'pageLink' => parse_url($_SERVER['REQUEST_URI'])['path'] . '?' . http_build_query(array_merge($pageParams,['page' => $i]))  ,
                'active' => $i == $template_data['unitTypes']['current_page']
            ];
        }
        $template_data['start_date'] = $this->Content->formatDate($fromDate);
        $template_data['end_date'] = $this->Content->formatDate($toDate);
        $template_data['start_time'] = date( "h:i A", strtotime($this->params['url']['start_time'])) ?? "";
        $template_data['end_time'] =  date( "h:i A", strtotime($this->params['url']['end_time'])) ?? "";
        $template_data['start_date'] = format_date($template_data['start_date']);
        $template_data['end_date'] = format_date($template_data['end_date']);
        $template_data['is_time_method'] = (!empty($unitTypes["data"][0]["method"]) && $unitTypes["data"][0]["method"] == "days") ? 0 : 1;
        $this->process_content('unit_type_list', $template_data);
    }
    
    function products_list()
    {
        if(!empty($this->params['url']['cat_id'])) {
            $cat_id = $this->params['url']['cat_id'];
        } else {
            $cat_id = null;
        }

        if(!empty($this->params['url']['brand'])) {
            $brand = $this->params['url']['brand'];
        } else {
            $brand = null;
        }

        $minPrice = $this->params['url']['min_price'] ?? null;
        $maxPrice = $this->params['url']['max_price'] ?? null;

        App::import('Vendor','MustacheEngine', array('file' => 'MustacheEngine/MustacheEngine.php'));

        Mustache_Autoloader::register();

        $engine = new Mustache_Engine;

        $this->loadModel('Cart');
        $this->ItemsCategory = ClassRegistry::init('ItemsCategory');
        $this->loadModel('Category');
        $categoryConditions = ['Category.id' => $cat_id];
        if (ifPluginActive(BranchesPlugin) && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
            $categoryConditions['Category.branch_id'] = getBranchForShopFront();
        }
        $this->Category->bindAttachmentRelation('category');
        $category = $this->Category->find('first', [ 'conditions' => $categoryConditions]);
 
        if(!empty($category['Attachments'])){
            $awsService = new Aws;
            RollbarLogService::logTempFilesToRollbar($category['Attachments'][0]['is_temp'],$category['Attachments'][0]);
            $category['Category']['image_full_path'] = $awsService->getPermanentUrl($category['Attachments'][0]['path']);
        }else{
            $category['Category']['image_full_path'] = str_replace("\\", '/' , $category['Category']['image_full_path']) ;
        }
        
        $template_data['product_name'] = $this->params['url']['product_name'];
        $template_data['categories_count'] = 0;
        if($cat_id)
        {
            $categories = $this->Category->find('all', ['conditions' => ['parent_id' => $cat_id]]);
            foreach($categories as $k => $v){
                $template_data['categories'][$k] = $v['Category'];
                if(!empty($v['Attachments'])){
                    $awsService = new Aws;
                    RollbarLogService::logTempFilesToRollbar($v['Attachments'][0]['is_temp'],$v['Attachments'][0]);
                    $template_data['categories'][$k]['image_full_path'] = $awsService->getPermanentUrl($v['Attachments'][0]['path']);
                
                }elseif (!empty( $processed_data['items'][$k]['image'])||!empty($v['Category']['image_full_path'])) {
                    $template_data['categories'][$k]['image_full_path'] = $this->Category->getFileSettings()['image']['folder'] . $template_data['categories'][$k]['image'];
                } else {
                    $template_data['categories'][$k]['image_full_path'] = false;
                }
            }
            $template_data['categories_count'] = count($categories);
            $itemsCategoryConditions = ['ItemsCategory.category_id' => $cat_id,];
            if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
                $itemsCategoryConditions['ItemsCategory.branch_id'] = getBranchForShopFront();
            }
            $product_ids = $this->ItemsCategory->find('list',['conditions' => $itemsCategoryConditions, 'fields' => ['ItemsCategory.item_id'] ]);
            $conditions['Cart.id'] = array_values($product_ids);
        }
        $conditions['AND'] = ['availabe_online = 1', ['OR' => ['status = '. ProductStatusUtil::STATUS_ACTIVE, 'status is null']]];
        if ($brand) {
            $conditions['brand'] = $brand;
        }
        if ($minPrice && $maxPrice) {
            $conditions['unit_price >='] = $minPrice;
            $conditions['unit_price <='] = $maxPrice;
        }
        if (ifPluginActive(BranchesPlugin)  && empty(settings::getValueEvenEmpty(BranchesPlugin, 'share_products'))) {
            $conditions['branch_id'] = getBranchForShopFront();
        }

        if($product_name = $this->params['url']['product_name'])
        {
            $conditions['OR'][] = $this->Cart->get_name_search_conditions($product_name);
            debug($conditions);
        }
        $template_data['Category'] = $category['Category'];
        $products = $this->paginate('Cart',$conditions);


        foreach ($products as $key => $product)
        {
            $temp = [];
            $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$product['Cart']['id']);
            if(count( $defaultS3Images))
            {
               $temp['file_full_path'] = $defaultS3Images[0]->files->path;
               $products[$key]['ProductMasterImage'] = [$temp];
            }
        }

        $template_data['products'] = MustacheEngine::model_data_to_placeholdersData($products, [
            'Cart' => ['id','name','unit_price', 'description','discount', 'discout_type', 'brand', 'category', 'stock_balance', 'product_code']
        ]);
        $client = getAuthClient();
        if(!empty($client)){
            $group_price_id=intval(getAuthClient('group_price_id'));
        }else{
            $group_price_id=0;
        };
        foreach($template_data['products'] as $k => &$product){
            if (count($products[$k]['Category'])) {
                foreach ($products[$k]['Category'] as $key => $category) {
                    $product['category'] .= ($category['name'] . ',');
                }
            }
            $product['description'] = str_limit($product['description'], 150);
            if (!empty($products[$k]['ProductPrice'])) {
                $priceList = current(array_filter($products[$k]['ProductPrice'], function ($item) use ($group_price_id) {
                    return $item['group_price_id'] == $group_price_id;
                }));
                if (!empty($priceList)) {
                    $product['unit_price'] = $priceList['price'];
                }
            }
            $product = $this->Cart->calculateProductDiscount($product);
            $product['final_price_number'] = "".(float) $product['final_price']."";
            $product['unit_price'] = format_price($product['unit_price']);
            $product['final_price'] = format_price($product['final_price']);
            $product['file_full_path'] = $products[$k]['ProductMasterImage'][0]['file_full_path'] ?$products[$k]['ProductMasterImage'][0]['file_full_path'] : 'https://cdn.daftra.com/templates/shop_1/img/product_img.svg';
        }
        $template_data['products_count'] = count($template_data['products']);

        //get paging data
        $query_params = $_GET;
        unset($query_params['url']);
        $new_query_params = [];
        foreach($query_params as $k => &$v)
        {
            $new_query_params[] = $k.'='.$v;
        }

        $query_params = implode('&',$new_query_params);
        $subdomian = getCurrentSite('subdomain');
        $paging = $this->params['paging']['Cart'];
        $counter = 0 ;

        if (!empty($this->params['named']['limit'])) {
            $limitStr = '/limit:'.$this->params['named']['limit'];
        } else {
            $limitStr = '';
        }

        for($i = ( ($paging['page']-2) >= 1 ? $paging['page'] - 2 : 1 ) ; ($i <= $paging['pageCount'] && $i < $paging['page'] + 2); $i++){

            $template_data['pages'][$counter]['page'] = "$i";
            $template_data['pages'][$counter]['link'] = "https://" .  $subdomian . '/contents/products_list'.$limitStr.'/page:'. $i . '?'.$query_params ;
            $template_data['pages'][$counter]['active'] =	$paging['page'] == $i ? true : false  ;
            $counter++;
        }
        $template_data['nextPage'] = $paging['nextPage'];
        $template_data['nextPageLink'] = "https://" . $subdomian . '/contents/products_list'.$limitStr.'/page:'. ($paging['page'] + 1) .  '?'.$query_params ;;
        $template_data['prevPage'] = $paging['prevPage'];
        $template_data['prevPageLink'] = "https://" . $subdomian . '/contents/products_list'.$limitStr.'/page:'. ($paging['page'] - 1) .  '?'.$query_params ;;
//end paging

        //sortfields
        $template_data['sortFields'] = [['title' => __('Price',true), 'field_name' => 'unit_price']];
        //end sortfields

        //limit fields
        for($i = 0 ;$i <= 3 ; $i++){

            $perPageValue = ($i+1) * 10;
            $limit[$i]['text'] = "$perPageValue";
            if(($i+1) * 10 == $paging['options']['limit'])
                $limit[$i]['active'] = TRUE;
            else
                $limit[$i]['active'] = FALSE ;
        }
        if(!empty($products)) {
            $template_data['results'] = true;
        } else {
            $template_data['results'] = false;
        }
        if (!empty($this->params['url']['product_name'])) {
            if(!empty($products)) {
                $template_data['query'] = __("Search for", true)." \"{$this->params['url']['product_name']}\"";
            } else {
                $template_data['query'] = __("No Results Matches", true)." \"{$this->params['url']['product_name']}\"";
            }
        }
        $template_data['limits'] = $limit;
        $template_data['page_title'] = __('Products',true).': ' . $category['Category']['name'] ;
        $this->process_content('list_products', $template_data);


    }

    function reservation_summary($unit_type_id)
    {
        if (empty(settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::RENTAL_PLUGIN, 'rental_enable_client_reservation'))) {
            $this->flashMessage(__("No Online Rental", true), 'alert alert-danger m-t');
            $this->redirect($this->referer());
        }
        $this->loadModel("Contents");
        $unitTypeRepo = new \Izam\Rental\Repositories\UnitTypeRepository();
        $unitType = $unitTypeRepo->find($unit_type_id);
        if (empty($unitType)) {
            $this->flashMessage(sprintf(__("Not found entity %s", true), __("Unit type", true)), 'alert alert-danger m-t');
            $this->redirect($this->referer());
        }
        $urls = [];
        $templateData['title'] = $unitType->name;
        foreach ($unitType->attachments as $key => $attachment) {
            $urls[] = ['url' => $attachment->url, 'index' => "".($key+1).""];
        }

        if (empty($urls)) {
            $urls[] = ['url' => 'https://cdn.daftra.com/templates/shop_1/img/category_thumb.svg', 'index' => '1'];
        }
        $templateData['images'] = $urls;
        $templateData['images_count'] = ''.count($urls).'';
        $params = $this->params['url'];
        unset($params['url']);
        unset($params['ext']);
        $templateData['start_date_input'] = $this->params['url']['start_date'];
        $templateData['end_date_input'] = $this->params['url']['end_date'];
        $templateData['custom_fields'] = $this->parsingRentalDataRequest($params['le_custom_data_rental_reservation_order']);
        $templateData['back'] = '/contents/unit_type_list?'.http_build_query($params);
        $templateData["id"] = $unit_type_id;
        $templateData['start_date'] = $this->Content->formatDate($this->params['url']['start_date']);
        $templateData['end_date'] = $this->Content->formatDate(($this->params['url']['end_date']));
        $templateData['start_time'] = date( "h:i A", strtotime($this->params['url']['start_time'])) ?? "";
        $templateData['end_time'] =  date( "h:i A", strtotime($this->params['url']['end_time'])) ?? "";
        if (!is_null($unitType->minimum_hours)) {
            $from = (new Carbon(trim($this->params['url']['start_date'] . ' ' . $this->params['url']['start_time'])));
            $to = (new Carbon(trim($this->params['url']['end_date'] . ' ' . $this->params['url']['end_time'])));
            $hoursDiff = $from->diffInHours($to);
            $minutesDiff = ($from->diffInMinutes($to) - ($hoursDiff * 60)) / 60;
            $diff = $hoursDiff + $minutesDiff;
            if ($diff < $unitType->minimum_hours) {
                $this->flashMessage(sprintf(__("The minimum hours of the reservation order should be greater than or equal %s Hours", true), $unitType->minimum_hours), 'alert alert-danger m-t');
                $this->redirect($this->referer());
            }
        }
        if ($unitType->method == "days" ) {
            $templateData['start_time'] =  date( "h:i A", strtotime($unitType->check_in)) ?? "";
            $templateData['end_time'] = date( "h:i A", strtotime($unitType->check_out));
        }

        $url = '/v2/unit-type/get-price-details/'.$unitType->id;
        App::import('Component', 'ApiRequestsComponent');
        $apiRequests = new ApiRequestsComponent();
        $response  = $apiRequests->request($url,false,'POST', ['start_date' => $templateData['start_date'], 'end_date' => $templateData['end_date'], 'start_time' => date( "H:i:s", strtotime($templateData['start_time'])), "end_time" => date( "H:i:s", strtotime($templateData["end_time"]))]);
        if (!empty($response["error"])) {
            $this->flashMessage(__($response["error"]["message"], true), 'alert alert-danger m-t');
            $this->redirect($this->referer());
        }
        $templateData['start_date'] = format_date($templateData['start_date']);
        $templateData['end_date'] = format_date($templateData['end_date']);
        $duration = UnitTypeService::getDiffParam($this->params['url']['start_date']. " ". date( "H:i:s", strtotime($templateData['start_time'])), $this->params['url']['end_date']. " ". date( "H:i:s", strtotime($templateData['end_time'])));
        $templateData['days_count'] = (string) $duration['days'] ?? '';
        $templateData['hours_count'] = (string) $duration['hours'] ?? '';
        $templateData['price_without_tax'] = (string)round($response['totalPriceWithoutTaxs'], 2);
        if (!empty($response['tax_1']['value'])) {
            $templateData['taxes'][] = ["name" => $response["tax_1"]['name'], "value" => (string)$response["tax_1"]['value'], "price" => (string)round($response["tax_1"]['price'], 2)];
        }

        if (!empty($response['tax_2']['value'])) {
            $templateData['taxes'][] = ["name" => $response["tax_2"]['name'], "value" => (string)$response["tax_2"]['value'], "price" => (string)round($response["tax_2"]['price'], 2)];
        }
        $templateData["total_price"] = format_price($response["totalPrice"], $response["currency_code"]);

        $this->process_content("reservation_summary", $templateData);
    }


    public function client_booking($unit_type_id)
    {
        if (empty(settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::RENTAL_PLUGIN, 'rental_enable_client_reservation'))) {
            $this->flashMessage(__("No Online Rental", true), 'alert alert-danger m-t');
            $this->redirect("/");
        }
        App::import('Component', 'ApiRequestsComponent');
        $apiRequests = new ApiRequestsComponent();
        $url = '/v2/unit-type/get-price-details/'.$unit_type_id;
        $validationResponse  = $apiRequests->request($url,false,'POST', ['start_date' => $_POST['start_date'], 'end_date' => $_POST['end_date'], 'start_time' => $_POST['start_time'], "end_time" => $_POST["end_time"]]);
        if (!empty($validationResponse["error"])) {
            $this->flashMessage($validationResponse["error"]["message"], 'alert alert-danger m-t');
            $this->redirect("/");
        }
        $url = '/v2/reservation-order-auto?request_branch_id='.getAuthClient('branch_id');
        $data =  [
            'start_date' => $_POST['start_date'], 'end_date' => $_POST['end_date'],
            'start_time' => $_POST['start_time'], "end_time" => $_POST["end_time"], "images" => "",
            "status_id" => settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::RENTAL_PLUGIN, 'rental_clients_initial_status'),
            "branch_id" => getAuthClient('branch_id'), 'unit_type_id' => $unit_type_id, "is_temp" => 1, "order_number" => uniqid(),
            'unit_id' => $validationResponse['first_unit_id'], 'client_id' => getAuthClient('id'),
            "currency" => getCurrentSite("currency_code"),
            'le_custom_data_rental_reservation_order' => $_POST['le_custom_data_rental_reservation_order'],
        ];
        if (empty(settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::RENTAL_PLUGIN, 'rental_client_payments'))) {
            $data['is_temp'] = 0;
            App::import('Vendor', 'AutoNumber');;
            $data["order_number"] = AutoNumber::get_auto_serial(AutoNumber::TYPE_RESERVATION_ORDER);

        }


        $response  = $apiRequests->request($url,true,'POST', $data);

        if (!empty($response["error"])) {
            $messages = "";
            foreach ($response["error"]["error"]??[] as $key => $error) {
                foreach ($error as $item) {
                    $messages = $key.": ".$item."<br/>";
                }
            }
            if (empty($messages)) {
                $messages = $response["error"]["message"];
            }
            $this->flashMessage($messages, 'alert alert-danger m-t');
            $this->redirect("/");
        }

        if (empty(settings::getValue(\Izam\Daftra\Common\Utils\PluginUtil::RENTAL_PLUGIN, 'rental_client_payments'))) {
            AutoNumber::update_auto_serial(\AutoNumber::TYPE_RESERVATION_ORDER);
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'thankyou')));
            return;
        }


        $reservationOrderId = $response["item"]["id"];

        $invoiceItems = [];
        foreach ($response["reservation_items"] as $reservationItem) {
            $invoiceItems[] = [
                'item' => $reservationItem['name'],
                'unit_price' => $reservationItem['total'],
                'tax1' => $reservationItem['tax1'],
                'tax2' => $reservationItem['tax2'],
                'quantity' => 1
            ];
        }

        $invoiceData = [
            'Invoice' => [
                'client_id' => getAuthClient('id'),
                'currency_code' => $response["item"]["currency"],
                'date' =>  $response["item"]["created"],
                'issue_date' => format_date(date("Y-m-d")),
                'source_type' => InvoiceSourceTypesUtil::RESERVATION_ORDER,
                'source_id' => $reservationOrderId,
                'branch_id' => getAuthClient('branch_id'),
                'staff_id' => $response["item"]["staff_id"],
                "type" => Invoice::TEMPINVOICE
            ],
            'InvoiceItem' => $invoiceItems
        ];
        /* forces method to be offline, in case client does not have email, it could be any value based method used in previous invoice */
        if (filter_var(getAuthClient("email"), FILTER_VALIDATE_EMAIL)) {
            $invoiceData['Invoice']['is_offline'] = 1;
        }

        $result = $this->send_request('/api2/invoices?send=draft', 'POST', $invoiceData);
        $invoice_id = $result["id"];
        $template_data['has_invoice'] = true;
        $template_data['payment_link'] = "/client/invoices/pay/$invoice_id?layout=-1&webfront_reservation=1";
        $template_data['page_title'] = __('Checkout', true);
        $template_data['is_logged_in'] = $this->is_logged_in();
        $this->process_content('checkout', $template_data);
    }

    public function client_after_payment_success_for_booking($invoice_id, $reservation_id)
    {

        $reservationRepo = new \Izam\Rental\Repositories\ReservationOrderRepository();
        $unit_type_id = $reservationRepo->find($reservation_id)->unitBooking->unit->unit_type_id;
        App::import('Component', 'ApiRequestsComponent');
        $apiRequests = new ApiRequestsComponent();
        $url = '/v2/unit-type/get-price-details/'.$unit_type_id;
        $validationResponse  = $apiRequests->request($url,false,'POST', ['start_date' => $_POST['start_date'], 'end_date' => $_POST['end_date'], 'start_time' => $_POST['start_time'], "end_time" => $_POST["end_time"]]);
//        if (!empty($validationResponse["error"])) {
//            $this->flashMessage($validationResponse["error"]["message"], 'alert alert-danger m-t');
//            $this->redirect("/");
//        }
        $unitBookingRepo = new \Izam\Rental\Repositories\UnitBookingRepository();
        $unitBookingRepo->setInvoiceIdByReservationOrderId($reservation_id, $invoice_id);
        App::import('Vendor', 'AutoNumber');;
        $reservationRepo->update($reservation_id, ['is_temp' => 0, "order_number" => AutoNumber::get_auto_serial(AutoNumber::TYPE_RESERVATION_ORDER)]);
        AutoNumber::update_auto_serial(\AutoNumber::TYPE_RESERVATION_ORDER);
        $this->updateInvoiceFromDraft($invoice_id);
        $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'thankyou')));
    }


    function product_view($product_id = null){
        $this->loadModel('Product');
        $conditions = ['Product.id' => $product_id, 'Product.availabe_online' => '1'];
        if (ifPluginActive(BranchesPlugin)  && empty(settings::getValue(BranchesPlugin, 'share_products'))) {
            $conditions['Product.branch_id'] = getBranchForShopFront();
        }
        $product = $this->Product->find('first', ['recursive' => 1,'conditions' => $conditions]);
        // Incase Product Not Found Or Not Available Online
        if(!$product){
            $this->flashMessage(__('This Product was not found',true), 'alert alert-danger m-t');
            $this->redirect(Router::url($this->homeUrl));
        }

        $client = getAuthClient();
        if(!empty($client)){
            $group_price_id=intval(getAuthClient('group_price_id'));
        }else{
            $group_price_id=0;
        };

        if (!empty($product['ProductPrice'])) {
            $priceList = current(array_filter($product['ProductPrice'], function ($item) use ($group_price_id) {
                return $item['group_price_id'] == $group_price_id;
            }));
            if (!empty($priceList)) {
                $product["Product"]['unit_price'] = $priceList['price'];
            }
        }
        // Calculate Discount For The Product
        $product = $this->Product->getProductWithDiscount($product);
        // Format Price Both
        $product['Product']['unit_price_formatted'] = format_price($product['Product']['unit_price']);
        $product['Product']['final_price_number'] = "".(float) $product['Product']['final_price']."";
        $product['Product']['final_price_formatted'] = format_price($product['Product']['final_price']);
        // Load s3_images .
        $s3Images = izam_resolve(AttachmentsService::class)->getAttachments('product',$product_id);
        $awsService = new Aws;
        $temp = [];
        foreach($s3Images as $image){
         $fileDetails = ['entity'=> $image->files->entity, 'path' => $image->files->path, 'mime_type' => $image->files->mime_type, 'name' => $image->files->name , 'site_id' => $_SESSION['OWNER']['id'] ];
         RollbarLogService::logTempFilesToRollbar($image->files->is_temp,$fileDetails);
         
         $temp[] = $awsService->getPermanentUrl($image->files->path);
        }
        // Add Product Images
        $images = $this->Product->getProductImages($product);
        $template_data['product_images'] = array_merge($images,$temp);
        $template_data['product_images_count'] = count($template_data['product_images']);
        $template_data['product'] = $product['Product'];
        $template_data['custom_fields'] = $product['CustomModel'];
        $this->process_content('view_product', $template_data);
    }

    /**
     * this function gets mustache templates that may contain another mustache templates and renders it recursively
     * base case the content has no embedded elements then the function returns the content after replacing the place holder with template data.
     * recursive the content has elements so the function loops through those elements and get their data and keys and pass it to the process_custom_element,</br>
     * and when the function returns the content we replace the embedded element with its rendered content after that we can render the parent element with its data
     * @param type $content_key
     * @param type $template_data
     * @return type content
     *
     */
    private function process_custom_element($content_key,$template_data)
    {
        App::import('Vendor','MustacheEngine', array('file' => 'MustacheEngine/MustacheEngine.php'));
        Mustache_Autoloader::register();

        $content = $this->Content->findByKey($content_key);
        if($temp = json_decode($content['Content']['content'],true))
        {
            if (isset($temp['gjs-css'])) {
                $re = '/\.-?[_a-zA-Z\-]+[\w\-]*\s*|#-?[_a-zA-Z\-]+[\w\-]*\s*/m';
                $css = preg_replace($re, '.custom_content $0' , $temp['gjs-css']);
                $content['Content']['content'] = '<style>'.$css.'</style> <div class="custom_content container">'.$temp['gjs-html'].'</div>';
            } else {
                $content['Content']['content'] =  $temp['gjs-html'];
            }
        }

        preg_match_all('/\{\{([^\}]*)\:([^\.]*)\.([^\(]*)\(([^\)]*)\)\}\}/', $content['Content']['content'],$m);
        $elements = array();

        if($m){
            foreach($m as $matchIndex => $matchData){
                foreach($matchData as $elementIndex => $elementData){
                    //m[0] exact string matched,m[1] content key,m[2]data controller,m[3] data action,m[params]
                    $elements[$elementIndex][] = $elementData;
                }
            }
            foreach($elements as $elementIndex => $elementData)
            {
                $element_content_key = $elementData[1];
                $element_data_model = $elementData[2];
                $element_data_action = $elementData[3];
                $element_data_params = explode(',',$elementData[4]);
                $element_template_data = $this->call_model_function($element_data_model, $element_data_action, $element_data_params);
                $rendered_element = $this->process_custom_element($element_content_key,$element_template_data);
                $content['Content']['content'] = str_replace($elementData[0], $rendered_element, $content['Content']['content']);
            }
        }

        $content['Content']['content'] = MustacheEngine::render($content['Content']['content'],$template_data);
        return $content['Content']['content'];
    }

    function logout()
    {
        App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));
        $logout_link = $this->get_logout_link();
        $cart = new ShopingCart(['itemMaxQuantity' => 99]);
        $cart->destroy();
        $this->redirect($logout_link);
    }

    /**
     *
     * @param String $content_key
     * @param mixed $template_data
     * @param bool $return if set to true it will return the content
     * set the content
     */
    function process_content($content_key = null,$template_data = array(),$return = false)
    {
        if (!ifPluginActive(PluginUtil::WebsiteFrontPlugin)) {
            $this->cakeError('error404');
        }
        App::import('Vendor','MustacheEngine', array('file' => 'MustacheEngine/MustacheEngine.php'));
        Mustache_Autoloader::register();
        $content = $this->Content->findByKey($content_key);

        $data['page_title'] = $content['Content']['title'];

        $currentSite = getCurrentSite();
        $data['logo'] = $currentSite['site_logo_full_path'];
        $data["authenticated"] = !!getAuthOwner() || !!getAuthClient();
        if (getAuthOwner("staff_id") === 0) {
            $dashboardURL = "/owner/sites/dashboard";
        } elseif (!empty(getAuthStaff())) {
            $dashboardURL = "/owner/staffs/account_info";
        } else {
            $dashboardURL = "/client/dashboard";
        }
        $data['dashboard_url'] = $dashboardURL;
        if ($data['logo'] == '/files/images/site-logos/') {
            $data['logo'] = false;
        }
        $data['siteName'] = $currentSite['business_name'];
        $data['client_id'] = getAuthClient('id') ? getAuthClient('id') : '0';
        $data['page_title'] = $currentSite['business_name'] . ' | ' . $data['page_title'];
        if ($content_key == "contact_us") {
            $template_data['address'] = getCurrentSite('address1');
            $template_data['mobile'] = getCurrentSite('phone1');
            $template_data['email'] = getCurrentSite('email');
            $template_data['whatsapp'] = getCurrentSite('phone2');
        }
        $content['Content']['content'] = $this->process_custom_element($content_key, $template_data);

        $data['css_ver'] =  (string) CSS_VERSION;
        $data['js_ver'] =  (string) JAVASCRIPT_VERSION;
        if($return)
            return $content;
        $this->set('content',$content);
        $this->set_template_layout($data);
    }


    function rental($return = false)
    {
        $content_key = 'unit_type_filter';
        
        if (!ifPluginActive(RENTAL_PLUGIN) || empty(settings::getValue(PluginUtil::RENTAL_PLUGIN, 'rental_enable_client_reservation'))) {
            $this->flashMessage(__("No Online Rental", true), 'alert alert-danger m-t');
            $this->redirect($this->referer());
        }

        if(!empty($this->params['url']['rental_types'])) {
            $rentalUnitTypes = $this->params['url']['rental_types'];
            $unitTypesId = [];
            
            foreach($this->params['url']['rental_types'] as $unitType){
                $unitTypesId[] = ['rental_types' => (string)$unitType];
            }
            $template_data['rental_types'] =  $unitTypesId;
        } else {
            $this->flashMessage(__("Error in the entered UnitTypes details", true), 'Errormessage');
            return $this->referer($_SERVER['REQUEST_URI']);
        }
        
        $unitTypeService = new UnitTypeService();
        
        if(!$unitTypeService->isSingleMethod($rentalUnitTypes)){
            $this->flashMessage(__('Error in the entered UnitTypes details', true), 'alert alert-danger m-t');
            return $this->referer($_SERVER['REQUEST_URI']);
        }
        
        $unitType = $unitTypeService->getDetails($rentalUnitTypes);
        
        if($unitType['method'] == "days"){
            $template_data['day'] = 1;
        }else{
            $template_data['hours'] = 1; 

            $unitTypeSlot = $unitTypeService->getUnitTypeSlots($rentalUnitTypes);
            if(!is_null($unitTypeSlot)){
                $template_data['data'] = $unitTypeService->prepareSlots($unitTypeSlot['time_slots']);
            }else{
                $this->flashMessage(__("Error in the entered UnitTypes details", true), 'alert alert-danger m-t');
                return $this->referer($_SERVER['REQUEST_URI']);
            }
        
        }

        // Load Additional Fields . 
        $entityDynamicService = new EntityDynamicService();
        $form = $entityDynamicService->getAdditionalFields('rental_reservation_order');
        $showAvailableEntityFields = Settings::getValue(PluginUtil::RENTAL_PLUGIN, SettingsUtil::RENTAL_RESERVATION_SHOW_LOCAL_ENTITY_FIELDES_IN_SHOPFRONT);
        $showAvailableEntityFields = isset($showAvailableEntityFields) ? json_decode($showAvailableEntityFields) : [];
        if ($form) {
            $helper = new \Izam\View\Form\Helper\FormCollection();
            $template_data['additional_fields'] = null;
            if(!is_null($form['form']))
            {
                $fieldsName = [];
                foreach ($form['form']->getElements() + $form['form']->getFieldsets() as $fieldName => $element){
                    $fieldsName[] = $fieldName;
                }
                $showAvailableEntityFields = array_merge(['id', 'reference_id'], $showAvailableEntityFields);
                $removedElement = array_diff($fieldsName, $showAvailableEntityFields);
                array_map(fn($val) => $form['form']->remove($val), $removedElement);
                $template_data['additional_fields'] = $helper->render($form['form']);
            }
        }

        $template_data['url'] = '/contents/unit_type_list';
        $template_data['method'] = 'GET';
        $this->process_content('unit_type_filter' ,$template_data);
    }

    function view_cart(){

        App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));

        $cart = new ShopingCart();
        $cart_items = $cart->getItems();

        $template_data = array();
        foreach($cart_items as $item_id => $item_data ){
            $temp['id'] = $item_id;
            $temp['quantity'] = $item_data[0]['quantity'];
            $temp['name'] = $item_data[0]['attributes']['name'];
            $temp['price'] = format_price_simple($item_data[0]['attributes']['price']);
            $temp['subtotal'] = format_price($item_data[0]['attributes']['price'] * $item_data[0]['quantity']);
            $temp['image'] = $item_data[0]['attributes']['image'];
            $template_data['items'][] = $temp;
        }
        $template_data['cart_total'] = format_price($cart->getAttributeTotal('price'));
        $template_data['page_title'] = __('Cart',true) ;

        $this->process_content('view-cart', $template_data);

    }

    function client_thankyou(){
        App::import('Vendor','CartClass', array('file' => 'class.Cart.php'));
        $cart = new ShopingCart();
        $cart->clear();
        $template_data['page_title'] = __('Thank You',true) ;
        $invoice_id = $_GET['invoice_id'] ?? null;
        if ($invoice_id) {
            $this->loadModel('Invoice');
            $invoice = $this->Invoice->find('first', ['conditions' => ['Invoice.id' => $invoice_id]]);
            if ($invoice)
                $template_data = array_merge($template_data, $invoice['Invoice']);
            else
                $template_data['id'] = null;
        } else {
            $template_data['id'] = null;
        }
        $this->process_content('thank', $template_data);
    }

    function client_processPaytabsPayment() {
        /** Handling Paytabs after Payment **/
        App::import('Vendor', 'PayTabsPayment', array('file' => 'payments/PayTabsPayment.php'));
        $this->loadModel('SitePaymentGateway');
        $paytabsRecord = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.payment_gateway' => 'paytabs')))['SitePaymentGateway'];
        $paymentReference = $_POST['payment_reference'];
        $paymentValidationData = PayTabsPayment::validatePaytabsPayment($paytabsRecord, $paymentReference);
        $responseMsg = $paymentValidationData['result'];
        $responseCode = $paymentValidationData['response_code'];
        $transactionID = $paymentValidationData['transaction_id'];

        $updateData = null;
        $flag = null;
        $codes = PayTabsPayment::getResponseCodeArrays();
        if (in_array($responseCode, $codes['successArr'])) {
            $flag = 'success';
            $updateData = array(
                'transaction_id' => $transactionID,
                'status' => PAYMENT_STATUS_COMPLETED,
                'response_code' => $responseCode,
                'response_message' => $responseMsg
            );
        } elseif (in_array($responseCode, $codes['pendingArr'])) {
            $flag = 'pending';
            $updateData = array(
                'transaction_id' => $transactionID,
                'status' => PAYMENT_STATUS_PENDING,
                'response_code' => $responseCode,
                'response_message' => $responseMsg
            );
        } else {
            $flag = 'fail';
            $updateData = array(
                'transaction_id' => $transactionID,
                'status' => PAYMENT_STATUS_FAILED,
                'response_code' => $responseCode,
                'response_message' => $responseMsg
            );
        }

        $payment_id = $paymentValidationData['reference_no'];
        $this->loadModel('InvoicePayment');
        $invoicePayment = $this->InvoicePayment->find('first', array('conditions' => array('InvoicePayment.id' => $payment_id)));
        foreach ($updateData as $key => $value) {
            $invoicePayment['InvoicePayment'][$key] = $value;
        }
        $this->InvoicePayment->save($invoicePayment, false);
        $this->Invoice->updateInvoicePayments($invoicePayment['InvoicePayment']['invoice_id']);

        $alertData = PayTabsPayment::displayPaytabsResponseCode($responseCode);
        $this->flashMessage("(Transaction: $transactionID) $responseMsg ".$alertData['msg']." (Code: $responseCode)", $alertData['class']);
        if ($flag == 'fail') {
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'error')));
        } else {
            $this->client_after_payment_success($invoicePayment['InvoicePayment']['invoice_id']);
        }
    }

    function client_error()
    {
        $template_data['page_title'] = __('Payment Error',true) ;
        $this->process_content('pay_error', $template_data);
    }
    
    public function client_placeOrder()
    {
        App::import('Vendor', 'CartClass', array('file' => 'class.Cart.php'));
        $cart = new ShopingCart();
        $attributes = $cart->getItem(-1)['attributes'];
        
        if (isset($_POST['shipping_method'])) {
            $shipping_option_id = $_POST['shipping_option_id'];
            $shipping_amount = $_POST['shipping_amount'];
            if (is_null($attributes)) {
                $attr = [
                    'orderPlaced' => true,
                    'shipping_amount' => $shipping_amount,
                    'shipping_option_id' => $shipping_option_id
                ];
                $cart->add(-1, 0, $attr);
            } else {
                $cart->update(-1, [
                    'quantity' => 0,
                    'attributes' => [
                        'up_to_date' => 0,
                        'orderPlaced' => true,
                        'shipping_amount' => $shipping_amount,
                        'shipping_option_id' => $shipping_option_id
                    ]
                ]);
            }
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'checkout')));
        } elseif (isset($_POST['shipping_disabled'])) {
            if (is_null($attributes)) {
                $attr = [
                    'orderPlaced' => false
                ];
                $cart->add(-1, 0, $attr);
            } else {
                $cart->update(-1, [
                    'quantity' => 0,
                    'attributes' => [
                        'up_to_date' => 0,
                        'orderPlaced' => false
                    ]
                ]);
            }
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'checkout')));
        } else {
            $this->flashMessage(__('Order Not Placed', true), 'alert alert-danger m-t');
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'order_summary')));
        }
    }

    public function client_order_summary()
    {
        $precision = 3;
        App::import('Vendor', 'CartClass', array('file' => 'class.Cart.php'));
        $this->loadModel("Tax");
        $cart = new ShopingCart(['itemMaxQuantity' => 99]);
        $items = $cart->getItems();
        $taxes = $this->formatTaxes($this->Tax->find("all", []));
        $subtotals = 0;
        $discounts = 0;
        $currency = getCurrentSite("currency_code");
        $totalTaxes = [];
        foreach ($items as $key => $item) {
            $subtotals += ($item["attributes"]["price"] * $item["quantity"]);
            $item_discount = $item["attributes"]["discount"];
            if ($item['attributes']['discount_type'] != 1 ) {
                $item_discount*=$item["quantity"];
            }
            $finalDiscount = $this->calculateDiscount($item["attributes"]["original_price"]* $item['quantity'],$item_discount, $item["attributes"]["discount_type"]);
            $items[$key]["attributes"]["final_discount"] = $finalDiscount;
            $discounts += (int)explode(" ", $finalDiscount)[0];
            $subtotal = $item["attributes"]["price"] * $item["quantity"];
            $items[$key]["attributes"]["subtotal"] = $subtotal;
            // this function get price of product event it has inclusive
            $item["attributes"]["price"] = $this->getOriginalPrice($item['attributes']['code'], $item['attributes']['price']);
            $items[$key]["attributes"]["formatted_price"] = format_price($items[$key]["attributes"]["original_price"], $currency);
            $items[$key]["attributes"]["formatted_subtotal"] = format_price($subtotal, $currency);
            if ($item["attributes"]["tax1"]) {
                $tax = $taxes[$item["attributes"]["tax1"]];
                $itemTax1 = $this->calculateItemTax($tax, $item["attributes"]["price"] * $item['quantity']);
                $totalTaxes[$item["attributes"]["tax1"]] = [
                    "id" => $tax['id'],
                    "name" => $tax["name"],
                    "included" => $tax['included'],
                    "value" => number_format((float)($itemTax1 + $totalTaxes[$item["attributes"]["tax1"]]["value"]), $precision, '.', ''),
                    "formatted_value" => format_price(number_format((float)($itemTax1 + $totalTaxes[$item["attributes"]["tax1"]]["value"]), $precision, '.', ''), $currency),
                    "fees" => $tax["value"]
                ];
            }
        
            if ($item["attributes"]["tax2"]) {
                $tax = $taxes[$item["attributes"]["tax2"]];
                $itemTax2 = $this->calculateItemTax($tax, $item["attributes"]["price"] * $item['quantity']);
                $totalTaxes[$item["attributes"]["tax2"]] = [
                    "id" => $tax['id'],
                    "name" => $tax["name"],
                    "included" => $tax['included'],
                    "value" => number_format((float)($itemTax2 + $totalTaxes[$item["attributes"]["tax2"]]["value"]), $precision, '.', ''),
                    "formatted_value" => format_price(number_format((float)($itemTax2 + $totalTaxes[$item["attributes"]["tax2"]]["value"]), $precision, '.', ''), $currency),
                    "fees" => $tax["value"]
                ];
            }
        
            if ($item["attributes"]["tax1"] && $taxes[$item["attributes"]["tax1"]]["included"] == 1) {
                $subtotals -= $itemTax1;
            }
            
            if ($item["attributes"]["tax2"] && $taxes[$item["attributes"]["tax2"]]["included"] == 1) {
                $subtotals -= $itemTax2;
            }

            // load s3 images .
            $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$key);
            if(count( $defaultS3Images))
            {
              $items[$key]["attributes"]["image"] = $defaultS3Images[0]->files->path;
             }
        }
        
        $hasDiscounts = false;
        foreach ($items as $item) {
            if ($item["attributes"]["discount"] > 0) {
                $hasDiscounts = true;
                break;
            }
        }
    
        $subtotals = number_format((float)$subtotals, $precision, '.', '');
        $formatted_subtotals = format_price(number_format((float)$subtotals, $precision, '.', ''), $currency);
        $totalTaxes = json_encode($totalTaxes);
        $this->loadModel("ShippingOption");
        $shippingOptions = $this->formatShippingOptions($this->ShippingOption->find("all", ["conditions" => ["status" => true, "deleted_at" => null], "order" => "display_order"]), $currency);
    
        // i remove subtotal - discount because its already deducted
        $total = ($subtotals);
        foreach ($totalTaxes as $tax) {
            $total += $tax["value"];
        }
    
        $formatted_total = format_price($total);
        $discounts = format_price($discounts);
        $items = $this->formatItems($items);
        $itemsCount = count($items);
        $shippingOptionsCount = count($shippingOptions);
        $all_taxes = json_encode($this->formatTaxes($this->Tax->find("all", [])));

        $this->process_content(
            'order_summary',
            compact(
                'items', 'hasDiscounts', 'subtotals', 'discounts',
                'totalTaxes', 'all_taxes', 'shippingOptions', 'total', 'itemsCount',
                'formatted_subtotals', 'formatted_total', 'shippingOptionsCount'
            )
        );
    }

    /**
     * @param $code
     * @param $price
     * @return float|int
     * This function will get original price of product regardless taxes
     * price will be 100% + tax amount etc 10% so we will divide price by 110% to get original price we will multiply by 100% price of net product
     */
    private function getOriginalPrice($code, $price)
    {
        $this->loadModel('Product');
        $product = $this->Product->find('first', ['conditions' => ['product_code' => $code]])['Product'];
        $this->loadModel('Tax');
        $taxs = $this->Tax->find('all', ['conditions' => ['included' => 1, 'id' => [$product['tax1'], $product['tax2']]]]);
        $totalTaxesPercentage = 0;
        foreach ($taxs as $tax) {
            $totalTaxesPercentage += $tax['Tax']['value'];
        }
        return ($price / ($totalTaxesPercentage + 100)) * 100;
    }
    
    /**
     * @param $items
     * @return array
     */
    private function formatItems($items)
    {
        $formattedItems = [];
        foreach ($items as $key => $item) {
            $formattedItems[$key]["quantity"] = $item["quantity"];
            $formattedItems[$key] = array_merge($item["attributes"], $formattedItems[$key]);
        }
        return array_values($formattedItems);
    }

    /**
     * @param $shippingOptions
     * @param $currency
     * @return array
     */
    private function formatShippingOptions($shippingOptions, $currency)
    {
        $formattedShippingOptions = [];
        foreach ($shippingOptions as $key => $shippingOption) {
            $formattedShippingOptions[] = $shippingOption["ShippingOption"];
            $formattedShippingOptions[$key]["id"] = $shippingOption["ShippingOption"]["id"];
            $formattedShippingOptions[$key]["tax_id"] = $shippingOption["ShippingOption"]["tax_id"];
            $formattedShippingOptions[$key]["tax_amount"] = !empty($shippingOption["ShippingOption"]["tax_id"]) ? "".$this->Tax->getTaxAmount($shippingOption["ShippingOption"]["tax_id"], $shippingOption["ShippingOption"]["fees"])."" : "0";
            if ($shippingOption["Tax"]["included"] === '1') {
                $shippingOption["ShippingOption"]["fees"] -= $formattedShippingOptions[$key]["tax_amount"];
            }
            $formattedShippingOptions[$key]["amount"] = "".$shippingOption["ShippingOption"]["fees"]."";
            $formattedShippingOptions[$key]["fees"] = format_price(number_format((float)$shippingOption["ShippingOption"]["fees"], 2, '.', ''), $currency);
        }
        return array_values($formattedShippingOptions);
    }

    /**
     * @param $taxes
     * @return array
     */
    private function formatTaxes($taxes)
    {
        $formattedTaxes = [];
        foreach ($taxes as $key => $tax) {
            $formattedTaxes[$tax["Tax"]["id"]] = $tax["Tax"];
        }
        return $formattedTaxes;
    }

    /**
     * @param $tax
     * @param $subtotal
     * @return float|int
     */
    private function calculateItemTax($tax, $subtotal)
    {
        return $subtotal * $tax["value"] / 100;
    }

    /**
     * @param $price
     * @param $discount
     * @param $discountType
     * @return string
     */
    private function calculateDiscount($price, $discount, $discountType)
    {
        if (empty($discount) || empty($discountType)) {
            return "-";
        }
        switch ($discountType) {
            case Product::DISCOUNT_TYPE_PERCENTAGE:
                $amount = ($price * $discount) / 100;
                return "$amount ($discount%)";
            case Product::DISCOUNT_TYPE_VALUE:
                $percentage = number_format((float)(($discount / $price) * 100), 3, '.', '');
                return "$discount ($percentage%)";
        }
    }

    function client_checkout()
    {
        $template_data['page_title'] = __('Checkout', true);
        $template_data['is_logged_in'] = $this->is_logged_in();

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true, false, false);
        if (!$paymentMethods) {
            $template_data['has_invoice'] = false;
            $template_data['errorMsg'] = "Payment Options Not Available";
            $this->process_content('checkout', $template_data);
            return;
        }

        if (isset($_SESSION['Message']['flash'])){
            $this->flashMessage($_SESSION['Message']['flash']['message'], 'alert alert-danger m-t');
        }

        App::import('Vendor', 'CartClass', array('file' => 'class.Cart.php'));
        $ShippingOptionModel = GetObjectOrLoadModel('ShippingOption');

        $cart = new ShopingCart(['itemMaxQuantity' => 99]);
        $cart_invoice = $cart->getItem(-1);
        $items = $cart->getItems();
        $this->Invoice = ClassRegistry::init('Invoice');
        $shipping_option = $ShippingOptionModel->find('first', ['conditions' => ['ShippingOption.id' => $cart_invoice['attributes']['shipping_option_id']]]);
        if (!empty($items)) {
            if (!$cart_invoice || $cart_invoice['attributes']['up_to_date'] == false) {
               // \App::import('Vendor', 'AutoNumber');
                //\AutoNumber::get_auto_serial(\AutoNumber::TYPE_INVOICE);
                $invoice = array();
                $invoice['Invoice']['type'] = Invoice::TEMPINVOICE;
                $invoice['Invoice']['client_id'] = getAuthClient()['id'];
                $invoice['Invoice']['currency_code'] = getCurrentSite('currency_code');
                $invoice['Invoice']['language_id'] = getCurrentSite('language_code');
                $invoice['Invoice']['date'] = date('Y-m-d');
                $invoice['Invoice']['issue_date'] = $invoice['Invoice']['date'];
                $invoice['Invoice']['date_format'] = getCurrentSite('date_format');
                $invoice['Invoice']['source_type'] = Invoice::SHOP_FRONT;
                $invoice['Invoice']['shipping_options'] = null;
//                $invoice['Invoice']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_TEMPINVOICE);
//                $invoice['Invoice']['hidden_no'] = $invoice['Invoice']['no'];
                if(isset($cart_invoice['attributes']['orderPlaced']) && $cart_invoice['attributes']['orderPlaced']) {
                    $invoice['Invoice']['shipping_amount'] = $shipping_option['ShippingOption']['fees'];
                    $invoice['Invoice']['shipping_option_id'] = $cart_invoice['attributes']['shipping_option_id'];
                } elseif (!isset($cart_invoice['attributes']['orderPlaced'])) {
                    $this->flashMessage(__('Order Not Placed', true), 'alert alert-danger m-t');
                    $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'order_summary')));
                } else {
                    $invoice['Invoice']['shipping_amount'] = null;
                    $invoice['Invoice']['shipping_option_id'] = null;
                }




                if (!empty($shipping_option['ShippingOption']['tax_id'])) {
                    $invoice['Invoice']['shipping_tax_id'] = $shipping_option['ShippingOption']['tax_id'];
                }

                $this->loadModel('InvoiceLayout');
                $invoiceLayoutColumns = $this->InvoiceLayout->find('first', array('conditions' => [
                    'InvoiceLayout.layout_type' => 0,
                    'InvoiceLayout.default' => true,
                    'item_columns IS NOT NULL AND item_columns <> \'\' '
                ], 'fields' => 'item_columns', 'order' => 'InvoiceLayout.name'));
                if ($invoiceLayoutColumns) {
                    $item_columns = json_decode($invoiceLayoutColumns['InvoiceLayout']['item_columns'], true);
                    foreach ($items as $item_id => $item_data) {
                        $invoice_item['item'] = $item_data['attributes'][$item_columns["field1"]["name"]];
                        $invoice_item['description'] = $item_data['attributes'][$item_columns["field2"]["name"]];
                        $invoice_item['site_id'] = getCurrentSite('id');
                        $invoice_item['unit_price'] = $item_data['attributes']['original_price'];
                        $invoice_item['tax1'] = $item_data['attributes']['tax1'];
                        $invoice_item['tax2'] = $item_data['attributes']['tax2'];
                        $invoice_item['quantity'] = $item_data['quantity'];
                        $invoice_item['discount'] = $item_data['attributes']['discount'];
                        $invoice_item['discount_type'] = $item_data['attributes']['discount_type'];
                        $invoice_item['unit_factor'] = $item_data['attributes']['unit_factor'];
                        $invoice_item['unit_factor_id'] = $item_data['attributes']['unit_factor_id'];
                        $invoice_item['product_id'] = $item_id;
                        $invoice['InvoiceItem'][] = $invoice_item;
                    }
                } else {
                    foreach ($items as $item_id => $item_data) {
                        $invoice_item['item'] = $item_data['attributes']['name'];
                        $invoice_item['description'] = $item_data['attributes']['description'];
                        $invoice_item['site_id'] = getCurrentSite('id');
                        $invoice_item['unit_price'] = $item_data['attributes']['original_price'];
                        $invoice_item['tax1'] = $item_data['attributes']['tax1'];
                        $invoice_item['tax2'] = $item_data['attributes']['tax2'];
                        $invoice_item['quantity'] = $item_data['quantity'];
                        $invoice_item['discount'] = $item_data['attributes']['discount'];
                        $invoice_item['discount_type'] = $item_data['attributes']['discount_type'];
                        $invoice_item['unit_factor'] = $item_data['attributes']['unit_factor'];
                        $invoice_item['unit_factor_id'] = $item_data['attributes']['unit_factor_id'];
                        $invoice_item['product_id'] = $item_id;
                        $invoice['InvoiceItem'][] = $invoice_item;
                    }
                }
    

                if (!isset($cart_invoice['attributes']['invoice_id'])) {
                /*
                   Log shopfront invoice data before sent to the backend to check the issue with wrong price lists
                */
                if(getCurrentSite('id') == 2213939) {
                    $directory = dirname(dirname(__FILE__)) . DS . 'webroot' . DS . 'files' . DS . 'shop-front-invoices';
                    if (!is_dir($directory)) {
                      mkdir($directory, 0755, true); 
                    }
                    $fileName = date('Y-m-d').'.txt';
                    file_put_contents($directory.DS.$fileName, '-----Time '.date('H:i a').' ------'."\r\n", FILE_APPEND);
                    file_put_contents($directory.DS.$fileName, print_r($invoice, true)."\r\n", FILE_APPEND);     
                    file_put_contents($directory.DS.$fileName, print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 10), true), FILE_APPEND);
                    file_put_contents($directory.DS.$fileName, print_r(getAuthClient(), true), FILE_APPEND);
                    file_put_contents($directory.DS.$fileName, '-----End ------'."\r\n", FILE_APPEND);
                }
                    $result = $this->send_request('/api2/invoices?send=draft', 'POST', $invoice);
                    if ($result['result'] == 'successful') {
                        $invoice_id = $result['id'];

                        // This check is for the shipping option in client_placeOrder
                        if (is_null($cart->getItem(-1))) {
                            $cart->add(-1, 0, ['up_to_date' => true, 'invoice_id' => $result['id'], 'invoice_total' => $cart->getAttributeTotal('price')]);
                        } else {
                            $cart->update(-1, ['attributes' => ['up_to_date' => true, 'invoice_id' => $result['id'], 'invoice_total' => $cart->getAttributeTotal('price')]]);
                        }
                    } else {
                        $this->flashMessage($result['message'], 'alert alert-danger m-t');
                        $invoice_id = null;
                    }
                } else {
                    $invoice['Invoice']['id'] = $cart_invoice['attributes']['invoice_id'];
                    $result = $this->send_request('/api2/invoices/' . $cart_invoice['attributes']['invoice_id'] . '?send=draft', 'PUT', $invoice);
                    if ($result['code'] == 200) {
                        $invoice_id = $invoice['Invoice']['id'];
                        $cart->update(-1, ['quantity' => 0, 'attributes' => ['up_to_date' => true]], $cart_invoice['attributes']);
                    } else {
                        debug($result);
                        $invoice_id = null;
                    }
                }
            } else {
                $invoice_id = $cart_invoice['attributes']['invoice_id'];
            }

            if (!is_null($invoice_id)) {
                $template_data['has_invoice'] = true;
                $template_data['payment_link'] = "/client/invoices/pay/$invoice_id?layout=-1&webfront=1";
            } else {
                $template_data['has_invoice'] = false;
                $template_data['errorMsg'] = sprintf(__('%s is not found', true), __('Invoice', true));
            }
        } else {
            $this->flashMessage('Cart is Empty', 'alert alert-danger m-t');
            $this->redirect($this->homeUrl);
        }

        $this->process_content('checkout', $template_data);
    }

    function view($page_key)
    {
        $content = $this->Content->findByKey($page_key);
        $content['Content']['content'] = json_decode($content['Content']['content'],true);
        $this->set_header_and_footer();
        $this->layout = false;
        $this->set($content);
    }

    function _createInvoice($id = null) {
        $invoiceController = new InvoicesController;
        $invoiceController->constructClasses();
        return $invoiceController->_createInvoice($id);
    }

    function _createDebitNote($id = null) {
        $invoiceController = new InvoicesController;
        $invoiceController->constructClasses();
        return $invoiceController->_createDebitNote($id);
    }


    function owner_create($page_key)
    {
        $title = getCurrentSite('business_name') . " | ";
        if($page_key)
        {

            $title .= __('Edit Page:',true) . ' ' . $page_key;
//
            $this->layout = '';
            $content = $this->Content->findByKey($page_key);
            $this->loadModel('Media');
            $medias = $this->Media->find('all', [ 'conditions' => [ 'Media.type' => 'photo' ] ]);
            $assets = [];
            foreach($medias as $k => $asset)
            {
                $assets[] = [ 'src' => 'https://'.getCurrentSite('subdomain').$asset['Media']['file_full_path'], 'type' => 'image'];
            }
            $assets = json_encode($assets, JSON_UNESCAPED_SLASHES);
//				die(Debug($assets));
            $header = $this->Content->findByKey('header');
            preg_match_all('/(<link[^>]*rel=[\'\"]stylesheet[^>]*>)/',$header['Content']['content'],$m);
            $css_files = $m[1];
            if(json_decode($content['Content']['content'],true))
            {
                $content['Content']['content'] = json_decode($content['Content']['content'],true);
//				$content['Content']['content']['gjs-css'] = '';
//				die(debug(($content['Content']['content'])));
                $content['Content']['content']['gjs-assets'] = $assets;
            }else{
//					preg_match_all('/(<link[^>]*rel=[\'\"]stylesheet[^>]*>)/',$header['Content']['content'],$m);
//
//					foreach($css_files as $css_file)
//					{
//						echo htmlentities($css_file);
//					}
//					die('222');
//
//
                $html = $content['Content']['content'];
                $content['Content']['content'] = [];
                $content['Content']['content']['gjs-html'] = $html;

                $content['Content']['content']['gjs-assets'] = $assets;
//					die(debug($content['Content']['content']['gjs-assets']));
                $content['Content']['content']['gjs-css'] = '';
            }
            $this->set('css_files',$css_files);

//				{"type":"image","src":"https://oidev.daftra.dev/files/db4715bd/medias/58bed_IMG_05022018_100425_0.png","unitDim":"px","height":0,"width":0},{"type":"image","src":"https://oidev.daftra.dev/files/db4715bd/medias/9063c_IMG_09012018_102916_0.png","unitDim":"px","height":0,"width":0},{"type":"image","src":"","unitDim":"px","height":0,"width":0}
//				die(debug(htmlentities($content['Content']['content']['gjs-html'])));
            if($this->RequestHandler->isAjax() && !empty($_POST))
            {
                $_POST['gjs-html'] = preg_replace('/<div[^>]*css\-files[\S\D]*?<\/div>/', '', $_POST['gjs-html']);
                $content['Content']['content'] = json_encode($_POST,JSON_UNESCAPED_SLASHES);
//					$content['Content']['content'] = preg_replace('/<div[^>]*css\-files[\S\D]*?<\/div>/', '', $content['Content']['content']);

                if($this->Content->save($content))
                {
                    die(__('Content Saved Successfully'));
                }{
                die(__('Content Couldn\'t be saved Successfully'));
            }
//					die(debug($_POST));
//					$this->Content->save()
            }else{
//					debug($content);
                $this->set('content',$content);
                $this->set('page_title',$title);

                $this->render('create');
            }
        }else{
            $title .= __('Create New Page');
            if (!empty($this->data)) {
                $this->data['Content']['type'] = Content::CONTENT_TYPE_PAGE;

                $this->Content->create();
                if ($this->Content->save($this->data)) {
                    $this->flashMessage(sprintf (__('The %s has been saved', true), __('content',true)), 'Sucmessage');
                    $this->redirect(array('plugin' => 'website_front', 'action'=>'create', $this->data['Content']['key']));
                } else {
                    $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('content',true)));
                }
            }
        }
        $this->set('page_title',$title);

    }

    function check_email(){

        $is_valid = true;
        if(!empty($this->data['Client']['email'])){
            $this->Client = ClassRegistry::init('Client');
            if($client = $this->Client->find('first',array( 'conditions' => array( 'Client.email' => $this->data['Client']['email'])))){
                $is_valid = false;
            }

        }
        die(json_encode($is_valid));
    }

    function login_redirect($url = null)
    {
        if($this->Session->check('LOGIN_REDIRECT') && !$this->Session->check('CLIENT'))
            $this->redirect($this->Session->read('LOGIN_REDIRECT'));
        else{
            if($this->Session->check('OWNER'))
                $this->redirect($this->ownerDashboardUrl);
            else{
                if ($url) {
                    $this->redirect($url);
                } else {
                    $this->redirect(Router::url($this->homeUrl));
                }

            }
        }
        exit;
    }

    function is_logged_in()
    {
        return isset($_SESSION['CLIENT']) | isset($_SESSION['OWNER']);
    }

    function register(){

        if(!settings::getValue(ClientsPlugin, 'client_permission_register'))
        {
            $this->flashMessage(__('Registration is disabled for this site',true));
            $this->redirect('/');
        }

        if(!$this->is_logged_in()){
            $this->loadModel('Client');
            $template_data = [];
            if(!empty($this->data)){
                $this->data['Client']['staff_id'] = -1;
                $this->data['Client']['type'] = 2;
                $this->data['Client']['is_offline'] = 0;
                $previousUrl = str_replace("redirect_to=", "", urldecode(parse_url($this->referer())["query"]));
                $previousUrl =base64_decode($previousUrl);
                // No need to validate custom fields here as we are not sending them to the server so we just ignore them.
                $result = $this->send_request('/api2/clients?verify_phone2=true&ignore_custom_fields=true', 'POST',$this->data);
                if ($result['result'] === 'successful') {
                    $result = $this->old_send_post('/rest/clients/login', $this->data);
                    if ($result['status']) {
                        $_SESSION = array_merge($result['data'], $_SESSION);
                        if (strpos($previousUrl, "reservation_summary") !== false) {
                            $this->login_redirect($previousUrl);
                        }
                        $this->login_redirect();
                    } else {
                        $this->flashMessage(__($result['message'], true), 'alert alert-danger m-t');
                    }
                } else {
                    $template_data = $this->data['Client'];
                    $template_data['errors'] = $result['validation_errors'];
                    if (empty($result['validation_errors'])) {
                        $this->flashMessage(__($result['message'], true), 'alert alert-danger m-t');
                    }
                }
            }

            $template_data['page_title'] = __('Register',true) ;

            $this->process_content('register', $template_data);
        }else{
            $this->flashMessage(__("You Are Already Logged In",true), 'alert alert-danger m-t');
            $this->redirect($this->homeUrl);
        }
    }

    function login()
    {
        if($this->is_logged_in()){
            $this->flashMessage(__("You Are Already Logged In",true), 'alert alert-danger m-t');
            $this->redirect($this->homeUrl);
        }
        if(!empty($this->data)){
            $previousUrl = str_replace("redirect_to=", "", urldecode(parse_url($this->referer())["query"]));
            $previousUrl =base64_decode($previousUrl);
            $result = $this->old_send_post('/rest/clients/login?debug=0', $this->data);
            if($result['status']){
                write_client_settings();
                $_SESSION = array_merge($result['data'],$_SESSION);
                if (ifPluginActive(BranchesPlugin)){
                    $this->Session->write('branch_id', $result['data']['branch_id']);
                }
                if (strpos($previousUrl, "reservation_summary") !== false) {
                    $this->login_redirect($previousUrl);
                }
                $this->login_redirect();
            } else {
                $template_data = $this->data['Client'];

                $this->flashMessage($result["message"], 'alert alert-danger m-t');
                $this->redirect($this->referer());
            }
        }
        $template_data['page_title'] = __('Login',true) ;
        $this->process_content('login', $template_data);
    }

    function owner_edit($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'content',true)));
            $this->redirect(array('plugin' => 'website_front', 'action'=>'index', '?' =>'editable=1'));
        }
        $content = $this->Content->read(null, $id);
        if (!empty($this->data)) {
            if ($this->Content->save($this->data)) {
                $this->flashMessage(sprintf (__('The %s  has been saved', true), __('content',true)), 'Sucmessage');
                $this->redirect(array('plugin' => 'website_front', 'action'=>'index', '?' =>'editable=1'));
            } else {
                $this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('content',true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $content;
        }
        $this->set('types_list', $this->Content->get_types_list());

        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('content',true)));
            $this->redirect(array('plugin' => 'website_front', 'action'=>'menu'));
        }
        $module_name= __('content', true);
        if(is_countable($id) && count($id) > 1){
            $module_name= __('contents', true);
        }
        $contents = $this->Content->find('all',array('conditions'=>array('Content.id'=>$id)));
        if (empty($contents)){
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer(array('plugin' => 'website_front', 'action' => 'menu'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Content->deleteAll(array('Content.id'=>$_POST['ids']))) {
                $this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
                $this->redirect(array('plugin' => 'website_front', 'action'=>'menu'));
            }
            else{
                $this->redirect(array('plugin' => 'website_front', 'action'=>'menu'));
            }
        }
        $this->set('contents',$contents);
    }

    private function prepareProductImages($cart){

        foreach ($cart['cart_products'] as $key => $product) {

            $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault('product',$product['id']);
            if(count( $defaultS3Images))
            {
                $cart['cart_products'][$key]['image'] = $defaultS3Images[0]->files->path;
            }
        }
        return $cart;
    }

    /**
     * @param $data
     * @return array
     * @todo refact foreach
     * sub foreach because parsing mulitDynamic and mulitDynamic in subForm
     */
    private function parsingRentalDataRequest($data = []): array
    {
        $templateData = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                foreach ($value as $index => $item) {
                    if(is_array($item)){
                        foreach ($item as $k => $v) {
                            if(is_array($v)){
                                foreach ($v as $k1 => $v1){
                                    $templateData[] = ['name' => "le_custom_data_rental_reservation_order[" . $key . "][" . $index . "][" . $k . "][".$k1."]", 'value' => $v1];
                                }
                            }else{
                                $templateData[] = ['name' => "le_custom_data_rental_reservation_order[" . $key . "][" . $index . "][" . $k . "]", 'value' => $v];
                            }
                        }
                    }else{
                        $templateData[] = ['name' => "le_custom_data_rental_reservation_order[".$key."][".$index."]", 'value' => $item];
                    }
                }

            } else {
                $templateData[] = ['name' => "le_custom_data_rental_reservation_order[".$key."]", 'value' => $value];
            }
        }
        return $templateData;
    }
}
?>
