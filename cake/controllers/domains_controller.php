<?php

use Izam\Daftra\Portal\Services\DomainService;

class DomainsController extends AppController {

	var $name = 'Domains';

	/**
	 * @var Domain
	 */
	var $Domain;
	var $helpers = array('Html', 'Form');

	function admin_index() {
		$this->Domain->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('domains', $this->paginate('Domain', $conditions));
	}

	function admin_add() {
		if (!empty($this->data)) {
			if (DomainService::create($this->data['Domain'])) {
				$this->flashMessage(sprintf (__('%s has been saved', true), __('Domain',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Domain',true)));
			}
		}
	}

	function admin_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'domain',true)));
			$this->redirect(array('action'=>'index'));
		}
		$domain = $this->Domain->read(null, $id);
		if (!empty($this->data)) {
			if (DomainService::updateById($this->data['Domain']['id'], $this->data['Domain'])) {
				$this->flashMessage(sprintf (__('%s  has been saved', true), __('Domain',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('%s could not be saved. Please, try again', true), __('Domain',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $domain;
		}
		$this->render('admin_add');
	}

	function admin_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 }
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid %s', true), __('domain',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('domain', true);
		$verb = __('has', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('domains', true);
			$verb = __('have', true);
		 }
		$domains = $this->Domain->find('all',array('conditions'=>array('Domain.id'=>$id)));
		if (empty($domains)){
			$this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && DomainService::deleteWhere(['id' => $id])) {
				$this->flashMessage(sprintf (__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('domains',$domains);
	}
}
?>