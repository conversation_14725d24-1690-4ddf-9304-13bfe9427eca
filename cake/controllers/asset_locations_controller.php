<?php

App::import('Vendor', 'settings');

class AssetLocationsController extends AppController {

    var $name = 'AssetLocations';
//    var $validate_schema;

   
    function beforeFilter() {
        parent::beforeFilter();
        
    }
    function __settings ($exclude_location = null ) {
        $asset_locations = $this->AssetLocation->find ( 'list' , ['conditions' => ['AssetLocation.id <> '.intval($exclude_location)] ]);
        $this->set ( 'asset_locations' , $asset_locations );
    }
    /** 
     * Form for adding an asset location
     */
    function owner_add ( ) 
    {
       if ( !empty ( $this->data ) ){
           if ( $asset_id = $this->AssetLocation->add ( $this->data , $validation_errors )) {
               $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Location', true)), 'Sucmessage');
               $this->redirect (Router::url(['action' => 'view' , $asset_id ]) ) ;
           } else {
               $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Location', true)));
           }
       }
       $this->__settings ( ) ;
    }
    /**
     * Form for editing an asset location
     */
    function owner_edit ( $id ) 
    {
       if ( !empty ( $this->data ) ){
           if ( $asset_id = $this->AssetLocation->update ( $id , $this->data , $validation_errors )) {
               $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset Location', true)), 'Sucmessage');
               $this->redirect (Router::url(['action' => 'edit' , $asset_id ]) ) ;
           } else {
               $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset Location', true)));
           }
       }else {
           $this->data = $this->AssetLocation->find ('first' ,['conditions' => ['AssetLocation.id' => $id] ] ) ;
       }
        $this->__settings ( $id ) ;
        $this->render('owner_add');
    }
    
    

}
