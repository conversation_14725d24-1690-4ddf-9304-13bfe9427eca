<?php

use Izam\Daftra\Common\Utils\PermissionUtil;
class <PERSON>piKeysController extends AppController {

	var $name = 'ApiKeys';

	/**
	 * @var ApiKey
	 */
	var $ApiKey;
	var $helpers = array('Html', 'Form');

	function owner_index() {
        if(!check_permission([PermissionUtil::Edit_General_Settings])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
		$this->ApiKey->recursive = 0;
		$conditions = $this->_filter_params();
		$conditions['ApiKey.site_id']=getCurrentSite('id');
		$this->set('apiKeys', $this->paginate('ApiKey', $conditions));

        $result = $this->ApiKey->query("SELECT secret FROM oauth_clients WHERE id = 1");
        $clientSecret = $result[0]['oauth_clients']['secret'];

		$this->set('client_id', 1);
		$this->set('client_secret', $clientSecret);
	}

	function owner_add() {
		if (!getAuthOwner('is_super_admin')) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
		if (!empty($this->data)) {
			$this->data['ApiKey']['site_id'] = getCurrentSite('id');
			$this->data['ApiKey']['key'] = sha1(openssl_random_pseudo_bytes(40));
			if (\Izam\Daftra\Portal\Models\ApiKey::create($this->data['ApiKey'])) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('api key',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('api key',true)));
			}
		}
	}

	function owner_delete($id = null) {
		if (!getAuthOwner('is_super_admin')) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect('/');
        }
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('api key',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('apiKey', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('apiKeys', true);
		 } 
		$apiKeys = $this->ApiKey->find('all',array('conditions'=>array('ApiKey.id'=>$id, 'ApiKey.site_id'=>getCurrentSite('id'))));
		if (empty($apiKeys)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->ApiKey->deleteAll(array('ApiKey.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('apiKeys',$apiKeys);
	}

	function owner_dashboard() {

    }
}
?>