<?php
/**
 * @property Tooltip $Tooltip
 */
class ActionLinesController extends AppController {

    var $name = 'ActionLines';
    var $helpers = array('Html', 'Form','Fck' , 'Mixed');
	
	 function owner_index($staff_id=false) {
		
		$this->set('title_for_layout',  __('Activity Logs', true));
		$this->set('is_ajax', false);
		
		$this->set('staff_id', $staff_id);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
		if (!check_permission(View_All_Activity_Logs) && !check_permission(View_His_Activity_Logs)) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$conditions=[];
		if (!check_permission(View_All_Activity_Logs) && check_permission(View_His_Activity_Logs)) {
			$staff_id=getAuthOwner('staff_id');
			$conditions['ActionLine.staff_id'] = $staff_id;
			
		}
		
		$params=array();
		if($staff_id)
		{
			$params['ActionLine.staff_id']=$staff_id;
			$this->loadModel('Staff');
			$staff=$this->Staff->read(null,$staff_id);
			$this->set('staff_name',$staff['Staff']['name']);
			
		}
		
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('All',array($params));
		$options=array();
		// warning suppression
		if(array_key_exists('quick',$_GET)) $options['per_page']=30;
        $this->set('data', $timeline->getDataArray($options));
        $this->loadModel('ActionLine');
        $action_list_cache_key = SITE_HASH . $staff_id .'_action_keys' ;
         $action_list = Cache::read($action_list_cache_key);
         if (!$action_list) {
             $this->ActionLine->unbindModel(array('belongsTo' => array('Staff')));
             $action_all = $this->ActionLine->find('all', array('fields' => array('DISTINCT ActionLine.action_key'), 'conditions' => $conditions));
             $action_list = [];
             foreach ($action_all as $line)
                 $action_list[] = $line['ActionLine']['action_key'];
             Cache::write($action_list_cache_key, $action_list);
         }
		
       
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $actions[$key] = $action;
            }
        }

        $this->set('actions', $actions);
		$filter_data=$timeline->getFilterData();

		
		if(!$staff_id)
		$this->set('filter_staff', $filter_data['staff']);
		
		if(!empty($_GET['quick']))
			$this->layout=false;

	 }

    function api_get_actions($staff_id=false) {
        $conditions=[];
		if (!check_permission(View_All_Activity_Logs) && check_permission(View_His_Activity_Logs)) {
			$staff_id=getAuthOwner('staff_id');
			$conditions['ActionLine.staff_id'] = $staff_id;			
		}

        $this->loadModel('ActionLine');
        $action_list_cache_key = SITE_HASH . $staff_id .'_action_keys' ;
        $action_list = Cache::read($action_list_cache_key);
        if (!$action_list) {
            $this->ActionLine->unbindModel(array('belongsTo' => array('Staff')));
            $action_all = $this->ActionLine->find('all', array('fields' => array('DISTINCT ActionLine.action_key'), 'conditions' => $conditions));
            $action_list = [];
            foreach ($action_all as $line)
                $action_list[] = $line['ActionLine']['action_key'];
            Cache::write($action_list_cache_key, $action_list);
        }

        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('All',array($params));
        $actions = [];

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $actions[$key] = $action;
            }
        }
        die(json_encode($actions));
    }

    function api_list_actionline_by_ids() {
        $ids = isset($_GET['ids']) ? $_GET['ids'] : [];
        if (empty($ids)) {
            die(json_encode([]));
        }
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('All',[]);
        $conditions = ['ActionLine.id' => $ids];
        $ActionLine = GetObjectOrLoadModel('ActionLine');
        $ActionLine->disableBranchFind();
        $actions = $ActionLine->find('all', ['conditions' => $conditions]);
        $actionStringsMapById = [];        
        foreach($actions as $action) {
            $actionStringsMapById[$action['ActionLine']['id']] = [
                'class' => $timeline->ActionKeys[$action['ActionLine']['action_key']]['class'],                
                'text' => $timeline->longaction2string($action),
                'staff_name' => $timeline->Slist[$action['ActionLine']['staff_id']] ?? null, // warning suppress
                'action_key' => $action['ActionLine']['action_key'],
                'id' => $action['ActionLine']['id'],
                'date' => $action['ActionLine']['created'],
                /** as defined in cake */
                'amount' => $action['ActionLine']['param1'],
                'branch_id' => $action['ActionLine']['branch_id'],
                'action-id' => $action['ActionLine']['id'],
                'ip' => $action['ActionLine']['ip'],
                'logged_as_admin' => $action['ActionLine']['logged_as_admin']
            ];
        }
        die(json_encode($actionStringsMapById));
    }

	 
	 function owner_timeline_row($id = null) {
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('All');
        $this->set('data', $timeline->getDataArray());
        echo $timeline->view_action($id);
        die();
    }

    
}