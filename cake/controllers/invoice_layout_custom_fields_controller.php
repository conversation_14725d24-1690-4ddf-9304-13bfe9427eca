<?php
class InvoiceLayoutCustomFieldsController extends App<PERSON>ontroller {

	var $name = 'InvoiceLayoutCustomFields';

	/**
	 * @var InvoiceLayoutCustomField
	 */
	var $InvoiceLayoutCustomField;
	var $helpers = array('Html', 'Form');

	function index() {
		$this->InvoiceLayoutCustomField->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('invoiceLayoutCustomFields', $this->paginate('InvoiceLayoutCustomField', $conditions));
	}

	function view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('invoice layout custom field', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('invoiceLayoutCustomField', $this->InvoiceLayoutCustomField->read(null, $id));
	}

	function add() {
		if (!empty($this->data)) {
			$this->InvoiceLayoutCustomField->create();
			if ($this->InvoiceLayoutCustomField->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('invoice layout custom field',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('invoice layout custom field',true)));
			}
		}
		$invoiceLayouts = $this->InvoiceLayoutCustomField->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
	}

	function edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'invoice layout custom field',true)));
			$this->redirect(array('action'=>'index'));
		}
		$invoiceLayoutCustomField = $this->InvoiceLayoutCustomField->read(null, $id);
		if (!empty($this->data)) {
			if ($this->InvoiceLayoutCustomField->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('invoice layout custom field',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('invoice layout custom field',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $invoiceLayoutCustomField;
		}
		$invoiceLayouts = $this->InvoiceLayoutCustomField->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
		$this->render('add');
	}

	function delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('invoice layout custom field',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('invoiceLayoutCustomField', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('invoiceLayoutCustomFields', true);
		 } 
		$invoiceLayoutCustomFields = $this->InvoiceLayoutCustomField->find('all',array('conditions'=>array('InvoiceLayoutCustomField.id'=>$id)));
		if (empty($invoiceLayoutCustomFields)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->InvoiceLayoutCustomField->deleteAll(array('InvoiceLayoutCustomField.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('invoiceLayoutCustomFields',$invoiceLayoutCustomFields);
	}

	function owner_index() {
		$this->InvoiceLayoutCustomField->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('invoiceLayoutCustomFields', $this->paginate('InvoiceLayoutCustomField', $conditions));
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('invoice layout custom field', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('invoiceLayoutCustomField', $this->InvoiceLayoutCustomField->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->InvoiceLayoutCustomField->create();
			if ($this->InvoiceLayoutCustomField->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('invoice layout custom field',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('invoice layout custom field',true)));
			}
		}
		$invoiceLayouts = $this->InvoiceLayoutCustomField->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'invoice layout custom field',true)));
			$this->redirect(array('action'=>'index'));
		}
		$invoiceLayoutCustomField = $this->InvoiceLayoutCustomField->read(null, $id);
		if (!empty($this->data)) {
			if ($this->InvoiceLayoutCustomField->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('invoice layout custom field',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('invoice layout custom field',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $invoiceLayoutCustomField;
		}
		$invoiceLayouts = $this->InvoiceLayoutCustomField->InvoiceLayout->find('list');
		$this->set(compact('invoiceLayouts'));
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('invoice layout custom field',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('invoiceLayoutCustomField', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('invoiceLayoutCustomFields', true);
		 } 
		$invoiceLayoutCustomFields = $this->InvoiceLayoutCustomField->find('all',array('conditions'=>array('InvoiceLayoutCustomField.id'=>$id)));
		if (empty($invoiceLayoutCustomFields)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->InvoiceLayoutCustomField->deleteAll(array('InvoiceLayoutCustomField.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('invoiceLayoutCustomFields',$invoiceLayoutCustomFields);
	}
}
?>