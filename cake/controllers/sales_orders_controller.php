<?php

use ActivityLog\Utils\EntityKeyTypesUtil;
use App\Modules\LocalEntity\ActivityLog\EntityActivityLogRequestsCreator;
use App\Services\CommonActivityLogService;
use Izam\Daftra\ActivityLog\Requests\ActivityLogRequest;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use Izam\Daftra\Common\Utils\InvoicePaymentStatusUtil;
use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\ProductionPlanSourceTypeUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;
use Izam\Daftra\Invoice\Services\ConvertSalesOrderToInvoiceService;
use Izam\Entity\Helper\EntityActivityLog;

class SalesOrdersController extends InvoicesController
{
    var $uses = ['Invoice'];
    public function owner_convert_to_invoice($id)
    {

        if (!$this->isSalesOrderSettingsEnabled()) {
            $settingsLink = sprintf('<a href="/owner/invoices/settings">%s</a>', __('the Sales Settings', true));
            if (IS_REST) $this->cakeError('error404', array('message' => sprintf(__("You disabled the sales order module from %s", true), $settingsLink)));
            $this->izamFlashMessage(sprintf(__("You disabled the sales order module from %s", true), $settingsLink), 'Errormessage');
            $this->redirectToIndex();
        }

        $salesOrder = $this->Invoice->getInvoice($id);
        if (!$salesOrder) {
            if (IS_REST) $this->cakeError('error404', array('message' => __t('Sales Order not found')));
            $this->izamFlashMessage(__t('Sales Order not found'), 'Errormessage');
            $this->redirectToIndex();
        }

        if (!$this->canConvertSalesOrderToInvoice($salesOrder)) {
            if (IS_REST) $this->cakeError('error404', array('message' => __t("You don’t have permission to create Invoice to the selected client")));
            $this->izamFlashMessage(__t("You don’t have permission to create Invoice to the selected client"), 'Errormessage');
            $this->redirectToView($id);
        }

        if (!$this->isSalesOrderAlreadyInvoiced($salesOrder)) {
            if (IS_REST) $this->cakeError('error404', array('message' => __t("Sales Order is Already Invoiced")));
            $this->izamFlashMessage(__t("Sales Order is Already Invoiced"), 'Errormessage');
            $this->redirectToIndex();
        }

        if (!IS_REST) {
            $this->owner_add();
        }
        if ($salesOrder['Invoice']['extra_details']) {
            $this->data['Invoice']['extra_details'] = $salesOrder['Invoice']['extra_details'];
        }

        if ($salesOrder['Invoice']['work_order_id']){
            $this->data['Invoice']['work_order_id'] = $salesOrder['Invoice']['work_order_id'];
        }
        if ($salesOrder['Invoice']['sales_person_id']){
            $this->data['Invoice']['sales_person_id'] = $salesOrder['Invoice']['sales_person_id'];
        }
        $this->data['Invoice']['shipping_amount'] = $salesOrder['Invoice']['shipping_amount'];
        $this->data['Invoice']['shipping_option_id'] = $salesOrder['Invoice']['shipping_option_id'];
        $this->data['Invoice']['shipping_tax_id'] = $salesOrder['Invoice']['shipping_tax_id'];

        $this->data['Invoice']['source_id'] = $id;
        $this->data['Invoice']['source_type'] = InvoiceSourceTypesUtil::SALES_ORDER;

        $this->data['Invoice']['client_id'] = $salesOrder['Invoice']['client_id'];
        $this->data['Invoice']['currency_code'] = $salesOrder['Invoice']['currency_code'];
        $this->data['Invoice']['adjustment_label'] = $salesOrder['Invoice']['adjustment_label'];
        $this->data['Invoice']['adjustment_value'] = $salesOrder['Invoice']['adjustment_value'];
        /**
         * Select the current client in case it was already assigned to the invoice 
         * I added this for compatibility and not to mess with the original owner_add in invoice which id extremely important 
         */
        if (!empty($this->data['Invoice']['client_id'])) {
            $this->Invoice->Client->disableBranchFind();
            $this->set('clients', $this->Invoice->Client->find('list', ['conditions' =>['Client.id' => $this->data['Invoice']['client_id']]]));
            $this->Invoice->Client->enableBranchFind();
        }
        if ($salesOrder['Invoice']['order_source_id']){
            $this->data['Invoice']['order_source_id'] = $salesOrder['Invoice']['order_source_id'];
        }

        if ($salesOrder['Invoice']['summary_discount']){
            $this->data['Invoice']['discount'] = $salesOrder['Invoice']['discount'];
            $this->data['Invoice']['summary_discount'] = $salesOrder['Invoice']['summary_discount'];
            $this->data['Invoice']['discount_amount'] = $salesOrder['Invoice']['discount_amount'];
            $this->data['Invoice']['item_discount_amount'] = $salesOrder['Invoice']['item_discount_amount'];
        }

        /** @var $convertSalesOrderToInvoiceService ConvertSalesOrderToInvoiceService */
        $convertSalesOrderToInvoiceService = resolve(ConvertSalesOrderToInvoiceService::class);

        if (!IS_REST)
            $items = $convertSalesOrderToInvoiceService->prepareInvoiceItems($salesOrder);
        else
            $items = $convertSalesOrderToInvoiceService->prepareInvoiceItems($this->data);

        if ($items == []) {
            $this->Invoice->id = $id;
            $this->Invoice->saveField('payment_status', InvoicePaymentStatusUtil::SALES_ORDER_STATUS_INVOICED);
            if (IS_REST) $this->cakeError('error404', array('message' => __t("Sales Order is Already Invoiced")));
            $this->izamFlashMessage(__t("Sales Order is Already Invoiced"), 'Errormessage');
            $this->redirectToView($id);
        }

        $this->data['InvoiceItem'] = $items;

        $copy_notes = settings::getValue(InvoicesPlugin, SettingsUtil::ENABLE_COPY_NOTES);
        
        if($copy_notes) {
            $this->data['Invoice']['html_notes'] = $salesOrder['Invoice']['html_notes'];
            $this->set('clone_id',$id);
            $this->set('is_clone',true);
        }
        if (IS_REST) {
            return $this->owner_add();
        }
        $this->render('/invoices/owner_add');
    }

    /* copied from sales order controller */
    private function isSalesOrderSettingsEnabled(): bool
    {
        $enableSalesOrder = settings::getValue(InvoicesPlugin, SettingsUtil::ENABLE_SALES_ORDER);
        if (!$enableSalesOrder) {
            return false;
        }

        return true;
    }

    private function canConvertSalesOrderToInvoice(array $salesOrder): bool
    {
        if (check_permission([Invoices_Add_New_Invoices, INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS])) {
            return true;
        }

        $owner = getAuthOwner();

        if ($owner['staff_id'] == 0) {
            return false;
        }

        $clientId = $salesOrder['Client']['id'];

        $clients = $this->Client->find('list', [
            'conditions' => ['staff_id' => $owner['staff_id']]
        ]);

        return isset($clients[$clientId]);
    }

    private function redirectToIndex()
    {
        return $this->redirect('/v2/owner/entity/sales_order/list');
    }

    private function redirectToView($id)
    {
        /** @todo change this if owner_view_sales_order is moved to here */
        return $this->redirect(['controller' => 'invoices', 'action' => 'owner_view_sales_order', $id]);
    }

    private function isSalesOrderAlreadyInvoiced($salesOrder): bool
    {
        return $salesOrder['Invoice']['payment_status'] != InvoicePaymentStatusUtil::SALES_ORDER_STATUS_INVOICED;
    }

    public function owner_delete($id = null)
    {
        if (!$this->isSalesOrderSettingsEnabled()) {
            $settingsLink = sprintf('<a href="/owner/invoices/settings">%s</a>', __('the Sales Settings', true));
            $this->izamFlashMessage(sprintf(__("You disabled the sales order module from %s", true), $settingsLink), 'Errormessage');
            $this->redirectToIndex();
        }

        $this->set('module_name', 'Sales Order');

        if (!$id && empty($_POST)) {
            if (IS_REST) {
                $this->cakeError("error400", ["message" => sprintf(__('Invalid %s', true), 'Sales Order')]);
            }

            $this->izamFlashMessage(sprintf(__('Invalid %s', true), 'Sales Order'), 'Errormessage');
            $this->redirectToIndex();
        }

        if (!$id) {
            $id = $_POST['id'];
        }

        $salesOrder = $this->Invoice->getInvoice($id);

        if (!$salesOrder) {
            if (IS_REST) {
                $this->cakeError("error400", ["message" => sprintf(__('Invalid %s', true), __t('Sales Order'))]);
            }

            $this->izamFlashMessage(sprintf(__('Invalid %s', true), __t('Sales Order')), 'Errormessage');
            $this->redirectToIndex();
        }

        $staffId = getAuthOwner('staff_id');
        $is_admin = getAuthOwner('staff_id') == 0 ? true : (bool)getAuthStaff()['Role']['is_super_admin'];
        if ( !$is_admin) {
            if (
                !check_permission(SALES_ORDER_EDIT_DELETE_ALL_SALES_ORDER) &&
                (
                    !check_permission(SALES_ORDER_EDIT_DELETE_HIS_OWN_SALES_ORDER) ||
                    !($staffId == $salesOrder['Invoice']['staff_id'] || $staffId == $salesOrder['Invoice']['sales_person_id'])
                )
            ) {
                if (IS_REST) {
                    $this->cakeError("error400", ["message" => sprintf(__t("You are not allowed to delete Sales Order #%s"), $salesOrder['Invoice']['no'])]);
                }

                $this->izamFlashMessage(__t("You are not allowed to delete sales order"), 'Errormessage');
                $this->redirectToView($id);
            }
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['id'])) {
            if ($_POST['submit_btn'] == 'no') {
                $this->redirectToView($id);
            }
        }

        if (in_array($salesOrder['Invoice']['payment_status'], [
            InvoicePaymentStatusUtil::SALES_ORDER_STATUS_INVOICED,
            InvoicePaymentStatusUtil::SALES_ORDER_STATUS_PARTIALLY_INVOICED,
        ])) {
            if (IS_REST) {
                $paymentStatusLabel = InvoicePaymentStatusUtil::getStatus($salesOrder['Invoice']['payment_status']);

                $this->cakeError("error400", [
                    "message" => sprintf(__t("Sales Order #%s failed to be deleted as it has already been %s"), $salesOrder['Invoice']['no'], $paymentStatusLabel)
                ]);
            }

            $this->izamFlashMessage(__t("You cannot delete the sales order with status partially Invoiced or Invoiced"), 'Errormessage');
            $this->redirectToView($id);
        }

        if(ifPluginActive(PluginUtil::MANUFACTURING_PLUGIN)) {
            $productionPlan = $this->Invoice->hasProductionPlan($id, ProductionPlanSourceTypeUtil::SALES_ORDER); 
            if ($productionPlan) {
                $message = sprintf(__("You cannot delete/update the sales order that already selected in the production plan #%s", true),
            '<a href="/v2/owner/entity/production_plan/'.$productionPlan['ProductionPlan']['id'].'/show" >'.$productionPlan['ProductionPlan']['code'].'</a>');
                $this->izamFlashMessage($message, 'Errormessage');
                if (IS_REST) $this->cakeError('error400', array('message' => $message));
                $this->redirectToView($id);
            }
        }


        if (IS_REST) {
            $_POST['submit_btn'] = true;
            $_POST['id'] = $id;
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['id']) && $_POST['submit_btn'] == 'yes') {

            if ($this->Invoice->deleteAll(['Invoice.id' => $id])) {
                $this->addDeleteActivityLog($salesOrder);

                if ($salesOrder['Invoice']['estimate_id']) {
                    $this->updateEstimateStatus($salesOrder['Invoice']['estimate_id'], ESTIMATE_STATUS_ACCEPTED);
                }

                if (IS_REST) {
                    $this->set("message", sprintf(__('Sales Order #%s has been deleted successfully', true), $salesOrder['Invoice']['no']));
                    $this->render("success");
                    return;
                }

                $this->izamFlashMessage(sprintf(__('%s has been deleted', true), __t('Sales Order')));
                $this->redirectToIndex();
            }
        }

        $this->set('salesOrder', $salesOrder);
        $this->set('title_for_layout', sprintf(__t("Delete %s"), __t("Sales Order")));

        $this->crumbs = [
            ['title' => __t('Sales Orders'), 'url' => ['action' => 'index']],
            ['title' => $this->pageTitle, 'url' => '/v2/owner/entity/sales_order/list'],
        ];

        $this->render('/invoices/owner_delete_sales_order');
    }

    /** could be moved to event */
    private function updateEstimateStatus($estimateId, $status): void
    {
        $this->Invoice->id = $estimateId;
        $this->Invoice->saveField('payment_status', $status);
    }

    private function addDeleteActivityLog($salesOrder): void
    {
        $request = new ActivityLogRequest(
            $salesOrder['Invoice']['id'],
            \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::SALES_ORDER,
            ActionLineMainOperationTypesUtil::DELETE_ACTION,
            '#' . $salesOrder['Invoice']['id'],
            null,
            [],
            [],
        );

        (new \App\Services\ActivityLogService())->addActivity($request);
    }
}
