<?php

use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\PermissionUtil;

class InsuranceAgentsController extends AppController {

    var $name = 'InsuranceAgents';

    /**
     * @var InsuranceAgent
     */
    var $InsuranceAgent;
    var $helpers = array('Html', 'Form');

    function owner_index() {
        if(!check_permission([PermissionUtil::MANAGE_INSURANCE_AGENTS])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        $this->set('title_for_layout', __t('Insurance Agents'));
        $this->InsuranceAgent->recursive = 1;
        $conditions = $this->_filter_params();
        $this->set('insuranceAgents', $this->paginate('InsuranceAgent', $conditions));
    }

    function owner_view($id = null) {
        if(!check_permission([PermissionUtil::MANAGE_INSURANCE_AGENTS])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!$id) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('Insurance Agent', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $this->InsuranceAgent->bindAttachmentRelation('insurance_agent');
        $this->loadModel('Invoice');
        $insuranceAgentInvoicesCount = $this->Invoice->find('count', ['conditions' => ['Invoice.type' => [Invoice::INSURANCE_INVOICE, Invoice::INSURANCE_REFUND], 'Invoice.client_id' => $id]]);
        $insuranceAgent = $this->InsuranceAgent->read(null, $id);
        $this->set('insuranceAgent', $insuranceAgent);
        $this->loadModel('InsuranceAgentPricingGroup');
        $this->InsuranceAgentPricingGroup->recursive = -1;
        $agentClasses = $insuranceAgent['InsuranceAgentClass'];
        foreach ($agentClasses as &$agentClass) {
            $priceGroups = $this->InsuranceAgentPricingGroup->find('all', ['conditions' => ['InsuranceAgentPricingGroup.insurance_agent_class_id' => $agentClass['id']]]);
            $agentClass['pricingGroups'] = [];
            foreach ($priceGroups as &$priceGroup) {
                $priceGroup['InsuranceAgentPricingGroup']['categories'] = $this->InsuranceAgent->query('SELECT GROUP_CONCAT(C.name) as categories FROM categories C 
                                                                        LEFT JOIN insurance_agent_pricing_categories PC ON C.id = PC.product_category_id
                                                                        LEFT JOIN insurance_agent_pricing_groups PG ON PG.id = PC.insurance_agent_pricing_group_id
                                                                        WHERE PG.id = ' . $priceGroup['InsuranceAgentPricingGroup']['id'])[0][0]['categories'];
                $agentClass['pricingGroups'][] = $priceGroup['InsuranceAgentPricingGroup'];
            }
        }

        $this->set('title_for_layout',__t('View')  .' ' .__t('Insurance Agent') );
        $this->set('agentClasses', $agentClasses);
        $this->set('insuranceAgentInvoicesCount', $insuranceAgentInvoicesCount);
    }

    function owner_add() {
        if(!check_permission([PermissionUtil::MANAGE_INSURANCE_AGENTS])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->set('file_settings',$this->InsuranceAgent->getFileSettings());//php8 fix
        if (!empty($this->data)) {
            $this->InsuranceAgent->create();
            if (empty($this->data['InsuranceAgent']['name'])) {
                return $this->flashMessage(sprintf(__('The %s could not be saved. The agent name can not be empty', true), __('Insurance Agent', true)));
            }
            if ($this->InsuranceAgent->find('count', ['conditions' => ['InsuranceAgent.name' => $this->data['InsuranceAgent']['name']]])) {
                return $this->flashMessage(sprintf(__('The %s could not be saved. The agent name must be unique', true), __('Insurance Agent', true)));
            }
            if ($this->InsuranceAgent->save($this->data)) {

                $attachments = $this->data['InsuranceAgent']['attachments'];
                if(!empty($attachments)){
                    $this->attachS3imageToInsurnace($attachments,$this->InsuranceAgent->id);
                }

                $this->flashMessage(sprintf (__('The %s has been saved', true), __('Insurance Agent', true)), 'Sucmessage');
                if (!empty($_POST['next_action']) && $_POST['next_action'] == 'add_new') {
                    return $this->redirect(['controller' => 'insurance_agent_classes', 'action' => 'add', $this->InsuranceAgent->id]);
                }
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Insurance Agent', true)));
            }
            $this->redirect(array('action'=>'index'));
        }
        $this->set('title_for_layout',__t('Add')  .' ' .__t('Insurance Agent') );

    }

    function owner_edit($id = null) {
        if(!check_permission([PermissionUtil::MANAGE_INSURANCE_AGENTS])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', true), __('Insurance Agent', true)));
            $this->redirect(array('action'=>'index'));
        }
        $this->InsuranceAgent->bindAttachmentRelation('insurance_agent');
        $insuranceAgent = $this->InsuranceAgent->read(null, $id);
        $this->set('file_settings',$this->InsuranceAgent->getFileSettings()); //php8 fix
        if (!empty($this->data)) {
            if ($this->InsuranceAgent->find('count', ['conditions' => ['InsuranceAgent.name' => $this->data['InsuranceAgent']['name'], 'InsuranceAgent.id != '.$id]])) {
                $this->flashMessage(sprintf(__('The %s could not be saved. The agent name must be unique', true), __('Insurance Agent', true)));
            } else {
                if ($this->InsuranceAgent->save($this->data)) {
                    
                    $attachments = $this->data['InsuranceAgent']['attachments'];
                    $this->attachS3imageToInsurnace($attachments,$this->InsuranceAgent->id);
                    // remove normal attachment .
                    if(!empty($attachments)){
                        $this->InsuranceAgent->saveField('image', '');
                    }

                    $this->flashMessage(sprintf(__('The %s  has been saved', true), __('Insurance Agent', true)), 'Sucmessage');
                    $this->redirect(['action' => 'index']);
                } else {
                    $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Insurance Agent', true)));
                }
            }
        }else {
            $this->data = $insuranceAgent;
        }
       
        $this->set('title_for_layout',__t('Edit')  .' ' .__t('Insurance Agent') );

        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        if(!check_permission([PermissionUtil::MANAGE_INSURANCE_AGENTS])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('Insurance Agent', true)));
            $this->redirect(array('action'=>'index'));
        }
        $module_name= __('Insurance Agent', true);
        $insuranceAgent = $this->InsuranceAgent->read(null, $id);
        if (empty($insuranceAgent)){
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $this->loadModel('ClientInsuranceClass');
            $insuranceAgentClasses = [];
            foreach ($insuranceAgent['InsuranceAgentClass'] as $insuranceAgentClass) {
                $insuranceAgentClasses[] = $insuranceAgentClass['id'];
            }
            $clientsCount = $this->ClientInsuranceClass->find('count',['conditions' => ['ClientInsuranceClass.insurance_agent_class_id' => $insuranceAgentClasses]]);
            if ($clientsCount > 0) {
                $this->flashMessage(__('there is one or more class assigned to clients', true));
                $this->redirect(array('controller' => 'insurance_agents', 'action' => 'index'));
            }
            if ($_POST['submit_btn'] == 'yes') {
                if($this->InsuranceAgent->deleteAll(array('InsuranceAgent.id'=>$_POST['ids']))) {
                    $this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
                    $this->redirect(array('action'=>'index'));
                }
            }
            else{
                $this->redirect(array('action'=>'index'));
            }
        }
        $this->set('insuranceAgent',$insuranceAgent);
    }

    private function attachS3imageToInsurnace($attachments, $entity_id)
    {
        $imagesIds = explode(',', $attachments);
        if (!empty($imagesIds)) {
            return  izam_resolve(AttachmentsService::class)->save('insurance_agent', $entity_id, $imagesIds);
        }
    }
}
?>
