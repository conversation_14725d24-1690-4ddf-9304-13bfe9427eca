<?php

class TimeActivitiesController extends AppController {

    var $name = 'TimeActivities';

    function owner_index($no_layout = null) {
		$this->layout= "box" ;
		
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Add_New_Time_Activity)&&!check_permission(Edit_Delete_Time_Activity)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->modelName = "TimeActivity";
        $this->TimeActivity->recursive = 0;
        $conditions = $this->_filter_params();
        $this->paginate['TimeActivity'] = array('conditions' => $conditions,
            'order' => array('TimeActivity.name asc'),
        );
        $this->set('timeactivities', $this->paginate());
        // $this->set('title_for_layout',  __('Set Taxes', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';

        $this->set('content', $this->get_snippet('taxes'));
    }

    function owner_add() {
		$this->layout= "box" ;
        if (!check_permission(Add_New_Time_Activity)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        if (!empty($this->data)) {
            $this->TimeActivity->create();
            $this->data['TimeActivity']['site_id'] = getAuthOwner('id');
            $this->data['TimeActivity']['staff_id'] = getAuthOwner('staff_id');
            $this->data['TimeActivity']['date'] = date("Y-m-d");
            if ($this->TimeActivity->save($this->data)) {
				$this->add_actionline(ACTION_ADD_ACTIVITY, array('primary_id' => $this->TimeActivity->id,'param4'=>$this->data['TimeActivity']['name'],'param2'=>$this->data['TimeActivity']['active']));                
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Activity', true)), 'Sucmessage');
                $redirect = array('action' => 'index');
                if (!empty($this->params['url']['box'])) {
                    $redirect = array('action' => 'add', '?' => array('box' => 1, 'success' => 1));
                }
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Activity', true)));
            }
        }
		else
		{
			$this->data['TimeActivity']['active'] = true;
		}
		
		

        $this->set('title_for_layout',  __('Add Activity', true));
    }

    function owner_edit($id = null) {
		$this->layout= "box" ;
        if (!check_permission(Edit_Delete_Time_Activity)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');
        $project = $this->TimeActivity->find(array('TimeActivity.id' => $id));
        if (!$project) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Activity', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }



        if (!empty($this->data)) {
            $this->data['TimeActivity']['id'] = $id;
            $this->data['TimeActivity']['site_id'] = $site_id;


            if ($this->TimeActivity->save($this->data)) {
				$this->add_actionline(ACTION_EDIT_ACTIVITY, array('primary_id' => $this->data['TimeActivity']['id'],'param4'=>$this->data['TimeActivity']['name'],'param2'=>$this->data['TimeActivity']['active']));                
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Activity', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Activity', true)));
            }
        } else {
            $this->data = $project;
        }
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
		$this->layout= "box" ;
        if (!check_permission(Edit_Delete_Time_Activity)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('Activity', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('TimeActivity', true);
        if (is_countable($id) && count($id) > 1) { //php8 fix
            $module_name = __('TimeActivity', true);
        }
        $conditions = array();
        $conditions['TimeActivity.id'] = $id;

        $timeactivities = $this->TimeActivity->find('all', array('conditions' => $conditions));
        if (empty($timeactivities)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->TimeActivity->deleteAll(array('TimeActivity.id' => $_POST['ids']))) {
				foreach( $timeactivities as $act)
					$this->add_actionline(ACTION_DELETE_ACTIVITY, array('primary_id' => $act['TimeActivity']['id'],'param4'=>$act['TimeActivity']['name']));                
				
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete  Activity', true));

        $this->set('TimeActivitys', $timeactivities);
        $this->set('module_name', $module_name);
    }

}

?>