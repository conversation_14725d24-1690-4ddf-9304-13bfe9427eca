<?php

use App\Utils\TrackStockUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;

App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
App::import('Vendor', 'OIBarcode', array('file' => 'OIBarcode/OIBarcode.php'));

class PrintableTemplatesController extends AppController {

    var $name = 'PrintableTemplates';

    /**
     * @var PrintableTemplate
     */
    var $PrintableTemplate;
    var $helpers = array('Html', 'Form');
    var $components = array('Email', 'SysEmails');

    /**
     * The templates index categorized to the templates type
     */
    function owner_index() {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $replaced_keys = $this->getCurrentActiveTypes();
        $this->set( 'AllTypes', $replaced_keys );
        if ( isset( $_GET['type'] ) )
        {
            if( ! in_array($_GET['type'], array_keys( $this->PrintableTemplate->getTypesList() ) ) )
            {
                $this->flashMessage( __('Invalid type', true) );
                $this->redirect(array('action'=>'index'));
            }


            //remove override
            $default_layout_templates = $this->PrintableTemplate->getPrintableTemplatesForDashboard($_GET['type']);
            $active = __("Active",true);
            $notActive = __("Available (Not Active)",true);

            $AllTemplates = [$active=>[],$notActive=>[]];
            foreach($default_layout_templates as $template)
            {
                if( $template['PrintableTemplate']['for_galary_only'] == 0 )
                {
                    $AllTemplates[$active][] = $template;
                    $AllTemplates[$active]['class'] = 'fa fa-check text-success';
                }
                else
                {
                    $AllTemplates[$notActive][] = $template;
                    $AllTemplates[$notActive]['class'] = 'flaticon-home text-primary';
                }
            }

            if( empty( $AllTemplates[ $active ] ) )
            {
                unset($AllTemplates[ $active ]);
            }

            if( empty( $AllTemplates[ $notActive ] ) )
            {
                unset($AllTemplates[ $notActive ]);
            }

//            dd($AllTemplates);

            $this->set( 'AllTemplates', $AllTemplates );
        }

    }

    /**
     * This method uses the id and the template_type to search the templates in portal and local
     * @param $data_id => if of the data the we will use later to grap all of the fields
     * @param $printable_template_id => template id
     * @param $type_template => the type of the template
     */
    function owner_view($data_id = null, $printable_template_id = null, $type_template = null)
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        ////////////////////////////Start of my PlayGround

        ////////////////////////////End of play ground

        if (!$data_id || !$printable_template_id || !$type_template) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('printable template', true)),true));
            $this->redirect(array('action'=>'index'));
        }

        $originalTypeTemplate = false;
        if($type_template == "product_item_barcode") {
            $originalTypeTemplate = $type_template;
            $type_template = "product";
        }

        /**
         * get printable template according to $defalt_db OR portal_db
         */
        $printableTemplate = $this->PrintableTemplate->searchFromAllTemplatesById($printable_template_id, $type_template);
        
        if($originalTypeTemplate) {
            $type_template = 'product_item_barcode';
            $printableTemplate['PrintableTemplate']['type'] = $type_template;
        }

        if($type_template === 'product' || $type_template == "product_item_barcode") {
            $printableTemplate['PrintableTemplate']['content'] = preg_replace('/(<img[^>]*barcode_image[^>]*?src=\")[^\"]*(\"[^>]*>)/','$1{%product_barcode_image_link%}$2',$printableTemplate['PrintableTemplate']['content']);
        }
        $type = $type_template;
       
        $type_data = $this->PrintableTemplate->getTypesList($type);
        //get the product data
        if(isset($type_data['is_plugin']))
        {
            $this->loadModel($type_data['is_plugin'].'.'.$type_data['model']);
        }
        else
        {
            $this->loadModel($type_data['model']);
        }
        
        if(empty($type_data['model']))
        {
            $this->flashMessage(__('Make sure you have an available type', true));
            $this->redirect( ['action'=>'index'] );
        }
        $this->{$type_data['model']}->applyBranch['onFind'] = false;

        if($type == 'bookings')
        {
            App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
            $booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
            $result = $booking->setEntityData($data_id,[]);
            $data_fields = $booking->data;
        }else{
            $data_fields = $this->{$type_data['model']}->read(null, $data_id);
        }

        $data = $data_fields;
        //replace the placeholders for the template
        $func = $type_data['replace_placeholder_func'];
        if( $type == 'expense' || $type == 'income' )
        {
            if(CurrentSiteLang() == 'ara' )
            {
                $data_placeholders = PlaceHolder::$func($data, $this->config['txt.domain'] );
            }
            else
            {
                $data_placeholders = PlaceHolder::$func($data,$this->config['txt.domain']);
            }
        }
        else
        {
            $extra_fields = json_decode($printableTemplate['PrintableTemplate']['extra_fields'],true)??[];
            $data_placeholders = PlaceHolder::$func($data,$extra_fields); //work_order_place_holder
        }
        $content_after_replace = $this->PrintableTemplate->replacePlaceHolders($printableTemplate, $data_placeholders );
        if( $content_after_replace == false )
        {
            $this->flashMessage(__('Error while rendering', true));
            $this->redirect( Router::url( $this->referer(), true ) );
        }
        $printableTemplate['PrintableTemplate']['content'] = $content_after_replace;

        //replace the placeholders for the breadcrumbs
        $type_data['bread_crumbs'] = $this->replaceBreadcrumbData($data, $type_data, $printableTemplate, 'bread_crumbs');
        $this->set(compact('printableTemplate'));
        $this->set('type_data', $type_data);
        $this->set('data_id', $data_id);
		$this->set('is_ajax', $this->RequestHandler->isAjax());
        if (low($this->params['url']['ext']) == 'pdf' || low($this->params['url']['ext']) == 'jpeg')
        {

            $this->render('owner_preview');
        }
    }

    /**
     * Will send templates to the specified email coming from the data field in the printable_template configuration
     * @param $data_id => id of the template.
     * @param $template_id => id of the template
     * @param $type => type of the template to search with
     */
    function owner_send_template($data_id , $template_id, $type){

//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $site = getAuthOwner();
        if ($site['staff_id'] == 0) {
            $staff_place_holders['{%staff-name%}'] = $staff_place_holders['{%staff-member-name%}'] = getAuthOwner('first_name') . ' ' . getAuthOwner('last_name');
            $staff_place_holders['{%staff-email%}'] = $staff_place_holders['{%staff-member-email%}'] = getAuthOwner('email');
            $staff_place_holders['{%staff-phone%}'] = $staff_place_holders['{%staff-member-phone%}'] = getAuthOwner('phone1');
            $staff_place_holders['{%staff-mobile%}'] = $staff_place_holders['{%staff-member-mobile%}'] = getAuthOwner('phone2');
            $staff_place_holders['{%staff-address1%}'] = $staff_place_holders['{%staff-member-address1%}'] = getAuthOwner('address1');
            $staff_place_holders['{%staff-address2%}'] = $staff_place_holders['{%staff-member-address2%}'] = getAuthOwner('address2');
            $staff_place_holders['{%staff-city%}'] = $staff_place_holders['{%staff-member-city%}'] = getAuthOwner('city');
            $staff_place_holders['{%staff-state%}'] = $staff_place_holders['{%staff-member-state%}'] = getAuthOwner('state');
            $staff_place_holders['{%staff-postalcode%}'] = $staff_place_holders['{%staff-member-postalcode%}'] = getAuthOwner('postal_code');
            $staff_place_holders['{%staff-country%}'] = $staff_place_holders['{%staff-member-country%}'] = getAuthOwner('country_code');
        } else {
            $staff_place_holders['{%staff-name%}'] = $staff_place_holders['{%staff-member-name%}'] = getAuthStaff('name');
            $staff_place_holders['{%staff-email%}'] = $staff_place_holders['{%staff-member-email%}'] = getAuthStaff('email_address');
            $staff_place_holders['{%staff-phone%}'] = $staff_place_holders['{%staff-member-phone%}'] = getAuthStaff('phone1');
            $staff_place_holders['{%staff-mobile%}'] = $staff_place_holders['{%staff-member-mobile%}'] = getAuthStaff('phone2');
            $staff_place_holders['{%staff-address1%}'] = $staff_place_holders['{%staff-member-address1%}'] = getAuthStaff('address1');
            $staff_place_holders['{%staff-address2%}'] = $staff_place_holders['{%staff-member-address2%}'] = getAuthStaff('address2');
            $staff_place_holders['{%staff-city%}'] = $staff_place_holders['{%staff-member-city%}'] = getAuthStaff('city');
            $staff_place_holders['{%staff-state%}'] = $staff_place_holders['{%staff-member-state%}'] = getAuthStaff('state');
            $staff_place_holders['{%staff-postalcode%}'] = $staff_place_holders['{%staff-member-postalcode%}'] = getAuthStaff('postal_code');
            $staff_place_holders['{%staff-country%}'] = $staff_place_holders['{%staff-member-country%}'] = getAuthStaff('country_code');
        }

        //validate data_id and template_id
        if( !isset( $data_id ) || !isset( $template_id ) )
        {
            $this->flashMessage(__('Please insert data_id and template_id', true));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        /**
         * first condition to select from the default => printable_template else to select from portal => layout_templates
         */
        $printableTemplate = $this->PrintableTemplate->searchFromAllTemplatesById($template_id, $type);

        $type = $printableTemplate['PrintableTemplate']['type'];
        $TypeDataList = $this->PrintableTemplate->getTypesList($type);

        if( ! in_array( $type, array_keys( $this->PrintableTemplate->getTypesList() ) ) )
        {
            $this->flashMessage( __('Please choose a valide type', true) );
            $this->redirect( $this->referer(array('action' => 'index')) );
        }

        $model_name = $TypeDataList['model'];

        $func = $TypeDataList['replace_placeholder_func'];
        
        
        $this->loadModel($model_name);
        $data_object = $this->$model_name->find(array($model_name.'.id' => $data_id));

        $placeHolders = PlaceHolder::$func($data_object, $this->config['txt.domain'] );
        //get the email to send the template to
        $model_and_field = explode('.',$TypeDataList['default_email']);
        $default_email = $data_object[$model_and_field[0]][$model_and_field[1]];

        if (!$data_object) {
            $this->flashMessage(__('Data not found', true));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        $this->loadModel('EmailLog');

        if (!empty($this->data)) {
            if (empty($this->data['PrintableTemplate']['to_email'])) {
                $this->PrintableTemplate->validationErrors['to_email'] = __('Please enter client email', true);
                $error = true;
            }

            $count_email = explode(',', $this->data['PrintableTemplate']['to_email']);
            $count_email_2 = explode(';', $this->data['PrintableTemplate']['to_email']);
            if (!empty($this->data['PrintableTemplate']['to_email']) and (count($count_email) > 5 or count($count_email_2) > 5)) {
                $this->PrintableTemplate->validationErrors['to_email'] = __('You can only send 5 emails at once', true);
                $error = true;
            }
            $count_cc_email = explode(',', $this->data['PrintableTemplate']['cc_email']);
            $count_cc_email_2 = explode(';', $this->data['PrintableTemplate']['cc_email']);
            if (!empty($this->data['PrintableTemplate']['cc_email']) and (count($count_cc_email) > 3 or count($count_cc_email_2) > 3)) {
                $this->PrintableTemplate->validationErrors['cc_email'] = __('You can only send 3 cc emails at once', true);
                $error = true;
            }

            $count_bcc_email = explode(',', $this->data['PrintableTemplate']['bcc_email']);
            $count_bcc_email_2 = explode(';', $this->data['PrintableTemplate']['bcc_email']);
            if (!empty($this->data['PrintableTemplate']['bcc_email']) and (count($count_bcc_email) > 3 or count($count_bcc_email_2) > 3)) {
                $this->PrintableTemplate->validationErrors['bcc_email'] = __('You can only send 3 bcc emails at once', true);
                $error = true;
            }

            if (empty($this->data['PrintableTemplate']['subject'])) {
                $this->PrintableTemplate->validationErrors['subject'] = __('Please enter subject', true);
                $error = true;
            }
            if (empty($this->data['PrintableTemplate']['body'])) {
                $this->PrintableTemplate->validationErrors['body'] = __('Please enter Message', true);
                $error = true;
            }
            if ($error) {
                $this->flashMessage(__('Could not send email', true));
            } else {
                //download the file and attach it to the form
                $attachement['attachments']['name'] = $printableTemplate['PrintableTemplate']['name'].".pdf";
                $attachement['attachments']['type'] = "application/pdf";
                $attachement['attachments']['tmp_name'] = $this->_createPDF($data_id, $template_id, $type);
                $attachement['attachments']['error'] = 0;
                $attachement['attachments']['size'] = filesize($attachement['attachments']['tmp_name']) ;

                $this->data['PrintableTemplate']['attachments'] = $attachement['attachments'];

                $attachments = array();
                if (file_exists($this->data['PrintableTemplate']['attachments']['tmp_name'])) {
                    move_uploaded_file($this->data['PrintableTemplate']['attachments']['tmp_name'], '/tmp/' . $this->data['PrintableTemplate']['attachments']['name']);
                    $attachments[$this->data['PrintableTemplate']['attachments']['name']] = $this->data['PrintableTemplate']['attachments']['tmp_name'];
                }

                $send_email = $this->SysEmails->sendEmail($this->data['PrintableTemplate']['to_email'], $site, $this->data, $placeHolders ,$attachments , array('data_id' => $data_object[$model_name]['id']));

                if ($send_email) {
                    //$this->add_actionline(ACTION_SEND_EMAIL, array('primary_id' => $data_object[$model_name]['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $this->data['EmailTemplate']['to_email'], 'param5' => $send_email, 'param4' => $client['Client']['client_number']));
                    $this->flashMessage(__('The Email message has been sent', true), 'Sucmessage');
                    $this->redirect(array('action' => 'view', $data_id, $printableTemplate['PrintableTemplate']['id'] , $printableTemplate['PrintableTemplate']['type']));
                } else {
                    $this->flashMessage(sprintf(__('Could not send email , %s', true), $this->SysEmails->error_message));
                }
            }
        } else {
            $this->data = $printableTemplate;
            unset($this->data["PrintableTemplate"]['id']);
        }

        //get the send template breadcrum
        $send_template_breadcrumb = ( isset($TypeDataList['send_template_bread_crumbs']) ? $TypeDataList['send_template_bread_crumbs'] : $TypeDataList['bread_crumbs'] );

        //replace the placeholders for the breadcrumbs
        $send_template_breadcrumb = $this->replaceBreadcrumbData($data_object, $TypeDataList, $printableTemplate, 'send_template_bread_crumbs');
//        foreach($send_template_breadcrumb as $index => $bread_crumb_link)
//        {
//            foreach($data_object[$model_name] as $k => $val)
//            {
//                if(isset($bread_crumb_link['link'] ))
//                {
//                    $bread_crumb_link['link'] = str_replace('$'.$k ,$val ,$bread_crumb_link['link']);
//
//                    $bread_crumb_link['link'] = str_replace('$template_id' ,$printableTemplate["PrintableTemplate"]['id'] ,$bread_crumb_link['link']);
//
//                    $bread_crumb_link['link'] = str_replace('$template_name' ,$printableTemplate["PrintableTemplate"]['name'] ,$bread_crumb_link['link']);
//
//                    $bread_crumb_link['link'] = str_replace('$template_type' ,$printableTemplate["PrintableTemplate"]['type'] ,$bread_crumb_link['link']);
//                }
//
//                if( isset($bread_crumb_link['title']) )
//                {
//                    //replace the fields
//                    $bread_crumb_link['title'] = str_replace('$'.$k ,$val ,$bread_crumb_link['title']);
//
//                    //replace the template name
//                    $bread_crumb_link['title'] = str_replace('$template_name' , $printableTemplate['PrintableTemplate']['name'] ,$bread_crumb_link['title']);
//                }
//
//                $send_template_breadcrumb[$index] = $bread_crumb_link;
//            }
//        }

        $this->set('breadcrumb',$send_template_breadcrumb);

        //set the data
        $this->set('PlaceHolders', $placeHolders);
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->set(compact('printableTemplate'));
        $this->set('data_object', $data_object);
        $this->set('model_name', $model_name);
        $this->set('default_email', $default_email);
    }
    function get_barcode_image($barcode_type, $barcode_id = null, $width = 20 , $height = 30)
    {
        $this->layout = false;
        $this->set( "image_tag", OIBarcode::getBarcodeImage( OIBarcode::getAutoBarcode($barcode_type, $barcode_id), [ 'img_only'=>true,'style'=> ['width'=>$width.'%','height'=>$height . 'px'] ] ) );
    }
    function owner_get_barcode_image($barcode_type, $barcode_id, $width = 20 , $height = 30)
    {
        $this->layout = false;
        $this->set( "image_tag", OIBarcode::getBarcodeImage( OIBarcode::getAutoBarcode($barcode_type, $barcode_id), [ 'img_only'=>true,'style'=> ['width'=>$width.'%','height'=>$height . 'px'] ] ) );
    }

    /**
     * This method will add templates and will replace aplceholders with the datafields later in the view_data_of the template
     * @param string $type => type of the template to load the placeholders of this type
     */
    function owner_add( $type = "barcode" ) {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if (!empty($this->data)) {

            $types = $this->PrintableTemplate->getTypeKeys();
            $currentActiveTypes = array_keys($this->getCurrentActiveTypes());
            $types = array_merge($types, $currentActiveTypes);

            //0 => ACTIVE, 1 => NOT ACTIVE
            if( empty( $this->data['PrintableTemplate']['for_galary_only'] ) )
            {
                $this->data['PrintableTemplate']['for_galary_only'] = 0;
            }

            //if the type is not existed do not save
            if( ! in_array( $this->data['PrintableTemplate']['type'] , $types) )
            {
                $this->flashMessage(sprintf(__('The type of %s does not exist', true), __('printable template',true)));
                $this->redirect(array('action'=>'index','?'=>['type'=>$this->data['PrintableTemplate']['type']]));
            }

            if( MustacheEngine::validate(  $this->data['PrintableTemplate']['content'] ) == false )
            {
                $this->flashMessage(__('Please fix content error and try add the template again', true));
                $this->redirect(  Router::url( $this->referer(), true ) );
            }
            $this->data['PrintableTemplate']['extra_fields'] = (!empty($this->data['PrintableTemplate']['extra_fields'])?json_encode($this->data['PrintableTemplate']['extra_fields']):NULL);
//            dd($this->data);
            $this->PrintableTemplate->create();
            if ($this->PrintableTemplate->save($this->data)) {

                //save the image url
                $tmp = $this->PrintableTemplate->read(null, $this->PrintableTemplate->id );
                $tmp['PrintableTemplate']['image_full_path'] =  $this->getImageFullPath($tmp);

                if($this->PrintableTemplate->save($tmp))
                {
                    $this->flashMessage(sprintf(__('The %s has been saved', true), __('printable template',true)), 'Sucmessage');
                    $this->redirect(array('owner'=>true,'action'=>'index','?'=>['type'=>$tmp['PrintableTemplate']['type']]));
                }

            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('printable template',true)));
            }
        }

        $types = $this->PrintableTemplate->getTypesList();

        //this method will override placeholders with the dummy data for the purposes of preview
        $placeholders =  $types[$type]['dummy_data'] + $types[$type]['placeholders'];



        /**
         * this can happen in two cases
         * CASE 1 : if the user clone from the portal to the local
         * CASE 2 : if the user edit the portal template so we will set the layout_override_id (NOT ACTUAL EDIT PORTAL)
         */
        if(isset($_GET['default_template']))
        {
            $id = $_GET['default_template'];
            $this->loadModel('PrintableLayout');
            //Check if the template is found in a local templates first
            if($this->PrintableTemplate->read(null, $id))
            {
                $printableTemplateDefault = $this->PrintableTemplate->read(null, $id);
            }
            //if it's copying from the portal or from the default
            elseif($this->PrintableLayout->read(null, $id) == true)
            {
                $printableTemplateDefault = $this->PrintableLayout->read(null, $id);
                $printableTemplateDefault = $this->PrintableTemplate->setArrayModelName($printableTemplateDefault, 'PrintableTemplate');
            }
            $this->set('printableTemplateDefault', $printableTemplateDefault );
        }

        // here we are editing the portal template (NOT ACTUAL EDIT PORTAL) check the previous comment
        if(isset($_GET['portal']))
        {
            if($_GET['portal'])
            {
                $this->set('portal', $_GET['portal']);
            }
        }
        $exception_placeholder_replacement = $this->get_exception_placeholders();
        $this->_settings($type);
        $this->set('exception_placeholder_replacement',$exception_placeholder_replacement);
        $this->set(compact('placeholders','types','type'));
    }

    /**
     * Editing templates
     * @param null $id => id of the template
     */
    function owner_edit($id = null) {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }

        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.',true), __('printable template',true)));
            $this->redirect(array('action'=>'index'));
        }

        if ( !$this->PrintableTemplate->exists($id) )
        {
            $this->flashMessage(sprintf(__('Invalid %s.', true), __('printable template',true)));
            $this->redirect(array('action'=>'index'));
        }

        $printableTemplate = $this->PrintableTemplate->read(null, $id);

        if (!empty($this->data))
        {
            if( MustacheEngine::validate(  $this->data['PrintableTemplate']['content'] ) == false )
            {
                $this->flashMessage(__('Please fix content error and try add the template again', true));
                $this->redirect( [ 'action' => 'edit', $this->data['PrintableTemplate']['id'] ] );
            }

            $this->data['PrintableTemplate']['image_full_path'] = $this->getImageFullPath($this->data);
            $this->data['PrintableTemplate']['extra_fields'] = (!empty($this->data['PrintableTemplate']['extra_fields'])?json_encode($this->data['PrintableTemplate']['extra_fields']):NULL);
//            die(debug($this->data['PrintableTemplate']['content']));
            if ($this->PrintableTemplate->save($this->data)) {
                $this->flashMessage(sprintf (__('The %s  has been saved', true), __('printable template',true)), 'Sucmessage');
                $this->redirect(array('action'=>'index','?'=>['type'=>$this->data['PrintableTemplate']['type']]));
            } else {
                $this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('printable template',true)));
            }
        }

        $this->data = $printableTemplate;
        $this->data['PrintableTemplate']['extra_fields'] = (!empty($this->data['PrintableTemplate']['extra_fields'])?json_decode($this->data['PrintableTemplate']['extra_fields'],true):NULL);

        $type = $printableTemplate['PrintableTemplate']['type'];
        $types = $this->PrintableTemplate->getTypesList();
        $placeholders = $this->PrintableTemplate->getTypesList($type)['dummy_data'];
        $exception_placeholder_replacement = $this->get_exception_placeholders();
        $this->_settings($type);
        $this->set('exception_placeholder_replacement',$exception_placeholder_replacement);
        $this->set(compact('sites','placeholders','types','type'));
        $this->render('owner_add');
    }

    /**
     * Will replace the placeholders and return the content to the view
     * @param $width => width of the template
     * @param $height => height of the template
     * @param $top => padding top of the template content
     * @param $right => padding right of the template content
     * @param $down => padding down of the template content
     * @param $left => padding top of the template content
     */
    function owner_preview($width = null, $height = null, $top = null, $right = null, $down = null, $left = null)
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if (empty($_POST)) {
            $this->flashMessage(__('This page is not allowed to be accessed this way.', true)  );
            $this->redirect(['action'=> 'index']);
        }
        $content = $_POST['content'];
        $type = $_POST['type'];
        $printable = $this->PrintableTemplate->getTypesList($type)['dummy_data'];

        App::import('Vendor','MustacheEngine', array('file' => 'MustacheEngine/MustacheEngine.php'));

        $template['PrintableTemplate']['content'] = $content;
        $content = $this->PrintableTemplate->replacePlaceHolders($template, $printable);

        $this->set(compact('content','type','width','height','top','right','down','left'));
    }

    /**
     * Will preview the saved template selecting it by id
     * @param $id => id of the template
     */
    function owner_preview_saved($id = null)
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $printableTemplate = $this->PrintableTemplate->read(null, $id);

        $content =  $printableTemplate['PrintableTemplate']['content'];
        $type = $printableTemplate['PrintableTemplate']['type'];

        $printable = $this->PrintableTemplate->getTypesList($type)['dummy_data'];

        $printable['{%barcode_image%}'] = '<img src="'.$printable['{%barcode_image%}'].'">';

        foreach($printable as $key => $val)
        {
            $content = str_replace($key , $val , $content);
        }
        $this->set(compact('content','type','printableTemplate'));
    }

    /**
     * The view for displaying only the default templates (deprecated) - use owner_index()
     */
    function owner_default_templates()
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $this->loadModel('PrintableLayout');
        $default_layout_templates = $this->PrintableLayout->find('all');

        $default_temps = [];
        foreach($default_layout_templates as $template)
        {
            $default_temps[$template['PrintableLayout']['type']][] = $template;
        }

        $this->set( 'default_layouts', $default_temps );
    }
    
    
    function barcode_show($number = null )
	{
        if (is_null($number)) {
            $this->flashMessage(__('You entered unvalid barcode', true)  );
            $this->redirect(['action'=> 'index']);
        }
		$widthFactor = 1;
		$totalHeight = 30;
		
		if(isset($_GET['w'])&&!empty($_GET['w'])) 	$widthFactor=$_GET['w'];
		if(isset($_GET['h'])&&!empty($_GET['h'])) 	$totalHeight = $_GET['h'];
		OIBarcode::showInline( $number,$widthFactor,$totalHeight  );
	}

    /**
     * Printing template
     * @param $width => width of the template
     * @param $height => height of the template
     * @param $top => top padding of the template
     * @param $right => right padding of the template
     * @param $down => down padding of the template
     * @param $left => left padding of the template
     * Data in the POST
     *    content => content of the template
     *    type => type of the template
     */
    function owner_print($width, $height , $top, $right , $down, $left)
    {

        if (empty($_POST)) {
            $this->flashMessage(__('This page is not allowed to be accessed this way.', true)  );
            $this->redirect(['action'=> 'index']);
        }

        $printable['item_name'] = "Product_Name";
        $printable['barcode_number'] = "12316512446565321";
        $printable['barcode_image'] = "https://oidev.daftra.com/img/dummy_barcode.jpg";

        $content = $_POST['content'];

        $type = $_POST['type'];

        $printable = $this->PrintableTemplate->getTypesList($type)['dummy_data'];

        $template['PrintableTemplate']['content'] = $content;
        $content = $this->PrintableTemplate->replacePlaceHolders($template, $printable);


        $this->set(compact('content', 'width', 'height', 'top', 'right', 'down', 'left'));
    }

    /**
     * Will preview the saved template selecting it by id
     * @param $template_id => id of the template
     * @param $data_id => id of the data
     * @param $template_type => type of the data
     */
    function owner_print_saved($data_id ,$template_id, $template_type = null)
    {

        $originalTypeTemplate = false;
        if($template_type == "product_item_barcode") {
            $originalTypeTemplate = $template_type;
            $template_type = "product";
        }

        $printableTemplate = $this->PrintableTemplate->searchFromAllTemplatesById($template_id, $template_type);

        if($originalTypeTemplate) {
            $template_type = 'product_item_barcode';
            $printableTemplate['PrintableTemplate']['type'] = $template_type;
        }

        if($template_type === 'product' || $template_type === 'product_item_barcode') {
            $printableTemplate['PrintableTemplate']['content'] = preg_replace('/(<img[^>]*barcode_image[^>]*?src=\")[^\"]*(\"[^>]*>)/','$1{%product_barcode_image_link%}$2',$printableTemplate['PrintableTemplate']['content']);
        }
        $type = $template_type;

        $content =  $printableTemplate['PrintableTemplate']['content'];

        $type_data = $this->PrintableTemplate->getTypesList($type);

        //get the product data
        if(isset($type_data['is_plugin']))
        {
            $this->loadModel($type_data['is_plugin'].'.'.$type_data['model']);
        }
        else
        {
            $this->loadModel($type_data['model']);
        }

        if(empty($type_data['model']))
        {
            $this->flashMessage(__('Make sure you have an available type', true));
            $this->redirect( ['action'=>'index'] );
        }
        $this->{$type_data['model']}->applyBranch['onFind'] = false;
        if($type == 'bookings')
        {
            App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
            $invoiceFactory = \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
            $booking =&$invoiceFactory;
            $result = $booking->setEntityData($data_id,[]);
            $data_fields = $booking->data;
        }else{
            $data_fields = $this->{$type_data['model']}->read(null , $data_id);
        }

        $data = $data_fields;

        //replace the placeholders for the template
        $func = $type_data['replace_placeholder_func'];
        if( $type == 'expense' || $type == 'income' )
        {
            if(CurrentSiteLang() == 'ara' )
            {
                $data_placeholders = PlaceHolder::$func($data, $this->config['txt.domain'] );
            }
            else
            {
                $data_placeholders = PlaceHolder::$func($data,$this->config['txt.domain']);
            }
        }
        else
        {
            $data_placeholders = PlaceHolder::$func($data,$this->config['txt.domain']);
        }

        $content_after_replace = $this->PrintableTemplate->replacePlaceHolders($printableTemplate, $data_placeholders );
        if( $content_after_replace == false )
        {
            $this->flashMessage(__('Error while rendering', true));
            $this->redirect( Router::url( $this->referer(), true ) );
        }
        $printableTemplate['PrintableTemplate']['content'] = $content_after_replace;
//
//        dd($printableTemplate);

        if (in_array($template_type, ['expense', 'income'])) {
            $this->set('template_type', $template_type . 's');
            $this->set('data_id', $data_id);
        }
        $this->set(compact('printableTemplate'));
    }


    /**
     * Deletes a template(s)
     * @param null $id => id of the templates that will be deleted
     */
    function owner_delete($id = null) {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('printable template',true)));
            $this->redirect(array('action'=>'index'));
        }
        $module_name=  __('printable template',true);
        if(is_countable($id) && count($id) > 1){
            $module_name=  __('printable template',true);
        }
        $printableTemplates = $this->PrintableTemplate->find('all',array('conditions'=>array('PrintableTemplate.id'=>$id)));

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            //delete Thumbnail images by deleting the template
            foreach($printableTemplates as $template)
            {
                $image_path = WWW_ROOT . $template['PrintableTemplate']['image_full_path'];
                unlink($image_path);
            }
            if ($_POST['submit_btn'] == 'yes' && $this->PrintableTemplate->deleteAll(array('PrintableTemplate.id'=>$_POST['ids']))) {
                $this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
                $this->redirect(array('action'=>'index','?'=>[ 'type' => $printableTemplates[0]['PrintableTemplate']['type'] ] ));
            }
            else{
                $this->redirect(array('action'=>'index'));
            }
        }
        $this->set('printableTemplates',$printableTemplates);
    }

    /**
     * TinyMce upload image action
     * uploads the images to the user files /user_HASH/files
     * @param $type => the type that we will upload the images to that folder
     */
    function owner_upload_image($type) {

        //SITE_HASH = 53c0b50f
        if (!empty($_FILES) && $_FILES['file']['size'] > 0) {
            if ($_FILES['file']['size'] > (1024 * 1024)) {
                $this->flashMessage(__('The image file size is larger than the allowed maximum size', true));
            } else {
                $errors = '';
                $realpath='files/'.SITE_HASH .'/'.$type.'/';
                $dir_path = WWW_ROOT . DS . $realpath;
                @mkdir($dir_path);
                $file = uniqid() . '_' . $_FILES['file']['name'];
                $permitted = array('image/gif', 'image/jpeg', 'image/pjpeg', 'image/png');
                list($width, $height) = getimagesize($_FILES['file']['tmp_name']);
                $info = getimagesize($_FILES['file']['tmp_name']);
                if (!in_array($_FILES['file']['type'], $permitted)) {
                    $errors = __("Invalid file type required (jpg, png, gif)", true);
                }

                $ext = strtolower(substr($_FILES['file']['name'], strrpos($_FILES['file']['name'], '.')));
                if (!in_array($ext,array('.jpg','.gif','.jpeg','.png'))) {
                    $errors = __("Invalid file type required (jpg, png, gif)", true);
                }

                if (empty($errors)) {
                    $image = move_uploaded_file($_FILES['file']['tmp_name'], $dir_path . $file);
                    echo json_encode(array('location' => Router::url("/$realpath"."$file",true)));
                    die();
                } else {
                    header("HTTP/1.0 500 $errors");
                    //    header("HTTP/1.0 500 $errors");
                }
            }
        }else{
            header("HTTP/1.0 500 Please Select File");
        }
        $this->layout = false;
        die();

    }

    /**
     * This method wil get the template reverse(true if false, false if true) the value of for_galary_only and save the template
     * @param $id => of the template
     * @param $database => portal or default
     */
    function owner_activate_and_deactivate($id , $database)
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if( !$id || !$database )
        {
            $this->redirect( Router::url( $this->referer(), true ) );
        }

        if($database == "local")
        {
            //get the template and reverse the value of "for_galary_only"
            $template = $this->PrintableTemplate->read(null, $id);
            $template['PrintableTemplate']['for_galary_only'] = ( $template['PrintableTemplate']['for_galary_only'] + 1 ) % 2 ;
            if($this->PrintableTemplate->save($template) )
            {
                $this->flashMessage(sprintf (__('%s has been '  . ( $template['PrintableTemplate']['for_galary_only']==0 ? 'activated' : 'deactivated' ), true), "printable template"), 'Sucmessage');
                $this->redirect(array('action'=>'index','?'=>['type'=>$template['PrintableTemplate']['type']]));
            }
        }
        else if($database == "portal")
        {
            //copy the template from portal and change the value for "for_galary_only"
            $this->loadModel('PrintableLayout');
            $template = $this->PrintableLayout->read(null, $id);
            $template = $this->PrintableTemplate->setArrayModelName($template, "PrintableTemplate");

            //copy from portal to local and set the override_layout_id
            unset($template['PrintableTemplate']['id']);
            $template['PrintableTemplate']['for_galary_only'] = ( $template['PrintableTemplate']['for_galary_only'] + 1 ) % 2 ;
            $template['PrintableTemplate']['override_layout_id'] = $id ;
            $template['PrintableTemplate']['printable_layout_id'] = $id ;
            $template['PrintableTemplate']['site_id'] = getCurrentSite('id') ;
            $template['PrintableTemplate']['image_full_path'] = $this->getImageFullPath($template);

            if( $this->PrintableTemplate->save($template) )
            {
                $this->flashMessage(sprintf (__('%s has been '  . ( $template['PrintableTemplate']['for_galary_only']==0 ? 'activated' : 'deactivated' ), true), "printable template"), 'Sucmessage');
                $this->redirect(array('action'=>'index','?'=>['type'=>$template['PrintableTemplate']['type']]));
            }
            else
            {
                $this->flashMessage(__("You can't dactivate or Activate this template"), 'Sucmessage');
                $this->redirect(array('action'=>'index','?'=>['type'=>$template['PrintableTemplate']['type']]));
            }
        }
    }

    /**
     * Will make the template default and dis-default it
     * @param $id => id of the template
     * @param $database => the database it will get the template from
     */
    function owner_set_as_default($id, $database)
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if( !$id || !$database )
        {
            $this->redirect( Router::url( $this->referer(), true ) );
        }

        if($database == "local")
        {
            //get the template and reverse the value of "for_galary_only"
            $template = $this->PrintableTemplate->read(null, $id);
            $template['PrintableTemplate']['default_template'] = ($template['PrintableTemplate']['default_template'] + 1 ) % 2 ;

            $printableTemplate = $this->PrintableTemplate->getPrintableTemplatesList( $template['PrintableTemplate']['type'] );

            $this->PrintableTemplate->save($template);

            foreach($printableTemplate as $single_template)
            {
                if($single_template['PrintableTemplate']['id'] == $id )
                    continue;

                $single_template['PrintableTemplate']['default_template'] = 0;
                $this->PrintableTemplate->save($single_template);
            }

            $this->redirect(array('action'=>'index','?'=>['type'=>$template['PrintableTemplate']['type']]));
        }

    }

    /**
     * This is clean view of the template only the content of the template for the iframe purposes
     * @param $data_id => the id of the item we want to use it's placeholder
     * @param $printable_template_id => the printable templates id
     * @param $type_template => the type of the template product,invoice,expense,income,.......
     */
    function owner_view_template( $data_id, $printable_template_id , $type_template )
    {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        if ( !$data_id || !$printable_template_id || !$type_template ) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('printable template', true)),true));
            $this->redirect(array('action'=>'index'));
        }

        $originalTypeTemplate = false;
        if($type_template == "product_item_barcode") {
            $originalTypeTemplate = $type_template;
            $type_template = "product";
        }
 
        /**
         * get printable template according to $defalt_db OR portal_db
         */
        $printableTemplate = $this->PrintableTemplate->searchFromAllTemplatesById($printable_template_id, $type_template);
        
        if($originalTypeTemplate) {
            $type_template = $originalTypeTemplate;
        }

        if($type_template === 'product'|| $type_template == "product_item_barcode") {
            $printableTemplate['PrintableTemplate']['content'] = preg_replace('/(<img[^>]*barcode_image[^>]*?src=\")[^\"]*(\"[^>]*>)/','$1{%product_barcode_image_link%}$2',$printableTemplate['PrintableTemplate']['content']);
        }
        
        $type = $type_template;  
        $type_data = $this->PrintableTemplate->getTypesList($type); 
        if(!isset($type_data['model'])) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('printable template', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $this->loadModel($type_data['model']);
        $this->{$type_data['model']}->applyBranch['onFind'] = false;

        if($type == 'bookings')
        {
            App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
            $booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
            $result = $booking->setEntityData($data_id,[]);
            $data_fields = $booking->data;
        }else{
            $data_fields = $this->{$type_data['model']}->read(null , $data_id);
        }


        //replace the placeholders for the template
        $func = $type_data['replace_placeholder_func'];
        if( $type == 'expense' || $type == 'income' )
        {
            if(CurrentSiteLang() == 'ara' )
            {
                $data_placeholders = PlaceHolder::$func($data_fields, $this->config['txt.domain'] );
            }
            else
            {
                $data_placeholders = PlaceHolder::$func($data_fields,$this->config['txt.domain']);
            }
        }
        else
        {
            $extra_fields = json_decode($printableTemplate['PrintableTemplate']['extra_fields'],true);
            $data_placeholders = PlaceHolder::$func($data_fields,$extra_fields);
        }

        $content_after_replace = $this->PrintableTemplate->replacePlaceHolders($printableTemplate, $data_placeholders );
        $printableTemplate['PrintableTemplate']['content'] = $content_after_replace;

        $this->layout = false;
        $this->set('printable_template',$printableTemplate);

    }

    /**
     * This is clean view for the client of the template only the content of the template for the iframe purposes
     * @param $data_id => the id of the item we want to use it's placeholder
     * @param $printable_template_id => the printable templates id
     * @param $type_template => the type of the template product,invoice,expense,income,.......
     */
    function client_view_template( $data_id, $printable_template_id , $type_template )
    {
        if ( !$data_id || !$printable_template_id || !$type_template ) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('printable template', true)),true));
            $this->redirect(array('action'=>'index'));
        }

        /**
         * get printable template according to $defalt_db OR portal_db
         */
        $printableTemplate = $this->PrintableTemplate->searchFromAllTemplatesById($printable_template_id, $type_template);


        $type = $printableTemplate['PrintableTemplate']['type'];
        $type_data = $this->PrintableTemplate->getTypesList($type);
        debug($type_data);
        $this->loadModel($type_data['model']);
        $data_fields = $this->{$type_data['model']}->read(null , $data_id);


        //replace the placeholders for the template
        $func = $type_data['replace_placeholder_func'];
        if( $type == 'expense' || $type == 'income' )
        {
            if(CurrentSiteLang() == 'ara' )
            {
                $data_placeholders = PlaceHolder::$func($data_fields, $this->config['txt.domain'] );
            }
            else
            {
                $data_placeholders = PlaceHolder::$func($data_fields,$this->config['txt.domain']);
            }
        }
        else
        {
            $data_placeholders = PlaceHolder::$func($data_fields,$this->config['txt.domain']);
        }

        $content_after_replace = $this->PrintableTemplate->replacePlaceHolders($printableTemplate, $data_placeholders );

        $printableTemplate['PrintableTemplate']['content'] = $content_after_replace;

        $this->layout = false;
        $this->set('printable_template',$printableTemplate);

    }

    /**
     * The form to submit to owner_multi_pdfs
     * @param string $type
     */
    function owner_multiple_print($type,$order_type = null,$order_id = null){

        set_time_limit(600);
        ini_set('memory_limit','5G');
        if ( !empty($order_id) && !empty($order_type)){
            $item_ids = $this->PrintableTemplate->generateItemsFromOrder($type,$order_id,$order_type, false);
            $this->set('item_ids',$item_ids);
        }
        $type_data = $this->PrintableTemplate->getTypesList( $type );
        if(!$type){
            $this->flashMessage(__('No type selected', true));
            $this->redirect($this->referer());
        }
        $this->loadModel($type_data['model']);
        if($type=="product" and $order_type=='purchase_order' and $order_id!=null){
            
            $this->loadModel('PurchaseOrderItem');
            $purchase_order_items=$this->PurchaseOrderItem->find('all',['conditions'=>['PurchaseOrderItem.purchase_order_id'=>$order_id]]);
            $product_ids = [];
            foreach($purchase_order_items as $purchase_order_item) {
              array_push( $product_ids , $purchase_order_item['PurchaseOrderItem']['product_id']);
            } 
            $allItemObjects = $this->{$type_data['model']}->find('all',['conditions'=>['Product.id'=>$product_ids]]);
            $this->loadModel('PurchaseOrder');
            $purchase_order_items_by_id  = $mapItemsToProducts = [];
            //@Todo apply default unit type for each line in purchase order or requisition
            foreach($purchase_order_items as $item) {
                $purchase_order_items_by_id[$item['PurchaseOrderItem']['id']] = $item['PurchaseOrderItem'];
                $mapItemsToProducts[$item['PurchaseOrderItem']['product_id']][] = $item['PurchaseOrderItem']['id'];
                $mapProductsToItems[$item['PurchaseOrderItem']['id']] = $item['PurchaseOrderItem']['product_id'];
            }
        } else if($type=="product" and $order_type=='requisition' and $order_id!=null){
            $purchase_order_items_by_id = [];
            $this->loadModel('RequisitionItem');
            $requistion_items=$this->RequisitionItem->find('all',['conditions'=>['RequisitionItem.requisition_id'=>$order_id]]);
            $product_ids = [];
            foreach($requistion_items as $requisition_item) {
              array_push( $product_ids , $requisition_item['RequisitionItem']['product_id']);
            }
            $allItemObjects = $this->{$type_data['model']}->find('all',['conditions'=>['Product.id'=>$product_ids]]);
            foreach($requistion_items as $item) {    
                $purchase_order_items_by_id[$item['RequisitionItem']['id']] = $item['RequisitionItem'];
                $mapItemsToProducts[$item['RequisitionItem']['product_id']][] = $item['RequisitionItem']['id'];
                $mapProductsToItems[$item['RequisitionItem']['id']] = $item['RequisitionItem']['product_id'];
            }
        } else {
            $this->loadModel($type_data['model']);
            $allItemObjects = $this->{$type_data['model']}->find('all');
        }

        $allItems = [] ;
        foreach ( $allItemObjects as $k => $l){
            switch($type_data['model']) {
                case 'Product':
                    foreach($mapItemsToProducts[$l[$type_data['model']]['id']] as $item) {
                        $allItems[$item] = $l[$type_data['model']];
                        $barcodes = $this->{$type_data['model']}->getBarcodeListByProduct($l);
                        /** Leave this commented till I discuss it later with Ashraf */
                       // $barcodes[$purchase_order_items_by_id[$item]['unit_factor_id']] = $purchase_order_items_by_id[$item]['unit_small_name'];
                        $allItems[$item]['barcodes'] = $barcodes;
                        $tracking_data = json_decode($purchase_order_items_by_id[$item]['tracking_data'],true);
                        $allItems[$item]['tracking_data'] = is_array($tracking_data) ? array_filter($tracking_data) : [];
                        $default_unit = isset($purchase_order_items_by_id) ? $purchase_order_items_by_id[$item]['unit_small_name'] : null;
                        $allItems[$item]['default_unit'] = array_search($default_unit, $barcodes);
                    }
                    break;

            }
        }
        $printableTemplateObjects = $this->PrintableTemplate->getPrintableTemplatesList($type);
        $printableTemplates = [] ; 
        foreach( $printableTemplateObjects as $k => $l){
            $printableTemplates[$l['PrintableTemplate']['id'] ] = $l['PrintableTemplate']['name'];
        }
        $this->set('default_template',$this->PrintableTemplate->getDefaultTemplateForType($type)[0]['PrintableTemplate']['id']);
        $this->set('type' , $type );
        $this->set('type_data' , $type_data );
        $this->set('items_label', __('Items', true));
        $this->set('item_label', __('Item', true));
        $this->set('allItems' , $allItems );
        $this->set('mapItemsToProducts', $mapItemsToProducts);
        $this->set('mapProductsToItems', $mapProductsToItems);
        $this->set('printableTemplates' , $printableTemplates );
    }
    /**
     * Extract multiple pdf after selecting multiple elements from the index page of any type
     * @param $type => the type of the data to be extracted as pdf
     * @param $template_id  => the template_id that will be
     */
    function owner_multi_pdfs( $type, $template_id = false )
    {
        set_time_limit(600);
        //load the template and the data
        $ids = $_POST['ids'];
        if(empty($ids)){
        }
        $conditions = [];

        $type_data = $this->PrintableTemplate->getTypesList( $type );
        if($_POST['index_action_select_type'] === 'all') {

            $conditions = $this->getCachedPaginationConditions($type_data['model']);
            if(isset($conditions[$type_data['model'].'.id'])){
                unset($conditions[$type_data['model'].'.id']);
            }
        }else {
            if (is_array($ids)) {
                $conditions = [$type_data['model'] . '.id' => $ids];
            } elseif (is_string($ids)) {
                $conditions = [$type_data['model'] . ".id in($ids)"];
            }
        }
        $order = [];
        if(!empty($_GET['sort'])){
            $order = ['order' => sprintf('%s.%s %s',$type_data['model'],$_GET['sort'] , $_GET['direction'])];
        }
        $this->loadModel($type_data['model']);
        $counts = $_POST['counts'] ;
        $items  = $_POST['ids'] ;
        $new_data = $this->{$type_data['model']}->find('all',array_merge(['conditions'=> $conditions], $order));
        $additionalData = isset($_POST['additionalData']) ? $_POST['additionalData'] : [];
        //This is added to support unit templates prices based on requisition

        switch($type) {
            case 'product':
                $this->loadModel('UnitFactor');
                $this->loadModel('UnitTemplate');
                $factors = $this->UnitFactor->find('all' , ['recursive' => -1]);
                $unit_templates = $this->UnitTemplate->find('all' , ['recursive' => -1 ]);
                break;
        }
//        if(count($new_data) > 50) {
//                $this->flashMessage(__('You can\'t export more than 50 records', TRUE));
//            $this->redirect( Router::url( $this->referer(), true ) );
//        }
        $data = $new_data ;
        if($type == 'product') {
        $data = [];
        $productByIds = [];
        foreach ( $new_data as $k => $product){
            $productByIds[$product['Product']['id']] = $product;
        }

        foreach ( $ids as $k => $id){
            $d = $productByIds[$id];
            if( empty($counts[$k])){
                $counts[$k] = 1 ;
            }
            $defaultBarcode = $d['Product']['barcode'];
            $defaultUnitPrice = $d['Product']['unit_price'];
            for($i = 0 ; $i < $counts[$k] ; $i ++ ){
                $d['Product']['barcode']    = $defaultBarcode; 
                $d['Product']['unit_price'] = $defaultUnitPrice;
                $trackingType = $d['Product']['tracking_type'];
                switch($type) {
                    case 'product':
                        $this->loadModel('ItemBarcode');
                        $barcodeList = $this->ItemBarcode->find('all', ['conditions' => ['product_id' => $d['Product']['id']]]);
                        $factors_list = []  ;
                        foreach ( $factors as $f ) {
                            $factors_list[$f['UnitFactor']['unit_template_id']][$f['UnitFactor']['id']] = $f['UnitFactor'];
                        }
                        foreach ( $unit_templates as  $t ){
                            $factors_list[$t['UnitTemplate']['id']][0] = ['id' => 0 , 'factor_name' => $t['UnitTemplate']['main_unit_name'], 'factor' => 1 ];
                        }
                
                        foreach($barcodeList as $barcode) { 
                           $title = $factors_list[$d['Product']['unit_template_id']][$barcode['ItemBarcode']['unit_factor_id']]['factor_name'];
                           $barcodes[$barcode['ItemBarcode']['id']] = ['title' => $title,'unit_price' => $barcode['ItemBarcode']['unit_price'],'id' => $barcode['ItemBarcode']['id'], 'barcode' => $barcode['ItemBarcode']['barcode']]; 
                        }
                        //Override default unit price and barcode in case there is a unit itembarcode list

                        if(isset($additionalData) && isset($additionalData[$k]) && $barcodes[$additionalData[$k]]) {
                            $d['Product']['unit_price'] = $barcodes[$additionalData[$k]]['unit_price'];
                            $d['Product']['barcode']    = $barcodes[$additionalData[$k]]['barcode'];
                        } 
                }
                switch($trackingType){
                    case TrackStockUtil::TYPE_SERIAL:
                        $d['Product']['tracking_data'] = $_POST['tracking_data'][$k][$i] ?? '';
                        break;
                    case TrackStockUtil::TYPE_LOT:   
                        $d['Product']['tracking_data'] = $_POST['tracking_data'][$k] ?? '';
                        break;
                    case TrackStockUtil::TYPE_LOT_EXPIRY:   
                        $d['Product']['tracking_data'] = $_POST['tracking_data'][$k] ?? [];
                        break;
                    case TrackStockUtil::TYPE_EXPIRY:   
                        $d['Product']['tracking_data'] = $_POST['tracking_data'][$k] ?? [];
                        break;    
                }

                $data[] = $d;
            }
        }
    }

        if ($template_id != false) {
            $template = $this->PrintableTemplate->searchFromAllTemplatesById($template_id, $type);
        } else {
            $template = $this->PrintableTemplate->getPrintableTemplatesList($type, false, true);
            $template = $template[0];
        }
        if (empty($template)) {
            $this->izamFlashMessage(__(sprintf (__('Invalid %s', true), __('Financial Period', true)),true), 'danger');
            $this->redirect(array('action' => 'index', '?' => ['type' => $type]));
        }
        if($type == "product") {
            $template['PrintableTemplate']['content'] = preg_replace('/(<img[^>]*barcode_image[^>]*?src=\")[^\"]*(\"[^>]*>)/','$1{%product_barcode_image_link%}$2',$template['PrintableTemplate']['content']);
        } 

 
        $printableTemplates = [];
        foreach($data as $d)
        {
            $printableTemplates[] = $template;
        }

        //get the printable template
        foreach($data as $index => $one_data)
        {
            //get the placeholder
            $func = $type_data['replace_placeholder_func'];
            if( $type == 'expense' || $type == 'income' )
            {
                if(CurrentSiteLang() == 'ara' )
                {
                    $data_placeholders = PlaceHolder::$func($one_data, $this->config['txt.domain'] );
                }
                else
                {
                    $data_placeholders = PlaceHolder::$func($one_data);
                }
            }
            else
            {
                $data_placeholders = PlaceHolder::$func($one_data);
            }

            //replace the content
            $content_after_rendering = $this->PrintableTemplate->replacePlaceHolders($template, $data_placeholders ) ;
      
            $printableTemplates[$index]['PrintableTemplate']['content'] = $content_after_rendering;
        }

        $this->set(compact('printableTemplates'));
        $this->params['ext'] = 'pdf';
        $this->params['url']['ext'] = 'pdf';
        $this->render('pdf/owner_multi_pdfs', '');

    }


    /**
     * This function will generate pdf of any data given in the same template and return the url of the pdf file on the server
     * @param $data_id => the data we will use for this current template
     * @param $template_id => the template we will use for putin the data
     * @param $type => the type of the (template another factor for selection)
     * @return mixed => return the pdf file name
     */
    function _createPDF($data_id, $template_id, $type) {

        //get the printable template
        $printableTemplate = $this->PrintableTemplate->searchFromAllTemplatesById($template_id, $type);

        $type = $printableTemplate['PrintableTemplate']['type'];
        $type_data = $this->PrintableTemplate->getTypesList($type);

        //get the product data
        if(isset($type_data['is_plugin']))
        {
            $this->loadModel($type_data['is_plugin'].'.'.$type_data['model']);
        }
        else
        {
            
            $this->loadModel($type_data['model']);
        }

        if(empty($type_data['model']))
        {
            $this->redirect(array('action'=>'index'));
        }
        $data_fields = $this->{$type_data['model']}->read(null , $data_id);
       

        $data = $data_fields;

        //replace the placeholders for the template
        $func = $type_data['replace_placeholder_func'];
        if( $type == 'expense' || $type == 'income' )
        {
            if(CurrentSiteLang() == 'ara' )
            {
                $data_placeholders = PlaceHolder::$func($data, $this->config['txt.domain'] );
            }
            else
            {
                $data_placeholders = PlaceHolder::$func($data);
            }
        }
        else
        {
            $data_placeholders = PlaceHolder::$func($data);
        }

        $content_after_rendering = $this->PrintableTemplate->replacePlaceHolders($printableTemplate, $data_placeholders );

        $printableTemplate['PrintableTemplate']['content'] = $content_after_rendering;

        $view = new View($this, true);
        $view->set(compact('printableTemplate'));
        $view->set('type_data', $type_data);
        $view->set('data_id', $data_id);
        $view->set('save', true);
        $view->params['ext'] = 'pdf';
        $view->params['url']['ext'] = 'pdf';
        $view->layout = '';

        $view->render('pdf/owner_preview', '');
        return $filename = $view->statementFileName;
    }

    /**
     * Will create image from the pdf file and upload it to /webroot/printable_img folder and return the full link to this path
     * @param $data => the data to generate image from
     * @return mixed => full path of the image after generated
     */
    function getImageFullPath($data)
    {
        $type = $data['PrintableTemplate']['type'];
        $type_data = $this->PrintableTemplate->getTypesList($type);

        //get the product data
        if(isset($type_data['is_plugin']))
        {
            $this->loadModel($type_data['is_plugin'].'.'.$type_data['model']);
        }
        else
        {
            $this->loadModel($type_data['model']);
        }

        $data_placeholders = $this->get_exception_placeholders() + $type_data['dummy_data'];

        $content_after_rendering = $this->PrintableTemplate->replacePlaceHolders($data, $data_placeholders );

        $data['PrintableTemplate']['content'] = $content_after_rendering;

        $view = new View($this, true);
        $view->set('data',$data);
        $view->set('save', true);

        $view->layout = '';

        $view->render('pdf/owner_create_image', '');
        return $filename = $view->statementFileName;
    }

    /**
     * @return array => Some placeholderrs we dont want to be replaced with the translation values so we add the placeholderr and the key in this array
     * To Add Exception place holder you should
     *      - Add it to get_exception_placeholders() array with the value you want to see in the Add/Edit(MCE-Editor) view only
     * Notes :-
     *    - if you are using images in the editor then don't change any of the imae attributes or then it will be a static image not loaded dynamically
     */
    function get_exception_placeholders(){
        $exception_placeholder_replacement = [
            '{%product_barcode_image%}'=> '<img id="barcode_image" src="https://' . getCurrentSite('subdomain') . '/img/dummy_barcode.jpg" data-mce-src="https://' . getCurrentSite('subdomain') . '/img/dummy_barcode.jpg">',
            '{%site_logo%}' => ( empty( getCurrentSite('site_logo') ) ? "" :'<img src="https://' . getCurrentSite('subdomain') . getCurrentSite('site_logo_full_path').'" data-mce-src="https://' . getCurrentSite('subdomain') . getCurrentSite('site_logo_full_path') . '">' )
        ];

        return $exception_placeholder_replacement;
    }

    //TODO delete me after finish testing (this function will never be removed LOL)
    function dummy_function()
    {
        Configure::write('debug', 1);
        $this->loadModel('PrintableTemplate');
        $defaultTemplate = $this->PrintableTemplate->getDefaultTemplateForType('work_order');
    }

    /**
     * @return array => Get the list of types with translation of every type
     */
    function getCurrentActiveTypes()
    {
        $replaced_keys = ( ifPluginActive(ExpensesPlugin) ? [ 'expense' => __('Expenses',true), 'income' => __('Incomes',true) ] : [] );

        $replaced_keys +=  ( ifPluginActive(SalesPlugin) ?      [ 'invoice_payment' => __('Client Payments',true) ] : [] );
        $replaced_keys +=  ( ifPluginActive(InventoryPlugin) ?  ['purchase_order_payment' => __('Supplier Payments',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(WorkOrderPlugin) ?  ['work_order' => __('Work Orders',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(InventoryPlugin) ?  ['requisition' => __('Requisitions',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(InventoryPlugin) ?  ['stocktaking' => __('Stocktakings',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(FollowupPlugin) ?   ['appointments' => __('Appointments',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(SalesPlugin) ?      ['product' => __('Product Label',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(ClientsPlugin) ?    ['client' => __('Clients',true)] : [] );
        $replaced_keys +=  ( ifPluginActive(AccountingPlugin) ?    ['journal' => __('Journals',true)] : [] );

        if(ifPluginActive(BookingPlugin))
        {
            $replaced_keys += ['bookings' => __('Bookings',true)];
        }

        $replaced_keys += ( ifPluginActive(PrescriptionPlugin) ? ['prescription' => __('Prescriptions',true)] : [] );

        return $replaced_keys;
    }

    /**
     * Replace the variables in the breadcrumb with the data of current item
     * @param $data => item data te t will be replaced
     * @param $type_data => the breadcrumb with other key like ['model'] => model name
     * @param $printableTemplate => the template data that might be in the breadcrumb who knows d-_-b
     * @param $which_breadcrumb_key => the key of the breadcrumb the which it is (default breadcrumb OR email breadcrumb)
     * @return mixed => the breadcrumb after replacing data
     */
    function replaceBreadcrumbData($data, $type_data, $printableTemplate, $which_breadcrumb_key )
    {
        foreach($type_data[$which_breadcrumb_key] as $index => $bread_crumb_link)
        {
            foreach($data[$type_data['model']] as $k => $val)
            {
                if(isset($bread_crumb_link['link'] ))
                {
                    $bread_crumb_link['link'] = str_replace('$'.$k ,$val ,$bread_crumb_link['link']);

                    $bread_crumb_link['link'] = str_replace('$template_id' ,$printableTemplate["PrintableTemplate"]['id'] ,$bread_crumb_link['link']);

                    $bread_crumb_link['link'] = str_replace('$template_name' ,$printableTemplate["PrintableTemplate"]['name'] ,$bread_crumb_link['link']);

                    $bread_crumb_link['link'] = str_replace('$template_type' ,$printableTemplate["PrintableTemplate"]['type'] ,$bread_crumb_link['link']);
                }

                if( isset($bread_crumb_link['title']) )
                {
                    //replace the fields
                    $bread_crumb_link['title'] = str_replace('$'.$k ,$val ,$bread_crumb_link['title']);

                    //replace the template name
                    $bread_crumb_link['title'] = str_replace('$template_name' , $printableTemplate['PrintableTemplate']['name'] ,$bread_crumb_link['title']);
                }

                $type_data[$which_breadcrumb_key][$index] = $bread_crumb_link;
            }
        }

        return $type_data[$which_breadcrumb_key];
    }
    function _settings($type){
        $this->set('extra_fields' , $this->PrintableTemplate->getExtraFields($type));
    }
}
?>
