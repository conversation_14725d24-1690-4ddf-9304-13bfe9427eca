<?php

use Izam\Daftra\Common\Utils\ProductStatusUtil;

class BulkController extends AppController
{
    var $uses = array();
    const REQUISITION = 'requisition';
    public function owner_update()
    {
        if (!empty($_POST['back_url']) && !empty($_POST['submit_btn']) && $_POST['submit_btn'] != 'yes') {
            return $this->redirect($_POST['back_url']);
        }

        if (!isset($_POST['ids']) && isset($_POST['id'])) {
            $_POST['ids'] = $_POST['id'];
        }
        foreach ($_POST as $key => $value) {
            $this->set($key, $value);
        }
        $url = $this->get_url($_POST['target_url'], $_POST['params']);
        $this->set('url', $url);
        $this->set('csrf', $_POST['_token'] ?? NULL);


         /** this should be removed */
        if ($_POST['entity_name'] == self::REQUISITION) {
            $this->set_requisition_data();
        } else if ($_POST['entity_name'] == "zatka") {
            $this->set_einvoice_data();
        } else if ($_POST['entity_name'] == "eta") {
            $this->set_eta_data();
        } else if ($_POST['entity_name'] == "expense") {
            $this->set_expense_data(false);
        }  else if ($_POST['entity_name'] == "income") {
            $this->set_expense_data(true);
        }  else if ($_POST['entity_name'] == "bulk_invoice_status") {
            $this->set_bulk_invoice_status_data();
        }  else if ($_POST['entity_name'] == "product") {
            $this->set_product_data();
        }    else {
            $this->set_data();
        }

        $this->render_izam_view('bulk/bulk');
    }

    public function owner_resume_import($id)
    {
        $repo = new \Izam\Daftra\Common\Repositories\EntityAppDataRepository();
        $record = $repo->find($id);
        $data = json_decode($record->data, true);
        $phpVersion = implode('.', array_slice(explode('.', PHP_VERSION), 0, 2));
        $siteID = getCurrentSite("id");



        // site_id and branch_id are required to set the correct site connection
        $checkCommand = sprintf(
            'cron/import_bulk_using_json_file/%s/%s/%s/%s',
            $siteID,
            $data['branch_id'] ?? getCurrentBranchID(),
            $data["staff_id"] ?? 0,
            $id
        );
        exec('ps aux | grep "'.$checkCommand.'"', $output, $status);
        $oldDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $record->modified->format('Y-m-d H:i:s'), 'UTC');
        $now = \Carbon\Carbon::now()->setTimezone('UTC');

        if (count($output) <= 2 && $data['count'] != $data['finished'] && $now->diffInMinutes($oldDate) > 3) {
            $command = sprintf(
                'nohup php%s /var/www/html/%s/webroot/cron.php /cron/import_bulk_using_json_file/%s/%s/%s/%s > /dev/null 2>&1 & echo $!',
                $phpVersion,
                CAKE_DIR,
                $siteID,
                $data['branch_id'],
                $data["staff_id"],
                $id
            );
            exec($command, $output, $status);
            $logPath = "/var/www/html/".CAKE_DIR."/webroot/files/import/logs/";
            if (!is_dir($logPath)) {
                // Create directory with proper permissions (e.g., 0755)
                if (!mkdir($logPath, 0777, true)) {
                    die('Failed to create directories...');
                }
            }
//            file_put_contents("/var/www/html/".CAKE_DIR."/webroot/files/import/logs/".$siteID."-import.txt" , json_encode(["server_name" => CURRENT_SERVER_NAME, "Command" => $command,
//                'output' => $output, "status" => $status]), FILE_APPEND);


        }
        $this->redirect($this->referer());
    }


    public function owner_import_records($entity, $id = null)
    {
        if ($limitationKey = getEntityLimitationKeyIfExists($entity)) {
            $site_limit = checkSiteLimitV2(getCurrentSite('id'), constant("\\Izam\\Limitation\\Utils\\LimitationUtil::$limitationKey"));
            if (!$site_limit['status']) {
                $this->handleSiteLimit(
                    $site_limit,
                    "/owner/owners/dashboard"
                );
            }
        }

        $repo = new \Izam\Daftra\Common\Repositories\EntityAppDataRepository();
        $record = $repo->find($id);
        $data = json_decode($record->data, true);

        $this->set('url',  "/v2/api/import/records/{$entity}/{$id}");
        $this->set('entity_name', __(ucfirst($entity), true));

        $title = sprintf(__t('Importing %s'), __t(ucfirst(singularToPlural($entity))));
        $this->viewVars['title_for_layout'] = $title;
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => __t(ucfirst(singularToPlural($entity))),
                'link' => '/owner/'.singularToPlural($entity).'/index',
            ],
            ['title' => $title]
        ];

        $filter_key = ucfirst($entity) . '.owner_index';
        $back_url = $this->Session->read($filter_key);
        if (!$back_url) {
            $back_url = '/owner/'.singularToPlural($entity).'/index';
        }
        $this->set('back_url', $back_url);
        $this->set('entityName', __t(ucfirst($entity)));
        $this->set('total', $data['count']);


        // site_id and branch_id are required to set the correct site connection
        $checkCommand = sprintf(
            'cron/import_bulk_using_json_file/%s/%s/%s/%s',
            getCurrentSite("id"),
            $data['branch_id'],
            $data["staff_id"],
            $id
        );
        exec('ps aux | grep "'.$checkCommand.'"', $output, $status);

        $oldDate = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $record->modified->format('Y-m-d H:i:s'), 'UTC');
        $now = \Carbon\Carbon::now()->setTimezone('UTC');
        if (count($output) <= 2 && $data['count'] != $data['finished'] && $now->diffInMinutes($oldDate) > 3) {
            $this->set('show_resume', 1);
            $this->set('id', $id);
        }

        if (!empty($_SESSION['Message']['flash']["message"])) {
            $this->izamFlashMessage($_SESSION['Message']['flash']["message"], 'danger' );
        }

        $this->render_izam_view('bulk/import');
    }

    protected function render_izam_view($view_path)
    {
        $this->setDefaultViewData();
        $this->view='izam';
        $this->render($view_path);
    }

    private function get_url($url, $params)
    {
        foreach ($params as $key => $value) {
             $url = str_replace('__' . $key . '__', $value, $url);
        }
        return $url;
    }

    private function set_data() 
    {
        $this->viewVars['title_for_layout'] = $_POST['title'] ?? '';
        $this->set('back_url', $_POST['back_url'] ?? '');

        $this->viewVars['_PageBreadCrumbs'] = json_decode($_POST['breadcrumbs'], 1) ?? [];

        /** @todo rename to entity_name */
        $this->set('entityName', $_POST['entity_name'] ?? '');
    }

    private function set_requisition_data()
    {
        $this->loadModel('Requisition');
        $title = ($this->viewVars['params']['status'] == Requisition::STATUS_ACCEPTED) ? __t('Bulk Accept Requisitions') : __t('Bulk Reject Requisitions');
        $this->viewVars['title_for_layout'] = $title;
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => __t('Requisitions'),
                'link' => '/owner/requisitions/index',
            ],
            ['title' => $title]

        ];

        $filter_key='Requisition.owner_index'; //this key set in Requisition controller
        $back_url = $this->Session->read($filter_key);
        if (!$back_url) {
            $back_url='/owner/requisitions/index';
        }
        $this->set('back_url', $back_url);
        $this->set('entityName', __t('Requisition'));
    }

    private function set_expense_data($is_income)
    {
        $title = __t('Bulk Issue '.($is_income ? "Incomes": "Expenses"));
        $this->viewVars['title_for_layout'] = $title;
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => ($is_income ? __t("Incomes"): __t("Expenses")),
                'link' => '/owner/'.($is_income  ? 'incomes': 'expenses').'/index',
            ],
            ['title' => $title]

        ];

        $filter_key= $is_income ? 'Income.owner_index' : 'Expense.owner_index'; //this key set in Requisition controller
        $back_url = $this->Session->read($filter_key);
        if (!$back_url) {
            $back_url= '/owner/'.($is_income  ? 'incomes': 'expenses').'/index';
        }
        $this->set('back_url', $back_url);
        $this->set('entityName', __t($is_income  ? 'Incomes': 'Expenses'));
    }


    private function set_einvoice_data()
    {
        $title = __t('Bulk Upload Invoices');
        $this->viewVars['title_for_layout'] = $title;
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => __t('Invoices'),
                'link' => '/owner/invoices/index',
            ],
            ['title' => $title]

        ];

        $filter_key='Invoice.owner_index'; //this key set in Requisition controller
        $back_url = $this->Session->read($filter_key);
        if (!$back_url) {
            $back_url='/owner/invoices/index';
        }
        $this->set('back_url', $back_url);
        $this->set('entityName', __t('Invoice'));
    }

    private function set_eta_data()
    {
        $title = __t('Bulk Upload Invoices to ETA');
        $this->viewVars['title_for_layout'] = $title;
        $url = 'index';
        if ($_POST['type'] == "refund") {
            $url = "refund";
        } else if ($_POST['type'] == "creditnote") {
            $url = "creditnotes";
        }
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => __t('Invoices'),
                'link' => '/owner/invoices/'.$url,
            ],
            ['title' => $title]

        ];

        $filter_key='Invoice.owner_index'; //this key set in Requisition controller
        $back_url = $this->Session->read($filter_key);
        if (!$back_url) {
            $back_url='/owner/invoices/index';
        }
        $this->set('back_url', $back_url);
        $this->set('entityName', __t('Invoice'));
    }


    private function set_bulk_invoice_status_data()
    {
        $title = __t('Bulk Update Invoices Follow Up Status');
        $this->viewVars['title_for_layout'] = $title;
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => __t('Invoices'),
                'link' => '/owner/invoices/index',
            ],
            ['title' => $title]

        ];
        $back_url='/owner/invoices/index';
        $this->set('back_url', $back_url);
        $this->set('entityName', __t('Invoice'));        
    }

    private function set_product_data()
    {
        $title = match((int) $this->viewVars['params']['status']){
            ProductStatusUtil::STATUS_ACTIVE => __t('Bulk Activate Products'),
            ProductStatusUtil::STATUS_INACTIVE => __t('Bulk Deactivate Products'),
            ProductStatusUtil::STATUS_SUSPENDED => __t('Bulk Suspend Products'),
        };
        $this->viewVars['title_for_layout'] = $title;
        $this->viewVars['_PageBreadCrumbs'] = [
            [
                'title' => __t('Products'),
                'link' => '/owner/products/index',
            ],
            ['title' => $title]

        ];

        $filter_key='Product.owner_index'; //this key set in Product controller
        $back_url = $this->Session->read($filter_key);
        if (!$back_url) {
            $back_url='/owner/products/index';
        }
        $this->set('back_url', $back_url);
        $this->set('entityName', __t('Product'));
    }
}
