<?php

use App\Services\FinancialYear\ShowService;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Entity\Components\ListingPageHeader\EntityComponents\FinancialYearComponent;
use Izam\Entity\Service\CreateFilterFormService;
use Izam\View\Form\Tab\Services\CreateTabsServices;

class FinancialYearsController extends AppController {

	var $name = 'FinancialYears';
	/**
	 * @var FinancialYear
	 */
	var $FinancialYear;
	var $helpers = array('Html', 'Form');

	function owner_index()
	{
		$conditions = [];

		if (!check_permission(VIEW_CLOSED_PERIODS)) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$financialYearForThisBranch = $this->FinancialYear->find('first');
		if(!$financialYearForThisBranch){
			$this->_filter_params();
			$this->redirect(array('action'=>'add'));
		}


		$accessibleBranches = [];
		$bindBranch  = [];
		if (ifPluginActive(BranchesPlugin)) {
			$accessibleBranches = getStaffBranches();
			$this->set('branches', $accessibleBranches);
			$conditions = ["FinancialYear.branch_id" =>  array_keys($accessibleBranches)];
			$bindBranch = ['belongsTo' => ['Branch' => ['className' => 'Branch', 'foreignKey' => 'branch_id']]];
		}

		if(count($accessibleBranches) > 1 ){
			$this->openedFinancialYearWithSameEndDate($accessibleBranches);
		}


		$this->FinancialYear->recursive = 3;

		$this->FinancialYear->bindModel(array_merge($bindBranch,[
			'hasOne'    => ['Journal' => ['className' => 'Journal', 'foreignKey' => 'entity_id', 'conditions' => ['entity_type' => 'financial_year']]],
		]), false);
        $newFilter = [];
        if ($_GET['filter']){
            $newFilter = [
                'from' => $_GET['filter']['start_date']['gte'],
                'to' => $_GET['filter']['end_date']['lte'],
                'is_closed' => $_GET['filter']['is_closed'],
                'branch_id' => $_GET['filter']['branch_id'],
            ];
        }
        $this->params['url'] = array_merge($this->params['url'], $newFilter);
        $conditions = array_merge($conditions, $this->_filter_params());
		$this->paginate['FinancialYear'] = array(
			'conditions' => $conditions,
		);

		$financial_years = $this->paginate('FinancialYear');

        $this->set('financialYears', $financial_years);
		$filtered = $this->params['url'];
		unset($filtered['ext'], $filtered['url']);
		$this->set('filtered', $filtered);

		$financialYearStatus = [
			FinancialYear::STATUS_OPENED => 'Open',
			FinancialYear::STATUS_CLOSED => 'Closed',
		];

		$this->setup_nav_data($financial_years);

		$this->set('financialYearStatus', $financialYearStatus);

		$this->setDefaultViewData();
		$this->view = 'izam';
        $this->setIndexData();
        if ($this->viewVars['pagination']->total() == 0) {
            if (empty($_GET)) {
                return $this->render('default/no_added_yet');
            }
            return $this->render('default/no_result_found');
        }
		return $this->render('default/index');
	}

	function get_order_settings(){

		if(!isset($this->params['named']['sort'])){
			return $this->paginate['FinancialYear']['order'] = ["is_closed" => 'ASC', ];
		}
		parent::get_order_settings();

	}

	function owner_view($id = null) {
		if (!check_permission(VIEW_CLOSED_PERIODS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id) {
			$this->izamFlashMessage(__(sprintf (__('Invalid %s', true), __('Financial Period', true)),true), 'danger');
			$this->redirect(array('action'=>'index'));
		}

		$bindBranch = [];
		if (ifPluginActive(BranchesPlugin)) {
			$bindBranch = ['belongsTo' => ['Branch' => ['className' => 'Branch', 'foreignKey' => 'branch_id']]];
		}

		$this->FinancialYear->bindModel(array_merge($bindBranch,[
			'hasOne'    => ['Journal' => ['className' => 'Journal', 'foreignKey' => 'entity_id', 'conditions' => ['entity_type' => 'financial_year']]],
		]), false);

		$this->FinancialYear->recursive = 3;
		$financialYear = $this->FinancialYear->read(null, $id);

		if (!$financialYear) {
			$this->izamFlashMessage(__(sprintf (__('Invalid %s', true), __('Financial Period', true)),true), 'danger');
			$this->redirect(array('action'=>'index'));
		}

        $this->set('financialYear', $financialYear);
        $this->set('financialYear_nav', $this->setup_nav_view($id));
        $this->setDefaultViewData();
        $this->view = 'izam';
        $this->render('financial_years/show');

        /*

        $tabs = izam_resolve(CreateTabsServices::class)
            ->createTabs(ShowService::getTabs($financialYear));

        $this->set('viewActions', ShowService::getActions($financialYear, $this->params));
        $this->set('pageHead', ShowService::getPageHeader($financialYear, $this->viewVars));
        $this->set('tabs', $tabs);
        $this->set('modals', ShowService::getModals($financialYear,$this->params['controller']));

        $this->set('title_for_layout', __t('Financial Period'));
        return $this->render('default/show');
        */
    }

    function owner_future_view($id = null)
    {
        if (!check_permission(VIEW_CLOSED_PERIODS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if (!$id) {
            $this->izamFlashMessage(__(sprintf (__('Invalid %s', true), __('Financial Period', true)),true), 'danger');
            $this->redirect(array('action'=>'index'));
        }

        $bindBranch = [];
        if (ifPluginActive(BranchesPlugin)) {
            $bindBranch = ['belongsTo' => ['Branch' => ['className' => 'Branch', 'foreignKey' => 'branch_id']]];
        }

        $this->FinancialYear->bindModel(array_merge($bindBranch,[
            'hasOne'    => ['Journal' => ['className' => 'Journal', 'foreignKey' => 'entity_id', 'conditions' => ['entity_type' => 'financial_year']]],
        ]), false);

        $this->FinancialYear->recursive = 3;
        $financialYear = $this->FinancialYear->read(null, $id);

        if (!$financialYear) {
            $this->izamFlashMessage(__(sprintf (__('Invalid %s', true), __('Financial Period', true)),true), 'danger');
            $this->redirect(array('action'=>'index'));
        }

        $this->set('financialYear', $financialYear);
        $this->set('financialYear_nav', $this->setup_nav_view($id));
        $this->setDefaultViewData();
        $this->view = 'izam';

        $tabs = izam_resolve(CreateTabsServices::class)
            ->createTabs(ShowService::getTabs($financialYear));

        $this->set('viewActions', ShowService::getActions($financialYear, $this->params));
        $this->set('pageHead', ShowService::getPageHeader($financialYear, $this->viewVars));
        $this->set('tabs', $tabs);
        $this->set('modals', ShowService::getModals($financialYear,$this->params['controller']));

        $this->set('title_for_layout', __t('Financial Period'));
        return $this->render('default/show');
    }

	function owner_test(){
		$this->FinancialYear->get_profit_jorunal([ 'FinancialYear' => ['end_date' => '2018-01-01']]);
		
	}
	
	function owner_add() {
		if (!check_permission([MANAGE_COST_CENTERS, MANAGE_CLOSED_PERIODS])) {
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if(ifPluginActive(BranchesPlugin) && isset($this->params['url']['branch']) && $this->params['url']['branch'] != getCurrentBranch()['id']){
			$this->checkBranchIsCurrenct($this->params['url']['branch']);
		}
		$count = $this->FinancialYear->find('count');
		$last_fy = $this->FinancialYear->last();
		$is_first_year = $count < 1;
		$this->loadModel('Journal');
        $journalNotRelatedTofinancialYear = $this->Journal->find('all', ['conditions' => ['Journal.date <' => $last_fy['FinancialYear']['start_date'], 'Journal.financial_year_id ' => 0, 'Journal.entity_type != "year_opening_balance"']]);
        if(!$is_first_year && $journalNotRelatedTofinancialYear ){
			$journals_message = '';
			$journals_message .= sprintf(__('You cannot close this Financial Year since there are journals with date less than the start date "%s" please edit this Financial Year or delete it and create new Financial Year', true), format_date($last_fy['FinancialYear']['start_date'])) . '</br><ul>';
			foreach($journalNotRelatedTofinancialYear as $journal){
				$journals_message .= '<li><a href="' . Router::url(['controller' => 'journals', 'action' => 'view', $journal['Journal']['id']]) . '">' . __('Journal #', true) . $journal['Journal']['number'] . '</a></li>';
			}
			$journals_message .= '</ul>';
			$this->izamFlashMessage($journals_message, 'danger');
			$this->redirect(array('action'=>'edit', $last_fy['FinancialYear']['id']));
		}
		$start_date = $this->FinancialYear->get_start_date();
		if (!empty($this->data)) {

			if(!$is_first_year)
				$this->data['FinancialYear']['start_date'] = $start_date;
			
			$this->FinancialYear->create();
			if ($newRecord = $this->FinancialYear->save($this->data)) {
				$newRecord['FinancialYear']['id'] = $this->FinancialYear->getLastInsertID();
				if($last_fy){
					$newData = $this->FinancialYear->read(null ,$last_fy['FinancialYear']['id']);
					$this->activityLog($last_fy['FinancialYear'], $newData['FinancialYear'], EntityKeyTypesUtil::FINANCIAL_YEAR_ENTITY_KEY);
				}
				$this->activityLog([],$newRecord['FinancialYear'], EntityKeyTypesUtil::FINANCIAL_YEAR_ENTITY_KEY);
				$this->izamFlashMessage(sprintf (__('The %s has been saved', true), __('Financial Period',true)));
				$this->redirect(array('action'=>'index'));
			} else {
				$this->izamFlashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('Financial Period',true)), 'danger');
			}

		}
		$this->set('is_first_year',$count < 1);
		$this->set('last_fy' , $last_fy);
		$this->set('start_date', $start_date);

        $financialYearForThisBranch = $this->FinancialYear->find('first');

        if (!$financialYearForThisBranch) {
            $this->set('cancelUrl', '/v2/owner/accounting/settings');
        } else {
            $this->set('cancelUrl', Router::url(['action' => 'owner_index']));
        }

		$this->setDefaultViewData();
		$this->view = 'izam';
		$this->render('financial_years/form');
	}

	function owner_edit($id = null) {
		if (!check_permission([MANAGE_COST_CENTERS, MANAGE_CLOSED_PERIODS])) {
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
			if(ifPluginActive(BranchesPlugin) && isset($this->params['url']['branch']) && $this->params['url']['branch'] != getCurrentBranch()['id']){
				$this->checkBranchIsCurrenct($this->params['url']['branch']);
			}
//		$start_date = $this->FinancialYear->get_start_date();

		if (!$id && empty($this->data)) {
			$this->izamFlashMessage(sprintf (__('Invalid %s.', true), __('Financial Period', true)), 'danger');
			$this->redirect(array('action'=>'index'));
		}
		$financialYear = $this->FinancialYear->read(null, $id);
		if (!$financialYear) {
			$this->izamFlashMessage(__(sprintf (__('Invalid %s', true), __('Financial Period', true)),true), 'danger');
			$this->redirect(array('action'=>'index'));
		}
		if (!empty($this->data)) {
			if ($newData = $this->FinancialYear->save($this->data)) {

				$this->activityLog($financialYear['FinancialYear'], $newData['FinancialYear'], EntityKeyTypesUtil::FINANCIAL_YEAR_ENTITY_KEY);

				$this->izamFlashMessage(sprintf (__('The %s  has been updated', true), __('Financial Period',true)));
				$this->redirect(array('action'=>'index'));
			} else {
				$this->izamFlashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('Financial Year',true)), 'danger');
			}
		}
// 		if (empty($this->data)) {
// 			$financialYear['FinancialYear']['start_date'] = format_date($financialYear['FinancialYear']['start_date']);
// 			$financialYear['FinancialYear']['end_date'] = format_date($financialYear['FinancialYear']['end_date']);
// 			$this->data = $financialYear;
// //			debug($this->data);
// 		}
//		$this->set('start_date', $start_date);
        $this->set('id', $id);
		$this->set('data', $financialYear['FinancialYear']);
		$this->setDefaultViewData();
		$this->view = 'izam';
        $this->set('cancelUrl', Router::url(['action' => 'owner_index']));
		$this->render('financial_years/form');

		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (!check_permission([MANAGE_COST_CENTERS, MANAGE_CLOSED_PERIODS])) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if(ifPluginActive(BranchesPlugin) && isset($this->params['url']['branch']) && $this->params['url']['branch'] != getCurrentBranch()['id']){
			$this->checkBranchIsCurrenct($this->params['url']['branch']);
		}
		// if (empty($id) && !empty ($_POST['ids'])) {
		// 	$id = $_POST['ids'];
		//  }
		$module_name= __t('Financial Period', true);
		 if (!$id || !$_POST) {
			$this->izamFlashMessage(sprintf (__('Invalid id for %s', true), $module_name), 'danger');
			$this->redirect(array('action'=>'index'));
		}

		$financialYears = $this->FinancialYear->find('all',array('conditions'=>array('FinancialYear.id'=>$id)));
		if (empty($financialYears)){
			$this->izamFlashMessage(sprintf(__('%s not found', true), $module_name), 'danger');
			$this->redirect($this->referer(array('action' => 'index'), true));
		}

			if ($this->FinancialYear->deleteAll(array('FinancialYear.id'=>[$id]), true, true)) {
				if(!empty($this->FinancialYear->validationErrors)){
					
					$this->FinancialYear->query('ROLLBACK');
					$this->izamFlashMessage(sprintf(__('Deletion failed, %s',true), implode(',', $this->FinancialYear->validationErrors)), 'danger');
					
				}else{
					$this->activityLog( $financialYears[0]['FinancialYear'], [], EntityKeyTypesUtil::FINANCIAL_YEAR_ENTITY_KEY);
					$this->izamFlashMessage(sprintf (__('%s deleted', true), $module_name));
				}
				$this->redirect(array('action'=>'index'));
			}
			else{
				if($this->FinancialYear->validationErrors){
					$this->izamFlashMessage(sprintf(__('Deletion failed, %s',true), implode(',', $this->FinancialYear->validationErrors)), 'danger');
				}
				$this->redirect(array('action'=>'index'));
			}
		$this->set('financialYears',$financialYears);
	}

	private function checkBranchIsCurrenct($branch_id)
	{
		$this->loadModel('Branch');
		$branch = $this->Branch->find('first', ['conditions' => ['Branch.id' => $branch_id]]);
		$this->izamFlashMessage(sprintf(__('You cannot edit or delete or close the financial year in branch %s', TRUE), $branch['Branch']['name']), 'danger');
		$this->redirect(array('action' => 'index'));
	}

	// the last Closed Financial Years in the current branch and if there’s opened financial year in other branch with the same end date not closed
	private function openedFinancialYearWithSameEndDate($accessibleBranches)
	{
		$lastClosedFinancial = $this->FinancialYear->find('first', ['conditions' => ['FinancialYear.is_closed' => FinancialYear::STATUS_CLOSED], 'order' => 'FinancialYear.id DESC']);
		if($lastClosedFinancial){
			$conditions =  ['FinancialYear.branch_id' => array_keys($accessibleBranches), 'FinancialYear.id !=' =>   $lastClosedFinancial['FinancialYear']['id'], 'FinancialYear.is_closed' => FinancialYear::STATUS_OPENED, 'FinancialYear.end_date' => $lastClosedFinancial['FinancialYear']['end_date']];

			$this->FinancialYear->bindModel([
				'belongsTo' => ['Branch' => ['className' => 'Branch', 'foreignKey' => 'branch_id']],
			]);
			$opeFinYearWithSameEndDate = $this->FinancialYear->find('all', ['conditions' => $conditions]);

			$this->set('openedFinancialYearWithSameEndDate', $opeFinYearWithSameEndDate);
		}
	}


	private function activityLog($oldData = [], $newData = [], $entity)
	{
		$entityBuilder = getEntityBuilder();
		$st = $entityBuilder->buildEntity($entity);
		$activityLogService = new \App\Services\ActivityLogService();
		$requests = (new EntityActivityLogRequestsCreator())->create($st,  $this->parseLogsData($newData), $this->parseLogsData($oldData), []);
		foreach ($requests as $request) {
			$activityLogService->addActivity($request);
		}
	}

	private function parseLogsData($data = []): array
	{
		if (!empty($data)) {
			unset($data['next_year_id']);
			$data['is_closed'] = $data['is_closed'] ? __t('Yes') : __t('No');
		}
		return $data;
	}

    /**
     * @todo re-fact this methods
     */
    private function setIndexData()
    {
        $pagination = new \Izam\View\Form\Element\LengthAwarePaginator('financial-year-pagination');
        $pagination->loadFromPaging($this->params['paging']['FinancialYear']);
        $pagination->setPath($this->params['url']['url']);

        $builder = getEntityBuilder();
        $structure = $builder->buildEntity(EntityKeyTypesUtil::FINANCIAL_YEAR_ENTITY_KEY);
        $form = CreateFilterFormService::build($structure);

        $this->set('pageHead', FinancialYearComponent::PageHeaderButtons($pagination));
        $this->set('pagination', $pagination);
        $this->set('view_folder', 'financial_years');
        $this->set('isGlobal', true);
        $this->set('reset_filters_url', $pagination->getPath() . '?reset=1');
        $this->set('links', FinancialYearComponent::pageButtonsWithoutData());
        $this->set('filtersForm', $form);
    }
}
