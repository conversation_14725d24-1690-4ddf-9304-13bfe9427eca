<?php
/**
 * @property Page $Page
 */
class PagesController extends AppController {

	var $name = 'Pages';
	var $helpers = array('Html', 'Form', 'Fck');

	function admin_index() {
		$this->Page->recursive = 0;
		$this->set('pages', $this->paginate());
	}

	function admin_view($id = null) {
		if (!$id) {
			$this->Session->setFlash(__('Invalid Page.', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('page', $this->Page->read(null, $id));
	}

	function admin_add() {
		if (!empty($this->data)) {
			$this->Page->create();
			if ($this->Page->save($this->data)) {
				$this->flashMessage(__('The Page has been saved', true), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(__('The Page could not be saved. Please, try again.', true));
			}
		}
	}

	function admin_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(__('Invalid Page', true));
			$this->redirect(array('action'=>'index'));
		}
		if (!empty($this->data)) {
			if ($this->Page->save($this->data)) {
				$this->flashMessage(__('The Page has been saved', true), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(__('The Page could not be saved. Please, try again.', true));
			}
		}
		if (empty($this->data)) {
			$this->data = $this->Page->read(null, $id);
		}
		$this->render('admin_add');
	}

	function home() {

	}

	function admin_delete($id = null) {
		if (!$id) {
			$this->flashMessage(__('Invalid id for Page', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->Page->del($id)) {
			$this->flashMessage(__('Page deleted', true), 'Sucmessage');
			$this->redirect(array('action'=>'index'));
		}else {
			$this->flashMessage(__('Invalid id for Page', true));
			$this->redirect(array('action'=>'index'));
		}
	}

	function admin_delete_multi() {
		if (empty($_POST['ids'])||!is_array($_POST['ids'])) {
			$this->flashMessage(__('Invalid ids for Page', true));
			$this->redirect(array('action'=>'index'));
		}
		if ($this->Page->deleteAll(array('Page.id'=>$_POST['ids']))) {
			$this->flashMessage(__('Static page items deleted', true), 'Sucmessage');
			$this->redirect(array('action'=>'index'));
		}
		else {
			$this->flashMessage(__('Unknown error', true));
			$this->redirect(array('action'=>'index'));
		}
	}

	function index($permalink) {
		
		$data= $this->Page->findByPermalink($permalink);
		if(is_countable($data) && sizeof($data)!=1) {
			$this->cakeError('error404');
		}
		else {
			$this->pageTitle=$data[$this->Page->name]['title'];
			$this->set('contents',$data[$this->Page->name]['content']);
			$this->set('page_title',$data[$this->Page->name]['title']);
		}
	}

	function display($page) {
		if(!$page) {
			$this->cakeError('404');
		}
		$this->render($page);
	}


	function get_i18n_text($text) {
		die(__($text,true));
	}
	
	
	

	
}