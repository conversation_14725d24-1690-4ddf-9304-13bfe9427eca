<?php
class LocalCurrencyRatesController extends AppController {

	var $name = 'LocalCurrencyRates';

	/**
	 * @var LocalCurrencyRate
	 */
	var $LocalCurrencyRate;
	var $helpers = array('Html', 'Form');

	
	
	
	function owner_index() {
		if (!check_permission(Edit_General_Settings) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		$this->LocalCurrencyRate->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('localCurrencyRates', $this->paginate('LocalCurrencyRate', $conditions));
	}

	function owner_view($id = null) {
		if (!check_permission(Edit_General_Settings) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id) {
			$this->flashMessage(sprintf (__('Invalid %s',true), __('Local Currency Rate',true)));
					

			$this->redirect('/');
		}
		$this->set('localCurrencyRate', $this->LocalCurrencyRate->read(null, $id));
	}

	function owner_get_currency_rate()
	{
		$from_curr = $_GET['from'];
		$to_curr = $_GET['to'];
		$from_date = $_GET['date'];
		if(!empty($from_curr) && !empty($to_curr)  && !empty($from_date)){
		App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));
		
		$from_date = $this->LocalCurrencyRate->formatDate($from_date);
		$rate = CurrencyConverter::index($from_curr, $to_curr, $from_date);
		
				die(json_encode(['rate' => $rate]));

		}
				die(json_encode(['rate' => '']));

	}
	
	function owner_add() {
        set_time_limit(300);
		if (!check_permission(Edit_General_Settings) ) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (!empty($this->data)) {
			$this->validate_open_day($this->data['LocalCurrencyRate']['date_from']);
			$result = $this->LocalCurrencyRate->addNewLocalCurrencyRate($this->data);
			if($result['status']){
				$this->flashMessage(sprintf (__('%s Saved',true), __('Local Currency Rate',true)), 'Sucmessage');
			}else{
				$this->flashMessage($result['error']);
			}			
			$this->redirect(['action' => 'index']);
		}
		$this->loadModel('Currency');
		$this->data['LocalCurrencyRate']['from_currency'] = $this->Currency->get_default_currency();
		$currencies = $this->Currency->getCurrencyList();
		$default_currency = $this->Currency->get_default_currency();
	
		$this->set(compact('currencies', 'default_currency'));
	}

	function owner_edit($id = null) {
        $this->loadModel('Currency');
	    set_time_limit(300);
		if (!check_permission(Edit_General_Settings) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
        }
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s',true), __('Local Currency Rate',true)));
			$this->redirect(['action' => 'index']);
		}
		$localCurrencyRate = $this->LocalCurrencyRate->find(array('id'=>$id));
		$this->validate_open_day($localCurrencyRate['LocalCurrencyRate']['date_from']);

		if (!empty($this->data)) {
			$time = explode(" ", $this->data['LocalCurrencyRate']['date_from'])[1];
			$this->data['LocalCurrencyRate']['date_from'] = $this->LocalCurrencyRate->formatDate($this->data['LocalCurrencyRate']['date_from']);
			if (!empty($time) && count(explode(" ", $this->data['LocalCurrencyRate']['date_from']))==1) { // no need to add time if its already has
				$this->data['LocalCurrencyRate']['date_from'] .= " ".$time;
			}
			if ($this->LocalCurrencyRate->save($this->data)) {
				if($localCurrencyRate['LocalCurrencyRate']['date_from'] < $this->data['LocalCurrencyRate']['date_from'])
					$smallest_date = $localCurrencyRate['LocalCurrencyRate']['date_from'];
				else
					$smallest_date = $this->data['LocalCurrencyRate']['date_from'];;

				$this->LocalCurrencyRate->update_currency_transactions($this->data['LocalCurrencyRate']['from_currency'], $this->data['LocalCurrencyRate']['to_currency'], $smallest_date);
				$this->add_actionline(ACTION_UPDATE_LOCAL_CURRENCY_RATE, ['primary_id' => $this->LocalCurrencyRate->id]);
				$this->flashMessage(sprintf (__('%s Saved',true), __('Local Currency Rate',true)), 'Sucmessage');
			}
		}
		if (empty($this->data)) {

			$this->data = $localCurrencyRate;
		}


		$currencies = $this->Currency->getCurrencyList();
		$default_currency = $this->Currency->get_default_currency();
	
		$this->set(compact('currencies', 'default_currency'));
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
        set_time_limit(300);
		if (!check_permission(Edit_General_Settings) ) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
		$this->flashMessage(sprintf (__('Invalid %s',true), __('Local Currency Rate',true)));
			$this->redirect('/');
		}
		$module_name= __('localCurrencyRate', true);
		if(is_array($id) && count($id) > 1){
			$module_name= __('localCurrencyRates', true);
		 } 
		$localCurrencyRates = $this->LocalCurrencyRate->find('all',array('conditions'=>array('LocalCurrencyRate.id'=>$id)));
		if (empty($localCurrencyRates)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->LocalCurrencyRate->deleteAll(array('LocalCurrencyRate.id'=>$_POST['ids']))) {
				foreach($localCurrencyRates as $k => $currency){
				$this->LocalCurrencyRate->update_currency_transactions($currency['LocalCurrencyRate']['from_currency'], $currency['LocalCurrencyRate']['to_currency'], $currency['LocalCurrencyRate']['date_from']);
				}
				$this->add_actionline(ACTION_DELETE_LOCAL_CURRENCY_RATE, ['primary_id' => implode(', ', $_POST['ids'])]);
				$this->flashMessage(sprintf (__('%s has been deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));

			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('localCurrencyRates',$localCurrencyRates);
	}
}
?>
