<?php

use App\Utils\ChartOfAccountsUtil;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

class JournalCatsController extends AppController {

	var $name = 'JournalCats';

	/**
	 * @var JournalCat
	 */
	var $JournalCat;
	var $helpers = array('Html', 'Form');
    var $components = ["ItemPermissions"];

    
		var $invoice_js_labels = array(
			'You are not allowed to view this page',
			'You have to select main account for any sub-account',
		
	);

	function owner_tree(){
	    ini_set('memory_limit', '1G');
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS) &&  !check_permission(VIEW_ALL_JOURNALS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->JournalCat->recursive = 1;
		$this->loadModel('Journal');
		$this->loadModel('JournalAccount');
        $count = $this->JournalAccount->find('count');
        if($count > 300) {
            return $this->redirect('/v2/owner/chart-of-accounts');
        }
        $this->set('canEditAccounts', check_permission(MANAGE_JOURNAL_ACCOUNTS));
		$categories = $this->JournalCat->getJournalCats('all', ['order'=>['JournalCat.code ASC']]);
//		die(debug($categories));
        $tree = $this->JournalCat->buildTree($categories);

        $this->loadModel('Journal');
				$this->set('cats_list',$this->JournalCat->find('list'));
		$this->set('title_for_layout',  __('Chart of Accounts',true) );
		$this->set('currency', $this->Journal->get_default_currency());
		$this->set('categories',$tree);

	}

	function owner_test()
    {
			
    }
	
	/*
	 * this function is called via ajax from owner_tree to calculate accounts,cats
	 * 
	 * input $_POST['account_ids'],$_POST['cat_ids']
	 * return each account or cat recalculated totals
	 */
    function owner_calculate_tree($fy_id=false){

		$this->loadModel('Journal');
		$this->Journal->bindModel(array('hasMany' => array('JournalTransaction' =>  array('className' => 'JournalTransaction', 'foreignKey' => 'journal_id', 'dependent' => false))));		
		
		if((!empty($_POST['account_ids']) || !empty($_POST['cat_ids'])) && $this->RequestHandler->isAjax() ){
			set_time_limit(90);
			$account_ids = isset($_POST['account_ids']) ? (array) $_POST['account_ids'] :[] ;
			$this->loadModel('JournalAccount');
			$cat_ids = isset($_POST['cat_ids']) ? (array) $_POST['cat_ids'] : [] ;
			$parent_cat_id = $_POST['parent_id'] ;
			
			$return_data  = array();
			
			if(count($account_ids))
			foreach ($account_ids as $account_id){
                $return_data['account'][$account_id] = $this->Journal->recaculate_account_total($account_id,false,false,$fy_id);
				$return_data['account'][$account_id]['deletable'] = $this->JournalAccount->deletAble($account_id, true, false);
			}
			if(count($cat_ids))
			foreach ($cat_ids as $cat_id){
                $return_data['cat'][$cat_id] = $this->Journal->recaculate_cat_total($cat_id,false,false,$fy_id);
				$return_data['cat'][$cat_id]['deletable'] = $this->JournalCat->deletAble($cat_id, false);
			}
            $return_data['parent'] = $this->Journal->recaculate_cat_total($parent_cat_id,false,false,$fy_id);
			die(json_encode($return_data));
		}
	}
	
	
	function owner_index() {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->JournalCat->recursive = 0;
		$conditions = $this->_filter_params();
		$journal_cats = $this->paginate('JournalCat', $conditions);
		$this->set('journalCats', $journal_cats);
		if(IS_REST){
			$this->set('rest_items', $journal_cats);
			$this->set('rest_model_name', "JournalCat");
			$this->render("index");
		}
	}

	function owner_view($id = null) {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (!$id) {
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Journal Category', true))]);
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('account', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$journal_cat = $this->JournalCat->read(null, $id);
		if(!$journal_cat && IS_REST) $this->cakeError('error404', array('message' => sprintf(__("%s not found.", true), __('Journal Category', true))));
		$this->set('journalCat', $journal_cat);
		if(IS_REST){
			$this->set('rest_item', $journal_cat);
			$this->set('rest_model_name', "JournalCat");
			$this->render("view");
		}
	}
	
	
	function owner_add() {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->_formCommon();
		$this->loadModel('JournalAccount');
        $catAccount = $this->JournalCat->find('first', ['conditions' => ['JournalCat.code' => $this->data['JournalCat']['code']]]);
        $account = $this->JournalAccount->find('first', ['conditions' => ['JournalAccount.code' => $this->data['JournalCat']['code']]]);
        if (!empty($this->data)) {
			$permissionsEmpty = true;
			if($this->data['ItemPermission']['group_type'] != ItemPermission::GROUP_EVERYONE){
				foreach ($this->data['ItemPermission']['group_id'][1] as $key => $value) {
					if(!empty($value)){
						$permissionsEmpty = false;
						break;
					}
				}
			} else {
				$permissionsEmpty = false;
			}
			if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)  && settings::getValue(BranchesPlugin, 'specify_accounts_branches') && $permissionsEmpty && $this->data['ItemPermission']["group_type"][1]!=-1){
				CustomValidationFlash([__('You must at least select one branch', true)]);
				$_SESSION['ErrorMessage'] = __('You must at least select one branch', true);
				$this->JournalCat->validationErrors['group_id'] = __('This Field is Required', true);
			}
			if(isset($_POST['iframe'])) {
                $redirectData = ChartOfAccountsUtil::getSaveAccountRedirectUrl($this->data);
            } else {
                $redirectData = array('controller'=>'journal_cats','action' => 'tree','#' =>  'folderid='.$this->data['JournalCat']['folderid'],'escape' => false);
            }
		    if($catAccount || $account)
            {
                $name = $catAccount['JournalCat'] ? $catAccount['JournalCat']['name'] : $account['JournalAccount']['name'];
                $this->flashMessage(sprintf(__('This code is already used by %s', true), $name));
                $this->redirect($redirectData);
            }
			$saved = false;
			$action_line_data = array();
			$action_line_id = ACTION_JOURNAL_ACCOUNT_ADD;
			$action_line_data['param3'] = $this->data['JournalCat']['name'] . ' - ' . $this->data['JournalCat']['code'];
			$action_line_data['param4'] = $this->data['JournalCat']['type'];
			if(empty($this->data['JournalCat']['journal_cat_id']))
            {
                $this->data['JournalCat']['journal_cat_id'] = -1;
            }
			$cat = $this->JournalCat->find('first',array('conditions' => array('JournalCat.id' => $this->data['JournalCat']['journal_cat_id'])));
			if($cat){
			$action_line_data['param5'] = $cat['JournalCat']['name'] . ' - ' . $cat['JournalCat']['code'];
			$action_line_data['secondary_id'] = $cat['JournalCat']['id'];
			}
			else
			$action_line_data['param5'] = 'it has no parents';
			if(IS_REST) $this->data['JournalCat']['account_type'] = $this->params['journal_account_type'];
			if($this->data['JournalCat']['account_type'] === JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT)
			{	
				$action_line_data['param1'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_ACCOUNT;
				$data['JournalAccount'] = $this->data['JournalCat'];
				$data['ItemPermission'] = $this->data['ItemPermission'];
				if(empty($data['JournalAccount']['journal_cat_id']) || $data['JournalAccount']['journal_cat_id'] == -1 ) {
					$this->JournalCat->validationErrors['journal_cat_id'] = __('This Field is Required', true);
				}
				$this->JournalAccount->create();
				if(empty($this->JournalCat->validationErrors) && $this->JournalAccount->save($data))
				{
					$action_line_data['primary_id'] = $this->JournalAccount->id;
					$this->add_actionline ($action_line_id,$action_line_data);
					if(IS_REST){
						$this->set('id', $this->JournalAccount->id);
						$this->render('created');
						return;
					}
					$this->flashMessage(sprintf (__('The %s has been saved', true), __('account',true)), 'Sucmessage');
				}else{
					if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->JournalAccount->validationErrors]);
					$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('account',true)));
				}
				
			}else
			{
			$action_line_data['param1'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT;
//								die(debug($this->data));

				$this->JournalCat->create();
				if (empty($this->JournalCat->validationErrors) && $this->JournalCat->save($this->data)) {
					$this->loadModel('Journal');
					$this->Journal->updateAccountLevelsRecursive($this->data['JournalCat']['journal_cat_id']);
					$this->loadModel('ItemPermission') ;
                    $this->ItemPermission->savePermissions($this->data ,$this->JournalCat->id , ItemPermission::ITEM_TYPE_JOURNAL_CAT);
					$action_line_data['primary_id'] = $this->JournalCat->id;
					$this->add_actionline ($action_line_id,$action_line_data);
					if(IS_REST){
						$this->set('id', $this->JournalCat->id);
						$this->render('created');
						return;
					}
					$this->flashMessage(sprintf (__('The %s has been saved', true), __('account',true)), 'Sucmessage');
				} else {
					if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->JournalCat->validationErrors]);
					$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('account',true)));
				}
			}
			if(isset($_POST['iframe'])) {
                $redirectData = ChartOfAccountsUtil::getSaveAccountRedirectUrl($this->data);
            } else {
			    $redirectData = array('controller'=>'journal_cats','action' => 'tree','#' =>  'folderid='.$this->data['JournalCat']['folderid'],'escape' => false);
            }
			$this->redirect($redirectData);

		}
		if(isset($_GET['folderid']) && !empty(trim($_GET['folderid']))){
			$this->set('is_ajax',true);
			$folderid = $_GET['folderid'];
			$this->set('folderid', urldecode($folderid));
			$parent = $this->JournalCat->find('first',array('conditions' => array('JournalCat.id' => $folderid),'order' => array('JournalCat.code' => 'DESC')));
            $new_account_code = $this->JournalCat->getNewAccountCode($parent['JournalCat']['id']);

			$this->data['JournalCat']['code'] = $new_account_code;
			$this->data['JournalCat']['type'] = $parent['JournalCat']['type'];
			if (ifPluginActive(BranchesPlugin) && settings::getValue(BranchesPlugin, 'specify_accounts_branches')) {
				$this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_JOURNAL_CAT, $folderid);
			}
//			$this->set('parent_type',$parent['JournalCat']['type']);
		}
		$categories = $this->JournalCat->find('all',['order'=>['JournalCat.code ASC']]);
		$tree = $this->JournalCat->buildTree($categories);
		$this->set('categories',$tree);
		$parentJournalCats = $this->JournalCat->ParentJournalCat->find('list');
		
        $test[-1] = __("Main account",true);
        $parentJournalCats = $test + $parentJournalCats;
		$this->set('title_for_layout',  __('Add Journal Account',true) );
		
		//bootstrap function
		$this->loadModel('Journal');
		$types = $this->Journal->get_journal_types_list();
		
		$this->set(compact('parentJournalCats','types'));
	}

	public function owner_permissions() {
		$cat_id = $_GET['cat_id'] ?? 0; 
		$this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_JOURNAL_CAT, $cat_id);
		$this->render('/elements/item_permissions');
	}

	function owner_get_new_account_code($journalCatId)
    {
        return die(json_encode($this->JournalCat->getNewAccountCode($journalCatId)));
    }

	function _formCommon($id = false)
    {
            $this->ItemPermissions->setData(ItemPermission::ITEM_TYPE_JOURNAL_CAT , $id);
    }

	function owner_edit($id = null) {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'journal cat',true)));
			$this->redirect(array('action'=>'index'));
		}
        $this->_formCommon($id);
		$journalCat = $this->JournalCat->read(null, $id);
		if(IS_REST){
			if(empty($journalCat)) $this->cakeError('error404', array('message' => sprintf (__('Invalid %s.', 'journal cat',true))));
			$this->data['JournalCat']['id'] = $id;
		}
		$this->loadModel('Journal');
		if (!empty($this->data)) {
			$permissionsEmpty = true;
			if($this->data['ItemPermission']['group_type'] != ItemPermission::GROUP_EVERYONE){
				foreach ($this->data['ItemPermission']['group_id'][1] as $key => $value) {
					if(!empty($value)){
						$permissionsEmpty = false;
						break;
					}
				}
			} else {
				$permissionsEmpty = false;
			}
			if(ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::BranchesPlugin)  && settings::getValue(BranchesPlugin, 'specify_accounts_branches') && $permissionsEmpty  && $this->data['ItemPermission']["group_type"][1]!=-1){
				CustomValidationFlash([__('You must at least select one branch', true)]);
				$_SESSION['ErrorMessage'] = __('You must at least select one branch', true);
				$this->JournalCat->validationErrors['group_id'] = __('This Field is Required', true);
			}
			if(isset($_POST['iframe'])) {
                $redirectData = ChartOfAccountsUtil::getSaveAccountRedirectUrl($this->data);
            } else {
                $redirectData = array('controller'=>'journal_cats','action' => 'tree','#' =>  'folderid='.$this->data['JournalCat']['folderid'],'escape' => false);
            }
            if ($this->data['JournalCat']['journal_cat_id'] == $id) {
	            if(IS_REST) $this->cakeError("error400", ["message"=> 'failed', "validation_errors" => ['journal_cat_id' => __('You can not choose the same account as the main account', true)]]);
	            $this->flashMessage(__('You can not choose the same account as the main account', true));
	            $this->redirect($redirectData);
            }
			if(empty($this->data['JournalCat']['journal_cat_id'])) {
				$this->data['JournalCat']['journal_cat_id'] = -1;
			}
			$parentCats = $this->Journal->get_all_parents_for_cat($this->data['JournalCat']['journal_cat_id']);
            if(in_array($id, $parentCats)) {
            	//check if circular dependency
				//that is the new parent is the child of this account
				$message = __("You can not choose a child account for this account to be it's parent account", true);
				if(IS_REST) $this->cakeError("error400", ["message"=> 'failed', "validation_errors" => ['journal_cat_id' => $message]]);
				$this->flashMessage($message);
				$this->redirect($redirectData);
			}
			$oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL_CAT, $id, 1);
			if (empty($this->JournalCat->validationErrors) && $this->JournalCat->save($this->data)) {
				$this->loadModel('Journal');
				$this->Journal->update_cat_parent_ids();
				$this->Journal->update_account_parent_ids();
				$this->loadModel('Journal');
				$this->Journal->updateAccountLevelsRecursive($this->data['JournalCat']['journal_cat_id']);
				$newData = getRecordWithEntityStructure(EntityKeyTypesUtil::JOURNAL_CAT, $id, 1);
				$request = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRequest(
					$id,
					EntityKeyTypesUtil::JOURNAL_CAT,
					ActionLineMainOperationTypesUtil::UPDATE_ACTION,
					"#$newData->code $newData->name",
					"/v2/owner/chart-of-accounts/cats/".$id,
					$newData->toArray(),
					$oldData->toArray()
				);
			(new \App\Services\ActivityLogService())->addActivity($request);

			$this->loadModel('ItemPermission') ;
            $this->ItemPermission->savePermissions($this->data ,$id , ItemPermission::ITEM_TYPE_JOURNAL_CAT);

			// Fetch all descendants (both categories and accounts) for the given category ID
			$allDescendants = $this->JournalCat->fetchDescendantItems($id);

			if (!empty($allDescendants)) {
				foreach ($allDescendants as $descendant) {
					// Determine the appropriate item type for permissions
					$itemType = ($descendant['type'] === 'account') 
						? ItemPermission::ITEM_TYPE_JOURNAL_ACCOUNT 
						: ItemPermission::ITEM_TYPE_JOURNAL_CAT;

					// Save permissions for each descendant item
					$this->ItemPermission->savePermissions($this->data, $descendant['id'], $itemType);
				}
			}

			$action_line_data = array();
			$action_line_id = ACTION_JOURNAL_ACCOUNT_EDIT;
			$action_line_data['param3'] = $this->data['JournalCat']['name'] . ' - ' . $this->data['JournalCat']['code'];
			$action_line_data['param4'] = $this->data['JournalCat']['type'];
			$cat = $this->JournalCat->find('first',array('conditions' => array('JournalCat.id' => $this->data['JournalCat']['journal_cat_id'])));
			if($cat){
			$action_line_data['param5'] = $cat['JournalCat']['name'] . ' - ' . $cat['JournalCat']['code'];
			$action_line_data['secondary_id'] = $cat['JournalCat']['id'];
			}
			else
			$action_line_data['param5'] = 'it has no parents';
			
			$action_line_data['param1'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT;
			$action_line_data['primary_id'] = $this->data['JournalCat']['id'];
			$this->add_actionline ($action_line_id,$action_line_data);
			if(IS_REST){
				$this->render('success');
				return;
			}
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('account',true)), 'Sucmessage');
				$this->redirect($redirectData);
			} else {
				if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->JournalCat->validationErrors]);
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('account',true)));
                $this->redirect($redirectData);
            }
		}
		if (empty($this->data)) {
			$this->data = $journalCat;
		}
		$this->loadModel('Journal');
		$parentJournalCats = $this->JournalCat->ParentJournalCat->find('list');
		$child_cats = $this->Journal->get_children_cats($id,true);
		$child_cats[] = $id;
		debug($child_cats);
		$categories = $this->JournalCat->find('all',['order'=>['JournalCat.code ASC'],'conditions' => array('JournalCat.id NOT IN ('. implode(',',$child_cats).')' )]);
		
		if(isset($_GET['folderid']) && !empty(trim($_GET['folderid']))){
			$this->set('is_ajax',true);
			$this->set('folderid', urldecode($_GET['folderid']));
		}
		$tree = $this->JournalCat->buildTree($categories);
		$this->set('categories',$tree);
        $test[-1] = __("Main account",true);
        $parentJournalCats = $test + $parentJournalCats;

		$types = $this->Journal->get_journal_types_list();
		$this->set('edit',true);
		$this->set(compact('parentJournalCats','types'));
		$this->set('title_for_layout',  __('Edit Journal Account',true) );

		$this->render('owner_add');
	}
	
	function owner_validate_code($code = null,$account_id = null){
		$account = $this->JournalCat->find('first',array('conditions'=> array('JournalCat.code'=> $code)));
		$this->loadModel('JournalAccount');
		$journal_account = $this->JournalAccount->find('first',array('conditions'=> array('JournalAccount.code'=> $code)));
		if(($account && $account['JournalCat']['id'] != $account_id) || ($journal_account && $journal_account['JournalAccount']['id'] != $account_id))
			die(false);
		else
			die(true);
	}

	function owner_delete($id = null) {
		if (!check_permission(MANAGE_JOURNAL_ACCOUNTS)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
				$this->set('title_for_layout',  __('Delete Journal Account',true) );
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Journal', true))]);
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('account',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('Account', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('Account', true);
		 } 
		$journalCats = $this->JournalCat->find('all',array('conditions'=>array('JournalCat.id'=>$id)));
	
		if(isset($_GET['folderid']) && !empty(trim($_GET['folderid']))){
			$this->set('is_ajax',true);
			$this->set('folderid', urldecode($_GET['folderid']));
		}
		if (empty($journalCats)){
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), __($module_name,true))]);
			$this->flashMessage(sprintf(__('%s not found', true), __($module_name,true)));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		$this->loadModel('JournalAccount');
		$action_line_data = array();
		$action_line_id = ACTION_JOURNAL_ACCOUNT_DELETE;
        if(isset($_POST['iframe'])) {
            $redirectData = ChartOfAccountsUtil::getSaveAccountRedirectUrl($this->data);
        } else {
            if(isset($_POST['folderid'])){
                $redirectData = array('controller'=>'journal_cats','action' => 'tree','#' =>  'folderid='.$_POST['folderid'],'escape' => false);
            }else {
                $redirectData = array('controller'=>'journal_cats','action'=>'tree');
            }
        }
		foreach($journalCats as $k => $cat)
		{
			
			if(!$this->JournalCat->deletAble($cat['JournalCat']['id']))
			{	
				if(IS_REST) $this->cakeError("error400", ["message"=>sprintf (__('You can\'t delete %s because it has dependent accounts', true) , $cat['JournalCat']['name'] )]);
				$this->flashMessage(sprintf (__('You can\'t delete %s because it has dependent accounts', true), $cat['JournalCat']['name'] ));
                 $this->redirect($redirectData);
			}
			$action_line_data[$k]['param3'] = $cat['JournalCat']['name'] . ' - ' . $cat['JournalCat']['code'];
			$action_line_data[$k]['param4'] = $cat['JournalCat']['type'];
			$parent_cat = $this->JournalCat->find('first',array('conditions' => array('JournalCat.id' => $cat['JournalCat']['journal_cat_id'])));
			if($parent_cat){
			$action_line_data[$k]['param5'] = $parent_cat['JournalCat']['name'] . ' - ' . $parent_cat['JournalCat']['code'];
			$action_line_data[$k]['secondary_id'] = $parent_cat['JournalCat']['id'];
			}
			else
			$action_line_data[$k]['param5'] = 'it has no parents';

			$action_line_data[$k]['param1'] = JournalAccount::JOURNAL_ACCOUNT_TYPE_CAT;
			$action_line_data[$k]['primary_id'] = $cat['JournalCat']['id'];
		}
		
		if(IS_REST){
			$_POST['submit_btn'] = 'yes';
			$_POST['ids'] = [$id];
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->JournalCat->deleteAll(array('JournalCat.id'=>$_POST['ids']) , $cascade = true, $callback = true)) {
				foreach($action_line_data as $k => $action_line){
					$this->add_actionline ($action_line_id, $action_line);
				}
				if(IS_REST){
					$this->set("message", sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb));
					$this->render("success");
					return;
				}
				$this->flashMessage(sprintf (__('%s deleted', true), __($module_name,true)), 'Sucmessage');
				$this->redirect($redirectData);
			}
			else{
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect($redirectData);
			}
		}
		$this->set('journalCats',$journalCats);
	}


	function owner_hide($accountId)
	{
		$account = $this->JournalCat->find('first',array('conditions'=>array('JournalCat.id'=>$accountId)));
		if(!$account || $account['JournalCat']['is_hidden']) {
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('Invalid %s', true), __('Account', true))]);
			$this->flashMessage(sprintf (__('Invalid %s', true), __('Account', true)));
			$this->redirect(array('action'=>'index'));
		}
		$canBeHidden = $this->JournalCat->canBeHidden($account['JournalCat']['id']);
		if(!$canBeHidden) {
			if(IS_REST) $this->cakeError("error400", ["message"=>__('This account can not be hidden', true)]);
			$this->flashMessage(__('This account can not be hidden', true));
			$this->redirect(array('action'=>'index'));
		}
		$this->JournalCat->id = $accountId;
		$this->JournalCat->saveField('is_hidden', 1);
		$request = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRequest(
			$accountId,
			EntityKeyTypesUtil::JOURNAL_CAT,
			ActionLineMainOperationTypesUtil::UPDATE_ACTION,
			"#{$account['JournalCat']['code']} {$account['JournalCat']['name']}",
			"/v2/owner/chart-of-accounts/cats/".$accountId,
			['is_hidden' => __t('No')],
			['is_hidden' => __t('Yes')]
		);
		(new \App\Services\ActivityLogService())->addActivity($request);

		if(IS_REST){
			$this->set("message", __('Account has been hidden', true));
			$this->render("success");
			return;
		}
		$this->flashMessage(__('Account has been hidden', true), 'Sucmessage');

		$this->redirect("/v2/owner/chart-of-accounts/cats/" . $account['JournalCat']['journal_cat_id']);
	}

	function owner_unhide($accountId)
	{
		$account = $this->JournalCat->find('first',array('conditions'=>array('JournalCat.id'=>$accountId)));
		if(!$account || !$account['JournalCat']['is_hidden']) {
			if(IS_REST) $this->cakeError("error404", ["message"=>sprintf(__('Invalid %s', true), __('Account', true))]);
			$this->flashMessage(sprintf (__('Invalid %s', true), __('Account', true)));
			$this->redirect(array('action'=>'index'));
		}
		$this->JournalCat->id = $accountId;
		$this->JournalCat->saveField('is_hidden', 0);
		if(IS_REST){
			$this->set("message", __('Account has been unhidden', true));
			$this->render("success");
			return;
		}
		$request = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRequest(
			$accountId,
			EntityKeyTypesUtil::JOURNAL_CAT,
			ActionLineMainOperationTypesUtil::UPDATE_ACTION,
			"#{$account['JournalCat']['code']} {$account['JournalCat']['name']}",
			"/v2/owner/chart-of-accounts/cats/".$accountId,
			['is_hidden' => __t('No')],
			['is_hidden' => __t('Yes')]
		);
		(new \App\Services\ActivityLogService())->addActivity($request);

		$this->flashMessage(__('Account has been unhidden', true), 'Sucmessage');
		$this->redirect("/v2/owner/chart-of-accounts/cats/" . $account['JournalCat']['journal_cat_id']);
	}
	
	//used in adavnced search element
	function owner_json_find() {
        
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        if (!empty($value)) {
			$result = $this->JournalCat->journalCatSearch($value);
		
            if (!empty($result))
                array_unshift($result, array(
                    'name' => __('Please Select', true) . ':',
                    'id' => '',
                    'details' => ''
                ));
            echo json_encode($result);
            die();
        }else {
			
            echo json_encode(array());
            die();
        }
    }
}
?>
