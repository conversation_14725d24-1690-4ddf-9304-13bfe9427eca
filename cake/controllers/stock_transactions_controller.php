<?php

use Izam\Daftra\Common\Utils\ProductStatusUtil;

class StockTransactionsController extends AppController {

    var $name = 'StockTransactions';
    var $helpers = array('Html', 'Form');

    public function api_index() {
		if(!check_permission(Track_Inventory)) if(IS_REST) $this->cakeError('error403');
        
        $urlParams = $this->params['url'];
        $conditions = [];
        if (!empty($urlParams['product_id'])) {
            $conditions += array('StockTransaction.product_id' => $urlParams['product_id'], 'StockTransaction.status' => StockTransaction::STATUS_PROCESSED);
        }
        if (!empty($urlParams['tracking_number_id'])) {
            $conditions += array('StockTransaction.tracking_number_id' => $urlParams['tracking_number_id'],'StockTransaction.status' => StockTransaction::STATUS_PROCESSED);
        }
        if (!empty($urlParams['store_id'])) {
            $conditions += array('StockTransaction.store_id' => $urlParams['store_id'], 'StockTransaction.status' => StockTransaction::STATUS_PROCESSED);
        }

        if(!getAuthOwner('is_super_admin')) {
            $this->loadModel('ItemPermission');
            $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE , ItemPermission::PERMISSION_VIEW);
            $conditions['StockTransaction.store_id'] = array_keys($stores_list);
        } else {
            $this->loadModel('Store');
            $inactive_stores = $this->Store->getInActiveStoreIds();
            if (!empty($inactive_stores)) {
                $conditions['NOT'] = ['StockTransaction.store_id' => $inactive_stores];
            }
        }

        if (!empty($urlParams['source_type'])) {
            $source_type = explode(",", $urlParams['source_type']);
            if (count($source_type) == 1) {
                $conditions += array('StockTransaction.source_type' => $source_type[0]);
            } else {
                $conditions += array('StockTransaction.source_type' => $source_type);
            }
        }
        if (!empty($urlParams['transaction_category_id'])) {
            $conditions['StockTransaction.transaction_category_id'] = intval($urlParams['transaction_category_id']);
        }

        if (!empty($urlParams['branch_id'])) {
            $conditions['StockTransaction.branch_id'] = (int)$urlParams['branch_id'];
        }

        $order = array('StockTransaction.received_date' => 'DESC', 'StockTransaction.id' => 'DESC');
        if (!empty($urlParams['date_from'])) {
            $conditions += array('StockTransaction.received_date >= ' => $this->StockTransaction->formatDate($urlParams['date_from']));
        }
        if (!empty($urlParams['date_to'])) {
            $conditions += array('StockTransaction.received_date <=' => $this->StockTransaction->formatDate($urlParams['date_to']) . ' 23:59:59');
        }
        $this->paginate['StockTransaction'] = array('applyBranchFind' => false, 'conditions' => $conditions);

		$transactions = $this->paginate();
		$this->set('rest_items', $transactions);
		$this->set('rest_model_name', "StockTransaction");
		$this->render("index");
    }
	
	public function api_view($id = null) {
		if(!check_permission(Track_Inventory)) if(IS_REST) $this->cakeError('error403');
		$stock_transaction = $this->StockTransaction->find("first", ["conditions" => array("StockTransaction.id" => $id)] );
		if(empty($stock_transaction)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Stock transaction", true))));
        else $this->set('rest_item', $stock_transaction);
		$this->set('rest_model_name', "StockTransaction");
		$this->render("view");
	}
	
	public function api_delete($id = null) {
		if(!check_permission(Adjust_Product_Inventory)) if(IS_REST) $this->cakeError('error403');
		$stock_transaction = $this->StockTransaction->find("first", ["conditions" => array("StockTransaction.id" => $id)] );
		if(empty($stock_transaction)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Stock transaction", true))));
		if($this->StockTransaction->delete($id)){
			$this->set("message", sprintf(__('%s has been deleted', true), __("Stock transaction", true)));
			$this->render("success");
			return;
		} else {
			$this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
		}
	}

    function owner_purchase_orders_stock($id = null)
    {
        $this->loadModel('PurchaseOrder');
        $po = $this->PurchaseOrder->find('first', ['conditions' => ['PurchaseOrder.id' => $id]]);
        if ($po['PurchaseOrder']['type'] == PurchaseOrder::Purchase_Refund) {
            $transactions = StockTransaction::getPRTransactions($id, true);

        }
        else if ($po['PurchaseOrder']['type'] == PurchaseOrder::DEBIT_NOTE) {
            $transactions = StockTransaction::getPDNTransactions($id, true);
        }
        else {
            $transactions = StockTransaction::getPOTransactions($id, true);
        }
        $transactions = $this->getTrackingData($transactions);
        $this->set('stocks', $transactions);
    }

    function owner_invoice_stock($id = null) {

        if (!check_permission(Add_New_Purchase_Orders)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel('Store');
        $this->loadModel('Invoice');
        $this->Invoice->recursive = -1;
        $invoicetype = $this->Invoice->Read('type', $id);
        if ($invoicetype['Invoice']['type'] == Invoice::Credit_Note) {
            $stocks = StockTransaction::getCreditNoteTransactions($id, true);
        }elseif($invoicetype['Invoice']['type'] == Invoice::Refund_Receipt){
            $stocks = StockTransaction::getRefundTransactions($id, true);
        } else {
            $stocks = StockTransaction::getInvoiceTransactions($id, true);
        }
        $transactions = $this->getTrackingData($stocks);
        $this->set('store_count', $this->Store->find('count', ['conditions' => ['Store.active' => 1]]));
        $this->set('stocks', $transactions);
    }

    function owner_stock_transactions($id = null, $type = 'product', $is_report = false) {

        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }

        $site = getAuthOwner();
        $urlParams = $this->params['url'];
        $this->set('staff_id', $site['staff_id']);
        
        $this->loadModel('Product');
        $this->loadModel('Store');
        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN)){
            $this->loadModel('TrackingNumber');
        }
        $this->loadModel('Invoice');
        $this->loadModel('PurchaseOrder');
        $this->loadModel('ItemPermission');
        $this->loadModel('Requisition');
        

        if ($type == 'product') {
            $product = $this->Product->find(array('Product.id' => $id));
            if (!$product) {
                $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
                $this->redirect($this->referer(array('action' => 'index')));
            }
            $conditions = array('StockTransaction.product_id' => $id, 'StockTransaction.status' => StockTransaction::STATUS_PROCESSED);
        } else if ($type == 'tracking_number') {
            $trackingNumber = $this->TrackingNumber->findById($id);
            if (!$trackingNumber) {
                $this->flashMessage(sprintf(__("%s not found.", true), __('Tracking Number', true)));
                $this->redirect($this->referer(array('action' => 'index')));
            }
            $conditions = array('StockTransaction.tracking_number_id' => $id,'StockTransaction.status' => StockTransaction::STATUS_PROCESSED);
        } else if ($type == 'store') {
            $store = $this->Store->findById($id);
            if (!$store) {
                $this->flashMessage(sprintf(__("%s not found.", true), __('Warehouse', true)));
                $this->redirect($this->referer(array('action' => 'index')));
            }
            $conditions = array('StockTransaction.store_id' => $id, 'StockTransaction.status' => StockTransaction::STATUS_PROCESSED);
        } else {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Product', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }
        $stores_list = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE , ItemPermission::PERMISSION_VIEW);
        if(!getAuthOwner('is_super_admin')) {
            $conditions['StockTransaction.store_id'] = array_keys($stores_list);
        } else {
            $inactive_stores = $this->Store->getInActiveStoreIds();
            if (!empty($inactive_stores)) {
                $conditions['NOT'] = ['StockTransaction.store_id' => $inactive_stores];
            }
        }

        if (!empty($urlParams['source_type'])) {
            $source_type = explode(",", $urlParams['source_type']);
            if (count($source_type) == 1) {
                $conditions += array('StockTransaction.source_type' => $source_type[0]);
            } else {
                $conditions += array('StockTransaction.source_type' => $source_type);
            }
        }
        if (!empty($urlParams['store_id'])) {
            $conditions['StockTransaction.store_id'] = intval($urlParams['store_id']) ;
        }
        if (!empty($urlParams['transaction_category_id'])) {
            $conditions['StockTransaction.transaction_category_id'] = intval($urlParams['transaction_category_id']);
        }
        if($type !== 'tracking_number') {
            $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
            $this->set('enable_multi_units', $enable_multi_units);
            if ($enable_multi_units && $type == 'product') {
                $main_unit_factor = $urlParams['unit_factor_id'];
                if (empty($main_unit_factor)) { //Main unit with factor_id of 0 or empty
                    $this->loadModel('UnitTemplate');
                    $unit_template = $this->UnitTemplate->findById($product['Product']['unit_template_id']);
                    $unit_factor = ['UnitFactor' => [
                        'id' => 0,
                        'factor_name' => $unit_template['UnitTemplate']['main_unit_name'] ?? '',
                        'small_name' => $unit_template['UnitTemplate']['unit_small_name'] ?? '',
                        'factor' => 1,
                    ]];
                    $factor = 1;
                } else {
                    $this->loadModel('UnitFactor');
                    $unit_factor = $this->UnitFactor->findById($main_unit_factor);
                    $factor = (empty($unit_factor['UnitFactor']['factor']) ? 1 : $unit_factor['UnitFactor']['factor']);
                }
                debug($main_unit_factor);
                debug($unit_factor);
                $this->set('unit_factor', $unit_factor);
                $this->set('factor', $factor);
                $this->set('main_unit_factor', $main_unit_factor);
            }
        }
        

        $order = array('StockTransaction.received_date' => 'DESC', 'StockTransaction.id' => 'DESC');
        $reverse = true;
        if (!empty($this->params['url']['order']) || (in_array($this->params['named']['direction'], ['asc', 'ASC']) && $this->params['named']['sort'] == 'StockTransaction.received_date')) {
            $order = array('StockTransaction.received_date' => 'ASC', 'StockTransaction.id' => 'ASC');
            $reverse = false;
        }

        if (!empty($this->params['url']['date_to'])) {
            $conditions += array('StockTransaction.received_date <=' => $this->StockTransaction->formatDate($this->params['url']['date_to']) . ' 23:59:59');
        }
        if (!empty($this->params['url']['date_from'])) {
            $conditions += array('StockTransaction.received_date >= ' => $this->StockTransaction->formatDate($this->params['url']['date_from']));
        }
        if (isset($this->passedArgs['sort'])) {
            unset($this->passedArgs['sort']);
        }
        unset($this->params['url']['order']);
        $this->paginate['StockTransaction'] = array('applyBranchFind' => false, 'conditions' => $conditions,
            'url' => Router::url(array('action' => 'stock_transactions', $id)),
            'order' => $order,
            'limit' => 100,
        );

        $transactions = $this->paginate();
        if (!empty($transactions) && ($type == 'product' || $type =='tracking_number')) {
            $start = ($reverse ? count($transactions) - 1 : 0);
            $end = ($reverse ? 0 : count($transactions) - 1);
            $step = ($reverse ? -1 : 1);

            $last_stocks = array();
            for ($i = $start; true; $i = $i + $step) {
                $product_id = $transactions[$i]['StockTransaction']['product_id'];
                if (!isset($last_stocks[$product_id]))
                    $last_stocks[$product_id] = StockTransaction::getProductStockBefore($product_id, $transactions[$i]['StockTransaction']['id'],$this->params['url']['store_id']);

                $transactions[$i]['StockTransaction']['stock_after'] = $last_stocks[$product_id] + ( (empty($transactions[$i]['StockTransaction']['ignored'])) ? $transactions[$i]['StockTransaction']['quantity'] : 0);
                $transactions[$i]['StockTransaction']['order_no'] = StockTransaction::getOrderNo($transactions[$i]);
                $last_stocks[$product_id] = $transactions[$i]['StockTransaction']['stock_after'];
                if ($i == $end)
                    break;
            }
        }
        $cached_product_names = [] ; 
        foreach ( $transactions as $k => $t ){
            if ($t['StockTransaction']['source_type'] == StockTransaction::SOURCE_BUNDLE)
            {
                if ( empty($cached_product_names[$t['StockTransaction']['ref_id']]))
                {
                    $cached_product_names[$t['StockTransaction']['ref_id']] = $this->Product->find('first' , ['recursive' => -1 , 'fields' => 'name' , 'conditions' => ['id' => $t['StockTransaction']['ref_id'] ] ])['Product']['name'] ; 
                }
                $transactions[$k]['StockTransaction']['bundle_product_name'] = $cached_product_names[$t['StockTransaction']['ref_id']] ; 
            }
        }


        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());

        $this->set('is_report', $is_report);
        $this->set('type', $type);
        $this->loadModel('TransactionCategory');
        $this->set('store_count', $this->Store->find('count', ['conditions' => ['Store.active' => 1]]));
        $cat_list = $this->TransactionCategory->find('list', ['conditions' => ['deleted = 0']]);
        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN)){
            $transactions = $this->getTrackingData($transactions);
        }
        $this->set('cat_list', $cat_list);
        $this->set('sources', $this->StockTransaction->getSources());
        $this->set('transactions', $transactions);
        $this->set('default_currency',$this->StockTransaction->get_default_currency() );
        $this->set('isCompound', settings::getValue(InventoryPlugin, 'bundle_type') === settings::OPTION_BUNDLE_COMPOUND);
        $this->set('product', $product);
    }

    function getTrackingData(&$transactions)
    {
        if(!ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            return $transactions;
        }
        foreach ($transactions as &$transaction) {
            $product_type = $transaction['Product']['tracking_type'];
            $trackingNumberModel = GetObjectOrLoadModel('TrackingNumber');
            $tracking_data = $trackingNumberModel->getTrackingData($transaction['StockTransaction']['tracking_number_id'],$product_type);
 
            if(!count($tracking_data)) continue;

            $transaction['TrackingNumber']['text'] = $tracking_data['text'];
            $transaction['TrackingNumber']['url']  =$tracking_data ['url'];
        }
        return $transactions;
    }


    function owner_get_store_products($store_id)
    {
        $this->loadModel('Product');
        $product_ids = array_values($this->StockTransaction->find('list' , ['limit'=>10000,'recursive' => -1, 'fields' => 'product_id' , 'conditions' => ['StockTransaction.store_id' => $store_id], 'group' => 'product_id' ] ));
        $productsConditions = ['(Product.status = '. ProductStatusUtil::STATUS_ACTIVE .' or Product.status is null)','track_stock' => 1];
        if (ifPluginActive(BranchesPlugin)) {
            if (isModelSharable('Product')) {
                $productsConditions['Product.branch_id'] = getStaffBranchesIDs();
            } else {
                $productsConditions['Product.branch_id'] = getCurrentBranchID();
            }
        }
        $products = $this->Product->getInvoiceProductList($product_ids,$productsConditions,false);
        die(json_encode($products));
    }

    function owner_bundle_transaction_details($transactionId) {
        $transaction = $this->StockTransaction->findById($transactionId);
        $bundleType = settings::getValue(InventoryPlugin, 'bundle_type');
        $childTransactions = $this->StockTransaction->find('all', ['conditions' => ['StockTransaction.order_id' => $transactionId, 'StockTransaction.source_type' => StockTransaction::SOURCE_BUNDLE]]);
        $detailsItems = [];
        $parentQuantity = abs($transaction['StockTransaction']['quantity']);
        $total = 0;
        foreach ($childTransactions as $childTransaction) {
            $childQuantity = abs($childTransaction['StockTransaction']['quantity']);
            $price = $childTransaction['StockTransaction']['price'];
            $subtotal = $price * $childQuantity;
            $total += $subtotal;
            $detailsItems['items'][] = [
                'product' => $childTransaction['Product']['name'],
                'quantity' => $childQuantity,
                'quantity_per_unit' => $childQuantity / $parentQuantity,
                'price' => $price,
                'subtotal' => $subtotal,
            ];
        }
        $avg = $parentQuantity != 0 ? $total / $parentQuantity : 0;
        $detailsItems['average'] = $avg;
        $detailsItems['total'] = $total;
        $this->set('currencyCode', $this->StockTransaction->get_default_currency());
        $this->set('detailsItems', $detailsItems);
    }
}

?>
