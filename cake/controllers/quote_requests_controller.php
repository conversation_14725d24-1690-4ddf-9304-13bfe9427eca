<?php

class QuoteRequestsController extends AppController {

    var $name = 'QuoteRequests';
    var $helpers = array('Fck');
    var $components = array('Email', 'SysEmails');

    function owner_index($listing = '') {

        $row = $this->QuoteRequest->SupplierDirectory->find('first', array('conditions' => array('SupplierDirectory.site_id' => getCurrentSite('id'))));
        if ($listing == '') {
            $this->redirect(array('controller' => 'invoices', 'action' => 'estimates'));
        } elseif ($listing == 'sent') {
            $this->set('title_for_layout',  h(__('Sent Quote Requests', true)));
            $conditions['QuoteRequest.site_id'] = getCurrentSite('id');
        } elseif ($listing == 'received') {
            $this->set('title_for_layout',  h(__('Received Quote Requests', true)));
            $conditions['QuoteRequest.supplier_directory_id'] = $row['SupplierDirectory']['id'];
        }
		
	
		
        $this->set('listing', $listing);
        $this->paginate['QuoteRequest'] = array('order'=>'QuoteRequest.id DESC','conditions' => $conditions);
        $this->set('quote_requests', $this->paginate());
        $this->set('content', $this->get_snippet('quote-requests'));

        $this->__settings();
    }

    function owner_add($id = null) {
        $site_limit = $this->Site->getLimits();
        $qr_daily_limit = ($site_limit['invoices']['limit'] / 3);
        $qr_monthly_limit = ($site_limit['invoices']['limit'] / 2);
        $daily_quote = $this->QuoteRequest->find('count', array('conditions' => array('date(`QuoteRequest`.`created`)' => date('Y-m-d'), 'QuoteRequest.site_id' => getCurrentSite('id'))));
        $monthly_quote = $this->QuoteRequest->find('count', array('conditions' => array('date(`QuoteRequest`.`created`) >' => date('Y-m-1'), 'QuoteRequest.site_id' => getCurrentSite('id'))));
		
		
		$daily_to_the_same = $this->QuoteRequest->find('count', array('conditions' => array('date(`QuoteRequest`.`created`)' => date('Y-m-d'), 'QuoteRequest.supplier_directory_id'=>$id, 'QuoteRequest.site_id' => getCurrentSite('id'))));
		if ($daily_to_the_same > 2) {
			$this->_flashLimitMessage(sprintf(__('You have sent too much requests to the same supplier', true), $monthly_quote));
            $this->redirect(array('action' => 'index'));
		}
		
        
        if ($qr_monthly_limit < $monthly_quote) {
        //    $this->add_stats(STATS_ERROR_IVOICE_LIMITATION_REACHED, array($check['message']));
            $this->_flashLimitMessage(sprintf(__('You can only send %d Quote Request per month.', true), $monthly_quote));
            $this->redirect(array('action' => 'index'));
        }
        if ($qr_daily_limit < $daily_quote) {
          //  $this->add_stats(STATS_ERROR_IVOICE_LIMITATION_REACHED, array($check['message']));
            $this->_flashLimitMessage(sprintf(__('You can only send %d quote requests per day.', true), $qr_daily_limit));
            $this->redirect(array('action' => 'index'));
        }


        $row = $this->QuoteRequest->SupplierDirectory->read(null, $id);
        if (empty($row)) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirect(array('controller' => 'supplier_directories', 'action' => 'search'));
        }
        $this->set('row', $row);
        $this->set('id', $id);
        if (!empty($this->data)) {
            $this->QuoteRequest->create();
            unset($this->data['QuoteRequestItem']['Tempid']);
            $this->data['QuoteRequest']['site_id'] = getCurrentSite('id');
            $this->data['QuoteRequest']['supplier_directory_id'] = $id;

            if ($this->QuoteRequest->saveall($this->data, array('deep' => true))) {
                $id = $this->QuoteRequest->id;
                $view = new View($this, false);
                $QuoteRequest = $view->element('quote_requests/quote_table', array('items' => $this->data['QuoteRequestItem']));

                $this->_send_email_to_user($row['SupplierDirectory'], $id, $QuoteRequest);
                $this->flashMessage(__('Thank you, Your quote request has been sent to the supplier, he is now able to view your details, send the quote, and share his contact details with you', true), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(__('An Error occurred, Could not send Quote Request', true));
            }

//            debug($this->data);
        }
		
		$this->set('title_for_layout',  __('Send a quote request', true));
    }

    function _send_email_to_user($Supplier, $RequestID, $QuoteRequest) {
        $site = getAuthOwner();
        $this->_setBranded();
        return $this->SysEmails->QuoteRequest($Supplier, $RequestID, $site, $QuoteRequest);
    }

    function owner_view($id = null) {
        $sd = $this->QuoteRequest->SupplierDirectory->find('first', array('conditions' => array('SupplierDirectory.site_id' => getCurrentSite('id'))));

		
		

        $this->__settings();
        $row = $this->QuoteRequest->read(null, $id);

        if (empty($row)) {
            $this->flashMessage(__('Quote Request not found', true));
            $this->redirect(array('action' => 'index'));
        }
        if ($row['QuoteRequest']['supplier_directory_id'] == $sd['SupplierDirectory']['id']) {
            $this->QuoteRequest->id = $id;
            $this->QuoteRequest->saveField('view_date', date("Y-m-d H:i:s"));
            $this->QuoteRequest->saveField('status', QuoteRequest::QUOTE_REQUEST_Viewed);
			$this->loadModel('Site');
			$sender_site=$this->Site->read(null,$row['QuoteRequest']['site_id']);
			$this->set('sender_site',$sender_site);
        } 
		else if($row['QuoteRequest']['site_id']==getCurrentSite('id'))
		{
			//it is ok
		}
		else
		{
			 $this->flashMessage(__('An Error occurred', true));
			 $this->redirect(array('action' => 'index'));
		}
		
		$this->set('title_for_layout',  __('View quote request', true));

        $this->set('row', $row);
        $this->set('sd', $sd);
    }

    function owner_convert_to_estimate($id = null) {
        $sd = $this->QuoteRequest->SupplierDirectory->find('first', array('conditions' => array('SupplierDirectory.site_id' => getCurrentSite('id'))));


        $this->__settings();
        $row = $this->QuoteRequest->read(null, $id);

        if (empty($row)) {
            $this->flashMessage(__('Quote Request not found', true));
            $this->redirect(array('action' => 'index'));
        }
        if ($row['QuoteRequest']['supplier_directory_id'] != $sd['SupplierDirectory']['id']) {
            $this->flashMessage(__('Quote Request not found', true));
            $this->redirect(array('action' => 'index'));
        }
        $this->Session->write('estimate_quote_id', $id);
        $this->loadModel('Client');
        $conditions['OR'] = array('Client.email' => $row['Site']['email'], 'Client.original_site_id' => $row['Site']['id']);
        $client = $this->Client->find('first', array('conditions' => $conditions));
        if (isset($client['Client']['id'])) {
            $this->Session->write('Invoice.client_id', $client['Client']['id']);
        } else {
            $clientdata['Client'] = $row['Site'];
            $clientdata['Client']['original_site_id'] = $row['Site']['id'];
            $clientdata['Client']['default_currency_code'] = $row['Site']['currency_code'];
            unset($clientdata['Client']['id']);
            unset($clientdata['Client']['site_logo']);
            unset($clientdata['Client']['invoice_logo']);
            unset($clientdata['Client']['subdomain']);
            unset($clientdata['Client']['timezone']);
            unset($clientdata['Client']['date_format']);
            unset($clientdata['Client']['password']);
            unset($clientdata['Client']['last_login']);
            unset($clientdata['Client']['plan_id']);
            unset($clientdata['Client']['expiry_date']);
            unset($clientdata['Client']['created']);
            unset($clientdata['Client']['modified']);
            unset($clientdata['Client']['db_config']);
            unset($clientdata['Client']['last_ip']);
            debug($clientdata);
            $newclient = $this->Client->saveClient($clientdata);
            $this->Session->delete("Invoice");
            $this->Session->write('Invoice.client_id', $newclient['data']['id']);
            $this->flashMessage(__('A new client has been created', true), 'Sucmessage');
        }
        $this->Session->delete("InvoiceItem");
        foreach ($row['QuoteRequestItem'] as $key => $details) {
            $this->Session->write("InvoiceItem.$key.item", $details['name']);
            $this->Session->write("InvoiceItem.$key.quantity", $details['quantity']);
            $this->Session->write("InvoiceItem.$key.unit_price", 0);
        }
        $this->redirect(array('controller' => 'invoices', 'action' => 'add_estimate'));
    }

    function __settings() {
        $this->loadModel('Country');
        $this->set('countries', $this->Country->getCountryListName());
        $this->set('statuses', QuoteRequest::Statuses());
        $this->set('statusescolor', QuoteRequest::getStatusescolor());
    }

}
