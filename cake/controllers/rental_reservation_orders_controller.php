<?php



/**
 * @property Tooltip $Tooltip
 */
class RentalReservationOrdersController extends AppController {

    var $name = 'RentalReservationOrders';
    var $helpers = array('Fck');


    /**
     * @var RentalReservationOrder
     */
    var $RentalReservationOrder;

    function client_index($listing = '') {
        $this->RentalReservationOrder->recursive = 3;
        $conditions = array();
        $conditions['RentalReservationOrder.client_id'] = getAuthClient('id');
        $conditions['RentalReservationOrder.is_temp'] = 0;
        $this->paginate['RentalReservationOrder']['order'] = 'RentalReservationOrder.created DESC';
        $reservations = $this->paginate('RentalReservationOrder', $conditions);
        foreach ($reservations as &$reservation) {
            $reservation['RentalUnitBooking']['unit_name'] = $reservation['RentalUnitBooking']['RentalUnit']['name'];
            $reservation['RentalUnitBooking']['unit_type'] = $reservation['RentalUnitBooking']['RentalUnit']['RentalUnitType']['name'];
        }

        App::import('Component', 'ApiRequestsComponent');
        $api = new ApiRequestsComponent();
        $priceRequest = array_map(function ($reservation) {
            return [
                'id' =>  $reservation['RentalUnitBooking']['RentalUnit']['unit_type_id'],
                'from' => $reservation['RentalReservationOrder']['start_date'],
                'to' =>  $reservation['RentalReservationOrder']['end_date'],
            ];
        }, $reservations);
        $prices = $api->request('/v2/api/owner/rental/get-unit-types-availability-and-pricing?all_prices=1', false, 'POST', ['unit_types' => $priceRequest]);
        foreach ($reservations as $key => &$reservation) {
            $reservation['RentalReservationOrder']['total'] = $prices[$key]["price"];
        }
        $this->set('reservations', $reservations);
        $paymentStatuses = Invoice::getPaymentStatuses();
        unset($paymentStatuses[-1]);
        $this->set('paymentStatuses', $paymentStatuses);
        $this->loadModel('Currency');
        $this->set('defaultCurrencyCodes', $this->Currency->getCurrencyList());

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Reservations Orders', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->set('title_for_layout',  __('Reservations Orders', true));
    }


    function client_view($id = false) {
        App::import('Vendor', 'settings');
        $this->set('client_settings',settings::getPluginValues(ClientsPlugin));
        $this->loadModel('Site');
        $site = $this->Site->findById(getAuthClient('site_id'));
        $this->set('site', $site['Site']);
        $this->_setBranded();


        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Reservation Orders', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('View Reservation', true);
        $this->crumbs[1]['url'] = '#';
        $this->set('id', $id);
    }
}
