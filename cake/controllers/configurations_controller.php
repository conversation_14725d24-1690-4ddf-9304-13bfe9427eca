<?php
class ConfigurationsController extends AppController{
	var $name = 'Configurations';
	var $uses = array();
	var $extensions = array(
		'txt' => 'TEXT',
		'pwd' => 'PASSWORD'
	);

	function admin_edit(){
		$formData = $this->params['form'];
		$this->set('extensions', $this->extensions);
		if (!empty($formData)){
			$output = '';
			$confFile = APP . 'app_config.ini';
			foreach($formData as $key => $value){
				$key = preg_replace("/_/", '.', $key, 1);
				$ext = substr($key, 0, strpos($key, '.'));
				if ($ext == 'pwd'){
					if (!empty($value)){
						$value = md5($value);
					} else {
						$value = $this->config[$key];
					}
				}
				$value = str_replace('"', '""', $value);
				$output .= "$key = \"$value\"\n";
			}
			if (is_writable($confFile)){
				if (file_put_contents($confFile, $output) !== false){
					$this->Session->setFlash('Configurations saved successfully.', 'default', array('class' => 'Sucmessage'));
				}
			} else {
					$this->Session->setFlash('The configuration file is not writable.', 'default', array('class' => 'Errormessage'));
			}
			$this->redirect(array('action' => 'admin_edit'));
		}
	}
}

?>