<?php

use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

/**
 * @property Invoice $Invoice
 */
class PostsController extends AppController {

    var $name = 'Posts';
    var $helpers = array('Html', 'Form', 'Fck', 'Mixed');
    var $components = array('Cookie', 'Email', 'SysEmails');

    function ajax_upload() {
        
    }

    function view($id, $hash) {
        $this->_checkHash($id, $hash);
    }

    function _checkHash($id, $hash) {
        $post = $this->Post->read(null, $id);
        if (!$post) {
            $this->redirect(array('controller' => 'clients', 'action' => 'login'));
        }
        $this->loadModel('Client');
        $this->loadModel ( 'Invoice');
        $item_type = $post['Post']['item_type'];
        if ( $item_type == Post::CLIENT_TYPE){
            $client_login_hash = $this->Client->getHash($post['Post']['item_id']);
            $client_id = intval ($post['Post']['item_id']);
        }else {
            $reread_invoice = $this->Invoice->findById ($post['Post']['item_id']);
            $client_login_hash = $this->Client->getHash($reread_invoice['Invoice']['client_id']);
            $client_id = $reread_invoice['Invoice']['client_id'];
        }
        
        if ($client_login_hash == $hash) {
            $client = $this->Client->findById($client_id);
            $this->Client->reload_session($client);

            if ( $item_type == Post::CLIENT_TYPE ){
                $this->redirect(array('action' => 'index', $id, 'client' => 1));
            }else {
                $this->loadModel ( 'FollowUpStatus');
                $this->redirect ( FollowUpStatus::$types_diff[$item_type]['client_view'].$post['Post']['item_id']);
            }
        } else {
            $this->redirect(array('controller' => 'clients', 'action' => 'login'));
        }
    }

    function __set_data($item_type) {
        $this->set('item_type', $item_type);
        $this->loadModel('FollowUpStatus');
        $statuses = $this->FollowUpStatus->getLisWithColor($item_type);
        if (check_permission(Edit_General_Settings)) {
            $statuses[] = array('name' => false, 'value' => false, 'data-divider' => "true");
            $statuses[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> '.__('Edit Statuses List',true).'</span>', 'name' => __('Edit Statuses List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
        }
        $this->set('statuses', $statuses);
        // $this->set('colors', $this->FollowUpStatus->getcolorlist());
        $this->set('partners_label', FollowUp::$partner_types[FollowUp::$types_data[$item_type]['partner_type']]['label']);

        $this->loadModel('FollowUpAction');
        $actions = $this->FollowUpAction->getList($item_type);
        if (check_permission(Edit_General_Settings)) {
            $actions[] = array('name' => false, 'value' => false, 'data-divider' => "true");
            $actions[] = array('data-content' => '<span class="text"><i class="fa fa fa-cog"></i> '.__('Edit Actions List',true).'</span>', 'name' => __('Edit Actions List', true), 'value' => '-1', "data-icon" => "fa fa-cog");
        }
        $this->set('actions', $actions);

        // $this->loadModel('File');
        //$this->set('upload_settings', $this->File->upload_settings);
    }

    function client_list_post($item_type, $item_id) {
        $this->set ( 'client' , 1 ) ;
		$ajax = $this->RequestHandler->isAjax();
        $this->Post->recursive = 3;
        $params = $this->params['url'];
        $this->Post->bindAttachmentRelation('post');

        if ( $item_type == Post::CLIENT_TYPE){
            $this->loadModel ('Invoice');
            $client_invoices = $this->Invoice->find ( 'list' , ['conditions' => ['Invoice.client_id' => $item_id ]]);
            
            $invoice_posts = $this->Post->find ( 'list' ,['fields' => array('Post.item_id'),  'conditions' => ['Post.item_type'=>Post::CLIENT_TYPE,'Post.item_id' => array_keys ($client_invoices), 'Post.client_permissions' => 1 ]] );
            
            if ( !empty ( $invoice_posts ) ){
                $conditions['OR'] = [array ('AND'=>array('Post.item_id' => $item_id ,'Post.item_type'=>Post::CLIENT_TYPE)),  'Post.id' =>  array_keys ( $invoice_posts )];
            }else {
                $conditions['Post.item_type'] = $item_type;
                $conditions['Post.item_id'] = $item_id;
            }
        }else {
            $conditions = array('Post.item_type' => $item_type );
            $conditions['Post.item_id'] = $item_id;
        }
        if (isset($params['linked_note'])) {
            $linked_post = $this->Post->find('first', array('order' => 'Post.id desc', 'conditions' => array_merge(array('Post.id' => $params['linked_note']), $conditions)));

            $this->set('linked_post', $linked_post);
        }


        $this->loadModel('FollowUpAction');
        $this->set('actions', $this->FollowUpAction->getList($item_type));

        $this->loadModel('FollowUpStatus');

        if ( $item_type == Post::CLIENT_TYPE)
        {
            $statuses = $this->FollowUpStatus->getLisWithColor([1,2,3], false, false, true);
        }else {
            $statuses = $this->FollowUpStatus->getLisWithColor($item_type, false, false, true);
        }
        

        foreach ($statuses as $status) {
            $newstatus[$status['value']] = $status;
        }

//         debug($newstatus);
        $this->set('newstatus', $newstatus);
        $this->set('statuses', $statuses);

        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('ItemStaff');
        $this->loadModel('Staff');

        $types = Post::getItemTypes();

        if (!isset($types[$item_type])) {
			
			
            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }

        $type = $types[$item_type];

        $conditions1 = array('ItemStaff.item_type' => $item_type, 'ItemStaff.item_id' => $item_id);
        $staff_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,staff_id', 'conditions' => $conditions1)));



        if (!check_permission($type['view_permission']) && check_permission($type['view_assiged_permission'])) {
            if(!empty($staff_id)) {
                $conditions['Post.staff_id'] = $staff_id;
            }
        }

        if (isset($params['action']) and !empty($params['action'])) {
            $conditions['Post.action_id'] = explode(',', $params['action']);
        }

        if (isset($params['followupid']) and !empty($params['followupid'])) {
            $conditions['Post.status_id'] = explode(',', $params['followupid']);
        }
        if (isset($params['staff_id']) and !empty($params['staff_id'])) {

            $conditions['Post.staff_id'] = explode(',', $params['staff_id']);
        }


        if (isset($params['date_from']) and !empty($params['date_from'])) {
            $conditions['Post.date >='] = $params['date_from'];
        }


        if (isset($params['date_to']) and !empty($params['date_to'])) {
            $conditions['Post.date <='] = $params['date_to'];
        }
        $conditions['OR'] = ['Post.client_permissions' => 1, 'AND' => ['Post.share_with_partner' => 1, 'Post.partner_id' => getAuthClient('id'), 'Post.partner_type' => FollowUp::CLIENT_PARTNER_TYPE]];
//        $conditions['Post.client_permissions'] = 1;
//        $conditions['Post.share_with_partner'] = 1;
//        $conditions['Post.partner_id'] = getAuthClient('id');
//        $conditions['Post.partner_type'] = FollowUp::CLIENT_PARTNER_TYPE;
        if (isset($params['order'])) {
            if ($params['order'] == "1") {
                $order = 'Post.date asc, Post.id asc';
            } else {
                $order = 'Post.date desc, Post.id desc';
            }
        } else {
            $order = 'Post.date desc, Post.id desc';
        }
        if (empty($_GET['page'])) {
            $page = 1;
        } else {
            $page = intval($_GET['page']) == 0 ? 1 : $_GET['page'];
        }
        $per_page = 30;

        $this->Post->bindModel([
            'hasMany' => [
                'GeneralAttachment' => [
                    'foreignKey' => 'entity_id',
                    'conditions' => [
                        'GeneralAttachment.entity_key' => EntityKeyTypesUtil::POST
                    ]
                ]
            ]
        ]);
        
        $posts['data'] = $this->Post->find('all', array('offset' => ($page - 1) * $per_page, 'limit' => $per_page, 'order' => $order, 'conditions' => $conditions));

        $posts['pagination']['total'] = $this->Post->find('count', array('conditions' => $conditions, 'order' => $order));
        $posts['pagination']['total_pages'] = ceil($posts['pagination']['total'] / $per_page);
        $this->set('posts', $posts);
        $this->set('type', $type);
        $this->set('staffs', $this->Staff->getList());
        $this->set('staff_id', $staff_id);
//        print_pre($this->Post->lastQuery(true));
//        die();
        $this->render ('/posts/owner_list_post');
    }
    
    function owner_list_post($item_type, $item_id, $post_id = 0) {
		$ajax = $this->RequestHandler->isAjax();
        $this->Post->recursive = 3;
        $params = $this->params['url'];
        $this->Post->bindAttachmentRelation('post');

 
        if ( $item_type == Post::CLIENT_TYPE){
            $this->loadModel ('Invoice');
            $client_invoices = $this->Invoice->find ( 'list' , ['conditions' => ['Invoice.client_id' => $item_id ]]);
            
            $invoice_posts = $this->Post->find ( 'list' ,['fields' => array('Post.item_id'),  'conditions' => ['Post.item_type'=>array(Post::INVOICE_TYPE,Post::ESTIMATE_TYPE),'Post.item_id' => array_keys ($client_invoices), 'Post.client_permissions' => 1 ]] );
            
            if ( false && !empty ( $invoice_posts ) ){
                $conditions['OR'] = [array ('AND'=>array('Post.item_id' => $item_id ,'Post.item_type'=>Post::CLIENT_TYPE)),  'Post.id' =>  array_keys ( $invoice_posts )];
            }else {
                $conditions['Post.item_id'] = $item_id;
                $conditions['Post.item_type'] = Post::CLIENT_TYPE;
            }
        }else {
            $conditions = array('Post.item_type' => $item_type );
            $conditions['Post.item_id'] = $item_id;
        }
        if (isset($params['linked_note'])) {
            $linked_post = $this->Post->find('first', array('order' => 'Post.id desc', 'conditions' => array_merge(array('Post.id' => $params['linked_note']), $conditions)));

            $this->set('linked_post', $linked_post);
        }

        $conditions = array_merge($conditions, $this->_filter_params());
        $this->loadModel('FollowUpAction');
        $this->set('actions', $this->FollowUpAction->getList($item_type));

        $this->loadModel('FollowUpStatus');

        if ($item_type == Post::CLIENT_TYPE) {
            $statuses = $this->FollowUpStatus->getLisWithColor([1,2,3], false, false, true);
        } elseif ($item_type == Post::WORKFLOW_TYPE) {
            $this->loadModel('WorkOrder');
            $workflow = $this->WorkOrder->find(['WorkOrder.id' => $item_id]);

            if ($workflow) {
                $workflowTypeId = $workflow['WorkOrder']['workflow_type_id'];
                $entityKey = getWorkFlowTypeEntityName($workflowTypeId);
                $statuses = $this->FollowUpStatus->getLisWithColor(0, false, false, true, [], $entityKey);
            }

        } else {
            $statuses = $this->FollowUpStatus->getLisWithColor([1,2,3], false, false, true);
        }
        

        foreach ($statuses as $status) {
            $newstatus[$status['value']] = $status;
        }

//         debug($newstatus);
        $this->set('newstatus', $newstatus);
        $this->set('statuses', $statuses);

        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('ItemStaff');
        $this->loadModel('Staff');

        $types = Post::getItemTypes();

        if (!isset($types[$item_type])) {
			
			
            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }

        $type = $types[$item_type];
		debug ( $type ) ;

        if ($post_id == 0 && (!check_permission($type['view_permission']) && !check_permission($type['view_assiged_permission']))) {
			
			if ( $ajax )
			{
				die ( __("You don't have permission to view", true)) ; 
			}else {
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
				$this->redirect('/');
			}
            
        }

        $conditions1 = array('ItemStaff.item_type' => $item_type, 'ItemStaff.item_id' => $item_id);
        $staff_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,staff_id', 'conditions' => $conditions1)));



        if ($staff_id != 0 && !in_array($staff_id, $staff_list) && !check_permission($type['add_permission'])&& (!check_permission($type['view_permission']) && !check_permission($type['view_assiged_permission'])) ) {
			 
            if ( $ajax )
			{
				die ( __("You don't have permission to view", true)) ; 
			}else {
				$this->flashMessage(__('You are not allowed to view this page', TRUE));
				$this->redirect('/');
			}
        }


        if (!check_permission($type['view_permission']) && check_permission($type['view_assiged_permission'])) {
            $conditions['Post.staff_id'] = $staff_id;
        }

        if (isset($params['action']) and !empty($params['action'])) {
            $conditions['Post.action_id'] = explode(',', $params['action']);
        }

        if (isset($params['followupid']) and !empty($params['followupid'])) {
            $conditions['Post.status_id'] = explode(',', $params['followupid']);
        }
        if (isset($params['staff_id']) and !empty($params['staff_id'])) {
            $conditions['Post.staff_id'] = explode(',', $params['staff_id']);
        }


        if (isset($params['date_from']) and !empty($params['date_from'])) {
            $conditions['Post.date >='] = $params['date_from'];
        }


        if (isset($params['date_to']) and !empty($params['date_to'])) {
            $conditions['Post.date <='] = $params['date_to'];
        }


        if (isset($params['order'])) {
            if ($params['order'] == "1") {
                $order = 'Post.date asc, Post.id asc';
            } else {
                $order = 'Post.date desc, Post.id desc';
            }
        } else {
            $order = 'Post.date desc, Post.id desc';
        }
        if (empty($_GET['page'])) {
            $page = 1;
        } else {
            $page = intval($_GET['page']) == 0 ? 1 : $_GET['page'];
        }
        debug ( $conditions ) ;
        $per_page = 30;

        $this->Post->bindModel([
            'hasMany' => [
                'GeneralAttachment' => [
                    'foreignKey' => 'entity_id', 
                    'conditions' => [
                        'GeneralAttachment.entity_key' => EntityKeyTypesUtil::POST
                    ]
                ]
            ]
        ]);

        $posts['data'] = $this->Post->find('all', array('offset' => ($page - 1) * $per_page, 'limit' => $per_page, 'order' => $order, 'conditions' => $conditions));
        $posts['pagination']['total'] = $this->Post->find('count', array('conditions' => $conditions, 'order' => $order));
        $posts['pagination']['total_pages'] = ceil($posts['pagination']['total'] / $per_page);
        $this->set('posts', $posts);
        $this->set('type', $type);
        $this->set('staffs', $this->Staff->getList());
        $this->set('staff_id', $staff_id);
		
		
		App::import('Vendor', 'notification_2');
		$post_ids = $this->Post->find('list', array( 'conditions' => $conditions));
		
		$this->loadModel('ItemsTag');
		$this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_NOTE);
		$tags = $this->ItemsTag->get_items_tags($post_ids,ItemsTag::TAG_ITEM_TYPE_NOTE);
		$this->set('tags',$tags);
		
		if(count($post_ids));
		{
			NotificationV2::view_notificationbyref(array_values($post_ids), NotificationV2::NOTI_UPDATE_NOTE);
		}
        $actions = $this->FollowUpAction->getList($item_type);

        $this->set('actions', $actions);
    }

    function __formCommon($item_type, $item_id, $post_id = 0, $follow_up_id = 0)
    {
        $this->loadModel('FollowUpReminder');
        $this->loadModel('Staff');
        $this->set('partners_label', FollowUpReminder::$partner_types[FollowUpReminder::$types_data[$item_type]['partner_type']]['label']);
        $this->set('staffs', $this->Staff->getList());
    }

    function owner_post($item_type, $item_id, $post_id = 0, $follow_up_id = 0) {
        ///////sending the related appointment assigned staff to view/////////
        if(!$this->data && $follow_up_id != 0){
            $this->loadModel('ItemStaff');
            $items = $this->ItemStaff->find('all', array('conditions' =>['item_type'=>9, 'item_id'=>$follow_up_id], 'fields'=>['staff_id']));
            $ids_arr = [];
            $current_site = getCurrentSite();
            foreach ($items as $value){
                if($value['ItemStaff']['staff_id'] != 0 && $value['ItemStaff']['staff_id'] != $current_site['id']){
                    $ids_arr [] = $value['ItemStaff']['staff_id'];
                }
            }
            $this->set ('appointment_assigned_staff_ids',$ids_arr);
        }
        //////end sending the related appointment assigned staff to view//////

        $this->loadModel ( 'FollowUpStatus');
        App::import('Vendor', 'settings');
        
        if ( $item_type == Post::INVOICE_TYPE)
        {
            $this->set ( 'show_status' , settings::getValue(InvoicesPlugin, 'enable_invoice_status' ) ) ;
        }else if ( $item_type == Post::ESTIMATE_TYPE)
        {
            $this->set ( 'show_status' ,settings::getValue(InvoicesPlugin, 'enable_estimate_status' ));
        }else if ( $item_type == Post::SALES_ORDER_TYPE)
        {
            $this->set ( 'show_status' ,settings::getValue(InvoicesPlugin, 'enable_sales_order_status' ));
        }else {
            $this->set ( 'show_status' , true ) ;
        }
        $this->loadModel ('FollowUpStatus');
        $url_params = $this->params['url'];
        $this->set('url_params', $url_params);

        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('ItemStaff');

        $types = Post::getItemTypes();

        if (!isset($types[$item_type])) {
			if(IS_REST) $this->cakeError("error400", ["message"=>__('Incorrect Request', TRUE)]);
            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }

        $type = $types[$item_type];
        $this->set('type',$type);


        if ($post_id == 0 && (!check_permission($type['add_permission']) && !check_permission($type['add_assiged_permission']))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if ($post_id != 0 && (!check_permission($type['edit_permission']) && !check_permission($type['edit_assiged_permission']))) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        $this->loadModel($type['model']);
        $item = $this->{$type['model']}->read(null, $item_id) ?: $this->{$type['model']}->read(null, $follow_up_id);
        $this->Post->bindAttachmentRelation('post');
		$this->Post->relatedItem = $item;
        $assigned_item_id = $item_id ; 
        $assigned_item_type = $item_type ; 
        if ( in_array($item_type ,  [Post::INVOICE_TYPE,Post::ESTIMATE_TYPE, Post::SALES_ORDER_TYPE , Post::DEBITNOTE_TYPE] )) {
            $assigned_item_id = $item['Invoice']['client_id'];
            $assigned_item_type = Post::CLIENT_TYPE;
        }
        
        $conditions = array('ItemStaff.item_type' => $assigned_item_type, 'ItemStaff.item_id' => $assigned_item_id);
        $staff_list = array_values($this->ItemStaff->find('list', array('fields' => 'id,staff_id', 'conditions' => $conditions)));

        if ($staff_id != 0 && !in_array($staff_id, $staff_list) && !check_permission($type['add_permission']) && !check_permission($type['add_assiged_permission'])) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }




        
        if (empty($item)) {
			if(IS_REST) $this->cakeError('error404', ["message"=>__('Item not found', TRUE)]);
            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }
        
        if ( $item_type == Post::CLIENT_TYPE)
        {
            $breadcrumbs_title = $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
            if ($post_id == 0 ){
                
                $title = __('Add note for', true) .' '. $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')';
            }else {
                $title = sprintf(__('Edit note #%s for %s', true), $post_id, $item['Client']['business_name'] . ' (#' . $item['Client']['client_number'] . ')');
            }
        }else if ($item_type == Post::WORK_ORDER_TYPE ) {
            $breadcrumbs_title = sprintf ( __(FollowUpStatus::$types_diff[$item_type]['name']. " %s", true ) , '#'.$item['WorkOrder']['number']);
            if ($post_id == 0 ){
                $title = __('Add note for', true) .' '. $item['WorkOrder']['title'] . ' (#' . $item['WorkOrder']['number'] . ')';
            }else {
                $title = sprintf(__('Edit note #%s for %s', true), $post_id, $item['WorkOrder']['title'] . ' (#' . $item['WorkOrder']['number'] . ')');
            }
        }else {
            $breadcrumbs_title = sprintf ( __(FollowUpStatus::$types_diff[$item_type]['name']. " %s", true ) , '#'.$item['Invoice']['no']);
            if ($post_id == 0 ){
                $title = __('Add note for', true) .' '. $item['Invoice']['no'] . ' (#' . $item['Invoice']['id'] . ')';
            }else {
                $title = sprintf(__('Edit note #%s for %s', true), $post_id, $item['Invoice']['no'] . ' (#' . $item['Invoice']['id'] . ')');
            }
        }
        $this->set('title_for_layout',  $title);
        $this->set('page_title', $title);
        $this->set('breadcrumbs_title', $breadcrumbs_title);
        
        


        if (!check_permission($type['add_permission'])) {
        
            $this->loadModel('ItemStaff');
            $assigned = $this->ItemStaff->find('all', ['conditions' => array('ItemStaff.staff_id' => $staff_id, 'ItemStaff.item_type' => $item_type, 'ItemStaff.item_id' => $item_id)]);
    
            if (empty($assigned) && $item[$type['model']][$type['staff_field']] != $staff_id) {
	            if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
            
        }
        $this->loadModel('FollowUpReminder');

        if (!empty($post_id)) {
            $post = $this->Post->findById($post_id);
            if ($post['Post']['item_type'] != $item_type || $post['Post']['item_id'] != $item_id) {
				if(IS_REST) $this->cakeError('error400');
                $this->flashMessage(__('Incorrect Request', TRUE));
                $this->redirect('/');
            }
        } else if (!empty($follow_up_id)) {
            $reminder = $this->FollowUpReminder->read(null, $follow_up_id);
            if ($reminder['FollowUpReminder']['item_type'] != $item_type || $reminder['FollowUpReminder']['item_id'] != $item_id) {
				if(IS_REST) $this->cakeError('error400');
                $this->flashMessage(__('Incorrect Request', TRUE));
                $this->redirect('/');
            }
        }


        $this->__formCommon($item_type, $item_id, $post_id, $follow_up_id);
        if (!empty($this->data)) {
            $beforesave=$this->data;
            $this->Post->create();
            $this->data['Post']['staff_id'] = getAuthOwner('staff_id');
            $this->data['Post']['item_id'] = $item_id;
            $this->data['Post']['item_type'] = $item_type;
            $this->data = $this->Post->set_type_related_data($item_type, $this->data, $item);
            $this->data['Post']['date'] = $this->Post->formatDateTime($this->data['Post']['date'] . ':00');

            if (!empty($follow_up_id)) {
                $this->data['Post']['follow_up_id'] = $follow_up_id;
            }
			

			App::import('Vendor', 'notification_2');
			if($post_id)
					$notification_action = NotificationV2::NOTI_ACTION_UPDATE;
			else
				$notification_action  = NotificationV2::NOTI_ACTION_ADD;


            if(isset($this->data['FollowUpReminder']['date']) && !empty($this->data['FollowUpReminder']['date'])) {
                $this->data['FollowUpReminder']['date'] = $this->Post->formatDateTime($this->data['FollowUpReminder']['date']);
                $this->data['FollowUpReminder']['date_date'] = $this->Post->formatDate($this->data['FollowUpReminder']['date_date']);
            }

            if ($this->Post->saveall($this->data, ['callbacks' => true])) {

                // Start Save Attachments To S3 . 
                $attachmentsId = explode(',',$this->data['attachments']);
                izam_resolve(AttachmentsService::class)->save(EntityKeyTypesUtil::POST, $this->Post->id, $attachmentsId);

				  if($this->data['Post']['item_type'] == Post::CLIENT_TYPE){
						$this->loadModel('ItemStaff');
						$assigned_staff = $this->ItemStaff->find('list',array('fields' => 'ItemStaff.staff_id','conditions' => array('ItemStaff.item_type' => $item_type , 'ItemStaff.item_id' => $item_id)));
						$trigger = NotificationV2::get_trigger();
						
						//add notification for staff
						 NotificationV2::add_notification(
								NotificationV2::NOTI_UPDATE_NOTE,
								$this->Post->id,
								array_values($assigned_staff),
								$user_type = NotificationV2::NOTI_USER_TYPE_STAFF,
								$trigger['trigger_type'] ,
								$trigger['trigger_id'],
								$notification_action,
								 ['name' => $item['Client']['first_name'] . ' '. $item['Client']['last_name'] ]
								);
						 
						 //add notification for client
						 if($this->data['Post']['client_permissions'])
						 {
							  NotificationV2::add_notification(
								NotificationV2::NOTI_CLIENT_SHARED_NOTE,
								$this->Post->id,
								[$item_id],
								$user_type = NotificationV2::NOTI_USER_TYPE_CLIENT,
								$trigger['trigger_type'] ,
								$trigger['trigger_id'],
								$notification_action,
								['name' => 'You']
								);
						 }
					}
					else if($this->data['Post']['item_type'] == Post::WORK_ORDER_TYPE)
					{
//							$this->loadModel('WorkOrdersStaff');
//							$assigned_staff = $this->WorkOrdersStaff->find('list',array('fields' => 'WorkOrdersStaff.staff_id','conditions' => array('WorkOrdersStaff.work_order_id' => $item_id)));
							$this->loadModel('ItemStaff');
							$this->loadModel('Post');
							$assigned_staff = $this->ItemStaff->find('list',array('fields' => 'ItemStaff.staff_id','conditions' => array('ItemStaff.item_id' => $item_id , 'item_type' => Post::WORK_ORDER_TYPE)));
							$trigger = NotificationV2::get_trigger();
							//add notification for staff
							 NotificationV2::add_notification(
								NotificationV2::NOTI_UPDATE_NOTE,
								$this->Post->id,
								array_values($assigned_staff),
								$user_type = NotificationV2::NOTI_USER_TYPE_STAFF,
								$trigger['trigger_type'] ,
								$trigger['trigger_id'],
								$notification_action,
								 ['name' => 'Work Order' ]
								);
					}

//                if (method_exists($this, '__AfterSave_' . $types[$item_type]['model'])) {
//                    $this->{'__AfterSave_' . $types[$item_type]['model']}($item_type, $item_id, $post_id, $follow_up_id, isset($post) ? $post : array());
//                }

                $this->__AfterSave_Client($item_type, $item_id, $post_id, $follow_up_id, isset($post) ? $post : array());

                if (!empty($this->data['Post']['status_id'])) {

                    $this->{$type['model']}->id = $item_id;
                    if ( $item_type == Post::WORK_ORDER_TYPE ) {
                        $this->loadModel('WorkOrder') ;
                        $this->WorkOrder->change_status ( $item_id  , $this->data['Post']['status_id']);
//                        echo ($item_id .' - '. $this->data['Post']['status_id'] );die ; 
                    }
                    $followup = $this->FollowUpStatus->find('first', ['conditions' => ['FollowUpStatus.id' => $this->data['Post']['status_id']]]);
                    if(!$this->{$type['model']}->saveField('secondary_follow_up_status', $this->data['Post']['status_id'])){
                        if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('%s could not be saved. Please, try again', true), __('Status', true))]);
                            $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Status', true)));
                    }
                    if($followup['FollowUpStatus']['is_primary']){
                        if (!$this->{$type['model']}->saveField('follow_up_status', $this->data['Post']['status_id'])) {
                            if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('%s could not be saved. Please, try again', true), __('Status', true))]);
                            $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Status', true)));
                        }
                    }
                }

				if(IS_REST){
					if(empty($post_id)) {
						$this->set('id', $this->Post->id);
						$this->render('created');
					} else {
						$this->render('success');
					}
					return;
				}

                $redirect = '/';
                if (!empty($this->params['url']['box'])) {
                    $redirect = array('action' => 'add', $this->Post->id, '?' => array('box' => 1, 'success' => 1));
                }
                $this->flashMessage(__('The note has been saved successfully', true), 'Sucmessage');
                if (!empty($follow_up_id) and $follow_up_id != 0) {
                    
                    
                    if(isset($url_params['back_to_client'])){
                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $item_id, '#' => 'NotesBlock'));    
                    }
                    $this->redirect(array('controller' => 'appointments', 'action' => 'index' , $item_type));
                }
                debug ( FollowUpStatus::$types_diff[$item_type]['view_url'].$item_id."#NotesBlock");
                $this->redirect($this->_getRedirectUrl(FollowUpStatus::$types_diff[$item_type], $item, $item_id));
//                if ($item_type == Post::CLIENT_TYPE) {
//                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $item_id, '#' => 'NotesBlock'));
//                }
                $this->set('save', 'true');
            } else {
				if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('%s could not be saved. Please, try again', true), __('Post', true)), "validation_errors"=>$this->Post->validationErrors]);
                $this->data=$beforesave;
                $this->data = $this->getOldAttachments($this->data);
                $errors = $this->Post->validationErrors;
                debug($errors);
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Post', true)));
            }

        } else {
            $this->data['Post']['date'] = date('Y-m-d H:i');
            if (!empty($post)) {
                $this->data = $post;
            } else if (!empty($reminder)) {
                $this->data['Post']['follow_up_id'] = $follow_up_id;
                $this->data['Post']['action_id'] = $reminder['FollowUpReminder']['action_id'];
            }
        }
        if (!empty($post_id)) {
            $post_files = $this->Post->PostFile->find('all', array('conditions' => array('PostFile.post_id' => $post_id)));
            $this->set('post_files', $post_files);
        }

        $this->set('upload_settings', $this->Post->PostFile->upload_settings);
        $this->set('file_path', Router::url("/files/" . SITE_HASH . "/post-files/", true));
        $this->set('item', $item);
        $this->set('item_id', $item_id);
        $this->set('post_id', $post_id);
        $this->set('follow_up_id', $follow_up_id);
        $this->__set_data($item_type);
    }

    /**
     * get redirect url based on follow up status
     * @param $followUpRedirect
     * @param $item
     * @param $itemId
     * @return string
     */
    public function _getRedirectUrl ($followUpRedirect, $item, $itemId)
    {
        $redirectUrl = $followUpRedirect['view_url'] . $itemId;
        if (isset($item['TrackingNumber'])) {
            $data = $item['TrackingNumber'];
            if ($data['lot'] && $data['expiry_date']) {
                $redirectUrl = sprintf($redirectUrl, 'lot_and_expiry_date');
            } else if ($data['lot']) {
                $redirectUrl = sprintf($redirectUrl, 'lots');
            } else if ($data['expiry_date']) {
                $redirectUrl = sprintf($redirectUrl, 'expiry_date');
            } else {
                $redirectUrl = sprintf($redirectUrl, 'serials');
            }
        }
        return $redirectUrl . "#NotesBlock";
    }

    function _send_email_to_user($ClientID, $PostID) {
        debug ( $ClientID. ' - '. $PostID );
        $site = getAuthOwner();
        $this->_setBranded();
        return $this->SysEmails->SharePost($ClientID, $PostID, $site);
    }

    function owner_delete_file($id, $allFileId = null) {
        $ajax = $this->RequestHandler->isAjax();
        if($allFileId)
        {
            $AllFile = GetObjectOrLoadModel('AllFile');
            $row = $AllFile->findById($allFileId);
            if (!$row) {
                if ($ajax) {
                    echo 'false';
                } else {
                    $this->flashMessage(__('Post File Not Found', TRUE));
                    $this->redirect('/');
                }
            }

            @unlink("/files/" . SITE_HASH . "/post-files/", $row['Allfile']['file_name']);
            $AllFile->delete($row['AllFile']['id']);
        }else{
            $row = $this->Post->PostFile->read(null, $id);
            if (!$row) {
                if ($ajax) {
                    echo 'false';
                } else {
                    $this->flashMessage(__('Post File Not Found', TRUE));
                    $this->redirect('/');
                }
            }
            $realpath = realpath("files/" . SITE_HASH . "/post-files/" . $row['Allfile']['file_name']);
            @unlink($realpath);
            $this->Post->PostFile->Allfile->delete($row['Allfile']['id']);
            $this->Post->PostFile->delete($id);
        }



        //  $redirect = array('action' => 'add', $this->Post->id, '?' => array('box' => 1, 'success' => 1));        
        die();
    }

    function owner_add_file($item_type, $item_id, $post_id = 0) {
        $ajax = $this->RequestHandler->isAjax();

        $types = Post::getItemTypes();

        $type = $types[$item_type];
        $staff_id = getAuthOwner('staff_id');

        $this->loadModel($type['model']);
        $item = $this->{$type['model']}->read(null, $item_id);

        if (empty($item)) {

            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }

        $this->set('upload_settings', $this->Post->PostFile->upload_settings);


        $owner = getAuthOwner();
        $site_id = getAuthOwner('id');


        if (!empty($this->data) and $ajax == true) {

            require_once APP . 'vendors' . DS . 'FileUploader.php';
            $status = FileUploader::Postupload($this->data['PostFile']['file'], $this->Post->PostFile->upload_settings['file']);
            if ($status['error']) {
                echo json_encode(array('post_file' => false, 'upgrade_link' => $status['upgrade_link'], 'error' => true, 'msg' => $status['msg']));
                die();
            }
            $row = FileUploader::ReadHash($status['hash']);

            $uniqid = substr(uniqid(), 8);
            $base_name = substr($row['file_name'], 0, strrpos($row['file_name'], '.'));
            $ext = substr($row['file_name'], strrpos($row['file_name'], '.'));
            $fname = Inflector::slug($base_name) . $ext;
            $filename = $uniqid . '_' . $fname;
            @mkdir(WWW_ROOT . "/files/" . SITE_HASH . "/post-files/", 0777);
            if(!move_uploaded_file($row['file_path'], WWW_ROOT . "/files/" . SITE_HASH . "/post-files/" . $filename)){
                echo json_encode(array('post_file' => false, 'error' => true, 'msg' => 'Cannot upload files'));
                die;
            }
            $data['Allfile']['user_id'] = $item_id;
            $data['Allfile']['date'] = date("Y-m-d H:i:s");
            $data['Allfile']['file_size'] = $row['file_size'];
            $data['Allfile']['file_type'] = substr($ext, 1);
            $data['Allfile']['file_name'] = $filename;
            $data['Allfile']['original_file_name'] = $row['file_name'];
            $data['Allfile']['md5'] = md5_file(WWW_ROOT . "/files/" . SITE_HASH . "/post-files/" . $filename);
            $this->loadModel('Allfile');
            $this->Allfile->create();
            $this->Allfile->save($data);
            header('Content-type: application/json');

            FileUploader::RemoveHash($status['hash']);
            if ($post_id != 0) {
                $data1['PostFile']['post_id'] = $post_id;
                $data1['PostFile']['allfile_id'] = $this->Post->PostFile->Allfile->getLastInsertID();
                $this->Post->PostFile->save($data1);
                echo json_encode(array('post_file' => true, 'error' => false, 'id' => $this->Post->PostFile->getLastInsertID(), 'postFileId' => $this->Post->PostFile->getLastInsertID(), 'allFileId' => $this->Post->PostFile->Allfile->getLastInsertID() ?? null, 'name' => $filename));
                die();
            }
            echo json_encode(array('post_file' => true, 'error' => false, 'id' => $this->Post->PostFile->Allfile->getLastInsertID(), 'postFileId' => $this->Post->PostFile->getLastInsertID(), 'allFileId' => $this->Post->PostFile->Allfile->getLastInsertID(), 'name' => $filename));
            die();
        }
    }

    function client_index($id = null) {
        $ajax = $this->RequestHandler->isAjax();
        $params = $this->params['url'];
        $this->Post->recursive = 3;
        $this->Post->bindAttachmentRelation('post');
        $client = getAuthClient();
		$this->loadModel('Invoice');

		$client_invoices = $this->Invoice->find('list', ['conditions' => ['Invoice.client_id' => $client['id']]]);
		$invoice_posts = $this->Post->find('list', ['fields' => array('Post.item_id'), 'conditions' => ['Post.item_type' => [Post::INVOICE_TYPE , Post::ESTIMATE_TYPE] ,  'Post.item_id' => array_keys($client_invoices)]]);
                debug ( $invoice_posts ) ;
		$conditions['OR'] = [ 'AND' => [ 'Post.share_with_partner' => 1,'Post.partner_id' => $client['id'], 'Post.partner_type' => FollowUp::CLIENT_PARTNER_TYPE], 'Post.id' => array_keys($invoice_posts)];
        //$conditions = array('Post.item_type' => Post::CLIENT_TYPE,'Post.item_id' => $client['id']);
        if ($id != null) {
            $linked_post = $this->Post->find('first', array('order' => 'Post.id desc', 'conditions' => array_merge(array('Post.id' => $id), $conditions)));

            $this->set('linked_post', $linked_post);
        }

        $last_posts = $this->Post->find('first', array('order' => 'Post.id desc', 'conditions' => $conditions));
        if (isset($last_posts['Post'])) {
            $arry = array('primary_id' => $last_posts['Post']['id'], 'secondary_id' => $client['id'], 'param1' => $last_posts['Post']['id']);
            $this->add_actionline(ACTION_CLIENT_VIEW_POST, $arry);
        }


        if (isset($params['date_from']) and !empty($params['date_from'])) {
            $conditions['Post.date >='] = $params['date_from'];
        }


        if (isset($params['date_to']) and !empty($params['date_to'])) {
            $conditions['Post.date <='] = $params['date_to'];
        }


        if (isset($params['order'])) {
            if ($params['order'] == "1") {
                $order = 'Post.date asc';
            } else {
                $order = 'Post.date desc';
            }
        } else {
            $order = 'Post.date desc';
        }


//        $conditions['Post.client_permissions'] = 1;

        if (empty($_GET['page'])) {
            $page = 1;
        } else {
            $page = intval($_GET['page']) == 0 ? 1 : $_GET['page'];
        }
        $per_page = 30;
		debug($conditions);
		debug($order);

        $this->Post->bindModel([
            'hasMany' => [
                'GeneralAttachment' => [
                    'foreignKey' => 'entity_id',
                    'conditions' => [
                        'GeneralAttachment.entity_key' => EntityKeyTypesUtil::POST
                    ]
                ]
            ]
        ]);

       $posts['data'] = $this->Post->find('all', array('offset' => ($page - 1) * $per_page, 'limit' => $per_page, 'order' => $order, 'conditions' => $conditions));
	   debug($posts);
        $posts['pagination']['total'] = $this->Post->find('count', array('conditions' => $conditions, 'order' => $order));
        $posts['pagination']['total_pages'] = ceil($posts['pagination']['total'] / $per_page);
        $this->set('posts', $posts);
        $this->set('is_ajax', $ajax);
    }

    function owner_delete($id = null) {
        $this->loadModel ('FollowUpStatus');
        $row = $this->Post->read(null, $id);
        if (!$row) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), "Note")]);
			}else{
				$this->flashMessage(__('Post not Found', TRUE));
				$this->redirect('/');
			}

        }

        $staff_id = getAuthOwner('staff_id');
        $this->loadModel('ItemStaff');

        $types = Post::getItemTypes();

        if (!isset($types[$row['Post']['item_type']])) {

            $this->flashMessage(__('Incorrect Request', TRUE));
            $this->redirect('/');
        }

        $type = $types[$row['Post']['item_type']];
        $this->loadModel($type['model']);
        $itemId = $row['Post']['item_id'];
        $item = $this->{$type['model']}->read(null, $itemId);


        if (!check_permission($type['edit_permission']) && !check_permission($type['edit_assiged_permission'])) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if (!empty($this->data) || IS_REST) {
            if ($_POST['submit_btn'] == 'yes' || IS_REST) {
//                $this->Post->delete($id);
                $this->Post->delete_with_files ( $id ) ;
                if ( $row['Post']['item_type'] == Post::CLIENT_TYPE  || $row['Post']['item_type'] == Post::WORK_ORDER_TYPE) {
                    $this->add_actionline(ACTION_REMOVE_THE_POST, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $row['Post']['item_id'], 'primary_id' => $id));
                } elseif ($row['Post']['item_type'] == Post::WORKFLOW_TYPE) {
                    $this->loadModel('Workflow');
                    $this->Workflow->addDeleteWorkflowPostActivityLog($item, $row['Post']);
                }elseif($row['Post']['item_type'] == FollowUp::PO_TYPE) {
                    $this->add_actionline(ACTION_REMOVE_THE_POST_PURCHASE_INVOICE, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' =>$id , 'primary_id' =>$row['Post']['item_id'] ));
                }else {
                    $this->add_actionline(ACTION_REMOVE_THE_POST_INVOICE, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' =>$id , 'primary_id' =>$row['Post']['item_id'] ));
                }
                
				if(IS_REST){
					$this->set("message", __('The note has been deleted successfully', true));
					$this->render("success");
					return;
				} else {
					$this->flashMessage(__('The note has been deleted successfully', true), 'Sucmessage');

					/*if ($row['Post']['item_type'] == Post::CLIENT_TYPE) {
	//                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['Post']['item_id'], '#' => 'NotesBlock'));

					}*/
					$this->set('save', 'true');
				}
            } else {
//                $this->redirect(array('controller' => 'clients', 'action' => 'view', $row['Post']['item_id'], '#' => 'NotesBlock'));
            }
            $this->redirect($this->_getRedirectUrl(FollowUpStatus::$types_diff[$row['Post']['item_type']], $item, $itemId));
            // $this->redirect(FollowUpStatus::$types_diff[$row['Post']['item_type']]['view_url'].$row['Post']['item_id']."#NotesBlock");
        }

        $this->set('id', $id);
    }

    function __AfterSave_Client($item_type, $item_id, $post_id, $follow_up_id, $post = array()) {

        $this->loadModel('FollowUpStatus');
        $this->loadModel('Client');
        
        $post_files = $this->Post->PostFile->find('all', array('conditions' => array('PostFile.post_id' => $post_id == 0 ? $this->Post->getLastInsertID() : $post_id)));
        $file_names = array();
        foreach ($post_files as $post_file) {
            $file_names[] = $post_file['Allfile']['original_file_name'];
        }
        $statuses = $this->FollowUpStatus->getList($item_type);
        if ( $item_type == Post::CLIENT_TYPE)
        {
            $client = $this->Client->read(null, $item_id);
        } elseif ( $item_type == Post::PO_TYPE) {
            $this->loadModel('PurchaseOrder');
            $invoice = $this->PurchaseOrder->findById($item_id ) ;
        } else {
            $this->loadModel ( 'Invoice');
            $invoice = $this->Invoice->findById ( $item_id ) ;
            $client = $this->Client->read(null, $invoice['Invoice']['client_id']);
        }
        $followup =  GetObjectOrLoadModel('FollowUpStatus');
        $types_diff = $followup->get_types_diff();
        $itemRecord = $this->FollowUpStatus->getItemRecord($item_type, $item_id);
        if ($post_id == 0) {
            if ( $item_type == Post::CLIENT_TYPE ) {
                $this->add_actionline(ACTION_ADD_NEW_POST, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $item_id, 'primary_id' => $this->Post->getLastInsertID(), 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $this->data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }elseif($item_type == FollowUp::WORK_ORDER_TYPE)
            {
                $this->add_actionline(ACTION_ADD_NEW_WORK_ORDER_POST, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $item_id, 'primary_id' => $this->Post->getLastInsertID(), 'param1' => $itemRecord['WorkOrder']['title'], 'param2' => $itemRecord['WorkOrder']['number'], 'param3' => $this->data['Post']['share_with_partner'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }elseif($item_type == FollowUp::PO_TYPE) {
                $this->add_actionline(ACTION_ADD_NEW_POST_PURCHASE_INVOICE, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $this->Post->getLastInsertID(), 'primary_id' =>  $item_id, 'param1' => $invoice['PurchaseOrder']['no'], 'param2' => $item_type, 'param3' => $this->data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }else {
                $this->add_actionline(ACTION_ADD_NEW_POST_INVOICE, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $this->Post->getLastInsertID(), 'primary_id' =>  $item_id, 'param1' => $invoice[($types_diff[$item_type]['class']?? 'Invoice')]['no'], 'param2' => $item_type, 'param3' => $this->data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }
        } else {
            if ( $item_type == Post::CLIENT_TYPE ) {
                $this->add_actionline(ACTION_UPDATE_THE_POST, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $item_id, 'primary_id' => $post_id, 'param1' => $client['Client']['business_name'], 'param2' => $client['Client']['client_number'], 'param3' => $this->data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }elseif($item_type == FollowUp::WORK_ORDER_TYPE)
            {
                $this->add_actionline(ACTION_UPDATE_WORK_ORDER_POST, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $item_id, 'primary_id' => $this->Post->getLastInsertID(), 'param1' => $itemRecord['WorkOrder']['title'], 'param2' => $itemRecord['WorkOrder']['number'], 'param3' => $this->data['Post']['share_with_partner'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }elseif($item_type == FollowUp::PO_TYPE) {
                $this->add_actionline(ACTION_UPDATE_THE_POST_PURCHASE_INVOICE, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $post_id, 'primary_id' => $item_id, 'param1' => $invoice['PurchaseOrder']['no'], 'param2' => $item_type, 'param3' => $this->data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }else {
                $this->add_actionline(ACTION_UPDATE_THE_POST_INVOICE, array('staff_id' => getAuthOwner('staff_id'), 'secondary_id' => $post_id, 'primary_id' => $item_id, 'param1' => $invoice[($types_diff[$item_type]['class']?? 'Invoice')]['no'], 'param2' => $item_type, 'param3' => $this->data['Post']['client_permissions'], 'param4' => $this->Post->formatDateTime($this->data['Post']['date']), 'param5' => $statuses[$this->data['Post']['status_id']], 'param6' => implode(',', $file_names)));
            }
            
        }
        if ($client && $this->data['Post']['share_with_partner'] == "1" and $post_id == 0) {
            $this->_send_email_to_user($client['Client']['id'], $post_id == 0 ? $this->Post->getLastInsertID() : $post_id);
        } elseif ($client && $this->data['Post']['client_permissions'] == "1" and ($post_id != 0 and $post['Post']['client_permissions'] != "1")) {
            $this->_send_email_to_user($client['Client']['id'], $post_id);
        }
    }

	public function api_view($id = null) {
		$this->Post->alias = "Note";
		$this->Post->unbindModel(["hasMany"=>['PostFile']], false);
		$this->Post->bindModel(["hasMany"=>["NoteAttachment"=>["className"=>"PostFile"]]], false);
		$this->Post->NoteAttachment->unbindModel(["belongsTo"=>["Post"]], false);
		$this->Post->recursive = 2;
		$post = $this->Post->find("first", ["conditions"=>['Note.id'=>$id]] );
		if(empty($post)) $this->cakeError('error404', array('message' => __('Note not found', true)));
        else $this->set('rest_item', $post);
		$this->set('rest_model_name', "Note");
		$this->render("view");
	}

	public function api_index() {
		$this->Post->alias = "Note";
		$this->Post->unbindModel(["hasMany"=>['PostFile']], false);
		$this->Post->bindModel(["hasMany"=>["NoteAttachment"=>["className"=>"PostFile"]]], false);
		$this->Post->NoteAttachment->unbindModel(["belongsTo"=>["Post"]], false);
		$this->Post->recursive = 2;

        $conditions = array();
        
        $filter_conditions = $this->_filter_params();
        $conditions[] = $filter_conditions;

        if (isset($_GET['from'])) {
            $conditions[] = ['Note.date >=' => $_GET['from']];
        }

        if (isset($_GET['to'])) {
            $conditions[] = ['Note.date <=' => $_GET['to']];
        }

		$posts = $this->paginate('Post', $conditions);
		
		$this->set('rest_items', $posts);
		$this->set('rest_model_name', "Note");
		$this->render("index");
	}
	
	public function api_edit($id = null) {
		$post = $this->Post->findById($id);
		if (empty($post)) $this->cakeError('error404', array('message' => __('Note not found', true)));
		$item_type = $post['Post']['item_type'];
		$item_id = $post['Post']['item_id'];
		$this->owner_post($item_type, $item_id, $id);
	}
	
	public function api_add($type, $id) {
		$types = [
			"client" => Post::CLIENT_TYPE,
			"invoice" => Post::INVOICE_TYPE,
			"estimate" => Post::ESTIMATE_TYPE,
			"purchase_order" => Post::PO_TYPE,
			"staff" => Post::STAFF_TYPE,
			"supplier" => Post::SUPPLIER_TYPE,
			"product" => Post::PRODUCT_TYPE,
			"work_order" => Post::WORK_ORDER_TYPE
		];
		$follow_up_id = $this->data['Post']['follow_up_id'];
		$this->owner_post($types[$type], $id, 0, $follow_up_id);
	}

    // Used To get attachments details in case of error occurred. 
    private function getOldAttachments($data)
    {
        if(!empty($data['attachments']))
        {                       
        $filesId = explode(',',$data['attachments']);
        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
        $data['Attachments'] = $attachment;
        }
        return $data;
    }
}
