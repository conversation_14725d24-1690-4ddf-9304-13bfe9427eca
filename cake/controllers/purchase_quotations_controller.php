<?php
//App::import('Vendor', 'notification');
/**
 * @property SysEmailsComponent $SysEmails Component to handle sending system emails
 * @property InvoiceLayout $InvoiceLayout
 */

use ActivityLog\Utils\EntityKeyTypesUtil;
use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use App\Helpers\PurchaseDocumentsS3Helper;
use App\Modules\LocalEntity\Actions\AppEntityStructureGetter;
use App\Services\CommonActivityLogService;
use App\Services\LocalEntityForm\AdditionalFormsHandler;
use App\Transformers\ServiceModelDataTransformer;
use App\Utils\TrackStockUtil;
use App\Validators\TrackStock\TrackStockValidator;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator ;
use Izam\Daftra\ActivityLog\Utils\ActionLineMainOperationTypesUtil;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil as EntityEntityKeyTypesUtil;
use Izam\Daftra\PurchaseOrder\Services\PurchaseOrderService;
use Izam\Limitation\Utils\LimitationUtil;

App::import('Vendor', 'settings');
class PurchaseQuotationsController extends AppController {

    var $name = 'PurchaseOrders';
    var $helpers = ['Fck'];
    var $components = ['Email', 'SysEmails','Cookie', 'StockValidation'];

    /**
     * @var EmailComponent
     */
    var $Email;

    /**
     * @var LmailComponent
     */
    var $Lmail;

    /**
     * @var QuotationRequest
     */
    var $QuotationRequest;

    /**
     * @var Product
     */
    var $Product;


    /**
     * @var PurchaseOrder
     */
    var $PurchaseOrder;

    /**
     * @var QuotationRequestSupplier
     */
    var $QuotationRequestSupplier;


    var $purchase_js_labels = array(
        'Discount',
        'Paid',
        'Unpaid',
        'Balance Due:',
        'Next Payment',
        'Partial Paid',
        'Supplier Details',
        'Stock Level After',
        'You can add up to 10 documents only',
        'Documents limit',
        'You can add up to 10 reminders only',
        'Reminders limit',
        'PurchaseOrder Template',
        'Please Save the supplier details first by clicking below on Save Supplier button',
        'Shipping',
        'You need to enter deposit the amount first'
    );

    function beforeFilter() {

        parent::beforeFilter();
        if (!IS_PC && !empty($this->data['PurchaseOrder']['html_notes']) && strpos($this->data['PurchaseOrder']['html_notes'], '>') == false) {
            $this->data['PurchaseOrder']['html_notes'] = nl2br($this->data['PurchaseOrder']['html_notes']);
        }
    }





    function _filterLinks() {
        $params = $this->params['url'];
        unset($params['url'], $params['ext']);

        $filterLinks = array();
        $action = str_replace('owner_', '', $this->action);
        if ($action != 'index') {
            $listing = low($action);
            if ($listing == 'overdue') {
                $filterLinks[] = array('title' => __('Listing', true), 'value' => __('Overdue', true), 'var' => '');
                $isListing = true;
            } elseif ($listing == 'due') {
                $filterLinks[] = array('title' => __('Listing', true), 'value' => __('Due', true), 'var' => '');
                $isListing = true;
            }
        }

        if (!empty($this->params['url']['supplier_id'])) {
            $supplier = $this->PurchaseOrder->Supplier->find(array('Supplier.id' => $this->params['url']['supplier_id']), false, false, -1);
            if ($supplier) {
                $filterLinks[] = array('title' => __('Supplier', true), 'value' => $supplier['Supplier']['business_name'], 'var' => 'supplier_id');
            }
        }
        $this->set(compact('filterLinks', 'params'));
    }

//-----------------------------------------
//-----------------------------------------

    private function trans($items) {
        $mappedData = [];
        //This is added to fix an issue regarding custom fields selection in purchase invoice templates
        //This will set a name for the cols in case it was selected from template
        $layout = $this->_getLayout();
        if($layout['InvoiceLayout']['item_columns']) {
            $item_columns = json_decode($layout['InvoiceLayout']['item_columns'], true);
        }
        $this->loadModel('Product');
        foreach ($items as $key => $item) {
            $mappedData[$key] = $item;
            $mappedData[$key]["item"] = $item["Product"]['name'];
            foreach($item_columns as $colKey => $col){
                if(is_array($col) && isset($item["Product"][$col['name']])) {
                    $mappedData[$key][str_replace('field','col_', $colKey)] = $item["Product"][$col['name']];
                }
                switch($colKey){
                    case 'field2':
                        $mappedData[$key]['description'] = $item["Product"][$col['name']];
                        break;
                }
            }
            $mappedData[$key]["unit_price"] = (floatval($item["Product"]['buy_price']));
            $mappedData[$key]["unit_factor"] = $item['factor'];
            $mappedData[$key]["tax1"] = $item["Product"]["tax1"];
            $mappedData[$key]["tax2"] = $item["Product"]["tax2"];
            $mappedData[$key]["summary_tax"] = $item['factor'];
            $mappedData[$key]["unit_factor"] = $item['factor'];            
            $products = [$mappedData[$key]["Product"]];
            $this->Product->add_multi_units ($products) ;
            $mappedData[$key]["Product"] = $products[0];
            unset($mappedData[$key]['id']);
        }
        return $mappedData;
    }

    private function createAdditionalFieldsFormHandlerInstance()
    {
        return new AdditionalFormsHandler(EntityEntityKeyTypesUtil::PURCHASE_INVOICE);
    }

    function owner_add($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        App::import('Vendor', 'AutoNumber');
        // Check site limitation
        $this->handleSiteLimit(
            checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::PURCHASE_CYCLE),
            '/v2/owner/entity/purchase_quotation/list'
        );

        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        $this->loadModel('QuotationRequest');
        $this->QuotationRequest->recursive = 3;
        $quotationRequest =  $this->QuotationRequest->findById($id);
        $quotationRequest['QuotationRequestItem'] = $this->formateItems($quotationRequest['QuotationRequestItem']);

        if (!empty($quotationRequest["QuotationRequest"])) {
            $items = $quotationRequest['QuotationRequestItem'];
            $items = $this->trans($items);  
        } else {
            $items = [];
        }

        if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::MANAGE_PURCHASE_QUOTATIONS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);

            foreach ($this->data['PurchaseOrderItem'] as $key => $item) {
                $resultActive = $this->PurchaseOrder->checkProductActive($item);
                if (!$resultActive['status']) {
                    if (IS_REST) {
                        $this->cakeError('error400', ['message' => $resultActive['message']]);
                    } else {
//                        $this->flashMessage($result['message']);
//                        return $this->redirect($this->referer());
                        $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][$key]['item'] = $resultActive['message'];
                    }
                }
            }

            $return = checkOrderItemsStoreIsActive($this->data['PurchaseOrderItem']);
            if (!$return['status']) {
                if (IS_REST) {
                    $this->cakeError('error400', ['message' => $return['message']]);
                } else {
                  //  return $this->redirect($this->referer());
                    $this->PurchaseOrder->validationErrors[] = $return['message'];
                }
            }

            if(!empty($this->data['PurchaseOrder']['html_notes'])){
                $this->data['PurchaseOrder']['html_notes'] = $this->data['PurchaseOrder']['html_notes'] . '<span class="replace_template_notes"></span>';
            }

            if (!empty($id)) {
                $this->data['PurchaseOrder']['source_type'] = \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::QUOTATION_REQUEST;
                $this->data['PurchaseOrder']['source_id'] = $id;
                $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_QUOTATION);
                $formats = getDateFormats(null);
                $this->data['PurchaseOrder']['issue_date'] = strftime($formats[getCurrentSite('date_format')], time());
                $this->data['PurchaseOrder']['date_format'] = getCurrentSite('date_format');


            }
       
            $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_QUOTATION;
            $this->data['PurchaseOrder']['payment_status'] = \Izam\Daftra\Common\Utils\PurchaseQuotationStatusUtil::Pending;
            $this->loadModel ( 'Store');
            $this->data['PurchaseOrder']['draft'] = ($this->params['url']['send'] == 'draft');
            $validationResult = \App\Validators\CanChangeToPurchaseQuotation::validate($this->data, $id);
            if($validationResult === true && $additionalFieldsFormHandler->validate($this->data))
            {   
                $result = $this->PurchaseOrder->addPurchaseOrder($this->data);
                $createdId = $result['data']['PurchaseOrder']['id'];

            }else{
                $result['status'] = false;
            }
            if ($result['status']) {
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $additionalFieldsFormHandler->store($re_read_po['PurchaseOrder']['id'], $this->data);

                //
                if ($id) {
                  $this->updateQuotationRequestStatus($id);
                }

                izam_resolve(PurchaseOrderService::class)->insert(ServiceModelDataTransformer::transform($result['data'], 'PurchaseOrder'));
                RouteSaverFactory::getSaver(Util::TRANSACTION_TYPE_PURCHASE_ORDER)
                    ->save($this->data['JournalAccountRoute'], $re_read_po['PurchaseOrder']['id']);
                $this->PurchaseOrder->recursive = 1;
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $re_read_po['PurchaseOrder']['date'] = format_datetime($re_read_po['PurchaseOrder']['date']);

                if(IS_REST){
                    $this->set('id', $re_read_po['PurchaseOrder']['id']);
                    $this->render('created');
                    return;
                }
                $newData = getRecordWithEntityStructure(EntityEntityKeyTypesUtil::PURCHASE_QUOTATION, $createdId, 1)->toArray();


                $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PURCHASE_QUOTATION);
                $st->getParentEntityData()->getForeignKeyName();
                $parentRelationName = $st->getParentEntityData()->getChildRelation()->getName();
                if (!empty($id)) {
                    $newData['is_clone'] = [$parentRelationName => $newData[$parentRelationName]];
                }
                $addedRelations = [];
                if (!empty($quotationRequest["QuotationRequest"]["purchase_request_id"])) {
                    $addedRelations[] = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($quotationRequest["QuotationRequest"]["purchase_request_id"], EntityEntityKeyTypesUtil::PURCHASE_REQUEST);
                }
                $requests = (new EntityActivityLogRequestsCreator(

                ))->create($st, $newData, [], $addedRelations);

                $activityLogService =  new \App\Services\ActivityLogService();
                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }
                $this->flashMessage(__('Purchase Quotation has been saved', true), 'Sucmessage');

                $redirect = array('action' => 'view');
                $action = '';

                if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                    $this->_directSendEmail($result['data']);
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                    $redirect = array('action' => 'send_to_supplier');
                } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                    $redirect = array('action' => 'view', 'print' => 1);
                    $action = "#print";
                }



                $redirect[] = $result['data']['PurchaseOrder']['id'];
                if ( !empty ($this->data['PurchaseOrder']['back_to_wo'] ) && !empty ( $this->data['PurchaseOrder']['work_order_id']) )
                {
                    $redirect = Router::url( ['controller' => 'work_orders' , 'action' => 'view' ,$this->data['PurchaseOrder']['work_order_id'].'#POBlock' ]);
                }
                $this->redirect('/v2/owner/entity/purchase_quotation/'.$result['data']['PurchaseOrder']['id'].'/show'.$action);
            } else {
                // $this->add_stats(STATS_INVOICE_VALIDATION_ERROR, array(serialize($this->PurchaseOrder->validationErrors), serialize($this->PurchaseOrder->data)));
                if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                $message = $validationResult['message'];
                if (empty($message)) {
                    $message = __("Could not save Purchase Quotation", true);
                }

                if(!empty($this->PurchaseOrder->validationErrors)) {
                    $errors = get_error_message($this->PurchaseOrder->validationErrors);
                        CustomValidationFlash($errors);
                }

                $this->flashMessage($message);
                if (empty($_SERVER['HTTP_REFERER']) || strpos($_SERVER['HTTP_REFERER'], Router::url( ['controller' => 'purchase_quotations' , 'action' => 'add'])) === false  ) {
                    $this->redirect($this->referer());
                }
            }
        } else {
            $formats = getDateFormats('std');
            $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_QUOTATION;
            $this->data['PurchaseOrder']['received_date'] = format_date(date("Y-m-d")) . date(' H:i');
            $this->data['PurchaseOrder']['date'] = format_date(date("Y-m-d"));
            if (!empty($id)) {
                $this->data['PurchaseOrder']['source_type'] = \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::QUOTATION_REQUEST;
                $this->data['PurchaseOrder']['source_id'] = $id;
            }
            $this->data['PurchaseOrderItem'] = $items;
            // $this->add_stats(STATS_OPEN_INVOICE_CREATION_PAGE, array($id));
            
            if ($id) {
                $template = $this->PurchaseOrder->getTemplate(null);
                if (!empty($template)) {
                    if (!$this->PurchaseOrder->Supplier->hasAny(array('Supplier.id' => $template['PurchaseOrder']['supplier_id']))) {
                        unset($template['PurchaseOrder']['supplier_id'], $template['PurchaseOrder']['supplier_business_name'], $template['PurchaseOrder']['supplier_first_name'], $template['PurchaseOrder']['supplier_last_name'], $template['PurchaseOrder']['supplier_city'], $template['PurchaseOrder']['supplier_state'], $template['PurchaseOrder']['supplier_postal_code'], $template['PurchaseOrder']['supplier_country_code'], $template['PurchaseOrder']['supplier_business_name']);
                    }


                    $template['PurchaseOrder']['date'] = $template['PurchaseOrder']['issue_date'] = format_date(date("Y-m-d"));
                    //$this->data = $template;
                } else {
                 //   $this->flashMessage(__('Prefilled purchase invoice not found', true));
                }
            }
            if (empty($this->data['PurchaseOrder']['currency_code'])) {
                $this->data['PurchaseOrder']['currency_code'] = getAuthOwner('currency_code');
            }

            if (empty($this->data['PurchaseOrder']['language_id'])) {
                $this->data['PurchaseOrder']['language_id'] = getAuthOwner('language_code');
            }
            if ( !empty($this->params['url']['supplier_id'])) {
                $this->data['PurchaseOrder']['supplier_id'] = intval($this->params['url']['supplier_id'] );
            }
            if (empty($this->data['PurchaseOrder']['date_format'])) {
                $this->data['PurchaseOrder']['date_format'] = getCurrentSite('date_format');
            }

            if (empty($this->data['PurchaseOrder']['issue_date'])) {
                $formats = getDateFormats(null);
                $this->data['PurchaseOrder']['issue_date'] = strftime($formats[getCurrentSite('date_format')], time());
            }

            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_QUOTATION);
            //TODO set default layout
            //$this->data['PurchaseOrder']['invoice_layout_id']=1;
        }
        if (is_array($this->Session->read('purchaseorder_items'))) {
            $more = $this->Session->read('purchaseorder_items');
            $this->data['PurchaseOrderItem'] = $more['PurchaseOrderItem'];
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
        }
        if ($this->Session->read('purchaseorder_type') == 1) {
            $this->data['PurchaseOrder']['purchaseorder_layout_id'] = -1;
            $this->Session->delete('purchaseorder_type');
        }
        $this->set('po_always_paid',  $po_always_paid= settings::getValue(InventoryPlugin, 'po_always_paid'));
        $this->_settings($id);
        $this->_formCommon();


        $this->set('content', $this->get_snippet('create-purchaseorder'));

        $this->set('title_for_layout',  __('New Purchase Quotation', true));

        $layout = $this->_getLayout($this->data['PurchaseOrder']['invoice_layout_id']);

        $this->data['PurchaseOrder']['html_notes'] = $this->__prepare_notes($layout, !empty($template) ? $template : $this->data);

        $this->loadModel('ItemPermission');
        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'Treasury');
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) ) ;
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $this->loadModel ( 'Store');

        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set ( 'primaryStore' , $this->Store->getPrimaryStore (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) ) ;
        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));
        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang('new-purchaseorder'));
        $this->set('isPurchaseOrder', true);
        $this->set('_PageBreadCrumbs',  []);
        $this->set('addStockTracking', false); // Disable stock tracking for products in purchase quotation
        $this->set('HtmlModel', 'PurchaseOrderDocument');  
        $this->render('../purchase_quotations/owner_add');
    }


    function __prepare_notes($layout, $old_data = array()) {
        $data = array();
        if (!empty($old_data['PurchaseOrder']['html_notes']) && strpos($old_data['PurchaseOrder']['html_notes'], 'replace_template_notes')) {
            $data['PurchaseOrder']['html_notes'] = $old_data['PurchaseOrder']['html_notes'];
        } else if (!empty($layout) && !empty($layout['InvoiceLayout']['footer'])) {
            if (!empty($old_data['PurchaseOrder']['html_notes']) && !strpos($old_data['PurchaseOrder']['html_notes'], 'replace_template_notes'))
                $data['PurchaseOrder']['html_notes'] = str_replace('{%invoice_notes%}', '' . $old_data['PurchaseOrder']['html_notes'], '<span style="font-style: normal;" >' . $layout['InvoiceLayout']['footer'] . '</span>');
            else
                $data['PurchaseOrder']['html_notes'] = $layout['InvoiceLayout']['footer'];
        }
        if (!empty($data['PurchaseOrder']['html_notes'])) {
            $data['PurchaseOrder']['html_notes'] = str_replace('{%invoice_notes%}', '', $data['PurchaseOrder']['html_notes']);
            return $data['PurchaseOrder']['html_notes'];
        }
        if (empty($data['PurchaseOrder']['html_notes']) && !empty($old_data['PurchaseOrder']['html_notes'])) {
            $data['PurchaseOrder']['html_notes'] = str_replace('{%invoice_notes%}', '', $old_data['PurchaseOrder']['html_notes']);
            return $data['PurchaseOrder']['html_notes'];
        }
        return null;
    }

 
    private function updateQuotationRequestStatus($id = null)
    {
        $status = "quoted";
        $this->loadModel("QuotationRequest");
        $itemsQuantity = [];
        $this->QuotationRequest->recursive = 2;
        $quotationRequest = $this->QuotationRequest->findById($id);
        $quotationRequestItems = $quotationRequest["QuotationRequestItem"];
        $this->PurchaseOrder->recursive = 1;
        $purchaseQuotations = $this->PurchaseOrder->find('all', ['conditions' => ['source_id' => $id, 'source_type' => \Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::QUOTATION_REQUEST]]);
        foreach ($purchaseQuotations as $purchaseQuotation) {
            foreach ($purchaseQuotation["PurchaseOrderItem"] as $item) {
                $itemsQuantity[$item["product_id"]] += $item["quantity"];
            }

        }
        if (count($itemsQuantity) != count($quotationRequestItems)) {
            $status = 'partially_quoted';
        } else {
            foreach ($itemsQuantity as $product_id => $quantity) {
               if ($this->getItemInfoFromDataBase($product_id, $quotationRequestItems)["quantity"] > $quantity) {
                   $status = 'partially_quoted';
                   break;
               }
            }
        }

        $this->QuotationRequest->id = $id;

        $this->QuotationRequest->save(["QuotationRequest" => ['status' => $status]]);
    }
    private  function getItemInfoFromDataBase($item_id, $items)
    {
        $index = array_search($item_id, array_column($items, "product_id"));

        if ($index !== false) {
            return $items[$index];
        }
        return false;
    }


//-----------------------------------------
    function owner_edit($id = null) {
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->loadModel('QuotationRequest');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->purchase_js_labels);
        $owner = getAuthOwner();
        $this->PurchaseOrder->bindAttachmentRelation('purchase_order');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id);

        if (in_array($purchase_orders["PurchaseOrder"]["payment_status"], [\Izam\Daftra\Common\Utils\PurchaseQuotationStatusUtil::Approved, \Izam\Daftra\Common\Utils\PurchaseQuotationStatusUtil::Rejected])) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You cannot edit the approved or rejected purchase Quotation", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $old_draft=$purchase_orders['PurchaseOrder']['draft'];
        if (!$purchase_orders) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Purchase Invoice not found', true)));
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $purchase_order = $this->PurchaseOrder->find('first',['recursive' => -1, 'conditions' => ['source_type' => EntityEntityKeyTypesUtil::PURCHASE_QUOTATION,'source_id' => $purchase_orders["PurchaseOrder"]['id'], 'type' => PurchaseOrder::PURCHASE_ORDER]]);
        if(!empty($purchase_order)){
            $purchaseOrderType = EntityEntityKeyTypesUtil::PURCHASE_ORDER;
            $this->flashMessage(__("You cannot edit the Purchase Quotation as there’s related purchase Order", true).
                " <a href='/v2/owner/entity/{$purchaseOrderType}/{$purchase_order['PurchaseOrder']['id']}/show'>#{$purchase_order["PurchaseOrder"]["no"]}</a>", 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        $this->loadModel('Tax');
        $this->set('changed_tax',$this->Tax->tax_compare($id,'purchase_order'));
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::MANAGE_PURCHASE_QUOTATIONS) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::MANAGE_PURCHASE_QUOTATIONS)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->validate_open_day($purchase_orders['PurchaseOrder']['date']);



        if(IS_REST) $this->data['PurchaseOrder']['id'] = $id;


        if (!empty($this->data)) {
            // Handle s3Attachments & old attachments
            $this->data = PurchaseDocumentsS3Helper::prepareAttachments($this->data);
            
            $this->data['PurchaseOrder']['draft'] = $this->params['url']['send'] == 'draft';
            $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_QUOTATION;

            foreach ($this->data['PurchaseOrderItem'] as $key => $item) {
                $resultActive = $this->PurchaseOrder->checkProductActive($item);
                if (!$resultActive['status']) {
                    if (IS_REST) {
                        $this->cakeError('error400', ['message' => $resultActive['message']]);
                    } else {
//                        $this->flashMessage($result['message']);
//                        return $this->redirect($this->referer());
                        $this->PurchaseOrder->validationErrors['PurchaseOrderItem'][$key]['item'] = $resultActive['message'];
                    }
                }
            }

            $map_products = [];
            foreach ($purchase_orders['PurchaseOrderItem'] as $k) {
                $map_products[$k['product_id']] = $k;
            }
            $return = checkOrderItemsStoreIsActive($this->data['PurchaseOrderItem']);
            if (!$return['status']) {
                if (IS_REST) {
                    $this->cakeError('error400', ['message' => $return['message']]);
                } else {
                   // return $this->redirect($this->referer());
                    $this->PurchaseOrder->validationErrors[] = $return['message'];
                }
            }
            $validationResult = \App\Validators\CanChangeToPurchaseQuotation::validate($this->data, $purchase_orders['PurchaseOrder']['source_id']);
            if($validationResult === true && $additionalFieldsFormHandler->validate($this->data))
            {
                $entityId = $this->data['PurchaseOrder']['id'];
                $oldData = getRecordWithEntityStructure(EntityEntityKeyTypesUtil::PURCHASE_QUOTATION, $entityId, 1);
                $result = $this->PurchaseOrder->updatePurchaseOrder($this->data);
                $errors  = $this->PurchaseOrder->validationErrors;
                $re_read_po = $this->PurchaseOrder->read(null, $result['data']['PurchaseOrder']['id']);
                $re_read_po['PurchaseOrder']['date'] = format_datetime($re_read_po['PurchaseOrder']['date']);
                $this->PurchaseOrder->validationErrors = $errors;
                if ($result['status']) {
                    $additionalFieldsFormHandler->update($result['data']['PurchaseOrder']['id'], $this->data);
                    if (!empty($purchase_orders['PurchaseOrder']['source_id'])) {
                        $this->updateQuotationRequestStatus($purchase_orders['PurchaseOrder']['source_id']);
                    }
                    $newData = getRecordWithEntityStructure(EntityEntityKeyTypesUtil::PURCHASE_QUOTATION, $entityId, 1)->toArray();
                    $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PURCHASE_QUOTATION);
                    $oldData = $oldData->toArray();
                    foreach ($newData['purchase_order_items'] as &$purchase_order_item) {
                       unset($purchase_order_item['tracking_data']);
                    }

                    foreach ($oldData['purchase_order_items'] as &$purchase_order_item) {
                        unset($purchase_order_item['tracking_data']);
                    }
                    $addedRelations = [];
                    $quotationRequest =  $this->QuotationRequest->findById($newData['source_id']);
                    if (!empty($quotationRequest["QuotationRequest"]["purchase_request_id"])) {
                        $addedRelations[] = new \Izam\Daftra\ActivityLog\Requests\ActivityLogRelationRequest($quotationRequest["QuotationRequest"]["purchase_request_id"], EntityEntityKeyTypesUtil::PURCHASE_REQUEST);
                    }
                    $requests = (new EntityActivityLogRequestsCreator())->create($st, $newData, $oldData, $addedRelations);
                    $activityLogService =  new \App\Services\ActivityLogService();
                    foreach ($requests as $requestObj) {
                        $activityLogService->addActivity($requestObj);
                    }
                    $this->flashMessage(__('Purchase Quotation has been updated', true), 'Sucmessage');
            
                    if (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'direct') {

                        $this->_directSendEmail($result['data']);
                        $redirect = array('action' => 'view', $id);
                    } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'revised') {
                        $redirect = array('action' => 'send_to_supplier', $id);
                    } elseif (!empty($this->params['url']['send']) && $this->params['url']['send'] == 'print') {
                        $redirect = array('action' => 'view', $id, 'print' => 1);
                    }

                    $this->redirect('/v2/owner/entity/purchase_quotation/'.$id.'/show');
                    
                } else {
                    if(IS_REST) $this->cakeError("error400", ["message"=>$result['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                    $this->flashMessage(__('Could not update Purchase Quotation', true));

                    if(!empty($this->PurchaseOrder->validationErrors)) {
                        $errors = get_error_message($this->PurchaseOrder->validationErrors);
                        CustomValidationFlash($errors);
                    }
                    
                    if (isset($result['message'])) {
                        $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
                    }

                }

            }else{
                // $this->add_stats(STATS_INVOICE_VALIDATION_ERROR, array(serialize($this->PurchaseOrder->validationErrors), serialize($this->PurchaseOrder->data)));
                if(IS_REST) $this->cakeError("error400", ["message"=>$validationResult['message'], "validation_errors"=>$this->PurchaseOrder->validationErrors]);
                $this->flashMessage($validationResult['message']);
            }

        } else {
            $this->data = $purchase_orders;
            if(!empty($this->data['PurchaseOrder']['html_notes'])){
                $this->data['PurchaseOrder']['html_notes'] = $this->data['PurchaseOrder']['html_notes'] . '<span class="replace_template_notes"></span>';
            }
            $this->data['PurchaseOrder']['hidden_no'] = $this->data['PurchaseOrder']['no'];
            $layout = $this->_getLayout($this->data['PurchaseOrder']['invoice_layout_id']);
            $this->data['PurchaseOrder']['html_notes'] = $this->__prepare_notes($layout, !empty($template) ? $template : $this->data);

        }
        $this->set('purchase_orders', $purchase_orders);
        $this->_settings($id);
        $this->_formCommon($id);
        $pstocks = array();

        $this->set('title_for_layout',  __('Edit Purchase Quotation', true));

        $this->set('isPurchaseOrder', true);
        $this->set('content', $this->get_snippet('create-purchaseorder'));
        $this->set('purchaseorderTemplates', $this->PurchaseOrder->find('list', array('conditions' => array('PurchaseOrder.site_id' => getAuthOwner('id'), 'PurchaseOrder.type' => 1),)));

        $this->loadModel ( 'Store');
        $this->loadModel('ItemPermission');
        $this->set('stores_list', $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING) );
        $this->set('purchaseorder_methods', PurchaseOrder::getMethods());
        $this->set('languages', $this->_json_lang());
        $this->set('HtmlModel', 'PurchaseOrderDocument');
        $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
        $this->render('../purchase_quotations/owner_add');
    }


//-------------------------------------
    function _filter_params($params = false, $filters = [], $passedModelName = false) {
        $conditions = parent::_filter_params($params, $filters);
        if (!empty($this->params['url']['supplier_id'])) {
            $conditions['PurchaseOrder.supplier_id'] = intval($this->params['url']['supplier_id']);
        }





        if (!empty($conditions['PurchaseOrder.due_date <='])) {
            $conditions[] = "DATE_ADD(`PurchaseOrder`.`date`,INTERVAL `PurchaseOrder`.`due_after` DAY) <= '{$conditions['PurchaseOrder.due_date <=']}'";
        }
        unset($conditions['PurchaseOrder.due_date <=']);

        if (!empty($conditions['PurchaseOrder.due_date >='])) {
            $conditions[] = "DATE_ADD(`PurchaseOrder`.`date`,INTERVAL `PurchaseOrder`.`due_after` DAY) >= '{$conditions['PurchaseOrder.due_date >=']}'";
        }
        unset($conditions['PurchaseOrder.due_date >=']);

        if (!empty($this->params['url']['draft'])) {
            $conditions['PurchaseOrder.draft'] = 1;
        }

        if (!empty($this->params['url']['subscription_id'])) {
            $conditions['PurchaseOrder.subscription_id'] = intval($this->params['url']['subscription_id']);
        }

        if (!empty($this->params['url']['follow_up_status'])) {
            $conditions['PurchaseOrder.follow_up_status'] = intval($this->params['url']['follow_up_status']);
        }

        return $conditions;
    }

//----------------------------
    function _settings($id = null) {
        $this->loadModel('Tax');
        $this->set('tax_count',$this->Tax->find('count'));
        $this->set('DeliveryStatuses', PurchaseOrder::getDeliveryStatuses());
        App::import('Vendor', 'settings');
        $this->loadModel('Journal');

        if($this->isRefund)
        {
            $this->setAccountRoute('purchase_returns_accounts_routing', Journal::PURCHASE_RETURNS_ACCOUNT_ENTITY_TYPE, $id);
        }else{
            $this->setAccountRoute('purchases_accounts_routing', Journal::PURCHASES_ACCOUNT_ENTITY_TYPE, $id);
        }

        $po_setting =settings::formData(InventoryPlugin) ;
        $this->set('po_setting',$po_setting);
        $this->loadModel('Product');
        $this->loadModel('PurchaseOrderItem');


        $this->loadModel('PurchaseOrderTax');

        if (empty($this->data['PurchaseOrder']['id'])) {
            $this->set('taxes', $this->PurchaseOrderTax->getPurchaseOrderTaxList());
            $this->set('jsTaxes', $this->PurchaseOrderTax->getJSONList());
        } else {
            $this->set('taxes', $this->PurchaseOrderTax->getPurchaseOrderTaxList($this->data['PurchaseOrder']['id']));
            $this->Set('jsTaxes', $this->PurchaseOrderTax->getJSONList($this->data['PurchaseOrder']['id']));
        }

        // $this->set('sendWhens', PurchaseOrder::getRemindersWhenList());

        $this->loadModel('EmailTemplate');
        $this->set('emailTemplates', $this->EmailTemplate->getEmailTemplateList());

        $this->set('languages', array());
        $this->loadModel('Term');
        $this->set('termsConditions', $this->Term->getTermList());
//		$this->set('termsFiles', $this->Term->getTermList('file'));

        $ajax_suppliers = false;
        $suppliers_count = $this->PurchaseOrder->Supplier->getSupplierCount();
        $this->loadModel('QuotationRequestSupplier');
        $suppliers_id = $this->QuotationRequestSupplier->find('list', ['conditions' => ['quotation_request_id' => $this->data['PurchaseOrder']['source_id']], 'fields' => ['supplier_id']]);
       if (!empty($this->data['PurchaseOrder']['source_id'])) {
           $suppliers = $this->PurchaseOrder->Supplier->getSuppliersList(false, ['id' => $suppliers_id]);
       } else {
           $suppliers = $this->PurchaseOrder->Supplier->getSuppliersList();
       }

        if ($suppliers_count < AJAX_CLIENT_COUNT) {
            $this->set('suppliers', $suppliers);
        } else {
            $this->set('suppliers', $suppliers);
            $ajax_suppliers = true;
        }
        $this->set('ajax_suppliers', $ajax_suppliers);

        $this->set("discount_types" , $this->PurchaseOrder->getDiscountTypes( ) );
        $discount_option =settings::getValue(InventoryPlugin,"purchase_discount_option" ) ;
        $this->set("discount_option" , ($discount_option?:'1' ) );

        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList());

        $this->loadModel('Country');
        $this->set('countries', $this->Country->getCountryList());

        $this->loadModel('InvoiceLayout');
        $this->set('default_pol', $this->InvoiceLayout->field('id', array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default_estimate' => true)));
        $this->set('default_prl', $this->InvoiceLayout->field('id', array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default_estimate' => true)));
        $invoiceLayoutsconditions[] = "InvoiceLayout.template_id <> 0";
        $invoiceLayoutsconditions['InvoiceLayout.layout_type']=InvoiceLayout::TYPE_PO;
        $invoiceLayouts = $this->InvoiceLayout->find('list', array('conditions' => $invoiceLayoutsconditions, 'order' => 'InvoiceLayout.name'));
        $this->set('invoiceLayouts', $invoiceLayouts);

        $invoiceLayoutsColumns = $this->InvoiceLayout->find('all', array('conditions'=>array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'item_columns IS NOT NULL AND item_columns <> \'\' ') ,'fields' => 'item_columns', 'order' => 'InvoiceLayout.name'));
        $item_columns=array();
        foreach ($invoiceLayoutsColumns as $key => $IL) {
//            debug($IL);
            $item_columns[$IL['InvoiceLayout']['id']] = json_decode($IL['InvoiceLayout']['item_columns'],true);

            //$this->data['Invoice']['item_columns'];
            //   $item_columns=  json_decode($this->data['Invoice']['item_columns'],true);
            if(isset($this->data['Invoice']['id']) && !empty($this->data['Invoice']['id'])){

                $item_columns[$IL['InvoiceLayout']['id']]=  json_decode($this->data['Invoice']['item_columns'],true);

            }


            //   debug($item_columns[$IL['InvoiceLayout']['id']] );
        }
        $this->set('item_columns',$item_columns);

        $LayoutLable[0] = array('label_item' => __('Item', true), 'label_tax1' => __('Tax 1', true), 'label_tax2' => __('Tax 2', true), 'label_quantity' => __('Quantity', true), 'label_unit_price' => __('Unit Price', true), 'label_item_total' => __('Item Total', true), 'label_from_date' => __('From Date', true), 'label_to_date' => __('To Date', true), 'label_discount' => __('Discount', true), 'label_description' => __('Description', true), 'label_subtotal' => __('Subtotal', true));
        $LayoutLable[-1] = array('label_item' => __('Item', true), 'label_tax1' => __('Tax 1', true), 'label_tax2' => __('Tax 2', true), 'label_quantity' => __('Hours', true), 'label_unit_price' => __('Hour Rate', true), 'label_item_total' => __('Item Total', true), 'label_from_date' => __('From Date', true), 'label_to_date' => __('To Date', true), 'label_discount' => __('Discount', true), 'label_description' => __('Description', true), 'label_subtotal' => __('Subtotal', true));

        $this->set('LayoutLable', $LayoutLable);

        $dbCustomFields = $this->InvoiceLayout->InvoiceLayoutCustomField->find('all', array('InvoiceLayoutCustomField.invoice_layout_id' => array_keys($invoiceLayouts)));
        $layoutCustomFields = array();
        $layoutCustomFields['0'] = $layoutCustomFields['-1'] = array();
        foreach ($dbCustomFields as $customField) {
            $layoutId = $customField['InvoiceLayoutCustomField']['invoice_layout_id'];
            if (empty($layoutCustomFields[$layoutId])) {
                $layoutCustomFields[$layoutId] = array();
            }
            $layoutCustomFields[$layoutId][] = $customField['InvoiceLayoutCustomField'];
        }


        $this->loadModel('Document');
        $this->set('documents', $this->Document->getInvoiceDocumentList());
        $this->set('documents_list', $this->Document->getDocumentList());
        $this->set('upload_settings', $this->Document->getFileSettings());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('dateFormats', getDateFormats(true));

//Ajax_product_search
        $aps = '1';
        $this->loadModel('Product');
        $count = $this->Product->find('count',array('recursive' => -1));
        if ($count <= 10) {
            $aps = '0';
            $this->set('products', $this->Product->getPurchaseOrderProductList());
        } else {
            $TopProductIds = [];
            $this->loadModel('PurchaseOrderItem');
            $this->PurchaseOrderItem->recursive = -1;
            $TopProductIds=[];

            $usd_product = $this->PurchaseOrderItem->find('all', array('order by count(PurchaseOrderItem.product_id) desc', 'limit' => 10,'fields' => array('count(PurchaseOrderItem.product_id) as total', 'PurchaseOrderItem.product_id'), 'conditions' => array('PurchaseOrderItem.product_id !=' => NULL, 'PurchaseOrderItem.product_id !=' => 0), 'group' => 'PurchaseOrderItem.product_id'));
            foreach ($usd_product as $product) {
                $TopProductIds[] = $product['PurchaseOrderItem']['product_id'];
            }
            foreach($this->data['PurchaseOrderItem'] as $purchaseOrderItem) {
                $TopProductIds[] = $purchaseOrderItem['product_id'];
            }
            // Get used product if there are no used products before !
            if (empty($TopProductIds)) {
                $used_product = $this->PurchaseOrderItem->find('all', ['limit' => 10, 'recursive' => -1]);
                foreach ($used_product as $product) {
                    $TopProductIds[] = $product['PurchaseOrderItem']['product_id'];
                }
            }
            $this->set('products', $this->Product->getPurchaseOrderProductList($TopProductIds, [], count($TopProductIds)));

        }

        $this->loadModel('Document');
        $this->set('aps', $aps);


        $this->loadModel('Language');
        $languages = $this->Language->getLanguageList();
        $this->set('languages', $languages);


        if (empty($this->data['PurchaseOrder']['language_id'])) {
            $ownerLanguage = getAuthOwner('language_id');
            if (!$ownerLanguage) {
                $ownerLanguage = 41;
            }
            $language = $languages[$ownerLanguage];
        } else {
            $language = $languages[$this->data['PurchaseOrder']['language_id']];
        }

        $this->set('purchaseorderLanguage', $language);




        if ( ifPluginActive(WorkOrderPlugin) ) {
            $this->loadModel('WorkOrder');
            if ( !empty ( $_GET['work_order_id'] )  ) {
                $this->data['PurchaseOrder']['work_order_id'] =  $_GET['work_order_id'];
            }
            $this->set ( 'work_order_ids' ,["" => '['.__("Choose from a list" , true ).']'] + $this->WorkOrder->get_work_orders ( ['WorkOrder.status'=>WorkOrder::STATUS_OPEN] ) ) ;
        }
        $enable_multi_units = settings::getValue(InventoryPlugin,'enable_multi_units');
        if ( $enable_multi_units ) {
            $this->loadModel('UnitTemplate');
            $unit_templates = $this->UnitTemplate->find('list' , ['fields' => 'template_name']);
            $this->set('unit_templates' ,$unit_templates );
        }
        $this->set('enable_multi_units' ,$enable_multi_units );

//        $this->set(compact('addSupplierLimit'));

        if (!empty($this->data['PurchaseOrderCustomField'])) {
            foreach ($layoutCustomFields as &$lcfs) {

                $icfs = $this->data['PurchaseOrderCustomField'];
                foreach ($lcfs as &$lcf) {
                    foreach ($icfs as $id => $icf) {
                        if (trim(strtolower($icf['label'])) == trim(strtolower($lcf['label']))) {

                            $lcf['value'] = $icf['value'];
                            unset($icfs[$id]);
                        }
                    }
                }
                foreach ($icfs as $id => $icf) {
                    $lcfs[] = $icf;
                }
            }
        }

        $zerolayout = json_decode(settings::getValue(InvoicesPlugin, 'initial_po_custom_fields'), true);

        $this->set('default_email_button', settings::getValue(InvoicesPlugin, 'default_email_button'));
        $this->set('default_print_button', settings::getValue(InvoicesPlugin, 'default_print_button'));

        foreach ($zerolayout as $line => $val2) {
            $layoutCustomFields[0][$line]['label'] = $val2['label'];
            $layoutCustomFields[0][$line]['value'] = $val2['value'];
            $layoutCustomFields[0][$line]['placeholder'] = $val2['placeholder'];
            $layoutCustomFields[0][$line]['display_order'] = 1;
            $layoutCustomFields[0][$line]['invoice_layout_id'] = 0;
            $layoutCustomFields[0][$line]['id'] = $line;
        }

        foreach ($this->data['PurchaseOrderCustomField'] as $line => $val2) {
            $layoutCustomFields[-99][$line]['label'] = $val2['label'];
            $layoutCustomFields[-99][$line]['value'] = $val2['value'];
            $layoutCustomFields[-99][$line]['placeholder'] = $val2['placeholder'];
            $layoutCustomFields[-99][$line]['display_order'] = 1;
            $layoutCustomFields[-99][$line]['invoice_layout_id'] = 0;
            $layoutCustomFields[-99][$line]['id'] = $line;
        }

        $this->set('layoutCustomFields', $layoutCustomFields);
    }

    function addBreadCrumbs($model, $url, $data = [])
    {
        App::import('vendor','BreadCrumbs',['file'=>'BreadCrumbs/autoload.php']);
        $tempUrl = [];
        foreach($url as $k => $v) {
            $tempUrl[$k] = $v;
        }
        $breadCrumbs = \BreadCrumbs\BreadCrumbs::generateBreadCrumbs($model, $tempUrl, $data);
        if($breadCrumbs) {
            $formattedBC = [];
            foreach($breadCrumbs as $k => $v) {
                foreach($v as $link => $title) {
                    $formattedBC[] = ['link' => $link, 'title' => __($title,true)];
                }
            }
            $formattedBC[0]['link'] = '/v2/owner/entity/purchase_quotation/list';  
            $action = array('action' => $this->params['action']);
            if (!empty($this->params['pass'])) {
                $action[] = $this->params['pass'][0];
            }
            $formattedBC[1]['link'] = "/v2/owner/entity/purchase_quotation/{$model->id}/show";
            foreach ($formattedBC as $key => $item) {
                $formattedBC[$key]['title'] = str_replace([__('Purchase Invoices', true), __('Purchase Invoice', true)], [__('Purchase Quotations', true), __('Purchase Quotation', true)], $item['title']);
            }

            $this->set('_PageBreadCrumbs', $formattedBC);
        }
    }

    function format_price($price, $code = false) {
        $this->layout = $this->autoRender = $this->autoLayout = false;
        echo format_price($price, $code);
        exit();
    }

    function _directSendEmail($purchase_orders, $layout_options = false, $owner = false) {

        if (empty($owner))
            $owner = getAuthOwner();

        if (is_numeric($purchase_orders)) {
            $purchase_orders_id = $purchase_orders;
        } else {
            $purchase_orders_id = $purchase_orders['PurchaseOrder']['id'];
        }

        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($purchase_orders_id);

        $this->loadModel('EmailTemplate');
        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-quotations');

        if (!empty($layout_options))
            $defaultTemplate = array_merge($defaultTemplate, $layout_options);
        if (!empty($layout_options['EmailTemplate']['to_email'])) {
            $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
        } else {
            $emails = array();
        }

        $result = $this->SysEmails->order($purchase_orders['PurchaseOrder'], $purchase_orders['Supplier'], $owner, $defaultTemplate, $purchase_orders['PurchaseOrderDocument'], false, $emails);
        if ($result) {
            $this->flashMessage(__('Purchase Quotation has been saved & sent', true), 'Sucmessage');
            $this->PurchaseOrder->save(array('PurchaseOrder' => array(
                'id' => $purchase_orders['PurchaseOrder']['id'],
                'draft' => 0,
                'last_sent' => date('Y-m-d')
            )), array('callbacks' => false, 'validation' => false, 'fieldList' => array('id', 'draft', 'last_sent')));
        } else {
            $this->flashMessage(sprintf(__('Failed to send the %s, %s', true), __('Purchase Quotation', true), $this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
        }
        return $result;
    }

    function owner_autocomplete() {
        Configure::write('debug', 0);
        if (!$this->RequestHandler->isAjax()) {
            $this->redirect(array('action' => ' index'));
        }

        if (empty($this->params['url']['term'])) {
            die(json_encode(array()));
        }
        $keywords = trim($this->params['url']['term']);
        $dbNos = $this->PurchaseOrder->find('all', array('limit' => 15, 'conditions' => array('PurchaseOrder.type' => 0,
            'OR' => array(
                'PurchaseOrder.supplier_business_name LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_first_name LIKE' => '%' . $keywords . '\'%',
                'PurchaseOrder.supplier_last_name LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.supplier_email LIKE' => '%' . $keywords . '%',
                'PurchaseOrder.no LIKE' => $keywords,
                'CONCAT(supplier_first_name,\' \',supplier_last_name) ' => '%' . $keywords . '%'
            )), 'recursive' => -1, 'fields' => 'PurchaseOrder.id, PurchaseOrder.no, PurchaseOrder.date, PurchaseOrder.supplier_business_name, PurchaseOrder.supplier_first_name,  PurchaseOrder.supplier_last_name', 'order' => 'PurchaseOrder.no, PurchaseOrder.date DESC, PurchaseOrder.supplier_first_name, PurchaseOrder.supplier_last_name'));
        $nos = array();
        $dateFormats = getDateFormats('std');
        $ownerFormat = $dateFormats[getCurrentSite('date_format')];
        foreach ($dbNos as $purchase_orders) {
            $nos[] = array('label' => $purchase_orders['PurchaseOrder']['no'] . ' - ' . $purchase_orders['PurchaseOrder']['supplier_business_name'] . ' ' . (!empty($purchase_orders['PurchaseOrder']['supplier_first_name']) ? '(' . $purchase_orders['PurchaseOrder']['supplier_first_name'] . ' ' . $purchase_orders['PurchaseOrder']['supplier_last_name'] . ')' : '') . ' - ' . date($ownerFormat, strtotime($purchase_orders['PurchaseOrder']['date'])), 'value' => $purchase_orders['PurchaseOrder']['id']);
        }

        die(json_encode($nos));
    }

    private function _formCommon($id = null) {
        App::import('Vendor', 'sites_local');
        $this->loadModel('Store');
        $this->loadModel('ItemPermission');
        $this->set('primary_store', $this->Store->getPrimaryStore(ItemPermission::PERMISSION_INVOICING));
        $this->set('bnf',Localize::get_business_number_fields());
        $this->set('more_than_1_store', \App\Helpers\Common\PurchaseOrderAndInvoiceHelper::invoiceHasMoreThanOneStore($this->data['PurchaseOrder']['store_id'], $this->data['PurchaseOrderItem']));
    }

    function owner_preview($id = false) {     
        App::import('Vendor', 'settings');
        $po_setting =  ( settings::formData(InventoryPlugin)) ;
        $this->set('po_setting',$po_setting);

        if (!empty($this->data)) {
            if(!empty($this->data['PurchaseOrder']['html_notes'])){
                $this->data['PurchaseOrder']['html_notes'] = $this->data['PurchaseOrder']['html_notes'] . '<span class="replace_template_notes"></span>';
            }
            if (!empty($this->data['PurchaseOrder']['id'])) {

                $oldPurchaseOrder = $this->PurchaseOrder->getPurchaseOrder($this->data['PurchaseOrder']['id']);

                $this->data['PurchaseOrder'] = array_merge($oldPurchaseOrder['PurchaseOrder'], $this->data['PurchaseOrder']);
            } else {

                $this->data['PurchaseOrder'] = array_merge($this->data['PurchaseOrder'], array('paid' => 0.0, 'payment_status' => 0));
            }

            $purchase_orders = $this->PurchaseOrder->prepareForView($this->data);
        } else {
            $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => array(PurchaseOrder::PURCHASE_QUOTATION)) , true );

            if (!$purchase_orders) {
                $this->flashMessage(__('Purchase Invoice not found', true));
                $this->redirect($this->referer(array('action' => 'index'), true));
            }

        }

        if(!isset($purchase_orders['PurchaseOrder']['type'])) {
            $purchase_orders['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_QUOTATION;
        }

        $row=$this->PurchaseOrder->Supplier->read(null,$purchase_orders['PurchaseOrder']['supplier_id']);

        $purchase_orders['Supplier']=$row['Supplier'];
        $this->set('purchase_orders', $purchase_orders);
        $tmp = getCurrentSite('purchaseorder_default_title');


        if (!empty($tmp)) {
            $purchase_orders_default_title = $tmp;
        } else {
            $purchase_orders_default_title = __('Purchase Quotation', true);
        }

        $this->set('purchaseorder_default_title', $purchase_orders_default_title);

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

//		$this->_setBranded();

        $this->set('taxes', $taxes = $this->PurchaseOrder->PurchaseOrderTax->getPurchaseOrderTaxList($id));
        $invoiceLayoutId = is_numeric($purchase_orders['PurchaseOrder']['id']) && $purchase_orders['PurchaseOrder']['id'] > 0 ? $purchase_orders['PurchaseOrder']['invoice_layout_id'] : false;

        $this->_getLayout($invoiceLayoutId);

        $this->set("show_discount", $purchase_orders['show_discount']);
        $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $this->layout = '';



        if ($this->params['url']['ext'] == 'pdf') {
            $this->render('../purchase_quotations/pdf/owner_preview');
        } else {
            $this->render('../purchase_quotations/owner_preview');
        }
    }

    function owner_pdf($id = false) {
        $this->owner_preview($id);
        $this->params['url']['ext'] = 'pdf';
        $this->render('../purchase_quotations/pdf/owner_preview');
    }

    function _getLayout($layout_id = false, $site_id = false, $purchase_refund = false) {

        $this->loadModel('InvoiceLayout');
        $layout = false;
        if (!$site_id) {
            $site_id = getCurrentSite('id');
        }
        if ($layout_id && $layout_id != '-1') {
            $layout = $this->InvoiceLayout->find(array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.id' => $layout_id));
        } else {
            $layout = $this->InvoiceLayout->find(array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.default_estimate' => 1));
        }

        if (!empty($layout) && !empty($layout['InvoiceLayout']['bug_v']) && $layout['InvoiceLayout']['bug_v'] == 3) {

            $layout = false;
            debug('BUG_V');
        }

        if ($layout_id == -1) {
            $layout = -1;
        }


        $this->set('layout', $layout);

        if (!empty($layout)) {
            if ($layout['InvoiceLayout']['template_id']) {
                $parentLayout = $this->InvoiceLayout->findById($layout['InvoiceLayout']['template_id']);
            } else {
                $parentLayout = $layout;
            }
            $this->set(compact('parentLayout'));
        }

        return $layout;
    }

    function owner_view($id = false) { 
        App::import('Vendor', 'settings');
        $po_setting =  (settings::formData(InventoryPlugin)) ;
        $this->set('enable_purchase_manual_status', settings::getValue(InvoicesPlugin, 'enable_purchase_manual_status'));
        $this->loadModel('FollowUpStatus');
        $this->set('po_setting',$po_setting);
        $this->loadModel('StockTransaction');
        $this->loadModel('Post');
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => PurchaseOrder::PURCHASE_QUOTATION));
        if (!$purchase_orders) {
            if(IS_REST) $this->cakeError('error404', array('message' => __('Purchase Invoice not found', true)));
            $this->flashMessage(__('Purchase Invoice not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        $this->loadModel('PurchaseOrderDocument');
        $PurchaseOrderDocuments = $this->PurchaseOrder->PurchaseOrderDocument->find('all',array('order'=>'PurchaseOrderDocument.id desc','conditions'=>array('PurchaseOrderDocument.purchase_order_id'=>$id)));
        $this->set('PurchaseOrderDocuments',$PurchaseOrderDocuments);
        $PurchaseOrderDocumentCount = $this->PurchaseOrder->PurchaseOrderDocument->find('count',array('conditions'=>array('PurchaseOrderDocument.purchase_order_id'=>$id)));
        $this->set('PurchaseOrderDocumentCount',$PurchaseOrderDocumentCount);
        $this->set("show_discount", $purchase_orders['show_discount']);
        $RequisitionModel = GetObjectOrLoadModel('Requisition');
        $requisition_exists = $RequisitionModel->find('first',
            [
                'conditions' => [
                    'Requisition.order_id' => $id,
                    'Requisition.order_type' => [Requisition::ORDER_TYPE_PURCHASE_REFUND, Requisition::ORDER_TYPE_PURCHASE_ORDER]
                ],
                'applyBranchFind' => false
            ]);
        $this->set('requisition_exists', $requisition_exists);


        $this->loadModel('Post');
//        $this->set('note_followup_list', $note_followup_list);
        $this->set('post_count', $this->Post->find('count', array('conditions' => ['item_id' => $id, 'item_type' => Post::PO_TYPE])));

        $this->set('refund_count',$this->PurchaseOrder->get_refund_count($id,true));
        $this->set('refund_list',$this->PurchaseOrder->get_refund_list($id,true));

        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if ((!check_permission(View_All_Purchase_Orders) && $purchase_orders['PurchaseOrder']['staff_id'] != $staff) || !check_permission(View_his_own_created_Purchase_Orders)) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__("You are not allowed to edit this purchase invoice", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }
        $this->set_journal($id);
        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->set('PurchaseOrderAllPaymentStatus', PurchaseOrder::getPaymentStatuses());
        $this->set('PurchaseOrderPaymentStatus', PurchaseOrderPayment::getPaymentStatus());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->set('stocks', StockTransaction::getPOTransactions($id));

        $emailLogs = $this->PurchaseOrder->EmailLog->find('all', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1, 'limit' => 10, 'order' => 'EmailLog.sent_date DESC'));
        $emailCount = $this->PurchaseOrder->EmailLog->find('count', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1));

        $this->set(compact('emailLogs', 'emailCount'));


        $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);

        $this->set('title_for_layout',  sprintf(__('Purchase Invoice #%s', true), $purchase_orders['PurchaseOrder']['no']));

        if ($this->params['url']['ext'] == 'pdf') {
            $this->_setBranded();
        }

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->find('list'));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Invoices', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';


        require_once APP . 'vendors' . DS . 'Timeline.php';

        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
        $this->loadModel('ActionLine');


        $action_list = $timeline->getPurchaseOrderActionsList();
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $invoice_actions[$key] = $action;
            }
        }
        $this->set('purchase_orders', $purchase_orders);
  
        $this->set('purchase_orders_id', $id);
        $this->set('actions', $invoice_actions);
        $this->loadModel('SitePaymentGateway');
        $pm=$this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        $pm['supplier_credit']=__('Supplier Credit',true);
        $this->set('payment_methods', $pm);
        $this->set('default_payment_method', $this->SitePaymentGateway->getDefaultPaymentGateway(getCurrentSite('id')));

        $this->loadModel('ItemsTag');
        $this->set('item_tag_type', ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER);
        $tags = $this->ItemsTag->get_item_tags($id,ItemsTag::TAG_ITEM_TYPE_PURCHASE_ORDER,true);
        $this->set('tags',$tags);

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('purchaseorder');

        if(!empty($printableTemplates))
            $this->set(compact('printableTemplates'));
        if(IS_REST){
            $this->set('rest_item', $purchase_orders);
            $this->set('rest_model_name', "PurchaseOrder");
            $this->render("view");
        }
        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        if ( $enable_requisitions){
            $this->loadModel('Requisition') ;
        }
        $this->set('enable_requisitions' ,$enable_requisitions);
        $FollowUpStatus = $this->FollowUpStatus->getLisWithColorList(Post::MANUAL_PURCHASE_ORDER);
        $this->set('FollowUpStatus', $FollowUpStatus);
    }


  //--------------------------------
  function preview($id = false) {
    App::import('Vendor', 'settings');
    $po_setting =  ( settings::formData(InventoryPlugin)) ;
    $this->set('po_setting',$po_setting);

    if (!empty($this->data)) {

        $this->data['PurchaseOrder']['type'] = PurchaseOrder::PURCHASE_QUOTATION;
        if (!empty($this->data['PurchaseOrder']['id'])) {
            $oldPurchaseOrder = $this->PurchaseOrder->getPurchaseOrder($this->data['PurchaseOrder']['id']);

            $this->data['PurchaseOrder'] = array_merge($oldPurchaseOrder['PurchaseOrder'], $this->data['PurchaseOrder']);
        } else {
            $this->data['PurchaseOrder'] = array_merge($this->data['PurchaseOrder'], array('paid' => 0.0, 'payment_status' => 0));
        }

        $purchase_orders = $this->PurchaseOrder->prepareForView($this->data);
    } else {
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.type' => PurchaseOrder::PURCHASE_QUOTATION));

        if (!$purchase_orders) {
            $this->flashMessage(__('Purchase Quotation not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

    }

    $this->set('purchase_orders', $purchase_orders);
    $tmp = getCurrentSite('purchaseorder_default_title');


    if (!empty($tmp)) {
        $purchase_orders_default_title = $tmp;
    } else {
        $purchase_orders_default_title = __('Purchase Quotation', true);
    }

    $this->set('purchaseorder_default_title', $purchase_orders_default_title);

    $this->loadModel('Country');
    $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
    $this->set('supplierCountry', $supplierCountry);

    $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
    $this->set('ownerCountry', $ownerCountry);

//		$this->_setBranded();

    $this->set('taxes', $taxes = $this->PurchaseOrder->PurchaseOrderTax->getPurchaseOrderTaxList($id));

    $this->_getLayout($purchase_orders['PurchaseOrder']['invoice_layout_id']);

    $this->layout = '';

      if (!isset($this->params['url']['ext']) || !in_array($this->params['url']['ext'], ['jpeg', 'pdf'])) { // render default view in case of jpeg ext
          $this->render('../purchase_quotations/owner_preview');
      }
  }


    function owner_send_to_supplier($id = null) {
  
        $check = check_daily_email_limit();


        if (!$check['status']) {
            $this->flashMessage($check['message']);
            $this->redirect(array('action' => 'view', $id));
        }

        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id);

        $purchase_quotations_type = 'purchase-quotations';
  
        if (empty($purchase_orders) || empty($purchase_quotations_type)) {
            $this->flashMessage(__('Purchase Quotation not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $showUrl = '/v2/owner/entity/purchase_quotation/'.$id.'/show';

        if (empty($purchase_orders['Supplier']['email'])) {
            $this->flashMessage(__('The Email address for this Supplier is not entered', true));
            $this->redirect($showUrl);
        }
        $this->set('purchase_quotations', $purchase_orders);
        $this->set('purchase_quotation', $purchase_orders);

        $this->loadModel('EmailLog');
        $this->loadModel('EmailTemplate');

        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('purchase-quotations');
 
        $this->set(compact('defaultTemplate'));

        if (!empty($this->data)) {
            
            if ($this->_directSendEmail($purchase_orders, $this->data)) {
                $this->flashMessage(__('The Purchase Quotation has been sent', true), 'Sucmessage');
                $this->redirect($showUrl);
            } else {
                $this->flashMessage(sprintf(__('Could not send %s', true), __('Purchase Quotation', true)));
            }
        } else {
            $this->data = $defaultTemplate;
            unset($this->data["EmailTemplate"]['id']);
        }
        $this->set('file_settings', $this->EmailLog->getFileSettings());
        $this->set('email_templates', $this->EmailTemplate->getEmailTemplates($purchase_quotations_type));
        $this->set('purchasequotation_type', $purchase_quotations_type);
        $this->set('PlaceHolders', $this->EmailTemplate->getPlaceHoldersList('purchase-quotations'));
        $this->set('supplier_email', $this->PurchaseOrder->Supplier->getSupplierEmails($purchase_orders['Supplier']['id']));
        $this->set('title_for_layout',  __('Send ' . $purchase_quotations_type . ' to supplier', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Quotations', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';
        $this->render('../purchase_quotations/owner_send_to_supplier');
    }


    function owner_timeline_row($id = null)
    {
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
        $this->set('data', $timeline->getDataArray());
        echo $timeline->view_action($id);
        die();
    }

    function owner_timeline($id = false) {
        $this->set('purchase_order_id', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        $purchase_orders = $this->PurchaseOrder->find('first',['recursive' => -1,'conditions'=>['PurchaseOrder.id'=>$id]]);
//
//
//        if (!$purchase_orders) {
//            $this->flashMessage(__('PurchaseOrder not found', true));
//            $this->redirect($this->referer(array('action' => 'index'), true));
//        }

        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PurchaseOrder', array('primary_id' => $id));
 
        if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::Purchase_Refund){
           $action_list = $timeline->getPurchaseRefundActionsList();
        }
        else if($purchase_orders['PurchaseOrder']['type']==PurchaseOrder::PURCHASE_QUOTATION){
            $action_list = $timeline->getPurchaseQuotationActionsList();  
        }else{ 
            $action_list = $timeline->getPurchaseOrderActionsList();
        }
 
        $timeline->init(array('primary_id' => $id), $action_list);

        $this->set('data', $timeline->getDataArray());

        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $purchase_orders_actions[$key] = $action;
            }
        }
        $this->set('actions', $purchase_orders_actions);
        $this->set('po_type',$purchase_orders['PurchaseOrder']['type']);
        //$this->set('jsonParams', $timeline->jsonAdapter(), false);
    }

    function view($id = null) {
        $this->Cookie->domain = Domain_Short_Name;
        $this->Cookie->write('trk', 'system_supplier',false,3.154e+7); // 1 year
        $this->_checkHash($id);
    }

    function _checkHash($id, $type = 12) {
        $conditions = array('PurchaseOrder.type' => $type);
        if ($type == 1) {
            $conditions['PurchaseOrder.draft !='] = 1;
        }
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, $conditions);
        if (!$purchase_orders) {
            $this->redirect(array('controller' => 'suppliers', 'action' => 'login'));
        }

        $purchase_ordersHash = $this->PurchaseOrder->getHash($purchase_orders);
        if ($purchase_ordersHash == $_GET['hash']) {
            $supplier = $this->PurchaseOrder->Supplier->findById($purchase_orders['PurchaseOrder']['supplier_id']);
            $this->PurchaseOrder->Supplier->reload_session($supplier);

            $this->redirect(array('action' => 'view_order', $id));
        } else {
            $this->redirect("/?error=404");
        }
    }

    function view_order($id = false) {  
 
        $purchase_orders = $this->PurchaseOrder->getPurchaseOrder($id, array('PurchaseOrder.supplier_id' => getAuth('SUPPLIER', 'id'), 'PurchaseOrder.type' => PurchaseOrder::PURCHASE_QUOTATION));

        if (!$purchase_orders) {
            $this->flashMessage(__('Purchase Quotation not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        $this->add_actionline(ACTION_SUPPLIER_VIEW_PO, array('staff_id' => -4, 'secondary_id' => $purchase_orders['PurchaseOrder']['supplier_id'], 'primary_id' => $purchase_orders['PurchaseOrder']['id'], 'param4' => $purchase_orders['PurchaseOrder']['no']));

//		$this->_setBranded();
        $this->set('purchase_orders', $purchase_orders);
        $emailLogs = $this->PurchaseOrder->EmailLog->find('all', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1, 'limit' => 10, 'order' => 'EmailLog.sent_date DESC'));
        $emailCount = $this->PurchaseOrder->EmailLog->find('count', array('conditions' => array('EmailLog.purchase_order_id' => $id), 'recursive' => -1));

        $this->set(compact('emailLogs', 'emailCount'));



        $this->set('title_for_layout',  sprintf(__('Purchase Quotation #%s', true), $purchase_orders['PurchaseOrder']['no']));

        if ($this->params['url']['ext'] == 'pdf') { 
            $this->_setBranded();
        }

        $this->loadModel('Country');
        $supplierCountry = $this->Country->field('country', array('Country.code' => $purchase_orders['PurchaseOrder']['supplier_country_code']));
        $this->set('supplierCountry', $supplierCountry);

        $ownerCountry = $this->Country->field('country', array('Country.code' => getCurrentSite('country_code')));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->find('list'));



        $this->set('statuses', PurchaseOrder::getStatuses());
        $this->set('statusescolor', PurchaseOrder::getStatusescolor());

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Purchase Quotations', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';
        $this->layout = "supplier";

        if($this->params['url']['ext'] == 'pdf') {
            $this->viewPath = "purchase_quotations/pdf";  
        } else {
            $this->viewPath = "purchase_quotations";  
        }

    }

    private function formateItems($items)
    {
        foreach ($items as $key => &$item) {
            if (!isset($item['description'])) {
                $item['description'] = $item['Product']['description'];
            }
        }
        return $items;
    }
    function owner_multi_pdf() {
        $this->loadModel('Country');
        $ids = $_POST['ids'];
        if(is_string($ids)){
            $ids = explode(',', $ids);
        }
        if(is_countable($ids) && count($ids) > 200){
            // TODO : try to split invoices into chunks and then assemble them together again
            $this->flashMessage(sprintf(__("Can't print more than %s %s", true), 200, __('Purchase Order', true)));
            $this->redirect('index');
        }
        $this->PurchaseOrder->recursive = 2;
        $selected_purchase_orders = $this->PurchaseOrder->find('all', ['conditions' => ['PurchaseOrder.id' => $ids]]);

        foreach ($selected_purchase_orders as $k => $v) {
            $selected_purchase_orders[$k] = $this->PurchaseOrder->prepareForView($v);
            $selected_purchase_orders[$k]['ilayout'] = $this->_getLayout($v['PurchaseOrder']['invoice_layout_id']);
            $selected_purchase_orders[$k]['clientCountry'] = $this->Country->field('country', array('Country.code' => $v['PurchaseOrder']['client_country_code']));
        }
        $this->set('selected_purchase_orders', $selected_purchase_orders);

        $ownerCountry = $this->Country->get_country_code(getCurrentSite('country_code'));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
    }
        
    function owner_update_draft($id, $draft_status = 1)
    {
        $owner = getAuthOwner();
        $purchase_quotation = $this->PurchaseOrder->getPurchaseOrder($id);
        if (!$purchase_quotation) {
            $this->izamFlashMessage(__('Purchase Quotation not found', true), 'danger');
            $this->redirect($this->referer('/v2/owner/entity/purchase_quotation/list', true));
        }

        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            if (check_permission(Edit_Delete_All_Purchase_Orders) || (check_permission(Edit_Delete_his_own_created_Purchase_Orders) && $owner['staff_id'] == $purchase_quotation['PurchaseOrder']['staff_id'])) {
                $this->izamFlashMessage(__("You are not allowed to edit this Purchase Quotation", true), 'danger');
                $this->redirect('/v2/owner/entity/purchase_quotation/list');
            }
        }
        $purchaseOrderNo = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_PURCHASE_QUOTATION);
        $result = $this->PurchaseOrder->update($id, ['draft' => $draft_status, 'no' => $purchaseOrderNo]);
        if ($result) {
            $oldData = ['id' => $id, 'draft' => $purchase_quotation['PurchaseOrder']['draft'], 'no' => $purchase_quotation['PurchaseOrder']['no']];
            $newData = ['id' => $id, 'draft' => $draft_status, 'no' => $purchaseOrderNo,];

            $st = getEntityBuilder()->buildEntity(EntityEntityKeyTypesUtil::PURCHASE_QUOTATION);
            $requests = (new EntityActivityLogRequestsCreator())->create($st, $newData, $oldData, []);
            $activityLogService = new \App\Services\ActivityLogService();
            foreach ($requests as $requestObj) {
                $activityLogService->addActivity($requestObj);
            }
            if (IS_REST) {
                $this->render('success');
                return;
            }
            $this->izamFlashMessage(__('Purchase Quotation has been updated', true));
            $this->redirect("/v2/owner/entity/purchase_quotation/$id/show");
        }
        if (IS_REST)
            $this->cakeError("error400", ["message" => $result['message'], "validation_errors" => $this->PurchaseOrder->validationErrors]);
        $this->izamFlashMessage(__('Could not update Purchase Quotation', true));
        if (!empty($this->PurchaseOrder->validationErrors)) {
            $errors = get_error_message($this->PurchaseOrder->validationErrors);
            CustomValidationFlash($errors);
        }
        if (isset($result['message'])) {
            $this->izamFlashMessage($result['message'], 'danger');
        }
    }

}

?>
