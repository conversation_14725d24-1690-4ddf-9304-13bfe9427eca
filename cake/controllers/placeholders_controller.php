<?php

class PlaceholdersController extends AppController
{
    var $uses = [];

    function owner_list($pluginId = null)
    {
        if (is_null($pluginId)) {
            $placeholderList = PlaceHolder::invoice_place_holder_list();
        } else {
            $placeholderList = match ((int)$pluginId) {
                WorkflowPlugin => PlaceHolder::work_order_place_holder_list(),
                ClientsPlugin => PlaceHolder::client_place_holder_list(),
                InvoicesPlugin => PlaceHolder::invoice_place_holder_list(),
                WorkOrderPlugin => PlaceHolder::work_order_place_holder_list(),
                FollowupPlugin => PlaceHolder::appointment_place_holder_list()+PlaceHolder::client_place_holder_list(),

                default => null
            };
        }
 
        $response = [];
        foreach ($placeholderList as $key => $value) {
            $response[] = [
                'id' => $key,
                'name' => $value
            ];
        }
        die(json_encode($response));
    }

    function owner_label($action = 'list', $pluginId = null)
    {
        $data = $_POST;
        $response = $data['equation'];
        if (is_null($pluginId)) {
            $placeholders = PlaceHolder::invoice_place_holder_list();
        } else {
            $placeholders = match ((int)$pluginId) {
                WorkflowPlugin => PlaceHolder::work_order_place_holder_list(),
                ClientsPlugin => PlaceHolder::client_place_holder_list(),
                InvoicesPlugin => PlaceHolder::invoice_place_holder_list(),
                WorkOrderPlugin => PlaceHolder::work_order_place_holder_list(),
                FollowupPlugin => PlaceHolder::appointment_place_holder_list()+PlaceHolder::client_place_holder_list(),
                default => null
            };
        }
        foreach ($placeholders as $placeholder => $label) {
            $response = str_replace($placeholder, "<span class='formula-tag'>${label}</span>", $response);
        }
        die($response);
    }
}