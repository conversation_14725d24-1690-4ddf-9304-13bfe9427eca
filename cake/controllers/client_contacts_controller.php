<?php

/**
 * @property Tooltip $Tooltip
 */
class ClientContactsController extends AppController {

    var $name = 'ClientContacts';
    var $helpers = array('Html', 'Form', 'Fck', 'Mixed');

    function owner_index() {
        if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Clients_View_All_Clients)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $conditions = $this->_filter_params();
		$conditions[] = 'ClientContact.Client__id IS NOT NULL';
        if (isBranchPluginActive() && !settings::getValue(BranchesPlugin, 'share_clients')) {
            $branchId = getCurrentBranchID();
            $conditions[] = "ClientContact.branch_id = $branchId";
        }
        $clientContacts = $this->paginate('ClientContact', $conditions) ?: [];
//        $this->set('client_contacts', $this->paginate('ClientContact', $conditions));
        $clientContacts = array_map(fn ($contact) => ['ClientContact' => array_merge($contact['ClientContact'], $contact[0])], $clientContacts);
        $this->set('client_contacts', $clientContacts);
        $this->setup_nav_data($clientContacts);
    }
	
	
	function _filter_params($params = false, $filters = array(), $passedModelName = false) {
        $conditions = parent::_filter_params($params, $filters, $passedModelName);

        if (!empty($conditions['ClientContact.name LIKE'])) {
            $conditions[] = "CONCAT_WS(' ', ClientContact.first_name, ClientContact.last_name, ClientContact.business_name, ClientContact.email, ClientContact.email, ClientContact.home_phone, ClientContact.mobile) LIKE '{$conditions['ClientContact.name LIKE']}'";
            unset($conditions['ClientContact.name LIKE']);
        }

	
        return $conditions;
    }

}
