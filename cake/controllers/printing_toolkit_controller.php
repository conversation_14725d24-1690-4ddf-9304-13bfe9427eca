<?php

use App\Services\PrintingToolkitService;

class PrintingToolkitController extends AppController
{
    var $uses = [];

    function owner_reopen_kiosk()
    {
        if (stristr(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows') === false) {
            $this->flashMessage(__("This action is only allowed on windows devices", true));
            return $this->redirect('/');
        }
        $this->layout = false;
        if ($_GET['download'] != '1')
            return;

        $service = new PrintingToolkitService();
        $language_code = getCurrentSite('language_code') == 7 ? 'ar' : 'en';
        $zipFile = $service->getDownloadable('https://' . getCurrentSite('subdomain') . '/pos/?lang=' . $language_code);

        $redirectUrl = substr($zipFile, strpos($zipFile, 'webroot'));
        header('Location: /' . $redirectUrl);
        exit();
    }
}
