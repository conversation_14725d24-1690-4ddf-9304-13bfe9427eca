<?php
/**
 * @property OibarcodeController $OibarcodeController
 *
 */
App::import('Vendor', 'OIBarcode', array('file' => 'OIBarcode/OIBarcode.php'));

class OibarcodesController extends AppController {

    public function owner_redirect_using_barcode( $barcode = null )
    {
        $data = OIBarcode::searchBarcode( $barcode );

        //you must entered a barcode that didn't exist or have un existed type
        if( empty( $data ) )
        {
            die( "fail" );
        }
        $view_link = str_replace('$id' ,$data['data'][$data['type_data']['model']]['id'] ,$data['type_data']['view_link']);

        $data = OIBarcode::getBarcodeData($barcode);

        $Refund_Receipt = 6;
        $Credit_Note = 5;
        $Estimate = 3;
        if($data['item_type'] == 2)
        {
            $this->loadModel($data['data']['model']);
            $invoice_to_redirect = $this->{$data['data']['model']}->read(null, $data['item_id']);
            if( $invoice_to_redirect['Invoice']['type'] == $Refund_Receipt )
            {
                $view_link = str_replace('invoices/view','invoices/view_refund',$view_link);
                die( "https://" . getCurrentSiteSubdomain() . '/' . $view_link );
            }
            else if( $invoice_to_redirect['Invoice']['type'] == $Credit_Note )
            {
                $view_link = str_replace('invoices/view','invoices/view_creditnote',$view_link);
                die( "https://" . getCurrentSiteSubdomain() . '/' . $view_link );
            }else if( $invoice_to_redirect['Invoice']['type'] == $Estimate )
            {
                $view_link = str_replace('invoices/view','invoices/view_estimate',$view_link);
                die( "https://" . getCurrentSiteSubdomain() . '/' . $view_link );
            }


        }
        die( "https://" . getCurrentSiteSubdomain() . '/' . $view_link );
    }

    public function owner_search_using_barcode( $barcode ){
        die(json_encode(OIBarcode::searchBarcode( $barcode )));
    }

}
?>