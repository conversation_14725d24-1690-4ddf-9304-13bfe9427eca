<?php

use Izam\Dynamic\List\Service\EntityDynamicService;

class EntitiesController extends  AppController {


    private $entityDynamicService;
    public  function  __construct()
    {
        $this->entityDynamicService = new EntityDynamicService();
        $this->setDefaultViewData();
        parent::__construct();
    }

    public function owner_list()
    {
        $this->set('data',$this->entityDynamicService->list());

        $this->view = 'izam';
        $this->render('entitiy/list');
    }


    public  function owner_index($key){

        $this->set('data', $this->entityDynamicService->index($key));
        $this->set('entityKey', $key);
        $this->view = 'izam';
        $this->render('entitiy/index');
    }

    public function owner_add($key)
    {
        $this->set('data',$this->entityDynamicService->create($key));
        $this->view = 'izam';
        $this->render('entitiy/add');
    }


    public function owner_store($key)
    {
        try {
            $this->entityDynamicService->store($this->params['form'], $key);
            $this->izamFlashMessage(sprintf(__t('%s Added Successfully'), __t($key)));
            $this->redirect(array('action'=>'index', $key));

        } catch (\Exception $exception) {
            $this->izamFlashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __($key)), 'danger');
            $this->redirect(array('action'=>'add', $key));
        }

    }

    public  function  owner_show($name, $id){
        dd($this->params['url'], $name, $id);
    }



    public function owner_edit($key, $id)
    {
        $this->set('data',$this->entityDynamicService->edit($key, $id));
        $this->view = 'izam';
        $this->render('entitiy/edit');
    }


    public function owner_update($key, $id)
    {
        try {
            $result = $this->entityDynamicService->update($this->params['form'], $id, $key);
            $this->izamFlashMessage(sprintf(__t('%s Added Successfully'), __t($key)));
            $this->redirect(array('action'=>'index', $key));

        } catch (\Exception $exception) {
            $this->izamFlashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __($key)), 'danger');
            $this->redirect(array('action'=>'add', $key));
        }

    }
    public function owner_delete($key, $id)
    {
        try {
            $result = $this->entityDynamicService->delete($key, $id);
            $this->izamFlashMessage(sprintf(__t('%s Deleted Successfully'), __t($key)));
            $this->redirect(array('action'=>'index', $key));

        } catch (\Exception $exception) {
            $this->izamFlashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __($key)), 'danger');
            $this->redirect(array('action'=>'add', $key));
        }

    }

}
