<?php

use App\Services\CostCenter\CostCenterAssigner;
use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Limitation\Utils\LimitationUtil;

class CostCentersController extends AppController {

	var $name = 'CostCenters';
	var $paginate = [
		'limit' => 1000
	];

	/**
	 * @var CostCenter
	 */
	var $CostCenter;
	var $helpers = array('Html', 'Form');
    var $index_url = '/v2/owner/cost-centers';
	

	function cost_center_action_line($type,$data){
		$action_line = [];
		$action_line['primary_id'] = $data['CostCenter']['id'];
		$action_line['param1'] = $data['CostCenter']['is_primary'];
		$action_line['param2'] = $data['CostCenter']['name'] ;
		$action_line['param3'] = $data['CostCenter']['code'] ;
		$parent_cost_center = $this->CostCenter->findById($data['CostCenter']['cost_center_id']);
		if($parent_cost_center)
		{
			$action_line['secondary_id'] = $data['CostCenter']['cost_center_id'];
			$action_line['param4'] = $parent_cost_center['CostCenter']['name'];
			$action_line['param5'] = $parent_cost_center['CostCenter']['code'];
			
		}
		$this->add_actionline($type,$action_line);
	}
	
	function journal_account_cost_center_action_line($type,$cost_center,$journal_account){

		
			$center = $this->CostCenter->findById($cost_center['cost_center_id']);
			$action_line['primary_id'] = $journal_account['JournalAccount']['id'];
			$action_line['secondary_id'] = $cost_center['cost_center_id'];
			$action_line['param1'] = $journal_account['JournalAccount']['name'];
			$action_line['param2'] = $journal_account['JournalAccount']['code'];
			$action_line['param3'] = $center['CostCenter']['name'];
			$action_line['param4'] = $center['CostCenter']['code'];
			$action_line['param5'] = $cost_center['percentage'];
			$action_line['param6'] = $cost_center['is_auto'];
			$this->add_actionline($type,$action_line);
	
	}
	
	function owner_index($id = null) {
		if (!check_permission(VIEW_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
        }
		$this->CostCenter->recursive = 0;
		$conditions = $this->_filter_params();
		// Root Parents Check
		if ($id === "-1") {
			$conditions[] = [
				'CostCenter.cost_center_id' => [0, -1]
			];
		}
		// Yes we have to use else if not else
		else if ($id && $id >= 0) {
			$conditions[] = [
				'CostCenter.cost_center_id' => $id
			];
		}
		$results = $this->paginate('CostCenter', $conditions);
		$CostCenterModel = GetObjectOrLoadModel('CostCenter');
		// Don't ask me why I did this
		foreach ($results as $key => $value) {
			$results[$key]['CostCenter']['has_children'] = $CostCenterModel->hasChildren($results[$key]['CostCenter']['id']);
		}
		if(IS_REST){
			$this->set('rest_items', $results);
			$this->set('rest_model_name', "CostCenter");
			return $this->render("index");
		}
		$this->set('costCenters', $results);
	}

	function owner_get_permissions() {
		die(json_encode([
			'view_cost_centers' => check_permission(VIEW_COST_CENTERS),
			'manage_cost_centers' => check_permission(MANAGE_COST_CENTERS),
			'view_his_own_reports' => check_permission(View_His_Own_Reports),
		]));
	}

	function owner_find() {
		$query = trim($_GET['q']);
		$data = [];
		$CostCenterModel = GetObjectOrLoadModel('CostCenter');
		$result = $CostCenterModel->find('all', [
			'recursive' => -1,
			'conditions' => ['OR' => ["CostCenter.name LIKE '%$query%'", "CostCenter.code LIKE '%$query%'"]]
		]);
		foreach ($result as &$item) {
			$costCenterIds = [];
			if($item['CostCenter']['cost_center_ids']) {
				$costCenterIds = array_merge($costCenterIds, explode(',', $item['CostCenter']['cost_center_ids']));
			} else {
				$item['CostCenter']['centers'] = "";
			}
		}
		if($costCenterIds) {
			$parents = $this->CostCenter->find('list', ['fields' => ['id', 'name'], 'conditions' => [ 'CostCenter.id' => $costCenterIds]]);
			if($parents) {
				foreach ($result as &$item) {
					if($item['CostCenter']['cost_center_ids']) {
						$itemParentIds = explode(',',$item['CostCenter']['cost_center_ids']);
						$itemParents = [];
						foreach ($itemParentIds as $id) {
							$itemParents[] = $parents[$id];
						}
						$item['CostCenter']['centers'] = implode(" > ", $itemParents);
					}
				}
			}
		}
		if (!empty($result)) {
			$data['data'] = $result;
		}
		die(json_encode($data));
	}

    function owner_json_find() {
		$query = trim($_GET['q']);
		$CostCenterModel = GetObjectOrLoadModel('CostCenter');
		$result = $CostCenterModel->find('all', [
			'fields' => ['id', 'name'],
			'recursive' => -1,
			'limit' => 30,
			'conditions' => [
                'OR' => ["CostCenter.name LIKE '%$query%'",
                "CostCenter.code LIKE '%$query%'"],
                "is_primary" => false,
                '(cost_center_id IN (SELECT id FROM cost_centers) OR (cost_center_id IN (-1,0) AND is_primary = 0))'
            ]
		]);
		$data = Set::extract('{n}.CostCenter', $result);
		die(json_encode($data ?: []));
	}
	function owner_view($id = null) {

        if (!check_permission(VIEW_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        if (!$id) {
            if (IS_REST) {
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Cost Center', true))]);
            }

            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('cost center', true)),true));
            $this->redirect(array('action'=>'index'));
        }

        $this->loadModel('CostCenter');
        $record = null;
        $record = $this->CostCenter->find('first', [
            'recursive' => -1,
            'conditions' => ['id' => $id]
        ]);
        if (!$record) {
            if (IS_REST) {
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Cost Center', true))]);
            }

            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('cost center', true)),true));
            $this->redirect(array('action'=>'index'));
        }

        $parent = null;
        if ($record['CostCenter']['cost_center_id'] != 0) {
            $parent = $this->CostCenter->find('first', [
                'recursive' => -1,
                'conditions' => ['id' => $record['CostCenter']['cost_center_id']]
            ]);
        }

        // Cost center financial accounts
        $this->loadModel('CostCentersJournalAccount');
        $financial_accounts = $this->CostCentersJournalAccount->find('count', [
            'recursive' => -1,
            'conditions' => ['cost_center_id' => $id]
        ]);

		$_SESSION['last_referer'] = '/' . $this->params['url']['url'];

        $this->set('financial_accounts', $financial_accounts);
        $this->set('cost_center', $record);
        $this->set('parent_cost_center', $parent);
    }

	function owner_accounts($id = null) {
		if (!check_permission(VIEW_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id) {
			if(IS_REST) $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('Cost Center', true))]);
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('cost center', true)),true));
			$this->redirect(array('action'=>'index'));
		}


        $this->loadModel('CostCentersJournalAccount');
		$cost_center_journal_accounts = $this->CostCentersJournalAccount->find('all',[ 'conditions' => ['CostCentersJournalAccount.cost_center_id' => $id] ]);
		if(IS_REST){
			$this->set('rest_item', $cost_center_journal_accounts);
			$this->set('rest_model_name', "CostCenterJournalAccount");
			$this->render("view");
		}
		$this->set('cost_center_accounts',$cost_center_journal_accounts);

	}
	
	function owner_journal_cost_center_transactions($journal_id){
		if (!check_permission(VIEW_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$journal_id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		
		$this->loadModel('CostCenterTransaction');
		$cost_transactions = $this->CostCenterTransaction->find('all',[ 'conditions' => ['CostCenterTransaction.journal_id' => $journal_id] ]);
		$grouped_transactions = [];
		foreach($cost_transactions as $k => $cost_transaction)
		{
            if($this->CostCenterTransaction->applicableForNewMode($journal_id)) {
                //group transactions by accounts
                $grouped_transactions[$cost_transaction['CostCenterTransaction']['journal_transaction_id']][] = $cost_transaction;
            }else {
                //group transactions by accounts
                $grouped_transactions[$cost_transaction['CostCenterTransaction']['journal_account_id']][] = $cost_transaction;
            }

			
		}
		
		if(isset($_GET['box']))
		{
			$this->layout = false;
			$this->set('isAjax',true);
		}
		
		$this->set('default_currency', $this->CostCenter->get_default_currency());
		$this->set('cost_center_transactions', $grouped_transactions);
	$this->set('id',$journal_id);
		
		}

	
	/**
	 * 
	 * @param type $journal_id
	 * @param type $account_id
	 * add manual cost center to account
	 */
	
	function owner_add_cost_center($journal_id = null, $account_id = null, $type = 'credit')
	{
		if (!check_permission(MANAGE_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		    $isEdit = false;
			$this->loadModel('JournalAccount');
			$this->loadModel('Journal');
			$is_ajax = $_GET['ajax'];
//			$is_ajax = true;
			$this->JournalAccount->recursive = -1;
			// for ajax
			if($_GET['account'])
				$account_id = $_GET['account'];
			
			
			$journal = $this->Journal->findByid($journal_id);

			if (!$journal && !$is_ajax) {
			
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal', true)),true));
			$this->redirect(array('action'=>'index'));
			}
			
			if($journal && !$account_id && $is_ajax){
				//for ajax edit
				foreach($journal['JournalTransaction'] as $k => $journal_transaction){
					if($journal_transaction['subkey'] == 'account')
					{
						//to get the account transaction only
						$account_id = $journal_transaction['journal_account_id'];
						break;
					}
				}
			}

			
			$journal_account = $this->JournalAccount->findById($account_id);
			if (!$journal_account && !$is_ajax) {
			
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('Journal Account', true)),true));
			$this->redirect(array('controller' => 'journals','action'=>'view', $journal_id));
			}

			$cost_centers = $this->JournalAccount->get_cost_centers($journal_account);

			$default_currency = $this->Journal->get_default_currency();
			$this->set('default_currency' , $default_currency);
			$fillData = isset($_POST['filldata']) ? $_POST['filldata'] : false;
            $journal_transaction = [];  
            foreach($journal['JournalTransaction'] as $k => $transaction)
            {
                if($transaction['journal_account_id'] == $account_id){
                    if(!empty($journal_transaction)) {
                        $journal_transaction[$type] += $transaction[$type];
                    } else {
                        $journal_transaction = $transaction;
                    }
                }
            }
            $total_transaction_amount = $journal_transaction[$type] ?: 0;
           
			if(!empty($this->data) && !$fillData)
			{

                $data = [];
                
				$cost_center_transactions = $this->data['CostCenterTransaction'];
           
				foreach($cost_center_transactions as $k => $cost_center_transaction)
				{
				    if($cost_center_transaction['percentage']==0){
                        $to_delete[]=$cost_center_transaction['id'];
				        continue;
                    }
					$data[$k]['CostCenterTransaction'] = $cost_center_transaction;
					$data[$k]['CostCenterTransaction']['journal_id'] = $journal_id;
					$data[$k]['CostCenterTransaction']['journal_account_id'] = $account_id;
					$data[$k]['CostCenterTransaction']['is_auto'] = false;
					$data[$k]['CostCenterTransaction']['currency_code'] = $default_currency;
					$data[$k]['CostCenterTransaction']['rate'] = 1;

				}
				if(!empty($to_delete)) {
                    $this->CostCenter->CostCenterTransaction->deleteAll(['CostCenterTransaction.id' => $to_delete]);
                }
                $sum = array_reduce($data, function ($sum, $item) {
                    $sum += (float) $item["CostCenterTransaction"]['credit'] + (float) $item["CostCenterTransaction"]['debit'] ;
                    return $sum;
                },0);
      
				if($total_transaction_amount+0.009 < $sum) {  
                    $this->flashMessage(__("Cost Center Transactions Total Amount Can't Exceed Account Transaction Amount", true));
                    $result = false;
				} else {
				    $result = true;
                }
                if($result) {

                    $result = $this->CostCenter->save_manual_cost_transactions($data);
					if (isset($_POST['box']) && $_POST['box']) {
						$this->handleResponseIframe('transaction_edited');
						return;
					}
                    if($result)
                    {

                        $this->flashMessage(sprintf (__('The %s has been saved', true), __('cost centers',true)), 'Sucmessage');
                        // $this->redirect(array('action'=>'index'));
                        $this->redirect(array('controller' => 'journals','action'=>'view', $journal_id));

                    }else{
                        $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('cost centers',true)));
                    }
                }
			}
			if($fillData)
            {
                foreach ($fillData as $k => $fillDatum)
                {
                    $cost_center_transactions[$k]['CostCenterTransaction'] = $fillDatum;
                }
            }else{
			    $isEdit = true;
                $this->loadModel('CostCenterTransaction');
                $conditions = ['CostCenterTransaction.journal_id' => $journal_id, 'CostCenterTransaction.journal_account_id' => $account_id ];
                switch($type) {
                    case 'debit':
                        $conditions[] = 'CostCenterTransaction.debit > 0';
                        break;
                    case 'credit':
                        $conditions[] = 'CostCenterTransaction.credit > 0';
                        break;	
                }
                $cost_center_transactions = $this->CostCenterTransaction->find('all', [ 'conditions' => $conditions ]);

                if(!$cost_center_transactions && $cost_centers){
                    foreach($cost_centers as $k => $v){
                        $cost_center_transactions[$k]['CostCenterTransaction']['journal_account_id'] = $v['JournalAccount']['id'];
                        $cost_center_transactions[$k]['CostCenterTransaction']['cost_center_id'] = $v['CostCenter']['id'];
                        $cost_center_transactions[$k]['CostCenter']['id'] = $v['CostCenter']['id'];
                        $cost_center_transactions[$k]['CostCenterTransaction']['percentage'] = $v['CostCentersJournalAccount']['percentage'];
                        if($_GET['type'] == 'debit')
                        {
                            $cost_center_transactions[$k]['CostCenterTransaction']['debit'] = $_GET['amount'] * $v['CostCentersJournalAccount']['percentage'] / 100;
                            $cost_center_transactions[$k]['CostCenterTransaction']['currency_debit'] = $_GET['amount'] * $v['CostCentersJournalAccount']['percentage'] / 100;
                        }else if($_GET['type'] == 'credit'){
                            $cost_center_transactions[$k]['CostCenterTransaction']['credit'] = $_GET['amount'] * $v['CostCentersJournalAccount']['percentage'] / 100;
                            $cost_center_transactions[$k]['CostCenterTransaction']['currency_credit'] = $_GET['amount'] * $v['CostCentersJournalAccount']['percentage'] / 100;
                        }
                    }
                }
            }

            $selectedCostCenter = [];
            foreach ($cost_center_transactions as $cost_center_transaction) {
                $selectedCostCenter[] = $cost_center_transaction['CostCenter']['id'];
            }
            $selectedCostCenter = $this->CostCenter->find('list', [
                'conditions' => [
                    'CostCenter.id' => $selectedCostCenter
                ]
            ]);
			$cost_centers_list = $this->CostCenter->getLastUsed(20) + $selectedCostCenter;
			if($is_ajax){
				if($_GET['type'] == 'debit')
				{
					$journal_transaction['debit'] = $_GET['amount'];	
					$journal_transaction['currency_debit'] = $_GET['amount'];	
				}else if($_GET['type'] == 'credit'){
					$journal_transaction['credit'] = $_GET['amount'];	
					$journal_transaction['currency_credit'] = $_GET['amount'];	
				}
				
				if(!$journal_account)
				{
					$journal_account['JournalAccount']['name'] = __('Auto', true);
					$journal_account['JournalAccount']['code'] = '';
				}
			}
            
			if($type == "debit")
            {   
                $accountIsDebit = true;
            }else{
                $accountIsDebit = false;
            }
            $this->set('type', $type);
            $this->set('accountIsDebit', $accountIsDebit);
			$this->set('cost_center_transactions', $cost_center_transactions);
			$this->set('cost_centers',$cost_centers);
			$this->set('cost_centers_list',$cost_centers_list);
			$this->set('journal_account',$journal_account);
			$this->set('journal_transaction', $journal_transaction);
			$this->set('journal',$journal_id);
			$this->set('title_for_layout',  __('Add Cost Center to Journal',true));
            $this->set('formAction',  [ 'url' => ['controller' => 'cost_centers','action' => 'add_cost_center', $journal_transaction['journal_id'],$journal_account['JournalAccount']['id'], $type]]);
			$this->set('is_ajax',$is_ajax);
            $this->set('isEdit', $isEdit);
            $this->set('formAction',  [ 'url' => ['controller' => 'cost_centers','action' => 'add_cost_center', $journal_transaction['journal_id'],$journal_account['JournalAccount']['id'], $type]]);
			$this->set('total_transaction_amount',round($total_transaction_amount,2));
	}


	public function owner_add_cost_center_draft($expenseId, $type)
	{
		if (!check_permission(MANAGE_COST_CENTERS)) {
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->loadModel('Expense');
		$expense = $this->Expense->find('first', ['conditions' => ['Expense.id' => $expenseId]])['Expense'];
		$cost_center_transactions = [];
		foreach (json_decode($expense['accounts_data'], true)  as $cost_center) {
			unset($cost_center['id']);
			$cost_center_transactions[]["CostCenterTransaction"] = $cost_center;
		}
		$cost_centers_list = $this->CostCenter->getLastUsed(20, array_column(json_decode($expense['accounts_data'], true), 'cost_center_id'));
		$this->set('type', $type);
		$this->set('accountIsDebit', true);
		$this->set('cost_center_transactions', $cost_center_transactions);
		$this->set('cost_centers',[]);
		$this->set('cost_centers_list',$cost_centers_list);
		$this->set('title_for_layout',  __('Add Cost Center to Journal',true));
		$this->set('is_ajax',true);
		$this->set('isEdit', false);
		$this->set('total_transaction_amount',round(array_sum( array_column(json_decode($expense['accounts_data'], true), $type)),2));
		$this->render('owner_add_cost_center');
	}
    
    function prepareCostCenterTransactionForView($journal_id, $account_id, $journal_transaction_id, $cost_centers, $currentTransaction) {
        $this->loadModel('CostCenterTransaction');
        $conditions = ['CostCenterTransaction.journal_id' => $journal_id,
        'CostCenterTransaction.journal_transaction_id' => $journal_transaction_id ,
        'CostCenterTransaction.journal_account_id' => $account_id ];

        $cost_center_transactions = $this->CostCenterTransaction->find('all', [ 'conditions' => $conditions ]);

        return $cost_center_transactions;
    }


  /**
     * This function add cost centers to journal transactions (New Impelementation)
     * @param mixed $journal_id 
     * @param mixed $journal_transaction_id 
     * @return void 
     */
   
    function owner_add_cost_center_v2($journal_id = null, $journal_transaction_id = null, $account_id = null)
    {
        if (!check_permission(MANAGE_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', true));
            $this->redirect('/');
        }
        $isEdit = false;
        $this->loadModel('JournalAccount');
        $this->loadModel('Journal');
        $is_ajax = $_GET['ajax'];
        $this->JournalAccount->recursive = -1;
        // for ajax
        if ($_GET['account']) {
            $account_id = $_GET['account'];
        }


        $journal = $this->Journal->findByid($journal_id);

        if (!$journal && !$is_ajax) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('Journal', true)), true));
            $this->redirect(array('action'=>'index'));
        }

        if ($journal && !$account_id && $is_ajax) {
            //for ajax edit
            foreach ($journal['JournalTransaction'] as $k => $journal_transaction) {
                if ($journal_transaction['subkey'] == 'account') {
                    //to get the account transaction only
                    $account_id = $journal_transaction['journal_account_id'];
                    break;
                }
            }
        }

        $journal_account = $this->JournalAccount->findById($account_id);
        if (!$journal_account && !$is_ajax) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('Journal Account', true)), true));
            $this->redirect(array('controller' => 'journals','action'=>'view', $journal_id));
        }

        $cost_centers = $this->JournalAccount->get_cost_centers($journal_account);

        $default_currency = $this->Journal->get_default_currency();
        $this->set('default_currency', $default_currency);
        $fillData = isset($_POST['filldata']) ? $_POST['filldata'] : false;

		// --- replaced POST block: delegate to service, keep same variable names/behavior ---
		if (!empty($this->data) && !$fillData) {
			$is_iframe = isset($_POST['box']) && $_POST['box'];
			$assigner = new \App\Services\CostCenter\CostCenterAssigner();

			$response = $assigner->assignCostCentersToJournalTransaction(
				$journal_id,
				$account_id,
				$journal_transaction_id,
				isset($this->data['CostCenterTransaction']) ? $this->data['CostCenterTransaction'] : [],
				$is_iframe
			);

			if (!$response['result']) {
				if (!empty($response['message'])) {
					$this->flashMessage($response['message']);
				}
				if (!empty($response['errors'])) {
					foreach ($response['errors'] as $err) {
						if ($err) {
							$this->flashMessage($err);
						}
					}
				}
				// stay on page (render) as before
			} else {
				if ($response['post_action'] === 'iframe') {
					$this->handleResponseIframe('transaction_edited');
					return;
				}
				$this->flashMessage(sprintf(__('The %s has been saved', true), __('cost centers', true)), 'Sucmessage');
				$this->redirect(array('controller' => 'journals','action'=>'view', $journal_id));
			}
		}
		// --- end replacement ---
		
        if ($fillData) {
            foreach ($fillData as $k => $fillDatum) {
                $cost_center_transactions[$k]['CostCenterTransaction'] = $fillDatum;
            }
        } else {
            $isEdit = true;
            $cost_center_transactions = $this->prepareCostCenterTransactionForView($journal_id, $account_id, $journal_transaction_id, $cost_centers, $currentTransaction);
        }

        $cost_centers_list = $this->CostCenter->getLastUsed(20);
        if ($is_ajax) {
            if ($currentTransaction['debit'] > $currentTransaction['credit']) {
                $journal_transaction['debit'] = $_GET['amount'];
                $journal_transaction['currency_debit'] = $_GET['amount'];
            } elseif ($_GET['type'] == 'credit') {
                $journal_transaction['credit'] = $_GET['amount'];
                $journal_transaction['currency_credit'] = $_GET['amount'];
            }

            if (!$journal_account) {
                $journal_account['JournalAccount']['name'] = __('Auto', true);
                $journal_account['JournalAccount']['code'] = '';
            }
        }

        if ($type == "debit") {
            $accountIsDebit = true;
        } else {
            $accountIsDebit = false;
        }
        $this->set('type', $type);
        $this->set('accountIsDebit', $accountIsDebit);
        $this->set('cost_center_transactions', $cost_center_transactions);
        $this->set('cost_centers', $cost_centers);
        $this->set('cost_centers_list', $cost_centers_list);
        $this->set('journal_account', $journal_account);
        $this->set('journal_transaction', $journal_transaction);
        $this->set('journal', $journal_id);
        $this->set('title_for_layout', __('Add Cost Center to Journal', true));
        $this->set('is_ajax', $is_ajax);
        $this->set('isEdit', $isEdit);
        $this->set('formAction', [ 'url' => ['controller' => 'cost_centers','action' => 'add_cost_center_v2', $journal_transaction['journal_id'],$journal_transaction['id'] ,$journal_account['JournalAccount']['id']]]);
        $this->set('total_transaction_amount', round($total_transaction_amount, 2));
        $this->render('../cost_centers/owner_add_cost_center');
    }

	
	
	/**
	 * used in cost_center_ajax request to check if the system containes cost_centers
	 */
	function owner_cost_center_count()
	{
        $this->CostCenter->recursive = -1;
		die(json_encode(['count' => $this->CostCenter->find('count')]));
	}
	
	function owner_assign_cost_centers($journal_account=null)
    {

        if (!check_permission(MANAGE_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        $ref = $_GET['ref'];
        if ($ref) {
            $cost_center = $this->CostCenter->findById($ref);
            $this->set('ref', $cost_center);
        }

        $this->loadModel('JournalAccount');
        $journal_account = $this->JournalAccount->findById($journal_account);
        if (!$journal_account) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('Journal Account', true)), true));
            $this->redirect($this->index_url);
        }

        $cost_centers = $this->JournalAccount->get_cost_centers($journal_account);
        if (!empty($this->data)) {
            $hasPleaseSelectId = false;
            foreach ($this->data['CostCentersJournalAccount'] as $costCenterTransactionJournalAccount) {
                // Ignore This Comment : array_search('-1', $this->data['CostCentersJournalAccount'], array_column($this->data['CostCentersJournalAccount'], 'cost_center_id)) === false
                if (empty($costCenterTransactionJournalAccount['cost_center_id'])) $hasPleaseSelectId = true;
            }
            if (!$hasPleaseSelectId) {
                $result = $this->CostCenter->save_account_cost_center($this->data, $journal_account);
                if ($result['status']) {
                    foreach ($this->data['CostCentersJournalAccount'] as $k => $cost_center_journal_account) {
                        if ($cost_center_journal_account['id']) {
                            $this->journal_account_cost_center_action_line(ACTION_JOURNAL_ACCOUNT_COST_CENTER_EDIT, $cost_center_journal_account, $journal_account);
                        } else {
                            $this->journal_account_cost_center_action_line(ACTION_JOURNAL_ACCOUNT_COST_CENTER_ADD, $cost_center_journal_account, $journal_account);
                        }
                        if (count($result['deleted_cost_centers'])) {
                            $this->loadModel('CostCentersJournalAccount');
                            $deleted_cost_center_journal_accounts = $result['deleted_cost_centers'];
                            foreach ($deleted_cost_center_journal_accounts as $csja) {
                                $this->journal_account_cost_center_action_line(ACTION_JOURNAL_ACCOUNT_COST_CENTER_DELETE, $csja['CostCentersJournalAccount'], $journal_account);
                            }
                        }
                    }
                    $this->flashMessage(sprintf(__('The %s has been saved', true), __('cost centers', true)), 'Sucmessage');
	                if (isset($_SESSION['last_referer'])) {
	                	$redirect_url = $_SESSION['last_referer'];
	                	unset($_SESSION['last_referer']);
		                $this->redirect($redirect_url);
	                }
	                $this->redirect(['action' => 'index']);
                } else {
                    $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('cost centers', true)));
                }
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please select a cost center', true), __('cost centers', true)));
            }
        }
        // No Data sent in $this->data but there was a post request (Delete all)
        else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	        $this->CostCenter->delete_account_cost_centers($journal_account);
	        $this->flashMessage(sprintf(__('The %s has been saved', true), __('cost centers', true)), 'Sucmessage');
	        if (isset($_SESSION['last_referer'])) {
	        	$redirect_url = $_SESSION['last_referer'];
	        	unset($_SESSION['last_referer']);
		        $this->redirect($redirect_url);
	        }
	        $this->redirect(['action' => 'index']);
        }
	    if (empty($_SESSION['last_referer'])) {
		    $_SESSION['last_referer'] = $this->referer();
	    }
        $cost_centers_list = $this->CostCenter->get_secondary_cost_centers(true);
        $this->set('cost_centers', $cost_centers);
        $this->set('cost_centers_list', $cost_centers_list);
        $this->set('journal_account', $journal_account);
        $this->set('title_for_layout',  __('Assign Cost Center To Account', true));
    }
	
	function owner_get_cost_center_child_code($parent_id)
	{
		$cost_center = $this->CostCenter->find('first', [ 'conditions' => ['CostCenter.cost_center_id' => $parent_id], 'order' => ['CostCenter.code' => 'DESC'] ]);
//		debug($cost_center);
		if($cost_center)
		{
			$code = $cost_center['CostCenter']['code'] + 1;
		}else{
			$cost_center = $this->CostCenter->findById($parent_id);
			$code = $cost_center['CostCenter']['code'] * 100 + 1;
		}
		if($code)
		die(json_encode($code));
		else die(json_encode(-1));
	}

    function owner_add()
    {
		$cost_center_limit = checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::COST_CENTERS);
		$this->set('cost_center_limit', $cost_center_limit);

        if (!check_permission(MANAGE_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        if (!empty($this->data)) {
            $this->CostCenter->create();
            if ($this->CostCenter->save($this->data)) {
                $this->data['CostCenter']['id'] = $this->CostCenter->id;
                $this->cost_center_action_line(ACTION_COST_CENTER_ADD, $this->data);

                if (isset($_POST['box']) && $_POST['box']) {
					$this->handleResponseIframe('cost_center_added');
					return;
                }
                $this->flashMessage(sprintf(__('The %s has been saved', true), __('cost center', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('cost center', true)));
            }
        }

        $this->loadModel('JournalAccount');
        $journalAccounts = $this->JournalAccount->find('list');
        $next_cost_center_code = $this->CostCenter->get_next_cost_center_code();
		$branch_state = $this->CostCenter->applyBranch;
		$this->CostCenter->applyBranch['onFind'] = false;
        $cost_center_codes = $this->CostCenter->find('list', ['fields' => ['id', 'code']]);
		$this->CostCenter->applyBranch = $branch_state;
        $cost_centers = $this->CostCenter->find('list', ['conditions' => ['CostCenter.is_primary' => 1], 'fields' => ['id', 'name']]);

        if (isset($_GET['box']) && $_GET['box'] || !empty($_POST['box'])) {
			$this->initBoxLayout();
		}


        if (isset($_GET['parent_id'])) {
            $parent_cost_center = $this->CostCenter->find(['CostCenter.id' => $_GET['parent_id']]);
            if ($parent_cost_center)
                $this->data['CostCenter']['cost_center_id'] = $parent_cost_center['CostCenter']['id'];
        }
        $this->set('cost_center_codes', $cost_center_codes);
        $this->set('next_cost_center_code', $next_cost_center_code);
        $this->set('cost_centers', $cost_centers);
        $this->set(compact('journalAccounts'));
        $this->set('title_for_layout',  __('Add Cost Center', true));
    }

    function owner_edit($id = null)
    {
        if (!check_permission(MANAGE_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', 'cost center', true)));
            $this->redirect($this->index_url);
        }

        $costCenter = $this->CostCenter->read(null, $id);
        if (!empty($this->data)) {
            if ($this->CostCenter->save($this->data)) {
                $this->cost_center_action_line(ACTION_COST_CENTER_EDIT, $this->data);
                if (isset($_POST['box']) && $_POST['box']) {
					$this->handleResponseIframe('cost_center_edited');
					return;
                }
                $this->flashMessage(sprintf(__('The %s  has been saved', true), __('cost center', true)), 'Sucmessage');
                $this->redirect('/owner/cost_centers/view/'.$costCenter['CostCenter']['id']);
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('cost center', true)));
            }
        }

        if (empty($this->data)) {
            $this->data = $costCenter;
        }

        $this->loadModel('JournalAccount');
        $journalAccounts = $this->JournalAccount->find('list');
        $this->set(compact('journalAccounts'));

        $cost_centers = $this->CostCenter->get_primary_cost_centers(true, $id, true, false);
        $cost_centers = ['-1' => __('Has No Parent Cost Center', true)] + $cost_centers;
        $next_cost_center_code = $this->CostCenter->get_next_cost_center_code();
        $cost_center_codes = $this->CostCenter->find('list', ['conditions' => ['CostCenter.id !=' => $id], 'fields' => ['id', 'code']]);
        $this->set('cost_center_codes', $cost_center_codes);
        $this->set('next_cost_center_code', $next_cost_center_code);
        $this->set('cost_centers', $cost_centers);

        $cost_center_transactions = $this->CostCenter->get_cost_center_transactions($id);
        $childs = $this->CostCenter->get_cost_center_children($id);
        $this->set('has_transactions', count($cost_center_transactions));
        $this->set('has_childs', count($childs));

        if (!empty($_GET['box'])  || !empty($_POST['box'])) {
			$this->initBoxLayout();
		}

        $this->set('title_for_layout',  __('Edit Cost Center', true));
        $this->render('owner_add');
		$this->set('cost_center_limit', []);
    }

	function owner_delete_assigned_cost_center($id = null)
    {
    
        if (!check_permission(MANAGE_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {

            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('cost center',true)));
            $this->redirect($this->index_url);
        }

        $module_name= __('Cost Center', true);
        if(is_countable($id) && count($id) > 1){
            $module_name= __('Cost Centers', true);
        }

        $this->loadModel('CostCentersJournalAccount');
        $records = $this->CostCentersJournalAccount->find('all', ['conditions' => ['CostCentersJournalAccount.id' => $id]]);

        if (empty($records)){

            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer($this->index_url, true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $cost_center_ids = $_POST['ids'];
            if($_POST['submit_btn'] == 'yes'){
                foreach($records as $k => $record){
                    if($this->CostCentersJournalAccount->delete($record['CostCentersJournalAccount']['id'])){
                        $this->journal_account_cost_center_action_line(ACTION_JOURNAL_ACCOUNT_COST_CENTER_DELETE, $record['CostCentersJournalAccount'], ['JournalAccount' => $record['JournalAccount']]);
                    }
                }
                $this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
	            if (isset($_SESSION['last_referer'])) {
		            $redirect_url = $_SESSION['last_referer'];
		            unset($_SESSION['last_referer']);
		            $this->redirect($redirect_url);
	            }
	            $this->redirect($this->index_url);
            }
            else{
                $this->redirect($this->index_url);
            }
        }
        if (empty($_SESSION['last_referer'])) {
	        $_SESSION['last_referer'] = $this->referer();
        }
        $this->set('records',$records);
    }

    function owner_delete($id = null)
    {
        if (!check_permission(MANAGE_COST_CENTERS)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }

        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf(__('Invalid id for %s', true), __('cost center', true)));
            $this->redirect($this->index_url);
        }

        $module_name = __('Cost Center', true);
        $costCenters = $this->CostCenter->find('all', array('conditions' => array('CostCenter.id' => $id)));
        if (empty($costCenters)) {
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer($this->index_url, true));
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $cost_center_ids = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes') {
                $deleted = true;
                foreach ($cost_center_ids as $k => $v) {
                    if ($this->CostCenter->deletAble($v)) {
						$actionLineObject = $this->CostCenter->read(['id', 'is_primary', 'name', 'code', 'cost_center_id',], $v);
                        if ($this->CostCenter->delete($v)) {
                            $this->cost_center_action_line(ACTION_COST_CENTER_DELETE, $actionLineObject);
                        }
                        $this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
                    } else {
                        $deleted = false;
                        $this->flashMessage(__("You can't delete a cost center which has transactions or sub cost centers or assigned to journal account", true));
                        break;
                    }
                }

                if (isset($_POST['box']) && $_POST['box']) {
                    if (!empty($deleted)) {
                        $this->handleResponseIframe('cost_center_deleted_yes');
                        return;
                    } else {
                        $this->handleResponseIframe('cost_center_deleted_no');
                        return;
                    }
                }

                $this->redirect($this->index_url);
            } else {
                if (isset($_POST['box']) && $_POST['box']) {
					$this->handleResponseIframe('cost_center_deleted_no');
					return;
				}
                $this->redirect($this->index_url);
            }
        }

        if (!empty($_GET['box']) || !empty($_POST['box']))
            $this->set('box', true);

        $this->set('costCenters', $costCenters);
    }

    function owner_get_auto_for_account($accId = null) {
        if(is_null($accId)) {
            die(json_encode([]));
        }
        $this->loadModel('JournalAccount');
		$this->loadModel('CostCenterTransaction');

		$account_cost_ceneters = $this->JournalAccount->get_cost_centers($accId,true);

        die(json_encode($account_cost_ceneters ?? []));

    }
}
?>
