<?php

App::import('Vendor', 'settings');

class UnitFactorsController extends AppController {

    var $name = 'UnitFactors';

   
    function beforeFilter() {
        parent::beforeFilter();
        
    }
    function owner_index ( ) {
        $unit_factors = $this->paginate ('UnitFactor' ) ;
        $this->set ( 'unit_factors' , $unit_factors ) ;
        $this->pageTitle=__("Unit Factors" , true );
    }
    function owner_add ( ) {
        
    }
    function owner_edit ( ) {
        
    }
    function owner_delete ( ) {
        
    }
}