<?php

use Izam\Daftra\ActivityLog\EntityActivityLogRequestsCreator;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;

App::import('Vendor', 'settings');

class UnitTemplatesController extends AppController {

    var $name = 'UnitTemplates';

   
    function beforeFilter() {
        parent::beforeFilter();
        
    }
    function owner_index ( ) {
//        if(!check_permission(PermissionUtil::Edit_General_Settings)){
//            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
//            $this->redirect($this->referer());
//        }
        $filters = $this->UnitTemplate->getFilters();
        $conditions = $this->_filter_params(null, $filters);
        $unit_templates = $this->paginate ('UnitTemplate', $conditions) ;
	    $this->setup_nav_data($unit_templates);
	    if (IS_REST) {
		    $unit_templates = $this->UnitTemplate->list_templates_with_units();
		    die(json_encode($unit_templates));
	    }


        $this->set ( 'unit_templates' , $unit_templates ) ;
        $this->pageTitle=__("Unit Templates" , true );
    }

    function owner_view($id = null) {
        if(!check_permission(PermissionUtil::Edit_General_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$unitTemplate = $this->UnitTemplate->read(null, $id);
		if (empty($unitTemplate)) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('Unit Template', true)),true));
			$this->redirect(array('action'=>'index'));
		}

        $this->set('unitTemplate', $unitTemplate);

	}

    function owner_add ( ) {
        if(!check_permission(PermissionUtil::Edit_General_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $unit_factors = [] ; 
        
        if (!empty($this->data)) {
            $this->UnitTemplate->create();
            $this->loadModel ('UnitFactor');
            $this->data['UnitTemplate']['staff_id'] = getAuthOwner('staff_id');
            if ($this->UnitTemplate->save($this->data)) {
                $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::UNIT_TEMPLATE_ENTITY_KEY, $this->UnitTemplate->id, 1)->toArray();
                $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::UNIT_TEMPLATE_ENTITY_KEY);
                $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
                $requests = $activityLogRequestCreator->create($st, $newData, [], []);
                $activityLogService =  new \App\Services\ActivityLogService();
                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }
                if (isset($this->data['UnitFactor']) && is_iterable($this->data['UnitFactor']))
				foreach ($this->data['UnitFactor'] as $k => $v ){
					$this->data['UnitFactor'][$k]['unit_template_id'] = $this->UnitTemplate->id;
				}
                    debug ( $this->data['UnitFactor']);
            if(count($this->data['UnitFactor'] ?? [])>0){
		$this->UnitFactor->saveAll($this->data['UnitFactor'] );
            }
                                           
//                $this->add_actionline(ACTION_ADD_PROJECT, array('primary_id' => $this->Project->id,'param1'=>$this->data['Project']['name'],'param2'=>$this->data['Project']['status']));                
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Unit Template', true)), 'Sucmessage');
                $redirect = array('action' => 'view', $this->UnitTemplate->id);
//                if (!empty($this->params['url']['box'])) {
//                    $redirect = array('action' => 'add', '?' => array('box' => 1, 'success' => 1));
//                }
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Unit Template', true)));
            }
            $unit_factors = [] ;
            if (isset($this->data['UnitFactor']) && is_iterable($this->data['UnitFactor']))
            foreach ( $this->data['UnitFactor'] as $v ){
                $unit_factors[]['UnitFactor'] = $v ;
            }
        }
        $this->set ( 'unit_factors' ,$unit_factors ) ;

    }
    function owner_edit ($id = null) {
        if(!check_permission(PermissionUtil::Edit_General_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $save=true;
        $this->loadModel ( 'UnitFactor'  ) ;
        $unit_template = $this->UnitTemplate->findById ( $id ) ;
        if ( empty ( $unit_template ) ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Unit Template",true)) ) ;
            $this->redirect ( 'index'); die ;
        }
        $unit_factors = $this->UnitFactor->find ('all' , ['conditions' => ['unit_template_id' => $id]]);
        if (!empty($this->data)) {
            $oldData = getRecordWithEntityStructure(EntityKeyTypesUtil::UNIT_TEMPLATE_ENTITY_KEY, $id, 3)->toArray();
            $this->data['UnitTemplate']['id'] = $id;
			
            foreach  ( $unit_factors as  $k=>$v ) {
                    if ( !in_array (  $v['UnitFactor']['id'] ,array_column($this->data['UnitFactor'] ?? [] ,'id' )) ){
                        if($this->UnitFactor->isUsed($v['UnitFactor']['id'])==true) {
                            $this->flashMessage(sprintf(__("Unit Factor With name <b>%s</b> have related items", true),$v['UnitFactor']['factor_name']), 'Errormessage', 'secondaryMessage');
                            $save=false;
                            break;
                        } else if
                        (ifPluginInstalled(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN) &&
                            $this->loadModel("SystemEtaUnit") &&
                           !empty( $this->SystemEtaUnit->find('first', ['conditions' => ['SystemEtaUnit.system_unit' => $v['UnitFactor']['id']]]))
                        ) {
                            $this->flashMessage(__('You cannot delete the unit template as it’s already selected in the electronic invoice Settings', true), 'Errormessage', 'secondaryMessage');
                            $save=false;
                            break;
                        }
                            $this->UnitFactor->delete($v['UnitFactor']['id']);

                    }
            }
//            $staffs =[];
            if (isset($this->data['UnitFactor']) && is_iterable($this->data['UnitFactor']))
            foreach ($this->data['UnitFactor'] as $k => $v ){
                    $this->data['UnitFactor'][$k]['unit_template_id'] = $id;
//                    $staffs[$this->data['UnitFactor'][$k]['staff_id']] = $this->data['StaffProjectHourlyRate'][$k]['hourly_rate'];
            }
            //print_r ( $this->data['StaffProjectHourlyRate']) ; die ; 
            
            if ($save && $this->UnitTemplate->save($this->data)) {
                if(count($this->data['UnitFactor'] ?? [])>0){
                            $this->UnitFactor->saveAll($this->data['UnitFactor'] );
                  }
              
                $newData = getRecordWithEntityStructure(EntityKeyTypesUtil::UNIT_TEMPLATE_ENTITY_KEY, $id, 3)->toArray();
                $st = getEntityBuilder()->buildEntity(EntityKeyTypesUtil::UNIT_TEMPLATE_ENTITY_KEY);
                $activityLogRequestCreator = new EntityActivityLogRequestsCreator();
                $requests = $activityLogRequestCreator->create($st, $newData, $oldData);
                
                $activityLogService =  new \App\Services\ActivityLogService();
                foreach ($requests as $requestObj) {
                    $activityLogService->addActivity($requestObj);
                }
                
//            $this->add_actionline(ACTION_EDIT_PROJECT, array('primary_id' => $this->Project->id,'param1'=>$this->data['Project']['name'],'param2'=>$this->data['Project']['status']));                                
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Unit Template', true)), 'Sucmessage');
                $this->redirect(array('action' => 'view', $id) );
            } else {
//                print_pre($this->UnitTemplate->validationErrors);
//                die();
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Unit Template', true)));
            }
        } 
            $this->data = $unit_template;
            $this->set ('unit_factors' ,  $unit_factors )  ;
            $this->render( 'owner_add' );
    }
    function owner_delete ( $id = null ) {
        if(!check_permission(PermissionUtil::Edit_General_Settings)){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (empty ($id) && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('Unit Template', true)));
            $referer_url = !($this->Session->check('referer_url')) ? array('action' => 'index') : $this->Session->read('referer_url');
            $this->Session->delete('referer_url');
            $this->redirect($referer_url);
        }
        $module_name = __('Unit Template', true);
        $unit_templates = $this->UnitTemplate->find('all', array('conditions' => array('UnitTemplate.id' => $id)));
        if (empty($unit_templates)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect(array('action' => 'index'));
        }
        if (ifPluginInstalled(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN)) {
            $this->loadModel('SystemEtaUnit');
            $systemEtaUnit = $this->SystemEtaUnit->find('first', array('conditions' => array('SystemEtaUnit.template' => $id)));
            if (!empty($systemEtaUnit)) {
                $this->flashMessage(__('You cannot delete the unit template as it’s already selected in the electronic invoice Settings', true));
                $this->redirect(array('action' => 'index'));
            }
        }
        $this->loadModel('Product');
        foreach ($unit_templates as $unit_template)
        {

        foreach($unit_template['UnitFactor'] as $v) {
            if ($this->UnitTemplate->UnitFactor->isUsed($v['id']) == true) {
                $this->flashMessage(sprintf(__("Unit template delete failed, Unit Factor With name <b>%s</b> have related items", true), $v['factor_name']));
                $this->redirect(array('action' => 'index'));
            }
        }
            if($this->Product->find('count', ['conditions' => ['Product.unit_template_id' => $unit_template['UnitTemplate']['id'] ] ]))
            {
                $this->flashMessage(__("Unit template delete failed, Make sure this unit template does'nt have related items.", true));
                $this->redirect(array('action' => 'index'));
            }
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
            if ($_POST['submit_btn'] == 'yes' && $this->UnitTemplate->delete_with_related($id)) {
//				App::import('Vendor', 'notification_2');
//				NotificationV2::delete_notificationbyref($id,NotificationV2::NOTI_UPDATE_WORK_ORDER);

//                foreach ($unit_templates as $w_order) {
//                    $this->add_actionline(ACTION_DELETE_WORK_ORDER, array('primary_id' => $w_order['WorkOrder']['client_id'], 'secondary_id' => $w_order['WorkOrder']['client_id'], 'param2' => $w_order['WorkOrder']['title'], 'param3' => $w_order['WorkOrder']['number']));
//                }
//                $this->add_stats(STATS_REMOVE_CLIENT, array(serialize($id)));
                $this->flashMessage(sprintf(__('%s has been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $referer_url = $this->_get_referer_path();
                $this->redirect(array('action' => 'index'));
            } else {
                $referer_url = $this->_get_referer_path();
                $this->redirect($referer_url);
            }
        }
        $this->set('unit_templates', $unit_templates);
        $this->set('module_name', $this->WorkOrder->title);
        
    }

    function _filter_params($params = false, $filters = array(), $passedModelName = false) {
        $conditions = parent::_filter_params($params , $filters, $passedModelName);
        return $conditions;
    }

    function owner_json_find($is_report = null, $emptyValue = null)
    {
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        if (!empty($value)) {
            $conditions['OR'][]['UnitTemplate.template_name like'] = "%$value%";
            $conditions['OR'][]['UnitTemplate.id like'] = "%$value%";
            $templates  = $this->UnitTemplate->find('all', ['conditions' => $conditions, 'recursive' => -1]);
            $result = array();
            foreach ($templates as $template) {
                $result[] = array(
                    'template_name' => $template['UnitTemplate']['template_name'],
                    'id' => $template['UnitTemplate']['id']
                );
            }
            if (!empty($result))
                array_unshift($result, array(
                    'template_name' => $emptyValue ? $emptyValue : __('Please Select', true) . ':',
                    'id' => ''
                ));
            echo json_encode($result, JSON_UNESCAPED_SLASHES);
            die();
        } else {
            echo json_encode(array());
            die();
        }
    }
}
