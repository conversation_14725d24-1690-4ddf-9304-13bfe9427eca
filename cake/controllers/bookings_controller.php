<?php

use Izam\Daftra\Common\Utils\InvoiceSourceTypesUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\ProductStatusUtil;

App::import('Vendor', 'settings');
App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
class BookingsController  extends AppController {
	var $uses = ['Invoice'];
	var $modelName = 'Invoice';
	var $Booking;
    var $components = array('Cookie', 'Email', 'SysEmails', 'ApiRequests');

    var $booking_js_labels = array(

        'Saving Client',
    );

    function beforeFilter()
    {
        if(!ifPluginActive(BookingPlugin)){
            $this->flashMessage(__("Enable the booking plugin to access this page", true));
            $this->redirect($this->referer());
        }
        parent::beforeFilter();
    }

	private function _formCommon($booking = null)
	{
        $staff_services = null;
	    if (settings::getValue(BookingPlugin, 'assign_services_to_employees')) {
	        $this->loadModel('StaffService');
            $staff_services = $this->StaffService->find('list',['fields' => ['StaffService.staff_id', 'StaffService.service_id', 'StaffService.service_id']]);
            $this->set('staff_services', $staff_services);
        }
		$this->loadModel('Category');
		$this->loadModel('Product');
		$productsConditions = ['Product.type' => 2, 'Product.duration_minutes IS NOT NULL','Product.duration_minutes > 0 ', ['OR' => ['Product.status is null', 'Product.status = ' . ProductStatusUtil::STATUS_ACTIVE]]];
		if ($staff_services) {
            $productsConditions['Product.id'] = array_keys($staff_services);
        }
		$categorized_products = $this->Category->getProdcutsCategoriezed($productsConditions);
        if(!getAuthClient() && empty($categorized_products)){
            $error_message = __("You need to have at least one active service and make sure you enter the duration and category of it to complete the Booking Reservation", true);
            $link = Router::url(['controller' => 'products', 'action' => 'add', 2, '?' => ['booking' => '1']]);
            $button_text = sprintf(__('Add %s', true), __('Service', true));
        }
		$this->loadModel('Client');
		if($client = getAuthClient()) {
		    if($client['group_price_id']) {
                $groupPrice = GetObjectOrLoadModel('GroupPrice');
                if(in_array($client['group_price_id'],array_keys($groupPrice->getActiveList()))) {
                    $ProductPriceModel = GetObjectOrLoadModel('ProductPrice');
                    $newPrices = $ProductPriceModel->find('all', [
                        'conditions' => [
                            'ProductPrice.group_price_id' => $client['group_price_id'],
                        ]
                    ]);
                    foreach ($categorized_products as $category => $items) {
                        foreach ($items as $k => $item) {
                            foreach ($newPrices as $price) {
                                if($item['Product']['id'] === $price['ProductPrice']['product_id']) {
                                    $categorized_products[$category][$k]['Product']['unit_price'] = $price['ProductPrice']['price'];
                                }
                            }
                        }
                    }
                }
            }
        }

		$this->loadModel('Staff');
		$this->loadModel('ItemStaff');
		$staffConditions = [];
        $staffsList = [];
		if(ifPluginActive(BranchesPlugin)) {
            $staffsList = array_keys($this->ItemStaff->getAssignedStaffList(\ItemStaff::BRANCH_ITEM_TYPE, getCurrentBranchID()));
        }
        $shiftStaffIds = array_keys($this->ItemStaff->getAssignedStaffList(\ItemStaff::SHIFT_ITEM_TYPE));

        $staffsList = array_intersect($shiftStaffIds, $staffsList);
        if($booking) {
            $staffsList[] = $booking->data['AssignedStaff']['staff_id'];
        }
        $staffConditions['OR'] = ['Staff.id' => $staffsList, 'AND' => ['Staff.branch_id' => getCurrentBranchID(), 'Staff.id' => $shiftStaffIds]];
        $staffConditions['and'] = ['Staff.active' => 1];
        $staff = $this->Staff->find('list', ['fields' => ['id','full_name'],'conditions' => $staffConditions]);
        if(!getAuthClient() && empty($shiftStaffIds) && !isset($error_message)){
            $error_message = __("You need to add a shift first after that assign it to one of the users of the system in order to complete the booking process", true);
            $link = Router::url('/v2/owner/shifts/create');
            $button_text =  sprintf(__('Add %s', true), __('Shift', true));
        }
        $this->set('error_message', $error_message);
        $this->set('link', $link);
        $this->set('button_text', $button_text);
		$this->set('staffs', $staff);
		$this->set('categorized_products', $categorized_products);
		$this->set('default_currency', $this->Product->get_default_currency());
		$this->set('book_one_service_only', settings::getValue(BookingPlugin, 'book_only_one_service'));
		$this->set('assign_services_to_employees', settings::getValue(BookingPlugin, 'assign_services_to_employees'));
	}
	
	function owner_add()
	{

		if(!check_permission([Add_Notes_Attachments_For_All_Clients,Add_Notes_Attachments_For_His_Assigned_Clients_Only])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
		$this->_formCommon();
		if(!empty($this->data)){

            $booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, $this->data);
			$result = $booking->save();
			if($result)
			{
			    $invoice = $this->Invoice->findById($this->Invoice->id);
			    $this->loadModel('Client');
			    $client = $this->Client->find('first', ['conditions' => ['Client.id' => $this->data['Invoice']['client_id']]]);
			    if($this->Invoice->id){
                    $this->add_actionline(ACTION_ADD_BOOKING, [
                        'primary_id' => $this->Invoice->id,
                        'secondary_id' => $this->data['Invoice']['client_id'],
                        'param1' => $this->Invoice->formatDate($this->data['Invoice']['date']),
                        'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                        'param6' => $invoice['Invoice']['no']
                    ]);
                }
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('Booking',true)), 'Sucmessage');
				$this->redirect(Router::url(['action' => 'view', $this->Invoice->id]));
			}else{
				$this->flashMessage(sprintf(__("The %s Couldn't be saved", true) , __('Booking',true) ), 'Errormessage', 'secondaryMessage');
			}
		}
		
	}
	
	function owner_edit($bookingId)
	{
		$booking = $this->checkBooking($bookingId);
        if(!$booking->canEdit()){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

		if($booking->isConverted())
		{
			$this->flashMessage(__("The Booking is Already Converted to Invoice, Delete the Invoice to Edit the Booking", true));
			$this->redirect(Router::url(['action' => 'view', $bookingId]));
		}
		$this->_formCommon($booking);
		if(!empty($this->data)){
			$this->data['Invoice']['id'] = $bookingId;
			$booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, $this->data);
			$result = $booking->updateI();
			if($result)
			{
                $invoice = $this->Invoice->findById($this->Invoice->id);
                $this->loadModel('Client');
                $client = $this->Client->find('first', ['conditions' => ['Client.id' => $this->data['Invoice']['client_id']]]);
                $this->add_actionline(ACTION_UPDATE_BOOKING, [
                    'primary_id' => $this->Invoice->id,
                    'secondary_id' => $this->data['Invoice']['client_id'],
                    'param1' => $this->Invoice->formatDate($this->data['Invoice']['date']),
                    'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                    'param6' => $invoice['Invoice']['no'],

                ]);
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('Booking',true)), 'Sucmessage');
				$this->redirect(Router::url(['action' => 'view', $this->Invoice->id]));
			}else{
				$this->flashMessage(sprintf(__("The %s Couldn't be saved", true) , __('Booking',true) ), 'Errormessage', 'secondaryMessage');
			}
		}else{
			
			$this->set('booking',$booking);
		}
		$this->render('owner_add');
	}
	
	function owner_index($response_type = 'data')
	{
        if(!check_permission([PermissionUtil::View_All_Attachments_ANd_Notes_For_His_Assigned_Clients,PermissionUtil::View_All_Attachments_And_Notes_For_All_Clients, PermissionUtil::View_His_Own_Notes_Attachments_Only])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->set('is_ajax', false);
        $onlyHasAppointments = true;
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
            $onlyHasAppointments = false;
        }

        $this->loadModel('Booking');
        // warning suppress
        $bookingRef = \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, [], $this->params);
		$booking =& $bookingRef;
        // end warning suppress
		$conditions = $this->_filter_params(False, $this->Booking->getFilters(), 'Invoice');

		if(isset($conditions['Invoice.staff_id']))
		{
			$conditions['Staff.id'] = $conditions['Invoice.staff_id'];
			unset($conditions['Invoice.staff_id']);
		}
		if(isset($conditions['Invoice.date >=']))
		{
			$conditions['FollowUpReminder.date >='] = $conditions['Invoice.date >='];
			unset($conditions['Invoice.date >=']);
		}
		if(isset($conditions['Invoice.date <=']))
		{
			$conditions['FollowUpReminder.date <='] = $conditions['Invoice.date <='];
			unset($conditions['Invoice.date <=']);
		}
        $limit  = 20;
		if($response_type === 'json') {
            $limit = 1000;
        }
        $owner = getAuthOwner();
        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            $viewHisOwnClient = !check_permission(Clients_View_All_Clients) && check_permission(Clients_View_his_own_Clients);
            $viewHisAppointment = !check_permission(View_All_Attachments_ANd_Notes_For_His_Assigned_Clients) && check_permission(View_His_Own_Notes_Attachments_Only);
            if ($viewHisOwnClient || $viewHisAppointment) {
                $conditions['`Staff`.`id` ='] = $staff;
            }
        }
		$bookings = $booking->getBookings($this,$conditions, $limit, $onlyHasAppointments);
		$this->setup_nav_data($bookings);
		$formats = getDateFormats('std');
		$dateFormat =  $formats[getCurrentSite('date_format')];
		$this->loadModel('FollowUpReminder');
		$this->set('followUpReminderStatuses',$this->FollowUpReminder->getStatuses());
		$this->set('dateFormat', $dateFormat);
		$this->set('invoiceStatuses', \InvoiceV2\Invoice::getStatuses());
		$this->set('bookings', $bookings);
		$this->set('bookingStatuses', \InvoiceV2\Booking::getStatuses());
        if ($response_type == 'json') {
            $app = []  ;
            foreach ($bookings as $key => $row) {
                $app[$key]['id'] = $row['Invoice']['id'];
                $app[$key]['appointment_id'] = $row['FollowUpReminder']['id'];
                $app[$key]['start'] = date('Y-m-d H:i',strtotime($row['FollowUpReminder']['date']));
                $app[$key]['end'] = date('Y-m-d H:i',strtotime($row['FollowUpReminder']['end_date']));
                $app[$key]['title'] = $row['Client']['business_name'] ?: "{$row['Client']['first_name']} {$row['Client']['last_name']}";
                // warning suppress
                $app[$key]['color'] = $FollowUpStatus[$row['Client']['follow_up_status']] ?? null;
            }
            echo json_encode($app);
            $this->autoRender = false;
            die ;
        }
	}
	
	function client_add()
	{
        $client_settings = settings::getPluginValues(ClientsPlugin);
		$bookingClientPaymentOption = Settings::getValue(BookingPlugin, 'booking_client_payment');
		$this->set('clientPaymentOption', $bookingClientPaymentOption);
		if($client_settings['client_permission_disable_booking'] == 1){
            if(IS_REST) $this->cakeError('error403');
            $this->redirect($this->referer());
		}
		
		$client = getAuthClient();
		$this->_formCommon();
         $this->loadModel('SitePaymentGateway');
        $paymentsGatewaysAvailable = $this->SitePaymentGateway->find('count',['conditions'=>['active'=>1,'disable_for_client IS NULL']]);
        $this->set('paymentGatewaysAvailable',$paymentsGatewaysAvailable);
		$this->set('client', $client);
		if(!empty($this->data)){
			$this->data['Invoice']['client_id'] = $client['id'];
			$booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, $this->data);
			$result = $booking->save();
			if($result)
			{
                $invoice = $this->Invoice->findById($this->Invoice->id);
                $this->add_actionline(ACTION_ADD_BOOKING_CLIENT, [
                    'primary_id' => $this->Invoice->id,
                    'secondary_id' => $this->data['Invoice']['client_id'],
                    'param1' => $this->Invoice->formatDate($this->data['Invoice']['date']),
                    'param2' => $client['business_name'] ?: "{$client['first_name']} {$client['last_name']}",
                    'param6' => $invoice['Invoice']['no'],
                ]);
                if(IS_REST){
                    $this->set('id', $result);
                    $this->render('created');
                    return;
                }
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('Booking',true)), 'Sucmessage');
				$this->redirect(Router::url(['controller' => 'appointments', 'action' => 'index']));
			}else{
			    $message = sprintf(__("The %s Couldn't be saved", true) , __('Booking',true) );
                if(IS_REST) $this->cakeError("error400", ["message"=>$message, "validation_errors"=>$booking->Model->validationErrors]);

                $this->flashMessage($message, 'Errormessage', 'secondaryMessage');
			}
		}
        $this->render('owner_add');
	}
	
	function client_edit($bookingId)
	{

        $client_settings = settings::getPluginValues(ClientsPlugin);
        $bookingClientPaymentOption = Settings::getValue(BookingPlugin, 'booking_client_payment');
        $this->set('clientPaymentOption', $bookingClientPaymentOption);
		if($client_settings['client_permission_disable_booking'] == 1){
			$this->redirect($this->referer());
		}
		$booking = $this->checkBooking($bookingId);
		if($booking->isConverted())
		{
			$this->flashMessage(__("This booking had been invoiced, to change or cancel your booking please contact support team", true));
            $this->redirect(Router::url(['controller' => 'appointments', 'action' => 'index']));
		}
		$client = getAuthClient();
		$this->set('client', $client);
		$this->_formCommon();
		if(!empty($this->data)){
			$this->data['Invoice']['id'] = $bookingId;
			$booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, $this->data);
			$result = $booking->updateI();
			if($result)
			{
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('Booking',true)), 'Sucmessage');
				$this->redirect(Router::url(['controller' => 'appointments','action' => 'index']));
			}else{
				$this->flashMessage(sprintf(__("The %s Couldn't be saved", true) , __('Booking',true) ), 'Errormessage', 'secondaryMessage');
			}
		}else{
			
			$this->set('booking',$booking);
		}
		$this->render('owner_add');
	}
	
	function owner_get_services()
	{
		if($this->RequestHandler->isAjax()){
			
		
		$services = [];
		if(!empty($_POST['services'])){
			$this->loadModel('Product');
			$services = $this->Product->find('all',['conditions' => ['Product.type' => 2, 'Product_id' => $_POST['services']]]);
		}
		die(json_encode($services));
		}
	}

	function owner_test()
    {
        $this->loadModel('Shift');
        $slots = $this->Shift->get_staff_available_time(14, '2019-08-11', 60, settings::getValue(BookingPlugin, 'booking_time_divider'));
        die(debug($slots));
    }

	public function get_staff_time(){
		$slots = [];
		if($this->RequestHandler->isAjax()){
			if(!empty($_POST['staff_id']) && !empty($_POST['date']) && !empty($_POST['duration']))
			{
				$this->loadModel('Shift');
				$duration = $_POST['duration'];
				
				$staff_id = $_POST['staff_id'];
				$date = $_POST['date'];
				$slot = settings::getValue(BookingPlugin, 'booking_time_divider');
				$ignore_appointments = $_POST['ignored_appointments'] ?: [];
				$slots = $this->Shift->get_staff_available_time($staff_id, $date, $duration, $slot, $ignore_appointments);
				die(json_encode($slots));
			}
		}
	}
	
	function owner_convert_booking_to_invoice($bookingId)
	{
		$booking = $this->checkBooking($bookingId);
		$result = $booking->convertToInvoice();
		
		$this->loadModel('Invoice');
		if($result['id'])
		{
            $invoice = $this->Invoice->findById($bookingId);
            $currentBooking = $booking->getBookings($this,['Invoice.id' => $bookingId])[0];
            $this->loadModel('Client');
            $client = $this->Client->find('first', ['conditions' => ['Client.id' => $currentBooking['Invoice']['client_id']]]);
            $this->add_actionline(ACTION_CONVERT_BOOKING_TO_INVOICE, [
                'primary_id' => $bookingId,
                'secondary_id' => $currentBooking['Invoice']['client_id'],
                'param1' => $this->Invoice->formatDate($currentBooking['Invoice']['date']),
                'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                'param5' => $result['id'],
                'param6' => $invoice['Invoice']['no']
            ]);
            if(IS_REST){
                $this->set('id', $result['id']);
                $this->render('created');
                return;
            }
			$this->flashMessage(sprintf (__('The %s has been saved', true), __('Invoice',true)), 'Sucmessage');
			$this->redirect(Router::url([ 'controller' => 'invoices','action' => 'edit', $result['id'], '?source_redirect=1']));
		}else if($result['error']){
            if(IS_REST) $this->cakeError("error400", ["message"=>$result['message']]);
            $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
			$this->redirect($this->referer());
		}
		
	}

    function client_convert_booking_to_invoice($bookingId)
    {
        $booking = $this->checkBooking($bookingId);
        $result = $booking->convertToInvoice();

        $this->loadModel('Invoice');
        if($result['id'])
        {
            if(IS_REST){
                $this->set('id', $result['id']);
                $this->render('created');
                return;
            }
            $this->flashMessage(sprintf (__('The %s has been saved', true), __('Invoice',true)), 'Sucmessage');
            $this->redirect(Router::url([ 'controller' => 'invoices','action' => 'edit', $result['id'], '?source_redirect=1']));
        }else if($result['error']){
            if(IS_REST) $this->cakeError("error400", ["message"=>$result['message']]);
            $this->flashMessage($result['message'], 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

    }

    function owner_view($bookingId = null)
    {
        if (empty($bookingId)) {
            $this->redirect(Router::url(['action' => 'index']));
        }
        $client_settings = settings::getPluginValues(ClientsPlugin);

        $this->set('client_settings',$client_settings);
		$booking = $this->checkBooking($bookingId);
        if(!$booking->canView())
        {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        $this->loadModel('PrintableTemplate');
        $printableTemplate = $this->PrintableTemplate->getDefaultTemplateForType('bookings');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('bookings');
        if(isset($printableTemplate[0])) {
            $printableTemplate = $printableTemplate[0];
        }else{
            $printableTemplate = $printableTemplates[0] ?? [];
        }
        $this->loadModel('FollowUpReminder');
		$appointment = $this->FollowUpReminder->getItemAppointments(FollowUpReminder::BOOKING_TYPE, $bookingId);
		$this->loadModel('Staff');

        $this->set('printableTemplate', $printableTemplate);
        $this->set('printableTemplates', $printableTemplates);
        $this->set('staffs', $this->Staff->getList());

		$this->set('appointment',$appointment[0]);
		$this->set('invoice', $booking->data);
		$this->set('booking', $booking);
		$this->set('entity', $booking);
        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('booking');
        $this->setup_nav_add_url(Router::url(['action'=>'add']));  

        $this->set('view_templates', $viewTemplates);

    }
	/**
	 * 
	 * @param type $bookingId
	 * @return type redirect if booking doesnt exist | booking entity if it exists
	 */
	private function checkBooking($bookingId)
	{
		$booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, $this->params);

        $conditions = [];
		if($clientId = getAuthClient('id'))
		{
			$conditions['Invoice.client_id'] = $clientId;
		}else if(!getAuthOwner()){

            $this->redirect('/');
		}

        $result = $booking->setEntityData($bookingId,$conditions);

        if(!$result)
		{
            if(IS_REST) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Invoice", true))));
            $this->flashMessage(__('Invoice not found',true), 'Errormessage', 'secondaryMessage');
			$this->redirect($this->referer());
			exit;
		}

        return $booking;
	}
	
	function client_cancel_booking($bookingId)
	{
		$booking = $this->checkBooking($bookingId);
        $statuses = \InvoiceV2\Booking::getStatuses();
        $oldStatus = $statuses[$booking->data['Invoice']['payment_status']];

        $result = $booking->changeStatus(\InvoiceV2\Booking::BOOKING_STATUS_CANCELLED);
		$msgType = $result['error'] == true?'Errormessage': 'Sucmessage';
        if(!$result['error'])
        {
            $this->loadModel('Client');
            $client = $this->Client->find('first', ['conditions' => ['Client.id' => $booking->data['Invoice']['client_id']]]);
            $this->add_actionline(ACTION_CLIENT_CANCEL_BOOKING, [
                'primary_id' => $bookingId,
                'secondary_id' => $booking->data['Invoice']['client_id'],
                'param1' => $booking->data['Invoice']['date'],
                'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                'param3' => $oldStatus,
                'param4' => $statuses[\InvoiceV2\Booking::BOOKING_STATUS_CANCELLED],
                'param6' => $booking->data['Invoice']['no'],
            ]);
        }

		$this->flashMessage($result['message'],$msgType);
		$this->redirect(Router::url(['action' => 'index','controller' => 'appointments']));

	}
	/**
	 * 
	 * @param type $bookingId
	 * @param type $newStatus
	 * changes the booking status
	 */
	function owner_change_status($bookingId, $newStatus)
	{
        if(IS_REST){
            $this->Invoice->applyBranch['onFind'] = false;
        }
		$booking = $this->checkBooking($bookingId);
		$statuses = \InvoiceV2\Booking::getStatuses();
		$oldBooking = $booking->getBookings($this,['Invoice.id' => $bookingId])[0];
		$oldStatus = $statuses[$oldBooking['Invoice']['payment_status']];
		$result = $booking->changeStatus($newStatus);
		$msgType = $result['error'] == true?'Errormessage': 'Sucmessage';
		$this->flashMessage($result['message'],$msgType);
		if ($result['error'] != true) {
            $invoice = $this->Invoice->findById($bookingId);
            $this->add_actionline(ACTION_UPDATE_BOOKING_STATUS, [
		        'primary_id' => $bookingId,
                'secondary_id' => $oldBooking['Invoice']['client_id'],
                'param3' => $oldStatus,
                'param4' => $statuses[$newStatus],
                'param6' => $invoice['Invoice']['no'],
            ]);
        }
		if(getAuthClient())
		{
			$this->redirect(Router::url(['controller' => 'appointments', 'action' => 'index']));
		}else{
			$this->redirect(Router::url(['action' => 'view', $bookingId]));
		}
		
		exit;
		
	}

    function client_delete($id = null)
    {
        $client_settings = settings::getPluginValues(ClientsPlugin);
        if($client_settings['client_permission_disable_booking'] == 1){
            if(IS_REST) $this->cakeError('error403');
            $this->redirect($this->referer());
        }
        $booking = $this->checkBooking($id);

        $this->loadModel('ApiKey');
        $apiKey = $this->ApiKey->findOrCreate('client');
        $result = $this->ApiRequests->request('/api2/invoices/'.$booking->data['ConvertedInvoice']['Invoice']['id'], $apiKey['ApiKey']['key'], 'DELETE');
        if($result['code'] == 200)
        {
            $result = $this->ApiRequests->request('/api2/bookings/'.$id, $apiKey['ApiKey']['key'], 'DELETE');
            if($result['code'] == 200)
            {
                if(IS_REST){
                    $this->set("message", $result['message']);
                    $this->render("success");
                    return;
                }
            }else{
                $this->cakeError("error400", ["message"=> $result['message']]);
            }

        }else{
            $this->cakeError("error400", ["message"=> $result['message']]);
        }
    }


	function owner_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 }
 		if (!$id && empty ($_POST)) {
            if(IS_REST){
                $this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), 'Booking')]);
            }
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('Booking',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('Booking', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('Bookings', true);
		 }
        $bookingEntity =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, []);
        if(IS_REST){
            $this->Invoice->applyBranch['onFind'] = false;
        }
        $bookings = $bookingEntity->getBookings($this, ['Invoice.id' => $_POST['ids']?:$id], 20, false);
        if(IS_REST){
            $_POST['submit_btn'] = true;
            $_POST['ids'] = [$id];
        }
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {

            if (empty($bookings)){
                if(IS_REST){

                    $this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), 'Booking')]);
                }
                $this->flashMessage(sprintf(__('%s not found', true), $module_name));
                $this->redirect($this->referer(array('action' => 'index'), true));
            }
//			$bookingsIds = $_POST['ids'];
			if($_POST['submit_btn'] == 'yes'){

				foreach($bookings as $k => $booking){
                    $bookingEntity->setEntityData($booking['Invoice']['id']);

					if($bookingEntity->canEdit() && !$bookingEntity->isConverted()){

						$bookingEntity->delete();

                        $client = $this->Client->find('first', ['conditions' => ['Client.id' => $booking['Invoice']['client_id']]]);
                        $this->add_actionline(ACTION_DELETE_BOOKING, [
                            'primary_id' => $booking['Invoice']['id'],
                            'secondary_id' => $booking['Invoice']['client_id'],
                            'param1' => $this->Invoice->formatDate($booking['Invoice']['date']),
                            'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                            "param6" => $booking['Invoice']['no'],
                        ]);

						$message = sprintf (__('%s Deleted', true), $module_name);
                        if(IS_REST){
                            $message = sprintf(__('%s Booking deleted successfully', true), "(#{$booking['Invoice']['no']})");
                            $this->set("message", $message);
                            $this->render("success");
                            return;
                        }
						$this->flashMessage($message, 'Sucmessage');
					}else{
					    $message = sprintf (__("%s Delete Failed, Please Make Sure You Have the Permission And the Booking is Not Converted to Invoice", true),  __('Booking',true) ."#".$booking['Invoice']['no']);
                        if(IS_REST){
                            $this->cakeError("error400", ["message"=> $message]);
                        }
					    $this->flashMessage($message);
						break;
					}

				}
				$this->redirect(array('action'=>'index'));

			}
			else{
				$this->redirect(array('action'=>'index'));
			}


		}

        $this->set('bookings',$bookings);
	}

    function _directSendEmail($record, $layout_options = false, $owner = false, $send_post_attachments = false) {
        $this->loadModel('Post');
        if (empty($owner))
            $owner = getAuthOwner();

        if (is_numeric($record)) {
            $record_id = $record;
        } else {
            $record_id = $record['Invoice']['id'];
        }
        $booking = $this->checkBooking($record_id);
        $record = $booking->data;
        $this->loadModel('EmailTemplate');
        // debug($post_attachments);
        $this->loadModel('Booking');
        //$defaultTemplate = $this->Booking->getDefaultEmailTemplate();
        $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('bookings');
        if (!empty($layout_options))
            $defaultTemplate = array_merge($defaultTemplate, $layout_options);
        if (!empty($layout_options['EmailTemplate']['to_email'])) {
            $emails = explode(",", $layout_options['EmailTemplate']['to_email']);
        } else {
            $emails = array();
        }

        $result = $this->SysEmails->booking($record, $record['Client'], $owner, $defaultTemplate, $emails);

        if ($result) {
            $this->flashMessage(__('Booking has been saved & sent', true), 'Sucmessage');
        } else {

            $this->flashMessage(sprintf(__('Could not send the Booking %s', true), $this->SysEmails->error_message), 'Errormessage', 'secondaryMessage');
        }
        return $result;
    }

    function owner_send_to_clients($id = null) {

        $this->loadModel('EmailLog');
        $this->loadModel('EmailTemplate');

        $check = check_daily_email_limit();
        if (!$check['status']) {
            $this->flashMessage($check['message']);
            $this->redirect(array('action' => 'index'));
        }
        if (count($_POST['ids'] ?? []) == 0 && empty($id)) {
            $this->flashMessage(__('Please select Invoice to send', true));
            $this->redirect(array('action' => 'index'));
        }
        $ids = $_POST['ids'];
        if($id)
        {
            $ids[] = $id;
        }
        foreach ($ids as $id) {

            $booking = $this->checkBooking($id);
            $record = $booking->data;
            if (!empty($record['Client']['email'])) {
                $this->set('record', $record);

                $defaultTemplate = $this->EmailTemplate->getDefaultTemplate('bookings');

                $this->set(compact('defaultTemplate'));

                $this->set('file_settings', $this->EmailLog->getFileSettings());
                $this->set('email_templates', $this->EmailTemplate->getEmailTemplates('bookings'));
                $this->set('template_type', 'bookings');
                $Placeholders = $this->EmailTemplate->getPlaceHoldersList('bookings');

                $this->set('PlaceHolders', $Placeholders);
                $this->set('client_email', $this->Invoice->Client->getClientEmails($record['Client']['id']));

                $row = $this->_directSendEmail($record);
            }
        }
        $this->flashMessage(__('The Bookings Have Been Sent', true), 'Sucmessage');
        $this->redirect(array('action' => 'index'));
    }

    function owner_settings()
    {
        if(!check_permission([Add_Notes_Attachments_For_All_Clients,Add_Notes_Attachments_For_His_Assigned_Clients_Only])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }
        if (!empty($this->data)) {
            $this->loadModel('SitePaymentGateway');
            $active_payment_options_enabled_for_client = $this->SitePaymentGateway->getPaymentGateways(false, true, true, false, false);

            if (!isset($this->data[BookingPlugin]['booking_time_divider']) || empty($this->data[BookingPlugin]['booking_time_divider'])) {
                $this->flashMessage(__('Please enter a valid number between 1 - 600 for the booking time divider', true));
            } elseif (
                ($this->data[BookingPlugin]['booking_client_payment'] == settings::BOOKING_CLIENT_PAYMENT_ENABLED || $this->data[BookingPlugin]['booking_client_payment'] == settings::BOOKING_CLIENT_PAYMENT_OPTIONAL)
                && empty($active_payment_options_enabled_for_client)
            ) {
                    $this->flashMessage(__('You should activate one payment option at least and enable it for the online client payments', true));
            } else {
                settings::setData($this->data);
                $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
            }
        }
        $this->set('form_data', settings::formData(BookingPlugin));
    }

    function owner_timeline($id = false) {
        $this->set('itemId', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }


        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('Booking', array('primary_id' => $id));

        $action_list = $timeline->BookingActionList();
        $timeline->init(array('primary_id' => $id), $action_list);
        $data = $timeline->getDataArray();
        $this->set('data', $data);
        $this->loadModel('ActionLine');

        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $actions[$key] = $action;
            }
        }
        $this->set('actions', $actions);
    }


    function get_services_prices($clientId) {
        $services = $_POST['services']?:[];
        $res = [];
        if(!empty($services)) {
            $client = GetObjectOrLoadModel('Client')->findById($clientId);
            //set client group prices for services
            if($client['Client']['group_price_id']) {
                $groupPrice = GetObjectOrLoadModel('GroupPrice');
                if(in_array($client['Client']['group_price_id'],array_keys($groupPrice->getActiveList()))) {
                    $ProductPriceModel = GetObjectOrLoadModel('ProductPrice');
                    $newPrices = $ProductPriceModel->find('all', [
                        'conditions' => [
                            'ProductPrice.group_price_id' => $client['Client']['group_price_id'],
                            'ProductPrice.product_id' => $services,
                        ]
                    ]);
                    $prices = [];
                    foreach ($newPrices as $price) {
                        $prices[$price['ProductPrice']['product_id']] = $price['ProductPrice']['price'];
                    }
                    $res['prices'] = $prices;
                }
            }
        }
        die(json_encode($res));
    }

    public function api_add(){
        $staff_services = null;
        if (settings::getValue(BookingPlugin, 'assign_services_to_employees')) {
            $this->loadModel('StaffService');
            $staff_services = $this->StaffService->find('list', ['fields' => ['StaffService.staff_id', 'StaffService.service_id', 'StaffService.service_id']]);
            $this->set('staff_services', $staff_services);
        }
        $this->loadModel('Category');
        $this->loadModel('Product');
        $productsConditions = ['Product.type' => 2, 'Product.duration_minutes IS NOT NULL', 'Product.duration_minutes > 0 ', ['OR' => ['Product.status is null', 'Product.status = ' . ProductStatusUtil::STATUS_ACTIVE]]];
        if ($staff_services) {
            $productsConditions['Product.id'] = array_keys($staff_services);
        }
        $servicesIds = [];
        foreach ($this->data['InvoiceItem'] as $invoiceItemKey => $invoiceItem) {
            $servicesIds[] = $invoiceItem['product_id'];
        }
        $productsConditions['AND']['Product.id'] = $servicesIds;
        if(!empty($this->data['Invoice']['branch_id'])){
            setRequestCurrentBranch($this->data['Invoice']['branch_id']);
        }
        $categorized_products = $this->Category->getProdcutsCategoriezed($productsConditions);
        if (!$categorized_products) {
            http_response_code(400);
            die(json_encode(['code' => 400, 'errors' => 'Please Enter Valid Service']));
        }
        if (!empty($this->data)) {
            $booking = &\InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, $this->data);
            $result = $booking->save();
            if ($result) {
                $invoice = $this->Invoice->findById($this->Invoice->id);
                $this->loadModel('Client');
                $this->loadModel('FollowUpReminder');
                $client = $this->Client->find('first', ['conditions' => ['Client.id' => $this->data['Invoice']['client_id']]]);
                if ($this->Invoice->id) {
                    $this->add_actionline(ACTION_ADD_BOOKING, [
                        'primary_id' => $this->Invoice->id,
                        'secondary_id' => $this->data['Invoice']['client_id'],
                        'param1' => $this->Invoice->formatDate($this->data['Invoice']['date']),
                        'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                        'param6' => $invoice['Invoice']['no']
                    ]);
                }
                $appointment = $this->FollowUpReminder->find('first', ['conditions' => ['FollowUpReminder.item_id' => $this->Invoice->id, 'FollowUpReminder.item_type' => FollowUpReminder::BOOKING_TYPE]]);
                if($this->data['Invoice']['convert']){
                    $booking = $this->checkBooking($this->Invoice->id);
                    $converted = $booking->convertToInvoice();
                    if($converted['id']){
                        http_response_code(200);
                        die(json_encode(['code' => 200, 'message' => sprintf(__('The %s has been saved', true), __('Booking', true)), 'id' => $this->Invoice->id, 'appointment_id' => $appointment['FollowUpReminder']['id'], 'invoice_id' => $converted['id']]));
                    }elseif($converted['error']){
                        http_response_code(200);
                        die(json_encode(['code' => 200, 'message' => sprintf(__('The %s has been saved', true), __('Booking', true)), 'id' => $this->Invoice->id, 'appointment_id' => $appointment['FollowUpReminder']['id'], 'error' => $converted['message']]));
                    }
                }
                http_response_code(200);
                die(json_encode(['code' => 200, 'message' => sprintf(__('The %s has been saved', true), __('Booking', true)), 'id' => $this->Invoice->id, 'appointment_id' => $appointment['FollowUpReminder']['id']]));
            } else {
                http_response_code(400);
                die(json_encode(['code' => 400, 'message' => sprintf(__("The %s Couldn't be saved", true), __('Booking', true))]));
            }
        }
    }

    public function api_edit($id = null){
        $this->Invoice->applyBranch['onFind'] = false;
        $booking = $this->checkBooking($id);
        if(!$booking->canEdit()){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

		if($booking->isConverted())
		{
			$this->flashMessage(__("The Booking is Already Converted to Invoice, Delete the Invoice to Edit the Booking", true));
			$this->redirect(Router::url(['action' => 'view', $bookingId]));
		}
        $staff_services = null;
        if (settings::getValue(BookingPlugin, 'assign_services_to_employees')) {
            $this->loadModel('StaffService');
            $staff_services = $this->StaffService->find('list', ['fields' => ['StaffService.staff_id', 'StaffService.service_id', 'StaffService.service_id']]);
            $this->set('staff_services', $staff_services);
        }
        $this->loadModel('Category');
        $this->loadModel('Product');
        $productsConditions = ['Product.type' => 2, 'Product.duration_minutes IS NOT NULL', 'Product.duration_minutes > 0 ', ['OR' => ['Product.status is null', 'Product.status = ' . ProductStatusUtil::STATUS_ACTIVE]]];
        if ($staff_services) {
            $productsConditions['Product.id'] = array_keys($staff_services);
        }
        $servicesIds = [];
        foreach ($this->data['InvoiceItem'] as $invoiceItemKey => $invoiceItem) {
            $servicesIds[] = $invoiceItem['product_id'];
        }
        $productsConditions['AND']['Product.id'] = $servicesIds;
        if(!empty($this->data['Invoice']['branch_id'])){
            setRequestCurrentBranch($this->data['Invoice']['branch_id']);
        }
        $categorized_products = $this->Category->getProdcutsCategoriezed($productsConditions);
        if (!$categorized_products) {
            http_response_code(400);
            die(json_encode(['code' => 400, 'errors' => 'Please Enter Valid Service']));
        }
        if (!empty($this->data)) {
            $this->data['Invoice']['id'] = $id;
			$booking =& \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, $this->data);
			$result = $booking->updateI();
            if ($result) {
                $invoice = $this->Invoice->findById($this->Invoice->id);
                $this->loadModel('Client');
                $this->loadModel('FollowUpReminder');
                $invoice = $this->Invoice->findById($this->Invoice->id);
                $this->loadModel('Client');
                $client = $this->Client->find('first', ['conditions' => ['Client.id' => $this->data['Invoice']['client_id']]]);
                $this->add_actionline(ACTION_UPDATE_BOOKING, [
                    'primary_id' => $this->Invoice->id,
                    'secondary_id' => $this->data['Invoice']['client_id'],
                    'param1' => $this->Invoice->formatDate($this->data['Invoice']['date']),
                    'param2' => $client['Client']['business_name'] ?: "{$client['Client']['first_name']} {$client['Client']['last_name']}",
                    'param6' => $invoice['Invoice']['no'],
                ]);
                if($this->data['Invoice']['convert']){
                    $booking = $this->checkBooking($id);
                    $converted = $booking->convertToInvoice();
                    if($converted['id']){
                        http_response_code(200);
                        die(json_encode(['code' => 200, 'message' => sprintf(__('The %s has been saved', true), __('Booking', true)), 'invoice_id' => $converted['id']]));
                    }elseif($converted['error']){
                        http_response_code(200);
                        die(json_encode(['code' => 200, 'message' => sprintf(__('The %s has been saved', true), __('Booking', true)), 'error' => $converted['message']]));
                    }
                }
                http_response_code(200);
                die(json_encode(['code' => 200, 'message' => sprintf (__('The %s has been saved', true), __('Booking',true))]));
            } else {
                http_response_code(400);
                die(json_encode(['code' => 400, 'message' => sprintf(__("The %s Couldn't be saved", true), __('Booking', true))]));
            }
        }
    }

    function owner_multi_delete()
    {
        $ids = $_POST['ids'];
        $conditions = [];
      
        if($_POST['index_action_select_type'] === 'all') {
            $conditions = $this->getCachedPaginationConditions("Booking");
            
            if(isset($conditions['Invoice.staff_id']))
            {
                $conditions['Staff.id'] = $conditions['Invoice.staff_id'];
                unset($conditions['Invoice.staff_id']);
            }
            if (isset($conditions['FollowUpReminder.date >='])) {
                $conditions['Invoice.date >='] = $conditions['FollowUpReminder.date >='];
                unset($conditions['FollowUpReminder.date >=']);
            }
            if (isset($conditions['FollowUpReminder.date <='])) {
                $conditions['Invoice.date <='] = $conditions['FollowUpReminder.date <='];
                unset($conditions['FollowUpReminder.date <=']);
            }

            $joins = [
                ['table' => 'follow_up_reminders' , 'alias' => 'FollowUpReminder' , 'type' => 'LEFT',
                    'conditions' => 
                    [
                        'FollowUpReminder.item_id = Invoice.id',
                        'FollowUpReminder.item_type = '.\Izam\Daftra\Common\Utils\FollowUpReminderUtil::BOOKING_TYPE,
                    ]
                ],
                ['table' => 'item_staffs' , 'alias' => 'ItemStaff' , 'type' => 'LEFT',
                    'conditions' => 
                    [
                        'FollowUpReminder.id = ItemStaff.item_id',
                        'ItemStaff.item_type = '.\ItemStaff::APPOINTMENT_ITEM_TYPE,
                    ]
                ],
                ['table' => 'staffs' , 'alias' => 'Staff' , 'type' => 'LEFT',
                    'conditions' => 
                    [
                        'Staff.id = ItemStaff.staff_id',
                    ]
                ],
            ];
            $filterData = $this->Invoice->find('all', ['conditions' => $conditions, 'joins' => $joins, 'recursive'=> -1]);
            $ids = array_column(array_column($filterData, "Invoice"), 'id');
        }       

        $params = json_decode($_POST['bulk_data'], true);        
        $this->set('ids', $ids);
        $this->set('url', $params['target_url']);
        $this->set('actionType', $params['action_type']);
        $this->set('entityName', $params['entity_name']);
        $this->set('back_url', $params['back_url']);
        $this->set('action', $params['_method']);
        $this->set('title_for_layout', $params['title_for_layout']);
        $this->set('_PageBreadCrumbs', json_decode($params['breadcrumbs'], true));
		$this->setDefaultViewData();
		$this->view = 'izam';
		$this->render('bulk/bulk');       
    }

    
}
