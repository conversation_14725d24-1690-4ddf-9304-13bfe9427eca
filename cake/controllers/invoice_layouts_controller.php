<?php

use Izam\Daftra\Common\Utils\PermissionUtil;

class InvoiceLayoutsController extends AppController {

    var $name = 'InvoiceLayouts';

    /**
     * @var InvoiceLayout
     */
    var $InvoiceLayout;
    
    var $helpers = array('Html', 'Form');
    var $more_js_lables = array('Add Field',
        'Client Info',
        'Custom Fields',
        'Ship Info',
        'Change Labels',
        'Logo',
        'Business Info',
        'Footer',
        'Reset Input',
        'Invoice Notes',
        'Ok',
        'Cancel',
        'Save', 'Edit invoice title', 'Description', 'Description here'
        ,'Item', 'Unit Price', 'Quantity', 'Subtotal', 'Item Total', 'Paid Amount', 'Unpaid Amount', 'Total', 'Ship', 'Invoice No', 'Date'
        ,'Style',
        'Invoice Title',
        'Note',
        'Item Columns',
        'None',
    );
    
    
    
    public static function get_columns_options()
    {
        return array('show_quantity_before_price'=>__('Show quantity before unit price',true),'hide_price_and_quantity'=>__('Hide quantity and unit price',true));
    }
    function owner_edit_html($id= null , $type = 1 ){
        $layout = $this->InvoiceLayout->find('first' , ['conditions' => ['id' => $id ]]);
        if ( !empty ( $this->data ) ) {
            $this->InvoiceLayout->id = $id ; 
            $this->InvoiceLayout->save($this->data ) ;
            $this->flashMessage(__("Saved Successfully.", true), 'Sucmessage', 'secondaryMessage');
            $this->redirect(['controller' => 'invoice_layouts' , 'action' => 'edit' , $id] );die ; 
        }
        $this->data = $layout ;
        $this->set('layout' , $layout);
        $this->set('type' , $type);
    }
    function admin_add() {
        $this->js_lang_labels = array_merge($this->more_js_lables, $this->invoice_js_labels);
        $owner = getAuthOwner();
        if (!empty($this->data)) {
            $this->data['InvoiceLayout']['staff_id'] = $owner['staff_id'];

            $this->InvoiceLayout->create();

            if ($this->InvoiceLayout->save($this->data)) {

//                App::import('Vendor', 'notification_2');
//                NotificationV2::delete_notification(NotificationV2::NOTI_NO_INVOICE_LAYOUT);
                $Errors = '';
                if (!empty($this->data['InvoiceLayoutCustomField'])) {
                    $this->loadModel('InvoiceLayoutCustomField');
                    foreach ($this->data['InvoiceLayoutCustomField'] as $i => $item) {
                        if (!empty($this->data['InvoiceLayoutCustomField'][$i]['label'])) {
                            $this->data['InvoiceLayoutCustomField'][$i]['invoice_layout_id'] = $this->InvoiceLayout->id;
                            $this->InvoiceLayoutCustomField->create();
                            $data['InvoiceLayoutCustomField'] = $this->data['InvoiceLayoutCustomField'][$i];
                            if (!$this->InvoiceLayoutCustomField->save($data['InvoiceLayoutCustomField'], false))
                                $Errors.="Can't Save for " . $data['InvoiceLayoutCustomField']['label'] . ":<br/>" . implode("<br/>", $this->InvoiceLayoutCustomField->validationErrors) . "<br/><br/>";
                        }
                        else {
                            unset($this->data['InvoiceLayoutCustomField'][$i]);
                        }
                    }
                }

                if (!empty($this->data['InvoiceLayoutTag'])) {
                    $this->loadModel('InvoiceLayoutTag');
                    foreach ($this->data['InvoiceLayoutTag'] as $i => $item) {
                        if (!empty($this->data['InvoiceLayoutTag'][$i]['label'])) {
                            $this->data['InvoiceLayoutTag'][$i]['invoice_layout_id'] = $this->InvoiceLayout->id;
                            $this->InvoiceLayoutCustomField->create();
                            $data['InvoiceLayoutTag'] = $this->data['InvoiceLayoutTag'][$i];
                            if (!$this->InvoiceLayoutTag->save($data['InvoiceLayoutTag'], false))
                                $Errors.="Can't Save for " . $data['InvoiceLayoutTag']['tag'] . ":<br/>" . implode("<br/>", $this->InvoiceLayoutTag->validationErrors) . "<br/><br/>";
                        }
                        else {
                            unset($this->data['InvoiceLayoutTag'][$i]);
                        }
                    }
                }

                if ($Errors) {
                    $this->flashMessage($Errors);
                } else {
                    $this->flashMessage(sprintf(__('%s has been saved', true), __('Invoice layout', true)), 'Sucmessage');
                    //   $this->redirect(array('action' => 'index'));
                }
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Invoice layout', true)));
            }
        }
    }

    function admin_edit($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
            $this->redirect(array('action' => 'index'));
        }
        $invoiceLayout = $this->InvoiceLayout->read(null, $id);
        $this->loadModel('Site');
        if (!empty($this->data)) {
            if (empty($this->data['InvoiceLayout']['id'])) {
                $check = $this->Site->check_add_layout_limit();
                if (!$check['status']) {
                    $this->_flashLimitMessage($check['message'], __('invoice layout', true));
                }
            }
            if ($this->InvoiceLayout->save($this->data)) {
                $Errors = '';
                $this->loadModel('InvoiceLayoutCustomField');
                if (!empty($this->data['InvoiceLayoutCustomField'])) {
                    $this->InvoiceLayoutCustomField->recursive = -1;
                    $layout_texts = $this->InvoiceLayoutCustomField->find('list', array('conditions' => array('InvoiceLayoutCustomField.invoice_layout_id' => $id)));
                    $delete_text_array = array();
                    foreach ($this->data['InvoiceLayoutCustomField'] as $i => $item) {
                        $texts_posted_ids[] = $item['id'];
                    }
                    $delete_text_array = array_diff($layout_texts, $texts_posted_ids);
                    $this->InvoiceLayoutCustomField->deleteAll(array('InvoiceLayoutCustomField.id' => $delete_text_array));

                    $this->loadModel('InvoiceLayoutCustomField');
                    foreach ($this->data['InvoiceLayoutCustomField'] as $i => $item) {
                        if (!empty($this->data['InvoiceLayoutCustomField'][$i]['label'])) {
                            $this->data['InvoiceLayoutCustomField'][$i]['invoice_layout_id'] = $this->InvoiceLayout->id;
                            $this->InvoiceLayoutCustomField->create();
                            $data['InvoiceLayoutCustomField'] = $this->data['InvoiceLayoutCustomField'][$i];
                            if (!$this->InvoiceLayoutCustomField->save($data['InvoiceLayoutCustomField'], false))
                                $Errors.="Can't Save for " . $data['InvoiceLayoutCustomField']['label'] . ":<br/>" . implode("<br/>", $this->InvoiceLayoutCustomField->validationErrors) . "<br/><br/>";
                        }
                        else {
                            unset($this->data['InvoiceLayoutCustomField'][$i]);
                        }
                    }
                } else {
                    $this->InvoiceLayoutCustomField->deleteAll(array('InvoiceLayoutCustomField.invoice_layout_id' => $id));
                }


                $this->loadModel('InvoiceLayoutTag');
                if (!empty($this->data['InvoiceLayoutTag'])) {
                    $this->InvoiceLayoutTag->recursive = -1;
                    $layout_texts = $this->InvoiceLayoutTag->find('list', array('conditions' => array('InvoiceLayoutTag.invoice_layout_id' => $id)));
                    $delete_text_array = array();
                    foreach ($this->data['InvoiceLayoutTag'] as $i => $item) {
                        $texts_posted_ids[] = $item['id'];
                    }
                    $delete_text_array = array_diff($layout_texts, $texts_posted_ids);
                    $this->InvoiceLayoutTag->deleteAll(array('InvoiceLayoutTag.id' => $delete_text_array));

                    foreach ($this->data['InvoiceLayoutTag'] as $i => $item) {
                        if (!empty($this->data['InvoiceLayoutTag'][$i]['tag'])) {
                            $this->data['InvoiceLayoutTag'][$i]['invoice_layout_id'] = $this->InvoiceLayout->id;
                            $this->InvoiceLayoutTag->create();
                            $data['InvoiceLayoutTag'] = $this->data['InvoiceLayoutTag'][$i];
                            if (!$this->InvoiceLayoutTag->save($data['InvoiceLayoutTag'], false))
                                $Errors.="Can't Save for " . $data['InvoiceLayoutTag']['tag'] . ":<br/>" . implode("<br/>", $this->InvoiceLayoutTag->validationErrors) . "<br/><br/>";
                        }
                        else {
                            unset($this->data['InvoiceLayoutTag'][$i]);
                        }
                    }
                } else {
                    $this->InvoiceLayoutTag->deleteAll(array('InvoiceLayoutTag.invoice_layout_id' => $id));
                }


                if ($Errors) {
                    $this->flashMessage($Errors);
                } else {
                  
                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Invoice layout', true)), 'Sucmessage');
                    $this->redirect(array('action' => 'index'));
                }
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Invoice layout', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $invoiceLayout;
        }
        //debug($this->data);exit();
        $this->set('image_settings', $this->InvoiceLayout->getImageSettings());

        $this->loadModel('InvoiceLayoutCustomField');
        $custom_fields = $this->InvoiceLayoutCustomField->find('all', array('conditions' => array('InvoiceLayoutCustomField.invoice_layout_id' => $id)));
        $this->set('custom_fields', $custom_fields);

        $this->render('admin_add');
    }

    function admin_delete($id = null) {
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('invoice layout', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('Invoice layout', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $verb = __('have', true);
            $module_name = __('Invoice layouts', true);
        }
        $invoiceLayouts = $this->InvoiceLayout->find('all', array('conditions' => array('InvoiceLayout.id' => $id)));
        if (empty($invoiceLayouts)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            //$this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $btn_action = low($_POST['submit_btn']);
            if ($btn_action == 'yes' && $this->InvoiceLayout->deleteAll(array('InvoiceLayout.id' => $_POST['ids']))) {
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete Invoice Layout', true));

        $this->set('invoiceLayouts', $invoiceLayouts);
    }

    function admin_index() {
        $this->InvoiceLayout->recursive = 0;
        $conditions = $this->_filter_params();

        $conditions['template_id'] = 0;
        $conditions['site_id'] = 0;
        $this->set('invoiceLayouts', $this->paginate('InvoiceLayout', $conditions));
    }

    function owner_index($layout_type=0) {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->set('layout_type',$layout_type);
         $this->loadModel('InvoiceLayoutTpl');
         $ilt=$this->InvoiceLayoutTpl->find('list',array('fields'=>'id,image'));
        $this->set('ilt',$ilt);
        $this->loadModel ( 'Post');
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->InvoiceLayout->recursive = 0;
        $conditions = $this->_filter_params();

        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $conditions['InvoiceLayout.staff_id'] = $site['staff_id'];
            }
        }
        $conditions[] = "InvoiceLayout.template_id <> 0";
        $conditions['InvoiceLayout.layout_type'] = $layout_type;

        $this->paginate['InvoiceLayout'] = array(
            'limit'=>100,
            'conditions' => $conditions,
            'order' => array('InvoiceLayout.name asc'),
        );
        $layouts = $this->paginate();
        foreach ($layouts as $i => $layout)
            if (!empty($layout['InvoiceLayout']['bug_v']))
                unset($layouts[$i]);

        $this->set('invoiceLayouts', $layouts);
        if($layout_type==InvoiceLayout::TYPE_PO){
        $this->set('title_for_layout',  __('Purchase Invoices Layouts', true));
        }else{
        $this->set('title_for_layout',  __('Invoice Layouts', true));    
        }
        $this->titleAlias = 'templates';
    }

    //---------------------------
    function owner_ajaxstyle() {
        $ViewCss = $this->data['InvoiceLayout']['more'];
        $this->set('ViewCss', $ViewCss);
    }

    function owner_add_po() {
		$this->set('title_for_layout',  __('Purchase Invoice Layout', true));
        $this->loadModel('InvoiceLayoutTpl');
        $this->titleAlias = 'templates';
        $site_lang = getCurrentSite('language_code') ? getCurrentSite('language_code') : (Domain_Name_Only == 'daftra' ? 7 : 41);
            $invoice_layouts = $this->InvoiceLayoutTpl->find('all', array('order' => array("FIELD(InvoiceLayout.language_id,$site_lang)" => 'desc', 'InvoiceLayout.language_id', 'InvoiceLayout.display_order' => 'ASC'), 'conditions' => array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO)));
            $this->set('invoice_layouts', $invoice_layouts);
            $this->render('owner_select_template');
            return;   
    }
    function owner_add($id = null) {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->set('quantity_price_options',self::get_columns_options());
        $this->set('js_current_action', 'add');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->more_js_lables);
       // debug($this->js_lang_labels);
        if (!check_permission(Edit_General_Settings)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        $this->loadModel('InvoiceLayoutTpl');
        $this->titleAlias = 'templates';

        $this->loadModel('Site');
        $result = $this->Site->check_add_layout_limit();
        if (!$result['status']) {
            $this->_flashLimitMessage($result['message'], __('invoice layout', true));
            $this->redirect(array('action' => 'index'));
        }

        $owner = getAuthOwner();
        if (!empty($this->data)) {
            $this->data['InvoiceLayout']['staff_id'] = $owner['staff_id'];
            $this->InvoiceLayout->create();
            $this->data['InvoiceLayout']['site_id'] = getAuthOwner('id');
            $invoice_layout = $this->InvoiceLayoutTpl->find('first', array('conditions' => array('InvoiceLayout.id' => $id)));

            $this->data['InvoiceLayout']['language_id'] = intval($invoice_layout['InvoiceLayout']['language_id']);
            if ($this->data['InvoiceLayout']['logo'] == getAuthOwner('site_logo')) {
                copy(WWW_ROOT . getAuthOwner('site_logo_full_path'), WWW_ROOT . 'files' . DS . 'images' . DS . 'logos' . DS . $this->data['InvoiceLayout']['logo']);
            }

            if ('other' == $this->data['InvoiceLayout']['more']['paper_size']) {
                $this->setWidthAndHeightForCustomTemplate();
            }
            $this->data['InvoiceLayout']['view_style'] = json_encode($this->data['InvoiceLayout']['more']);
            $status = $this->validate_view_style($this->data['InvoiceLayout']['more']);

            unset($this->data['InvoiceLayout']['more']);
            $this->data['InvoiceLayout']['default_purchase_order'] = $this->data['InvoiceLayout']['default_purchase_order']??0; 

            if ($status && $this->InvoiceLayout->save($this->data)) {
                if($this->data['InvoiceLayout']['default']=="1"){
                    $this->loadModel('Invoice');
                    $this->Invoice->recursive=-1;
                    $this->Invoice->updateAll(["item_columns"=>"'".mysqli_real_escape_string(getMysqli(), $this->data['InvoiceLayout']['item_columns'])."'"],["Invoice.type"=>Invoice::Invoice,"Invoice.invoice_layout_id"=>0]);
                }
//                App::import('Vendor', 'notification_2');
//                NotificationV2::delete_notification(NotificationV2::NOTI_NO_INVOICE_LAYOUT, null);

                if (!empty($this->data['InvoiceLayoutCustomField'])) {
                    $this->loadModel('InvoiceLayoutCustomField');
                    $invoice_layout_id = $this->InvoiceLayout->getLastInsertID();
                    $counter = 1;
                    foreach ($this->data['InvoiceLayoutCustomField'] as $custom_field) {
                        $custom_field['invoice_layout_id'] = $invoice_layout_id;
                        $custom_field['display_order'] = $counter;
                        $data = array();
                        $data['InvoiceLayoutCustomField'] = $custom_field;
                        $this->InvoiceLayoutCustomField->create();
                        if (!$this->InvoiceLayoutCustomField->save($data)) {
                            $erorrs[] = $this->InvoiceLayoutCustomField->validationErrors;
                        }
                        $counter++;
                    }
                }
                  if (check_permission(Edit_General_Settings)) {
                App::import('Vendor', 'settings');
                settings::setValue(InvoicesPlugin, 'initial_invoice_custom_fields',  json_encode($this->data['InvoiceLayoutCustomField']));
                  }
                if (!empty($this->data['InvoiceLayoutTag'])) {
                    $this->loadModel('InvoiceLayoutTag');
                    $invoice_layout_id = $this->InvoiceLayout->getLastInsertID();
                    foreach ($this->data['InvoiceLayoutTag'] as $custom_tag) {
                        unset($custom_tag['id']);
                        $custom_tag['invoice_layout_id'] = $invoice_layout_id;
                        $data = array();
                        $data['InvoiceLayoutTag'] = $custom_tag;
                        $this->InvoiceLayoutTag->create();
                        if (!$this->InvoiceLayoutTag->save($data)) {
                            $erorrs[] = $this->InvoiceLayoutTag->validationErrors;
                        }
                    }
                }
                $this->add_actionline(ACTION_ADD_INVOICE_LAYOUT, array('primary_id' => $this->InvoiceLayout->id, 'param2' => $this->data['InvoiceLayout']['template_id'], 'param3' => $this->data['InvoiceLayout']['name'], 'param4' => $this->data['InvoiceLayout']['default'], 'param5' => $this->data['InvoiceLayout']['default_estimate'], 'param6' => $this->data['InvoiceLayout']['default_timesheet']));
                
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Invoice layout', true)), 'Sucmessage');
                
                    if(!empty($this->data['InvoiceLayout']['item_columns'])){
                $this->flashMessage(__("IMPORTANT NOTE: You have added new custom columns to your invoice layout, changes will be apply on newly created invoices only (i.e. Previously created invoices will still have the old template).", true), 'Sucmessage', 'secondaryMessage');
                }
                
                $this->redirect(array('action' => 'index',$this->data['InvoiceLayout']['layout_type']));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Invoice layout', true)));
            }
        } else if (!empty($id)) {
            $invoice_layout = $this->InvoiceLayoutTpl->find('first', array('conditions' => array('InvoiceLayout.id' => $id, 'InvoiceLayout.template_id' => 0)));
           

            App::import('Vendor', 'settings');
            $zerolayout = json_decode(settings::getValue(InvoicesPlugin, 'initial_invoice_custom_fields'), true);

            $i = 0;
            foreach ($zerolayout as $line => $val2) {

                $invoice_layout['InvoiceLayoutCustomField'][$i]['id'] = $i + 1;
                $invoice_layout['InvoiceLayoutCustomField'][$i]['display_order'] = "1";
                $invoice_layout['InvoiceLayoutCustomField'][$i]['label'] = $val2['label'];
                $invoice_layout['InvoiceLayoutCustomField'][$i]['value'] = $val2['placeholder'] != "" ? $val2['placeholder'] : $val2['value'];
                $invoice_layout['InvoiceLayoutCustomField'][$i]['placeholder'] = $val2['placeholder'];
                $i++;
            }


            if (empty($invoice_layout['InvoiceLayout']['invoice_title'])) {
                $tmp = getCurrentSite('invoice_default_title');
                if (!empty($tmp)) {
                    $invoice_layout['InvoiceLayout']['invoice_title'] = $tmp;
                } else {
                    $invoice_layout['InvoiceLayout']['invoice_title'] = __('Invoice', true);
                }
            }

            if(ifPluginActive(EINVOICE_SA_PLUGIN))
                {
                    $replaced=false;
                    foreach($invoice_layout['InvoiceLayout'] as $k=>$val)
                    {

                        if(strpos($val,'<!--qr_code-->')!==false)
                        {
                            $invoice_layout['InvoiceLayout'][$k]=str_replace('<!--qr_code-->','{%sa_qr_code_image%}<br/>', $invoice_layout['InvoiceLayout'][$k]);
                            $replaced=true;
                        }

                    }

                    if(!$replaced&&strpos( $invoice_layout['InvoiceLayout']['business_info'],'qr_code')===false&&strpos( $invoice_layout['InvoiceLayout']['html'],'qr_code')===false) {
                        $invoice_layout['InvoiceLayout']['business_info'] = '{%sa_qr_code_image%}<br/>' . $invoice_layout['InvoiceLayout']['business_info'];
                    }



                }


            if (empty($invoice_layout)) {
                $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
                $this->redirect(array('action' => 'index'));
            }
            $this->set('invoice_layout', $invoice_layout);
            $this->data = $invoice_layout;


            $site = getAuthOwner();
            if (!empty($site['bn1']) && !empty($site['bn1_label'])) {
                $this->data['InvoiceLayout']['business_info'] = preg_replace('/[\r\n]+/', "\r\n{$site['bn1_label']}: {$site['bn1']}\r\n", $this->data['InvoiceLayout']['business_info'], 1);
            }
            if (!empty($site['bn2']) && !empty($site['bn2_label'])) {
                $this->data['InvoiceLayout']['business_info'] = preg_replace('/[\r\n]+/', "\r\n{$site['bn2_label']}: {$site['bn2']}\r\n", $this->data['InvoiceLayout']['business_info'], 1);
            }

            $view = new View($this, false);
            if (empty($site['address2']))
                $this->data['InvoiceLayout']['business_info'] = preg_replace('/{%address2%}[\s\n]*(<br\s*\/>)?/', '', $this->data['InvoiceLayout']['business_info']);
            
            $this->data['InvoiceLayout']['business_info'] = str_replace('{%city%}, {%state%} {%postal_code%}', $view->element('format_address_html', array('city' => '{%city%}', 'state' => '{%state%}', 'postal_code' => '{%postal_code%}', 'is_inline' => true, 'is_plain' => true)), $this->data['InvoiceLayout']['business_info']);
            $this->data['InvoiceLayout']['client_info'] = str_replace('{%client_city%}, {%client_state%} {%client_postcode%}', $view->element('format_address_html', array('city' => '{%client_city%}', 'state' => '{%client_state%}', 'postal_code' => '{%client_postcode%}', 'is_inline' => true, 'is_plain' => true)), $this->data['InvoiceLayout']['client_info']);


            unset($this->data['InvoiceLayout']['id']);
        } else {

            $site_lang = getCurrentSite('language_code') ? getCurrentSite('language_code') : (Domain_Name_Only == 'daftra' ? 7 : 41);

            $invoice_layouts = $this->InvoiceLayoutTpl->find('all', array('order' => array("FIELD(InvoiceLayout.language_id,$site_lang)" => 'desc', 'InvoiceLayout.language_id', 'InvoiceLayout.display_order' => 'ASC'), 'conditions' => array('layout_type'=>InvoiceLayout::TYPE_Invoice,'template_id' => 0, 'OR' => array('site_id' => 0, 'site_id ' => getAuthOwner('id')))));

            $this->set('invoice_layouts', $invoice_layouts);
            $this->render('owner_select_template');
            return;
        }
        $this->set('template_id', $id);
        $this->set('thumbs_templates', $this->InvoiceLayout->find('all', array('conditions' => array('InvoiceLayout.layout_type'=>InvoiceLayout::TYPE_PO,'InvoiceLayout.site_id' => 0, 'InvoiceLayout.template_id' => 0))));
        $this->set('custom_tags', $this->InvoiceLayout->InvoiceLayoutTag->get_tags($id));
        $this->set('image_settings', $this->InvoiceLayout->getImageSettings());
        
        $this->set_custom_form_field();
        
        
//            App::import('Vendor', 'settings');
//          $zerolayout=  json_decode(settings::getValue(InvoicesPlugin, 'initial_invoice_custom_fields'),true);
//       
//        foreach ($zerolayout as $line=>$val2){
//       $this->data['InvoiceLayoutCustomField'][$line]['id']=$line;      
//        $this->data['InvoiceLayoutCustomField'][$line]['label']=$val2['label'];      
//          $this->data['InvoiceLayoutCustomField'][$line]['value']=$val2['placeholder']!=""?$val2['placeholder']:$val2['value'];    
//          $this->data['InvoiceLayoutCustomField'][$line]['placeholder']=$val2['placeholder'];      
//        }



        if($invoice_layout['InvoiceLayout']['layout_type']==InvoiceLayout::TYPE_PO){
          $this->set('title_for_layout',  __('Purchase Invoice Layout', true));
        }else{
        $this->set('title_for_layout',  __('Create Invoice Layout', true));
        }
    }

    //-------------------------------------

    function owner_get_file_content($template_id, $add = false) {

        $this->layout = $this->autoLayout = $this->autoRender = false;
        if ($add) {
            $this->loadModel('InvoiceLayoutTpl');
            $template = $this->InvoiceLayoutTpl->findById($template_id);
        } else {
            $template = $this->InvoiceLayout->findById($template_id);
        }
        echo $template['InvoiceLayout']['html'];
        exit();
    }

    //---------------------------
    function owner_view($id = null)
    {
        $this->redirect(array('action' => 'edit', $id));
    }
    
    function owner_edit($id = null) {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel('Invoice');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->more_js_lables);

        $owner = getAuthOwner();
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
            $this->redirect(array('action' => 'index'));
        }


        if ($owner['staff_id'] != 0) {
            $staff = getAuthOwner('staff_id');
            // if ((!check_permission(Invoices_Edit_All_Invoices) && $invoiceLayout['Invoice']['staff_id'] != $staff) || !check_permission(Invoices_Edit_his_own_Invoices)) {
            if ((!check_permission(Edit_General_Settings))) {
                $this->flashMessage(__("You are not allowed to edit this layout ", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }
        $invoiceLayout = $this->InvoiceLayout->find('first', array('conditions' => array('InvoiceLayout.id' => $id, 'InvoiceLayout.template_id !=' => 0)));
        debug($invoiceLayout);
        
        if (empty($invoiceLayout)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
            $this->redirect(array('action' => 'index'));
            die();
        }
        $this->loadModel('Site');
        if (!empty($this->data)) {
            $this->data['InvoiceLayout']['site_id'] = getAuthOwner('id');
            if (empty($this->data['InvoiceLayout']['id'])) {
                $this->loadModel('Site');
                $result = $this->Site->check_add_layout_limit();
                if (!$result['status']) {
                    $this->_flashLimitMessage($result['message']);
                    $this->redirect(array('action' => 'index',$this->data['InvoiceLayout']['layout_type']));
                }
            }
        
            unset($this->data['InvoiceLayout']['template_id']);
            if ('other' == $this->data['InvoiceLayout']['more']['paper_size']) {
                $this->setWidthAndHeightForCustomTemplate();
            }

            $this->data['InvoiceLayout']['view_style'] = json_encode($this->data['InvoiceLayout']['more']);
           
            $status = $this->validate_view_style($this->data['InvoiceLayout']['more']);
            if ($status && $this->InvoiceLayout->save($this->data)) {
                if($this->data['InvoiceLayout']['default']=="1"){
                    $this->Invoice->recursive=-1;
                $this->Invoice->updateAll(["item_columns"=>"'".mysqli_real_escape_string(getMysqli(),$this->data['InvoiceLayout']['item_columns'])."'"],["Invoice.type"=>Invoice::Invoice,"Invoice.invoice_layout_id"=>0]);

                }
                $this->loadModel('InvoiceLayoutCustomField');
                $this->InvoiceLayoutCustomField->deleteAll(array('InvoiceLayoutCustomField.invoice_layout_id' => $id));
                if (!empty($this->data['InvoiceLayoutCustomField'])) {
                    $invoice_layout_id = $id;
                    $counter = 1;
                    foreach ($this->data['InvoiceLayoutCustomField'] as $custom_field) {
                        $custom_field['invoice_layout_id'] = $invoice_layout_id;
                        $custom_field['display_order'] = $counter;
                        $data = array();
                        $data['InvoiceLayoutCustomField'] = $custom_field;
                        $this->InvoiceLayoutCustomField->create();
                        $this->InvoiceLayoutCustomField->save($data);
                        $counter++;
                    }
                }
                $this->add_actionline(ACTION_UPDATE_INVOICE_LAYOUT, array('primary_id' => $id, 'param2' => $this->data['InvoiceLayout']['template_id'], 'param3' => $this->data['InvoiceLayout']['name'], 'param4' => $this->data['InvoiceLayout']['default'], 'param5' => $this->data['InvoiceLayout']['default_estimate'], 'param6' => $this->data['InvoiceLayout']['default_timesheet']));
                if($this->data['InvoiceLayout']['layout_type']==InvoiceLayout::TYPE_Invoice){
                    if(!empty($this->data['InvoiceLayout']['item_columns']) && $this->data['InvoiceLayout']['item_columns'] != $invoiceLayout['InvoiceLayout']['item_columns']){
                        $this->flashMessage(__("IMPORTANT NOTE: You have added new custom columns to your invoice layout, changes will be apply on newly created invoices only (i.e. Previously created invoices will still have the old template).", true), 'Sucmessage', 'secondaryMessage');
                    }
                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Invoice layout', true)), 'Sucmessage');
                }elseif($this->data['InvoiceLayout']['layout_type']==InvoiceLayout::TYPE_PO){
                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Purchase Invoice layout', true)), 'Sucmessage');
                }
                $this->redirect(array('action' => 'index',$this->data['InvoiceLayout']['layout_type']));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Invoice layout', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $invoiceLayout;
        }
        $this->set('quantity_price_options',self::get_columns_options());
        $this->set('template_id', $id);
        $this->set('thumbs_templates', $this->InvoiceLayout->find('all', array('conditions' => array('InvoiceLayout.site_id' => 0, 'InvoiceLayout.template_id' => 0))));
        $this->set('image_settings', $this->InvoiceLayout->getImageSettings());
        $this->set('custom_tags', $this->InvoiceLayout->InvoiceLayoutTag->get_tags($id));
         $this->set_custom_form_field();
         if($invoiceLayout['InvoiceLayout']['layout_type']==InvoiceLayout::TYPE_PO){
        $this->set('title_for_layout',  __('Edit Purchase Invoice', true));
         }else{
             $this->set('title_for_layout',  __('Edit Invoice Layout', true));
         }

        $this->titleAlias = 'templates';
        $this->render('owner_add');
    }

    function validate_view_style($data)
    {
        $allMarginsPositive = true;
        $marginKeys = ['margin_top', 'margin_bottom', 'margin_left', 'margin_right'];
        $error = [];
        foreach ($marginKeys as $key) {
            if (isset($data[$key]) && is_numeric($data[$key])) {
                if (!empty($data[$key]) && $data[$key] < 0) {
                    $error[] = sprintf(__('The %s must be greater than or equal %s.', true), __t('Paper Margin') . ' ' . __t(ucfirst(str_replace('margin_', '', $key))), 0);
                    $allMarginsPositive = false;
                }
            }
        }
        if (!empty($error)) {
            CustomValidationFlash($error);
        }
        return $allMarginsPositive;
    }
    //---------------------------
    
    
    function owner_alt_template($id,$status=1){

        $invoiceLayout = $this->InvoiceLayout->find('first', array('conditions' => array(
                'InvoiceLayout.id' => $id, 'InvoiceLayout.template_id !=' => 0))
        );   
                if (empty($invoiceLayout)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
            $this->redirect(array('action' => 'index'));
            die();
        }
        $this->InvoiceLayout->id=$id;
        $this->InvoiceLayout->saveField('alt_template', $status);
        $this->flashMessage(sprintf(__('%s  has been saved', true), __('Invoice layout', true)), 'Sucmessage');
        $this->redirect($this->referer() ?: ['action' => 'index']);
        die();
    }
    function owner_delete($id = null) {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('invoice layout', true)));
            $this->redirect(array('action' => 'index'));
        }
        $owner = getAuthOwner();
//        if (!check_permission(Invoices_Edit_All_Invoices) && !check_permission(Invoices_Edit_his_own_Invoices)) {
        if (!check_permission(Edit_General_Settings)) {
            $this->flashMessage(__("You are not allowed to delete layout", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        $module_name = __('Invoice layout', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $verb = __('have', true);
            $module_name = __('Invoice layouts', true);
        }

        $conditions = array('InvoiceLayout.id' => $id);
        if ($owner['staff_id'] != 0 && !check_permission(Invoices_Edit_All_Invoices)) {
            $conditions['InvoiceLayout.staff_id'] = $owner['staff_id'];
        }

        $invoiceLayouts = $this->InvoiceLayout->find('all', array('conditions' => $conditions));
        if (empty($invoiceLayouts)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            $layout_type =  isset($invoiceLayouts[0]['InvoiceLayout']['layout_type']) ? $invoiceLayouts[0]['InvoiceLayout']['layout_type']:null;
            $btn_action = low($_POST['submit_btn']);
            $delete_conditions = array('InvoiceLayout.id' => $_POST['ids']);
            if ($owner['staff_id'] != 0 and !check_permission(Invoices_Edit_All_Invoices)) {
                $delete_conditions['InvoiceLayout.staff_id'] = $owner['staff_id'];
            }
            if ($btn_action == 'yes') {
				
                foreach ($invoiceLayouts as $layout){
					
					$layout=$this->InvoiceLayout->read(null,$layout['InvoiceLayout']['id']);
				
					$this->InvoiceLayout->delete($layout['InvoiceLayout']['id']);
                    $this->add_actionline(ACTION_DELETE_INVOICE_LAYOUT, array('primary_id' => $layout['InvoiceLayout']['id'], 'param2' => $layout['InvoiceLayout']['template_id'], 'param3' => $layout['InvoiceLayout']['name']));
				}					
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
            } 
            $this->redirect(array('action' => 'index',$layout_type));
            
        }

        $this->set('title_for_layout',  __('Delete Invoice Layout', true));

        $this->set('invoiceLayouts', $invoiceLayouts);
    }
    
    function owner_virtual_invoice_columns()
    {
       //  var_dump($this->data);
      //  die();
        
        $layout['InvoiceLayout']=$this->data['InvoiceLayout'];
        $this->set('layout',$layout );
    
        $this->loadModel('Invoice');
        $invoice=array(
          'InvoiceItem'  =>array('0'=>array('item'=>'Demo Item','description'=>'Demo Product Description','unit_price'=>200,'quantity'=>1)),
          'Invoice'=>array('paid' => 0.0, 'payment_status' => 0)
        );
        $invoice = $this->Invoice->prepareForView($invoice);
        $this->set('invoice',$invoice );
       
         
    }

    function owner_upload_image($layout_id = false, $action_source='') {
        
        $action = str_replace($this->params['prefix'] . '_', '', $action_source);
        if ($action == 'edit') {
            $layout = $this->InvoiceLayout->find('first', array('recursive' => -1, 'conditions' => array('InvoiceLayout.id' => $layout_id), 'fields' => array('layout_type','max_width', 'max_height', 'min_width', 'min_height', 'default_width', 'default_height')));
        } else {
            $this->loadModel('InvoiceLayoutTpl');
            $layout = $this->InvoiceLayoutTpl->find('first', array('recursive' => -1, 'conditions' => array('InvoiceLayout.id' => $layout_id), 'fields' => array('layout_type','max_width', 'max_height', 'min_width', 'min_height', 'default_width', 'default_height')));
        }

        if (empty($layout)) {
            return false;
        }

//        Configure::write('debug', 0);

        if (!empty($_FILES['data']) && $_FILES['data']['size']['Site']['site_logo'] > 0) {
            if ($_FILES['data']['size']['Site']['site_logo'] > (1024 * 1024)) {
                $this->flashMessage(__('The image file size is larger than the allowed maximum size', true));
            } else {
                $errors = '';
                $dir_path = WWW_ROOT . DS . 'files/images/logos/';
                $file = uniqid() . '_' . $_FILES['data']['name']['Site']['site_logo'];
                $permitted = array('image/gif', 'image/jpeg', 'image/pjpeg', 'image/png');
                list($width, $height) = getimagesize($_FILES['data']['tmp_name']['Site']['site_logo']);
                $info = getimagesize($_FILES['data']['tmp_name']['Site']['site_logo']);
                if (!in_array($_FILES['data']['type']['Site']['site_logo'], $permitted)) {
                    $errors = __("Invalid file type required (jpg, png, gif)", true);
                }
                
                 $ext = strtolower(substr($_FILES['data']['name']['Site']['site_logo'], strrpos($_FILES['data']['name']['Site']['site_logo'], '.')));
                if (!in_array($ext,array('.jpg','.gif','.jpeg','.png'))) {
                   $errors = __("Invalid file type required (jpg, png, gif)", true);   
                }
                
//                elseif ($width > 650) {
//                    $errors = __('Image width must be less than 650 px', true);
//                } elseif ($_FILES['data']['size']['Site']['site_logo'] <= 0 && $_FILES['data']['error']['Site']['site_logo'] != 0) {
//                    $errors = __('Error in uploading. Please try again', true);
//                }
                if (empty($errors)) {
                    $reszie = 0;
                    if (!empty($layout['InvoiceLayout']['max_width']) && $width > $layout['InvoiceLayout']['max_width']) {
                        $height = ($layout['InvoiceLayout']['max_width'] / $width) * $height;
                        $width = $layout['InvoiceLayout']['max_width'];
                        $reszie = 1;
                    }

                    if (!empty($layout['InvoiceLayout']['max_height']) && $height > $layout['InvoiceLayout']['max_height']) {
                        $width = ($layout['InvoiceLayout']['max_height'] / $height) * $width;
                        $height = $layout['InvoiceLayout']['max_height'];
                        $reszie = 1;
                    }
                    App::import('Vendor', 'S3LogoUploader', array('file' => 'S3LogoUploader.php'));
                    S3LogoUploader::createS3Client();
                    if (!empty($reszie)) {
                        App::import('Vendor', 'Image');
                        $image = Image::smart_resize_image($_FILES['data']['tmp_name']['Site']['site_logo'], $width, $height, true, 'file', true, "/tmp/" . $file, true);
                        $res = S3LogoUploader::uploadToS3("logos/" . $file, file_get_contents($image));
                        // $image = move_uploaded_file($_FILES['data']['tmp_name']['Site']['site_logo'], $dir_path . $file);
                    } else {
                      //  $image = move_uploaded_file($_FILES['data']['tmp_name']['Site']['site_logo'], $dir_path . $file);

                        $res = S3LogoUploader::uploadToS3("logos/" . $file, file_get_contents($_FILES['data']['tmp_name']['Site']['site_logo']));

                        debug(error_get_last());
                    }

//                    dd($image);
                    if ($image) {
                        $this->set('image_name', $file);
                        $this->set('image_path', Router::url("/files/images/logos/$file"));
                    } else {
                        $this->flashMessage(__('Error uploading image', true));
                    }
                } else {
                    $this->flashMessage($errors);
                }
            }
        }

        if (empty($width)) {

            //$width = $layout['InvoiceLayout']['default_width'];
        }
        // echo $width."<br>"; 
        // echo $reszie; 
        $this->set('width', $width);

        $this->set('invoice_layout', $layout);
        $this->set('layout_id', $layout_id);
        $this->set('action_source', $action_source);
        $this->layout = false;
    }
   
    function owner_upload_sigimage() {
   
        if (!empty($_FILES) && $_FILES['file']['size'] > 0) {
            if ($_FILES['file']['size'] > (1024 * 1024)) {
                $this->flashMessage(__('The image file size is larger than the allowed maximum size', true));
            } else {
                $errors = '';
                $realpath='files/'.SITE_HASH.DS.'logos/';
                $dir_path = WWW_ROOT . DS . $realpath;
                @mkdir($dir_path,0777,true);
                $file = uniqid() . '_' . $_FILES['file']['name'];
                $permitted = array('image/gif', 'image/jpeg', 'image/pjpeg', 'image/png');
                list($width, $height) = getimagesize($_FILES['file']['tmp_name']);
                $info = getimagesize($_FILES['file']['tmp_name']);
                if (!in_array($_FILES['file']['type'], $permitted)) {
                    $errors = __("Invalid file type required (jpg, png, gif)", true);
                }
                
                 $ext = strtolower(substr($_FILES['file']['name'], strrpos($_FILES['file']['name'], '.')));
                if (!in_array($ext,array('.jpg','.gif','.jpeg','.png'))) {
                   $errors = __("Invalid file type required (jpg, png, gif)", true);   
                }
                
//                elseif ($width > 650) {
//                    $errors = __('Image width must be less than 650 px', true);
//                } elseif ($_FILES['data']['size']['Site']['site_logo'] <= 0 && $_FILES['data']['error']['Site']['site_logo'] != 0) {
//                    $errors = __('Error in uploading. Please try again', true);
//                }
                if (empty($errors)) {
                    $image = move_uploaded_file($_FILES['file']['tmp_name'], $dir_path . $file);
                    if(!$image) {
                        $errors = 'Couldn\'t upload the file'.$_FILES["file"]["error"];
                        header("HTTP/1.0 500 $errors");
                    }
                     echo json_encode(array('location' => Router::url("/$realpath".DS."$file",true)));
                     die();
                } else {
                     header("HTTP/1.0 500 $errors");
                //    header("HTTP/1.0 500 $errors");
                }
            }
        }else{
             header("HTTP/1.0 500 Please Select File");
        }
$this->layout = false;
     die();
        
    }

   function set_custom_form_field(){
       $this->loadModel('CustomForm');
        $this->loadModel('CustomFormField');
           $CustomHolder = array();
        $reread_form = $this->CustomForm->findByTableName("products");
        
        if ($reread_form) {
            $form_fields = $this->CustomFormField->get_form_fields($reread_form['CustomForm']['id']);
            
            foreach ($form_fields as $v) {
                $v ['CustomFormField']['is_required'] = 0;
                $holdername = 'field_' . $v['CustomFormField']['id'];
                $CustomHolder[$holdername] = $v['CustomFormField']['label'];
            }
            foreach ($CustomHolder as $key => $name) {
                $placeholder[$key] = $name;
            }
            
            $this->set('product_placeholder',$placeholder);
        }
   }

    private function setWidthAndHeightForCustomTemplate(): void
    {
        if (empty($this->data['InvoiceLayout']['more']['paper_width'])) {
            $this->data['InvoiceLayout']['more']['paper_width'] = '0';
        }
        if (empty($this->data['InvoiceLayout']['more']['paper_height'])) {
            $this->data['InvoiceLayout']['more']['paper_height'] = '0';
        }
    }

    public function owner_backup($id = null){
        if(!isLoggedAsAdmin()){
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $layout = $this->InvoiceLayout->read(null, $id);
        if(!$id || !$layout){
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst(__('Invoice layout', true))));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        unset($layout['Site']);
        settings::setValue(0, "invoice_layout_{$id}_backup", json_encode($layout));
        $this->flashMessage(__('The system took a backup from the template successfully', true), 'Sucmessage');
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

    public function owner_restore_backup($id){
        $backup = settings::getValue(0, "invoice_layout_{$id}_backup");
        if(!$backup){
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst(__('Backup', true))));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if($this->InvoiceLayout->save(json_decode($backup, true))){
            $this->flashMessage(__('The system restored the template backup successfully', true), 'Sucmessage');
        }else{
            $this->flashMessage(__('The system failed to restore the template backup', true), 'Sucmessage');
        }
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

    public function owner_export_backup($id){
        if(!isLoggedAsAdmin()){
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $backup = settings::getValue(0, "invoice_layout_{$id}_backup");
        if(!$backup){
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst(__('Backup', true))));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        header('Content-type: text/plain');
        header("Content-Disposition: attachment; filename=invoice_layout_{$id}_backup.txt");
        echo $backup;
        die();
    }

    public function owner_import(){
        if(!isLoggedAsAdmin()){
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if(!empty($this->data)){
            $data = json_decode($this->data['InvoiceLayout']['json'], true);
            unset($data['InvoiceLayout']['id']);
            unset($data['InvoiceLayout']['created']);
            unset($data['InvoiceLayout']['modified']);
            unset($data['InvoiceLayout']['site_id']);
            unset($data['InvoiceLayout']['branch_id']);
            if($this->InvoiceLayout->save($data)){
                $this->flashMessage(__('Layout imported successfully', true), 'Sucmessage');
                $this->redirect(array('action' => 'edit', $this->InvoiceLayout->id), true);
            }else{
                $this->flashMessage(__('There was an error importing the layout, Please try again', true), 'Sucmessage');
                $this->redirect(array('action' => 'index', true));
            }
        }
    }

    public function owner_delete_backup($id){
        if(!isLoggedAsAdmin()){
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $backup = settings::getValue(0, "invoice_layout_{$id}_backup");
        if(!$backup){
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst(__('Backup', true))));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        settings::deleteByKey("invoice_layout_{$id}_backup");
        $this->flashMessage(__('Backup Deleted successfully', true), 'Sucmessage');
        $this->redirect($this->referer(array('action' => 'index'), true));
    }

    public function owner_clone($id){
        $this->loadModel('Invoice');
        $this->js_lang_labels = array_merge($this->js_lang_labels, $this->more_js_lables);

        $owner = getAuthOwner();
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
            $this->redirect(array('action' => 'index'));
        }


        if ($owner['staff_id'] != 0) {
            if ((!check_permission(Edit_General_Settings))) {
                $this->flashMessage(__("You are not allowed to edit this layout ", true), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'index'));
            }
        }
        $invoiceLayout = $this->InvoiceLayout->find('first', array('conditions' => array('InvoiceLayout.id' => $id, 'InvoiceLayout.template_id !=' => 0)));
        
        if (empty($invoiceLayout)) {
            $this->flashMessage(sprintf(__('Invalid %s.', true), 'invoice layout'));
            $this->redirect(array('action' => 'index'));
            die();
        }

        $this->loadModel('Site');
        $result = $this->Site->check_add_layout_limit();
        if (!$result['status']) {
            $this->_flashLimitMessage($result['message'], __('invoice layout', true));
            $this->redirect(array('action' => 'index'));
        }

        if (!empty($this->data)) {
            unset($this->data['InvoiceLayout']['id']);
            $this->data['InvoiceLayout']['staff_id'] = $owner['staff_id'];
            $this->InvoiceLayout->create();
            $this->data['InvoiceLayout']['site_id'] = getAuthOwner('id');
            $this->loadModel('InvoiceLayoutTpl');
            $invoice_layout = $this->InvoiceLayoutTpl->find('first', array('conditions' => array('InvoiceLayout.id' => $id)));

            $this->data['InvoiceLayout']['language_id'] = intval($invoice_layout['InvoiceLayout']['language_id']);
            if ($this->data['InvoiceLayout']['logo'] == getAuthOwner('site_logo')) {
                copy(WWW_ROOT . getAuthOwner('site_logo_full_path'), WWW_ROOT . 'files' . DS . 'images' . DS . 'logos' . DS . $this->data['InvoiceLayout']['logo']);
            }

            if ('other' == $this->data['InvoiceLayout']['more']['paper_size']) {
                $this->setWidthAndHeightForCustomTemplate();
            }
            $this->data['InvoiceLayout']['view_style'] = json_encode($this->data['InvoiceLayout']['more']);

            unset($this->data['InvoiceLayout']['more']);

            if ($this->InvoiceLayout->save($this->data)) {
                if ($this->data['InvoiceLayout']['default'] == "1") {
                    $this->loadModel('Invoice');
                    $this->Invoice->recursive = -1;
                    $this->Invoice->updateAll(["item_columns" => "'" . mysqli_real_escape_string(getMysqli(), $this->data['InvoiceLayout']['item_columns']) . "'"], ["Invoice.type" => Invoice::Invoice, "Invoice.invoice_layout_id" => 0]);
                }

                if (!empty($this->data['InvoiceLayoutCustomField'])) {
                    $this->loadModel('InvoiceLayoutCustomField');
                    $invoice_layout_id = $this->InvoiceLayout->getLastInsertID();
                    $counter = 1;
                    foreach ($this->data['InvoiceLayoutCustomField'] as $custom_field) {
                        $custom_field['invoice_layout_id'] = $invoice_layout_id;
                        $custom_field['display_order'] = $counter;
                        $data = array();
                        $data['InvoiceLayoutCustomField'] = $custom_field;
                        $this->InvoiceLayoutCustomField->create();
                        if (!$this->InvoiceLayoutCustomField->save($data)) {
                            $erorrs[] = $this->InvoiceLayoutCustomField->validationErrors;
                        }
                        $counter++;
                    }
                }
                if (check_permission(Edit_General_Settings)) {
                    App::import('Vendor', 'settings');
                    settings::setValue(InvoicesPlugin, 'initial_invoice_custom_fields',  json_encode($this->data['InvoiceLayoutCustomField']));
                }
                if (!empty($this->data['InvoiceLayoutTag'])) {
                    $this->loadModel('InvoiceLayoutTag');
                    $invoice_layout_id = $this->InvoiceLayout->getLastInsertID();
                    foreach ($this->data['InvoiceLayoutTag'] as $custom_tag) {
                        unset($custom_tag['id']);
                        $custom_tag['invoice_layout_id'] = $invoice_layout_id;
                        $data = array();
                        $data['InvoiceLayoutTag'] = $custom_tag;
                        $this->InvoiceLayoutTag->create();
                        if (!$this->InvoiceLayoutTag->save($data)) {
                            $erorrs[] = $this->InvoiceLayoutTag->validationErrors;
                        }
                    }
                }
                $this->add_actionline(ACTION_ADD_INVOICE_LAYOUT, array('primary_id' => $this->InvoiceLayout->id, 'param2' => $this->data['InvoiceLayout']['template_id'], 'param3' => $this->data['InvoiceLayout']['name'], 'param4' => $this->data['InvoiceLayout']['default'], 'param5' => $this->data['InvoiceLayout']['default_estimate'], 'param6' => $this->data['InvoiceLayout']['default_timesheet']));

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Invoice layout', true)), 'Sucmessage');

                if (!empty($this->data['InvoiceLayout']['item_columns'])) {
                    $this->flashMessage(__("IMPORTANT NOTE: You have added new custom columns to your invoice layout, changes will be apply on newly created invoices only (i.e. Previously created invoices will still have the old template).", true), 'Sucmessage', 'secondaryMessage');
                }

                $this->redirect(array('action' => 'index', $this->data['InvoiceLayout']['layout_type']));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Invoice layout', true)));
            }
        }

        if (empty($this->data)) {
            $this->data = $invoiceLayout;
        }

        $this->set('quantity_price_options',self::get_columns_options());
        $this->set('template_id', $id);
        $this->set('thumbs_templates', $this->InvoiceLayout->find('all', array('conditions' => array('InvoiceLayout.site_id' => 0, 'InvoiceLayout.template_id' => 0))));
        $this->set('image_settings', $this->InvoiceLayout->getImageSettings());
        $this->set('custom_tags', $this->InvoiceLayout->InvoiceLayoutTag->get_tags($id));
        $this->set_custom_form_field();

        $this->titleAlias = 'templates';
        $this->render('owner_add');

        if ($invoice_layout['InvoiceLayout']['layout_type'] == InvoiceLayout::TYPE_PO) {
            $this->set('title_for_layout',  __('Clone Purchase Invoice Layout', true));
        } else {
            $this->set('title_for_layout',  __('Clone Invoice Layout', true));
        }
    }
}

?>
