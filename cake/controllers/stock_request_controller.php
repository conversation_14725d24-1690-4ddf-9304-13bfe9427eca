<?php

use App\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PermissionUtil as PermissionUtilAlias;
use App\Helpers\CurrencyHelper;

class StockRequestController extends AppController
{
    var $name = 'StockRequest';
    var $components = ['StockValidation'];
    /**
     * @var StockRequest
     */
    public $StockRequest;
    public $autoRender = false;

    public function owner_convert_to_requisition($id)
    {
        $this->loadModel('StockRequest');
        $this->loadModel('Requisition');
        $this->loadModel('Store');
        $this->loadModel('Product');
        $this->Product->applyBranch['onFind']=false;
        $stockRequestService = resolve(\Izam\StockRequest\Services\StockRequestService::class);
        $storeTwo=null;
        $this->check_source_validations($id);

        if (!check_permission(PermissionUtilAlias::REQUISITION_ADD)) {
            $this->flashMessage(__('You are not allowed to perform this action', TRUE));
            $this->redirect('/v2/owner/entity/stock_request/list');
        }
        $stockRequest = $this->StockRequest->find('first', ['conditions' => ['StockRequest.id' => $id ] ] );

        //handle case if you want to transfer to req from a suspended store.
        $storeOne = $this->Store->find('first', ['conditions' => ['Store.id' => $stockRequest['StockRequest']['store_id'] ] ] );
        if ($stockRequest['StockRequest']['store2_id']){
            $storeTwo = $this->Store->find('first', ['conditions' => ['Store.id' => $stockRequest['StockRequest']['store2_id'] ] ] );
        }
        if ((!$storeOne || $storeOne['Store']['active'] != \Izam\Daftra\Store\Utils\StoreStatusUtil::ACTIVE) || ($storeTwo && $storeTwo['Store']['active'] != \Izam\Daftra\Store\Utils\StoreStatusUtil::ACTIVE)){
            $message = __('You cannot choose a deactivated or suspended warehouse' , true);
            $this->flashMessage(__($message, TRUE));
            $this->redirect($this->referer());
        }
        $stockRequestSource = $stockRequestService->checkSource($id);
        $requisitionData = $stockRequestService->getRequisitionCanConvert($id);
        $storeId = $stockRequest['StockRequest']['store_id'];
        if (count($requisitionData[$storeId]['RequisitionItem'])){
            $requisition = $requisitionData[$stockRequest['StockRequest']['store_id']];
            $requisition_store = $this->Store->find('first', ['conditions' => ['Store.id' =>  $storeId ] ] );
            $this->set('storeOneIsActive',  $requisition_store['Store']['active'] == \Izam\Daftra\Common\Utils\StoreStatusUtil::ACTIVE );
            $requisition_store_two_is_active = false;
            if ($stockRequest['StockRequest']['type'] == \Izam\Daftra\Common\Utils\StockRequestUtil::TYPE_TRANSFER){
                $requisition_store_two = $this->Store->find('first', ['conditions' => ['Store.id' =>   $stockRequest['StockRequest']['store2_id'] ]] );
                $requisition_store_two_is_active = $requisition_store_two['Store']['active']  == \Izam\Daftra\Common\Utils\StoreStatusUtil::ACTIVE;
                $requisition['Requisition']['to_store_id']= $stockRequest['StockRequest']['store2_id'];
            }
            $this->set('storeTwoIsActive',  $requisition_store_two_is_active);
            //get and set products
            $this->Product->recursive = -1;
            $productIds = array_map(function($item) {
                return $item['product_id'];
            },$requisition['RequisitionItem']);
            $products = $this->Product->getInvoiceProductList(false, ['Product.id' => $productIds], false,[],null,'requisition');
            $this->Product->applyBranch['onFind']=true;
            $this->set('products', $products);
            foreach ($requisition['RequisitionItem'] as $i => $item) {

                if (!empty($item['product_id'])) {
                    if(isset($products[$item['product_id']])) {
                        $pp[0] = $products[$item['product_id']];
                        $requisition['RequisitionItem'][$i]['Product'] = $pp[0];
                        $requisition['RequisitionItem'][$i]['Product']['stock_balance'] = $pp[0]['stock_balance'];
                        $requisition['RequisitionItem'][$i]['unit_price'] = $pp[0]['average_price'];
                        $requisition['RequisitionItem'][$i]['item'] = $pp[0]['name'];
                    }
                }
            }
            $requisition['Requisition']['order_number']=$stockRequest['StockRequest']['code'];
            $orderType = $this->resolveOrderType($stockRequest , $stockRequestSource);
            $type = match ($stockRequest['StockRequest']['type']){
                'outbound' => Requisition::TYPE_OUTBOUND,
                'transfer' =>  Requisition::TYPE_NOEFFECT,
                default =>Requisition::TYPE_INBOUND,
            };
            $requisition['Requisition']['order_type']=$orderType;
            $requisition['Requisition']['work_order_id']= $stockRequest['StockRequest']['work_order_id'] ?? $stockRequest['StockRequest']['work_flow_id'];
            $this->setSourceRelatedData($requisition,$stockRequest ,$stockRequestSource);
            App::import('Vendor', 'AutoNumber');
            $requisition['Requisition']['number'] = $this->data['Requisition']['hidden_number'] = \AutoNumber::get_auto_serial(\AutoNumber::mapRequisitionTypeToAutoNumberType($type));
            $this->_settings($orderType,$type);
            $this->data =$requisition;
            $this->render('/requisitions/owner_add');
            return;
        }
        $message = __('All product quantities have already been converted', true);
        $this->flashMessage($message);
        $this->redirect($this->referer());
    }

    public function owner_view($id)
    {
        $sockSettingsEnabled = settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS);
        if (!$sockSettingsEnabled){
            $this->flashMessage(sprintf(__('You disabled the %s module from the %s',true) , __('Stock Requests' ,true), "<a href='/owner/settings/inventory'>". __("Inventory Settings" ,true) ."</a>"  ));
            $this->redirect($this->referer());
        }
        return $this->redirect('/v2/owner/entity/stock_request/'.$id .'/show');
    }
    private function _settings($orderType,$type) {
        $this->loadModel('ItemPermission');
        $stores_list = $this->ItemPermission->getAuthenticatedList (ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_STOCK_UPDATING) ;
        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        $this->loadModel('Currency');
        $this->set('currencies', $this->Currency->getCurrencyList());
        $aps = '1';
        $this->loadModel('RequisitionItem');
        $this->loadModel('Document');
        $this->set('aps', $aps);
        $primary = $this->Store->getPrimaryStore();
        $this->set('primaryStore', $primary);
        $this->set('title_for_layout',  __('Add Requisition', true));
        if (ifPluginActive(ExpensesPlugin)) {
            $this->loadModel('Treasury');
            $this->set('treasuries', $this->Treasury->get_list());

            $this->set('primary_treasury_id', $this->Treasury->get_primary());
        }

        $enable_multi_units = settings::getValue(InventoryPlugin, 'enable_multi_units');
        if ($enable_multi_units) {
            $this->loadModel('UnitTemplate');
            $unit_templates = $this->UnitTemplate->find('list', ['fields' => 'template_name']);
            $this->set('unit_templates', $unit_templates);
        }
        $this->set('enable_multi_units', $enable_multi_units);
        $this->set('itemModel', 'RequisitionItem');
        $this->set('primary_store', $this->Store->getPrimaryStore());
        $this->set('stores_list', $stores_list);
        $show_price = false;
        $edit_price = false;
        if ($orderType == Requisition::TYPE_INBOUND) {
            if (check_permission(EDIT_STOCK_TRANSACTION_PRICE)) {
                $edit_price = true;
            }
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        } else if (in_array($orderType , [Requisition::TYPE_OUTBOUND,Requisition::TYPE_NOEFFECT]) ) {
            /*
            if (check_permission(EDIT_STOCK_TRANSACTION_PRICE) && $orderType === Requisition::TYPE_OUTBOUND) {
                $edit_price = true;
            }
            */
            if (check_permission(VIEW_STOCK_TRANSACTION_PRICE)) {
                $show_price = true;
            }
        }
        $this->set('order_type', $orderType);
        $this->set('type', $type);
        $this->set('show_price', $show_price);
        $this->set('edit_price', $edit_price);
        //Loading in the controller because it should be loaded once and not for each record
        $this->set('currencyFraction' , CurrencyHelper::getFraction(getCurrentSite('currency_code')));

    }

    private function resolveOrderType($stockRequest , $source)
    {
        if ($source['related_model']  === \app\Models\ManufacturingOrder::class){
            return Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL;
        }
        return match ($stockRequest['StockRequest']['type']){
            'outbound' => Requisition::TYPE_OUTBOUND,
            'transfer' => Requisition::ORDER_TYPE_TRANSFER_REQUISITION,
            default => Requisition::TYPE_INBOUND
        };
    }

    private function setSourceRelatedData(&$requisition,$stockRequest, $source)
    {
        if ($source['related_model']  === \app\Models\ManufacturingOrder::class){
            $this->loadModel('ManufacturingOrder');
            $manufactureOrder = $this->ManufacturingOrder->find('first', ['conditions' => ['ManufacturingOrder.id' => $source['related_model_id'] ] , 'recursive'=>-1 ] );
            $this->set('is_manufacturing_order', true);
            $requisition['Requisition']['order_number']=$manufactureOrder['ManufacturingOrder']['code'];
            $requisition['Requisition']['journal_account_id'] = $manufactureOrder["ManufacturingOrder"]["journal_account_id"];
            $requisition['Requisition']['order_id']=$manufactureOrder['ManufacturingOrder']['id'];
            $this->data['order_id']=$manufactureOrder['ManufacturingOrder']['id'];
        }else{
            $this->set('is_stock_request' , true);
        }
        $this->set('stock_request_id' , $stockRequest['StockRequest']['id']);
    }

    private function check_source_validations($id)
    {
        $stockRequestService = resolve(\Izam\StockRequest\Services\StockRequestService::class);
        $source = $stockRequestService->checkSource($id);
        if ($source && isset($source['related_model']) && $source['related_model'] == "app\Models\ManufacturingOrder"){
            $moId = $source['related_model_id'];
            $this->loadModel('ManufacturingOrder');
            $manufactureOrder = $this->ManufacturingOrder->find('first', ['conditions' => ['ManufacturingOrder.id' => $moId ] ] );
            $code = $manufactureOrder["ManufacturingOrder"]["code"];
            $outbound = Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL;
            $requisitionData = $this->Requisition->compare_requisitions($manufactureOrder,$outbound,false,false);
            $link= "<a href='/v2/owner/entity/manufacturing_order/$moId/show'>#$code</a>" ;
            $allowsExceeding =settings::getValue(MANUFACTURING_PLUGIN, 'exceeding_the_requested_quantity_in_manufacturing_order', null, false);
            if (is_bool($requisitionData) && !$allowsExceeding){
                $message = sprintf(__t("All of manufacturing order %s quantities have been converted") , $link);
                $this->flashMessage($message);
                $this->redirect($this->referer());
            }
        }
    }
}