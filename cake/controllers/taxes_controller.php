<?php

use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;

class TaxesController extends AppController {

    var $name = 'Taxes';

    /**
     * @var Tax
     */
    var $Tax;
    var $uses = array('Tax');
    var $helpers = array('Html', 'Form');

    function owner_update() {    
            $site = getAuthOwner();
            if ($site['staff_id'] != 0) {
            if (!check_permission(Settings_Set_Taxes)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
//        NotificationV::delete_notification(NOTI_NO_TAXES_ADDED, null);                        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $Errors = '';
            $negativeInclusiveErrors = [];
            $this->Tax->recursive = -1;
            
            $taxes = $this->Tax->getTaxList();
	        foreach ($this->data['Tax'] as $item) {
                $taxes_posted_ids[] = $item['id'];
            }
            if (!is_array($taxes_posted_ids))
                $taxes_posted_ids = array();
            $delete_taxes_array = array_diff(array_keys($taxes), $taxes_posted_ids);
            if ($delete_taxes_array) {
                $this->loadModel('Product');
                $this->Product->disableBranchFind();
                $product = $this->Product->find('first', ['conditions' => ['OR' => ['Product.tax1' => $delete_taxes_array, 'Product.tax2' => $delete_taxes_array]]]);
                if ($product) {
                    $invalidTaxID = in_array($product['Product']['tax1'], $delete_taxes_array) ? $product['Product']['tax1'] : $product['Product']['tax2'];
                    $this->flashMessage(sprintf(
                        __('You cannot delete the Tax %s as it’s already assigned to an Item <a href="%s" target="_blank">#%s</a>', true),
                        $taxes[$invalidTaxID],
                        Router::url(['controller' => 'products', 'action' => 'view', $product['Product']['id']]),
                        $product['Product']['product_code'] ?: $product['Product']['id']
                    ));
                    $url = array('action' => 'update');
                    if (isset($_GET['box'])) {
                        $url['?'] = array('box' => 1, 'updateparent' => true);
                    }
                    $this->redirect($url);
                }
                if (ifPluginInstalled(\Izam\Daftra\Common\Utils\PluginUtil::ETA_PLUGIN)) {
                    $this->loadModel('SystemEtaTax');
                    $systemTax = $this->SystemEtaTax->find('first', ['conditions' => ['SystemEtaTax.system_tax' => $delete_taxes_array]]);
                    if (!empty($systemTax)) {
                        $this->flashMessage(__('You cannot delete the taxes as it’s already selected in the Electronic Invoice Settings', true));
                        $url = array('action' => 'update');
                        if (isset($_GET['box'])) {
                            $url['?'] = array('box' => 1, 'updateparent' => true);
                        }
                        $this->redirect($url);
                    }
                }
                $this->loadModel('JournalAccount');
                $this->JournalAccount->disableBranchFind();
                $this->loadModel('JournalTransaction');
                $journalAccount = $this->JournalAccount->find('list',  ['fields' => ['id'],'conditions' => ['JournalAccount.entity_type' => [ 'income_tax','outcome_tax'], 'JournalAccount.entity_id' => $delete_taxes_array]]);
                $journalTransaction = $this->JournalTransaction->find('first', ['conditions' => ['JournalTransaction.journal_account_id' => $journalAccount]]);


                if (!empty($journalTransaction['JournalTransaction']['id'])) {
                    $invalidTaxID = $journalTransaction['JournalAccount']['entity_id'];
                    $this->flashMessage(sprintf(__('You cannot delete the Tax %s as it’s already assigned in Journal <a href="%s" target="_blank">#%s</a>', true), $invalidTaxID, '/owner/journals/view/'.$journalTransaction['Journal']['id'] , $journalTransaction['Journal']['number']));
                    $url = array('action' => 'update');
                    if (isset($_GET['box'])) {
                        $url['?'] = array('box' => 1, 'updateparent' => true);
                    }
                    $this->redirect($url);
                }
	            $this->loadModel('ShippingOption');
				$shipping = $this->ShippingOption->find('first', ['conditions' => ['OR' => ['ShippingOption.tax_id' => $delete_taxes_array]]]);
	            if ($shipping) {
		            $invalidTaxID = $shipping['ShippingOption']['tax_id'];
		            $this->flashMessage(sprintf(__('You cannot delete the Tax %s as it’s already assigned to a %s <a href="%s" target="_blank">#%s</a>', true), $taxes[$invalidTaxID], __('ShippingOption', true), '/v2/owner/shipping_options' , $shipping['ShippingOption']['name']));
		            $url = array('action' => 'update');
		            if (isset($_GET['box'])) {
			            $url['?'] = array('box' => 1, 'updateparent' => true);
		            }
		            $this->redirect($url);
	            }
                if(ifPluginInstalled(PluginUtil::RENTAL_PLUGIN))
                {
                    $this->loadModel('RentalUnitType');
                    $rentalUnitType = $this->RentalUnitType->find('first', ['conditions' => ['OR' => ['tax_1' => $delete_taxes_array, 'tax_2' => $delete_taxes_array]]]);
                    if ($rentalUnitType) {
                        $invalidTaxID = in_array($rentalUnitType['RentalUnitType']['tax_1'], $delete_taxes_array) ? $rentalUnitType['RentalUnitType']['tax_1'] : $rentalUnitType['RentalUnitType']['tax_2'];
                        $this->flashMessage(sprintf(__('You cannot delete the Tax %s as it’s already assigned to a Unit Type %s <a href="%s" target="_blank">#%s</a>', true), $taxes[$invalidTaxID] , $rentalUnitType['RentalUnitType']['name'], '/v2/owner/rental-unit-type/' . $rentalUnitType['RentalUnitType']['id'], $rentalUnitType['RentalUnitType']['id']));
                        $url = array('action' => 'update');
                        if (isset($_GET['box'])) {
                            $url['?'] = array('box' => 1, 'updateparent' => true);
                        }
                        $this->redirect($url);
                    }
                }
	            $this->loadModel('PurchaseOrderTax');
                $purchase_order_tax = $this->PurchaseOrderTax->find('first', ['conditions' => ['OR' => ['PurchaseOrderTax.tax_id' => $delete_taxes_array]]]);
                if($purchase_order_tax) {
                    $this->loadModel('PurchaseOrder');
		            $invalidTaxID = $purchase_order_tax['PurchaseOrderTax']['tax_id'];
                    $purchase_order = $this->PurchaseOrder->find('first', ['conditions' => [
                        'PurchaseOrder.id' => $purchase_order_tax['PurchaseOrderTax']['purchase_order_id']
                    ]]);

                    $resolved_message = self::create_cannot_delete_tax_message( $purchase_order);
//                    $message = "You cannot delete the tax as it’s already selected in the %s <a target='_blank' href='%s'>#%s</a>";
//                    $id = $purchase_order['PurchaseOrder']['id'];
//                    if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_QUOTATION) {
//                        $resolved_message = sprintf(__($message, true), __("Purchase Quotation", true), '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_QUOTATION.'/'.$id.'/show' , $purchase_order['PurchaseOrder']['no']);
//                    }else if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_ORDER) {
//                        $resolved_message = sprintf(__($message, true), __("Purchase Order", true), '/v2/owner/entity/'.EntityKeyTypesUtil::PURCHASE_ORDER.'/'.$id.'/show'  , $purchase_order['PurchaseOrder']['no']);
//                    } else if($purchase_order['PurchaseOrder']['type'] == PurchaseOrder::PURCHASE_INVOICE) {
//                        $resolved_message = sprintf(__($message, true), __("Purchase Invoice", true), '/owner/purchase_invoices/view/'. $purchase_order['PurchaseOrder']['id'] .'?reset=1'  , $purchase_order['PurchaseOrder']['no']);
//                    }
               
		            $this->flashMessage($resolved_message);
		            $url = array('action' => 'update');
		            if (isset($_GET['box'])) {
			            $url['?'] = array('box' => 1, 'updateparent' => true);
		            }
		            $this->redirect($url);
                }
            }
            $this->Tax->deleteAll(array('Tax.id' => $delete_taxes_array));
            if (!empty($this->data['Tax'])) {
                foreach ($this->data['Tax'] as $i => $item) {
                    if ($item['value'] < 0 && $item['included']) {
                        $negativeInclusiveErrors[0] = sprintf(__("You cannot save the tax that called %s with negative amount and type inclusive", true), '"'.$item["name"].'"');
                        continue;
                    }
                    $item['site_id'] = getAuthOwner('id');
                    $this->Tax->create();
                    $data['Tax'] = $item;
                    if (!$this->Tax->save($data)) {
                        $errors[$i] = $this->Tax->validationErrors;
                    }
                }
                if (!empty($errors)) {
                    $this->flashMessage(__('Could not update taxes settings', true));
                    $this->Tax->validationErrors = $errors;
                } elseif (!empty($negativeInclusiveErrors)) {
                    $this->flashMessage(__(implode("<br/>", $negativeInclusiveErrors), true));
                } else {
                    $this->flashMessage(__('Tax settings have been updated', true), 'Sucmessage');
                    $url = array('action' => 'update');
                    if (isset($_GET['box'])) {
                        $url['?'] = array('box' => 1, 'updateparent' => true);
                    }
                    $this->redirect($url);
                }
            }
        }
        $this->owner_index();
        $this->render('owner_index');
    }

    public static function create_cannot_delete_tax_message(array $purchase_order): string
    {
        $message = "You cannot delete the tax as it’s already selected in the %s <a target='_blank' href='%s'>#%s</a>";
        $type = $purchase_order['PurchaseOrder']['type'];
        $id = $purchase_order['PurchaseOrder']['id'];
        $no = $purchase_order['PurchaseOrder']['no'];

        switch ($type) {
            case PurchaseOrder::PURCHASE_QUOTATION:
                $label = __("Purchase Quotation", true);
                $url = '/v2/owner/entity/' . EntityKeyTypesUtil::PURCHASE_QUOTATION . '/' . $id . '/show';
                break;

            case PurchaseOrder::PURCHASE_ORDER:
                $label = __("Purchase Order", true);
                $url = '/v2/owner/entity/' . EntityKeyTypesUtil::PURCHASE_ORDER . '/' . $id . '/show';
                break;

            case PurchaseOrder::PURCHASE_INVOICE:
                $label = __("Purchase Invoice", true);
                $url = '/owner/purchase_invoices/view/' . $id . '?reset=1';
                break;

            default:
                return __("Unknown purchase order type", true);
        }

        return sprintf(__($message, true), $label, $url, $no);
    }

//----------------------------
    function owner_index($invoice_id=false) {
         $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (IS_REST) {
                if (!check_permission([Settings_Set_Taxes,OPEN_OWN_POS_SESSIONS, PermissionUtil::MANAGE_BOOKING_SETTINGS]))
                    $this->cakeError('error403');
            } else {
                if (!check_permission(Settings_Set_Taxes)) {
                    if (IS_REST) $this->cakeError('error403');
                    $this->flashMessage(__('You are not allowed to view this page', TRUE));
                    $this->redirect('/');
                }
            }
        }
        $this->modelName = "Tax";
        $this->Tax->recursive = 0;
        
        $taxes = $this->Tax->find('all', array('order' => array('Tax.id'=>'ASC')));
		
		
        $this->set('taxes', $taxes);
        $this->set('title_for_layout',  __('Set Taxes', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

        $this->crumbs[1]['title'] = $this->pageTitle;
        $this->crumbs[1]['url'] = '#';

        $this->set('content', $this->get_snippet('taxes'));
		if(IS_REST){
			$this->set('rest_items', $taxes);
			$this->set('rest_model_name', "Tax");
			$this->render("index");
		}
    }

    //----------------------------------
    function owner_add() {
         $site = getAuthOwner();
            
            if (!check_permission(Settings_Set_Taxes)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        
        if (!empty($this->data)) {
            $this->Tax->create();
            $this->data['Tax']['site_id'] = getAuthOwner('id');
            if ($this->Tax->save($this->data)) {
				if(IS_REST){
					$this->set('id', $this->Tax->id);
					$this->render('created');
					return;
				}
//                NotificationV::delete_notification(NOTI_NO_TAXES_ADDED, null);                        
                $this->flashMessage(sprintf(__('The %s has been saved', true), __('tax', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Tax->validationErrors]);
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('tax', true)));
            }
        }
        $this->set('title_for_layout',  __('Add Tax', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

        $this->crumbs[1]['title'] = 'Set Taxes';
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';
    }

    //----------------------------------
    function owner_edit($id = null) {
         $site = getAuthOwner();
            if ($site['staff_id'] != 0) {
            if (!check_permission(Settings_Set_Taxes)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf(__('Invalid %s.', 'tax', true)));
            $this->redirect(array('action' => 'index'));
        }
        $tax = $this->Tax->find('first', array('conditions' => array('Tax.id' => $id)));
		if(IS_REST){
			if(empty($tax)) $this->cakeError('error404', array('message' => __('Invoice not found', true)));
			$this->data['Tax']['id'] = $id;
		}
        if (!empty($this->data)) {
            $this->data['Tax']['site_id'] = getAuthOwner('id');
            if ($this->Tax->save($this->data)) {
				if(IS_REST){
					$this->render('success');
					return;
				}
                $this->flashMessage(sprintf(__('The %s  has been saved', true), __('tax', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
				if(IS_REST) $this->cakeError("error400", ["message"=>null, "validation_errors"=>$this->Tax->validationErrors]);
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('tax', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $tax;
        }

        $this->set('title_for_layout',  __('Edit Tax', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

        $this->crumbs[1]['title'] = 'Set Taxes';
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->render('owner_add');
    }

    //----------------------------
    function owner_delete($id = null) {
         $site = getAuthOwner();
            if ($site['staff_id'] != 0) {
            if (!check_permission(Settings_Set_Taxes)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true),__('tax', true))]);
			}else{
				$this->flashMessage(sprintf(__('Invalid id for %s', true), __('tax', true)));
				$this->redirect(array('action' => 'index'));
			}
        }
        $module_name = __('tax', true);
        if (is_countable($id) && count($id) > 1) {
            $module_name = __('taxes', true);
        }
        $taxes = $this->Tax->find('all', array('conditions' => array('Tax.id' => $id)));
        if (empty($taxes)) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), $module_name)]);
			}else{
				$this->flashMessage(sprintf(__('%s not found', true), $module_name));
				$this->redirect($this->referer(array('action' => 'index'), true));
			}
        }
		if(IS_REST){
			$_POST['submit_btn'] = 'yes';
			$_POST['ids'] = [$id];
		}
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Tax->deleteAll(array('Tax.id' => $_POST['ids']))) {
				if(IS_REST){
					$this->set("message", sprintf(__('%s deleted', true), $module_name));
					$this->render("success");
					return;
				} else {
					$this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
					$this->redirect(array('action' => 'index'));
				}
            } else {
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect(array('action' => 'index'));
            }
        }
        $this->set('title_for_layout',  __('Delete Tax', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Settings', true);
        $this->crumbs[0]['url'] = array('action' => 'change_settings', 'controller' => 'sites');

        $this->crumbs[1]['title'] = 'Set Taxes';
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->set('taxes', $taxes);
    }

    function owner_taxeslist($invoice_id = false,$updated=false) {
        
        $this->loadModel('InvoiceTax');
        $taxes = $this->InvoiceTax->getInvoiceTaxList();
        $jsTaxes = $this->InvoiceTax->getJSONList($invoice_id,$updated);
        die('{"taxes":' . json_encode($taxes) . ',"jsTaxes":' . $jsTaxes . '}' . PHP_EOL . PHP_EOL . PHP_EOL);
    }
	
	public function api_view($id = null) {
		$site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Settings_Set_Taxes)) {
				$this->cakeError('error403');
            }
        }
		$tax = $this->Tax->find("first", ["conditions" => array("Tax.id" => $id)] );
		if(empty($tax)) $this->cakeError('error404', array('message' => sprintf(__('%s not found', true), __("Tax", true))));
        else $this->set('rest_item', $tax);
		$this->set('rest_model_name', "Tax");
		$this->render("view");
	}

    /**
     * Compare Model tax list with current tax list values
     * @param $item_id Int Invoice id  or PurchaseOrder id
     * @param $model String name of model invoice or purchase_order
     * @return string json list of changed tax
     */
    public function owner_tax_compare($item_id = null, $model = null)
    {
        echo $this->Tax->tax_compare($item_id,$model);
        die();
    }

}
