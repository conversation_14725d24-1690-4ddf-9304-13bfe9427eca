<?php
/** @property SmsCampaign $SmsCampaign */
/** @property Client $Client */

use App\Repositories\WhatsAppTemplateRepository;
use Izam\Daftra\Portal\Services\SmsSenderNameService;
use Izam\Limitation\Utils\LimitationUtil;

/** @property FollowUpReminder $FollowUpReminder */
class SmsCampaignsController extends AppController {
	function beforeFilter(){
		parent::beforeFilter();
		if(!sms_enabled()){
			$this->flashMessage(__('You need to enable the SMS plugin first', true));
			$this->redirect(["controller"=>"owners", "action"=>"plugin_manager",2]);
		}
		App::import('Vendor', 'settings');
		$external = \settings::getValue(0, "sms_external",null,false,false);
		if(empty($external)){
			$this->flashMessage(__('You need to configure the SMS plugin first', true));
			$this->redirect(["controller"=>"sms", "action"=>"index"]);
		}
	}
	function owner_view($id = null) {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
		$campaign = $this->SmsCampaign->find('first', ['conditions'=>['SmsCampaign.id'=>$id, 'SmsCampaign.site_id'=> getCurrentSite('id')]]);
		if (empty($campaign)) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('SMS Campaign', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('campaign', $campaign);
	}
	
	function owner_status($id = null) {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
		$this->autoRender = false;
		$this->RequestHandler->respondAs('json');
		$campaign = $this->SmsCampaign->find('first', ['recursive'=>1, 'conditions'=>['SmsCampaign.id'=>$id, 'SmsCampaign.site_id'=> getCurrentSite('id')]]);
//		var_dump($campaign);
		if (empty($campaign)) {
			return json_encode(['data'=>[]]);
		}
		$data = [];
		foreach ($campaign['SmsLog'] as $value) {
            $my_datetime=$value["sent_time"];
			$data[]=[
				"id" => $value["id"],
				"message" => $value["message"],
				"phone_number" => $value["phone_number"],
				"sms_size" => $value["sms_size"],
				"sent_time" =>$my_datetime ? date('Y-m-d H:i:s',strtotime("$my_datetime UTC")) : null,
				"error_message" => $value["error_message"],
			];
		}
		return json_encode(["status"=>$campaign["SmsCampaign"]["state"], 'data'=>$data]);
	}
	
	function owner_sender_name() {
		$this->loadModel("Post");
		$this->loadModel("SmsSenderName");
		App::import('Vendor', 'settings');
		$approved_sender_name = settings::getValue(0, 'approved_sender_name');
		if(!empty($this->data["sender_name"])&&$this->data["sender_name"]!==$approved_sender_name){
			$sender_name = $this->SmsSenderName->findBySiteId(getCurrentSite('id'));
            $smsSenderNameData = ['status' => 'Pending', 'name' => $this->data["sender_name"]];
			if(empty($sender_name)){
                $smsSenderNameData['site_id'] = getCurrentSite('id');
                $this->SmsSenderName->data = $smsSenderNameData;
                if (!$this->SmsSenderName->validates()) {
                    $this->flashMessage(__('Invalid Sender Name', true));
                    $this->redirect(["action"=>"settings"]);
                }
                SmsSenderNameService::create($smsSenderNameData);
			} else {
                SmsSenderNameService::updateById($sender_name['SmsSenderName']['id'], $smsSenderNameData);
			}
			$_POST["data"]["SitesEnquiry"]["subject"]="SMS name";
			$_POST["data"]["SitesEnquiry"]["description"]=$this->data["sender_name"];
			$this->requestAction("/owner/sites_enquiries/index/20");
		} else {
			$this->flashMessage(__('Invalid Sender Name', true));
			$this->redirect(["action"=>"settings"]);
		}
	}
	
	function approve_name($secret_hash) {
		if($secret_hash !== "74f8a65e9941ce0ada692dc819ca5fad") exit("0");
		$this->loadModel("SmsSenderName");
		App::import('Vendor', 'settings');
		$sender_name = $this->SmsSenderName->findBySiteId(getCurrentSite('id'));
		if(empty($sender_name)) exit("0");
		settings::setValue(0, 'approved_sender_name', $sender_name['SmsSenderName']['name']);
		exit("1");
	}
	
	function owner_settings() {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
		if(!sms_enabled()){
			$this->flashMessage(__('SMS not enabled', true));
            $this->redirect('/');
		}
		$this->loadModel("Post");
		$this->loadModel("SmsSenderName");
		App::import('Vendor', 'settings');
		\App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$approved_sender_name = settings::getValue(0, 'approved_sender_name');
		$external = \settings::getValue(0, "sms_external",null,false,false);
		$this->data = json_decode(\settings::getValue(0, "sms_settings",null,false,false),true);
		if(!empty($external)) $this->data['external'] = 1;
		$sender_name = $this->SmsSenderName->findBySiteId(getCurrentSite('id'));
        $this->set('approved_sender_name', $approved_sender_name);
        $this->set('sender_name', $sender_name);
		$this->set('country_code', getCurrentSite("country_code"));
	}
	
	function owner_index() {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $this->paginate['SmsCampaign']['order'] = 'SmsCampaign.id DESC';
		if(!sms_enabled()){
			$this->flashMessage(__('SMS not enabled', true));
            $this->redirect('/');
		}
		$this->SmsCampaign->recursive = 0;
		$site_id = getAuthOwner('id');
		$conditions = $this->_filter_params();
		$conditions['SmsCampaign.site_id'] = $site_id;
		$this->set('smsCampaigns', $this->paginate('SmsCampaign', $conditions));
	}
	
	private function get_placeholder_list_by_type($type) {
		switch ($type) {
			case 'invoices':
				return PlaceHolder::invoice_get_all_place_holder_list();
			case 'clients':
				return PlaceHolder::client_place_holder_list();
			case 'appointments':
				return PlaceHolder::appointment_place_holder_list();
			case 'appointments_staff':
				return PlaceHolder::appointment_place_holder_list()+PlaceHolder::staff_place_holder_list();
            case 'bookings':
                return PlaceHolder::bookings_place_holder_list()+PlaceHolder::datetime_place_holder_list();

            default:
				return [];
		}
	}
	
    function owner_send_bulk_sms($type, $extra = null) {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        ini_set("memory_limit", "5542M");
	    set_time_limit(3600);
        if(!sms_enabled()){
			$this->flashMessage(__('SMS not enabled', true));
            $this->redirect('/');
		}
		if(empty($type) || !in_array($type, ['clients', 'bookings','appointments', 'appointments_staff', 'invoices'])){
			$this->flashMessage(sprintf(__('Invalid %s', true), __('type', true)));
            $this->redirect(array('action' => 'index'));
		}

        if(empty($this->data['SmsTemplate']['ids']) and empty($_POST['ids'])){
            $this->flashMessage(sprintf(__('Invalid %s', true), __('client', true)));
            $this->redirect(array('action' => 'index'));
        }
        $ids = isset($_POST['ids']) ? $_POST['ids'] : explode(',', $this->data['SmsTemplate']['ids']);
		
		if(!is_array($ids)){
            $ids = explode(',', $ids);
        }

        if (empty($ids)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __($type, true)));
            $this->redirect(array('action' => 'index'));
        }
        $this->set('type', $type);
		App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$sms_records = $sms_tuples = [];
		$invoice_ids_list = [];
        switch ($type) {
			case "invoices":
				$action = ACTION_SEND_SMS_TO_INVOICE;
				$this->loadModel('Invoice');
				$invoices = $this->Invoice->find('all', array(
					'conditions' => array('OR'=>['Client.phone1 !=' => '', 'Client.phone2 !=' => ''], 'Invoice.id' => isset($this->data['SmsTemplate']['invoice_ids']) ? explode(',', $this->data['SmsTemplate']['invoice_ids']) : $ids),
					'joins' => [
						[
							'table' => 'clients',
							'alias' => 'Client',
							'type' => 'inner',
							'conditions' => array(
								'Client.id = Invoice.client_id'
							)
						],
					],
					'fields' => ["Client.*", "Invoice.*"],
					'recursive' => -1,
				));
				foreach ($invoices as $invoice) {
					$id_list[] = $invoice['Client']['id'];
					$invoice_ids_list[] = $invoice['Invoice']['id'];
					if(!empty($invoice['Client']['phone1'])){
						$sms_tuples[]=['phone'=>$invoice['Client']['phone1'], 'country_code'=>$invoice['Client']['country_code']?$invoice['Client']['country_code']:getCurrentSite('country_code'), 'ref_id'=>[\SendSMS\SendSMS::INVOICE=>$invoice['Invoice']['id']], 'ref_type'=>null];
						$sms_records[] = array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Client']['id'], 'param2' => $invoice['Client']["type"]!=2?$invoice['Client']['business_name']:$invoice['Client']["first_name"].' '.$invoice['Client']["last_name"], 'param3' => $invoice['Client']['phone1'], 'param4' => $invoice['Client']['client_number'], 'param6'=> \SendSMS\SendSMS::formatMessage([\SendSMS\SendSMS::INVOICE=>$invoice['Invoice']['id']], $this->data['SmsTemplate']['body'], null));
					}
					if(!empty($invoice['Client']['phone2'])&&$invoice['Client']['phone1']!==$invoice['Client']['phone2']){
						$sms_tuples[]=['phone'=>$invoice['Client']['phone2'], 'country_code'=>$invoice['Client']['country_code']?$invoice['Client']['country_code']:getCurrentSite('country_code'), 'ref_id'=>[\SendSMS\SendSMS::INVOICE=>$invoice['Invoice']['id']], 'ref_type'=>null];
						$sms_records[] = array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Client']['id'], 'param2' => $invoice['Client']["type"]!=2?$invoice['Client']['business_name']:$invoice['Client']["first_name"].' '.$invoice['Client']["last_name"], 'param3' => $invoice['Client']['phone2'], 'param4' => $invoice['Client']['client_number'], 'param6'=> \SendSMS\SendSMS::formatMessage([\SendSMS\SendSMS::INVOICE=>$invoice['Invoice']['id']], $this->data['SmsTemplate']['body'], null));
					}
				}
				$this->set('ids', $id_list);
				$this->set('invoices', $invoices);
				$this->set('invoice_ids_list', $invoice_ids_list);
				break;
			case "clients":
				$action = ACTION_SEND_SMS_TO_CLIENT;
				$this->loadModel('Client');
				$clients = $this->Client->find('all', array('conditions' => array('OR'=>['Client.phone1 !=' => '', 'Client.phone2 !=' => ''], 'Client.id' => $ids)));
				foreach ($clients as $client) {
					$id_list[] = $client['Client']['id'];
					if(!empty($client['Client']['phone1'])){
						$sms_tuples[]=['phone'=>$client['Client']['phone1'], 'country_code'=>$client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code'), 'ref_id'=>$client['Client']['id'], 'ref_type'=>\SendSMS\SendSMS::CLIENT];
						$sms_records[] = array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['phone1'], 'param4' => $client['Client']['client_number'], 'param6'=> \SendSMS\SendSMS::formatMessage($client['Client']['id'], $this->data['SmsTemplate']['body'], \SendSMS\SendSMS::CLIENT));
					}
					if(!empty($client['Client']['phone2'])&&$client['Client']['phone1']!==$client['Client']['phone2']){
						$sms_tuples[]=['phone'=>$client['Client']['phone2'], 'country_code'=>$client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code'), 'ref_id'=>$client['Client']['id'], 'ref_type'=>\SendSMS\SendSMS::CLIENT];
						$sms_records[] = array('primary_id' => $client['Client']['id'], 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $client['Client']['phone2'], 'param4' => $client['Client']['client_number'], 'param6'=> \SendSMS\SendSMS::formatMessage($client['Client']['id'], $this->data['SmsTemplate']['body'], \SendSMS\SendSMS::CLIENT));
					}
				}
				$this->set('ids', $id_list);
				$this->set('clients', $clients);
				break;
			case "appointments":
                $action = ACTION_SEND_SMS_TO_APPOINTMENT;
				$this->loadModel('FollowUpReminder');
				$appointments = $this->FollowUpReminder->find('all',["conditions"=>["FollowUpReminder.id"=>$ids]]);
				$id_list = $partners = [];
				foreach ($appointments as $key => $appointment) {

                    $partner = $this->FollowUpReminder->get_type_contact($appointment['FollowUpReminder']['id'], false);
					if(!empty($partner)&&!empty($partner['phone'])) {
						$has_phone = false;
						foreach ($partner['phone'] as $phone) {
							if(!empty($phone)) {
								$has_phone = true;
								$sms_tuples[]=['phone'=>$phone, 'country_code'=>$partner['country_code'], 'ref_id'=>$appointment['FollowUpReminder']['id'], 'ref_type'=>\SendSMS\SendSMS::APPOINTMENT];
								$sms_records[] = array('primary_id' => $appointment['FollowUpReminder']['id'], 'secondary_id' => $partner['id'], 'param2' => $partner['name'], 'param3' => $phone, 'param4' => $partner['number'], 'param6'=> \SendSMS\SendSMS::formatMessage($appointment['FollowUpReminder']['id'], $this->data['SmsTemplate']['body'], \SendSMS\SendSMS::APPOINTMENT));
							}
						}
						if($has_phone){
							$partners[]=$partner;
							$id_list[] = $appointment['FollowUpReminder']['id'];
						}
					}
				}
				if (empty($partners)) {
					$this->flashMessage(__('Client phone number not set', true));
					$this->redirect($this->referer(array('action' => 'index')));
				}
				$partners= array_unique($partners, SORT_REGULAR);
				$this->set('ids', $id_list);
				$this->set('partners', $partners);
				break;
				case "bookings":
                $this->loadModel('FollowUpReminder');
                $action = ACTION_SEND_SMS_TO_APPOINTMENT;
                App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
                $invoiceFactory = \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
                $booking =& $invoiceFactory;
                $partners = [];
				foreach($ids as $k => $id)
                {

                    $booking->setEntityData($id,[]);
                    $appointment = $booking->data;
                    $partner = $this->FollowUpReminder->get_type_contact($appointment['FollowUpReminder']['id'], false);

                    if(!empty($partner)&&!empty($partner['phone'])) {
                        $has_phone = false;
                        foreach ($partner['phone'] as $phone) {
                            if(!empty($phone)) {
                                $has_phone = true;
                                $sms_tuples[]=['phone'=>$phone, 'country_code'=>$partner['country_code'], 'ref_id'=>$appointment['FollowUpReminder']['id'], 'ref_type'=>\SendSMS\SendSMS::APPOINTMENT];
                                $sms_records[] = array('primary_id' => $appointment['FollowUpReminder']['id'], 'secondary_id' => $partner['id'], 'param2' => $partner['name'], 'param3' => $phone, 'param4' => $partner['number'], 'param6'=> \SendSMS\SendSMS::formatMessage($appointment['FollowUpReminder']['id'], $this->data['SmsTemplate']['body'], \SendSMS\SendSMS::APPOINTMENT));
                            }
                        }
                        if($has_phone){
                            $partners[]=$partner;
                            $id_list[] = $appointment['Invoice']['id'];
                        }
                    }
                }
				if (empty($partners)) {

                    $this->flashMessage(__('Client phone number not set', true));
					$this->redirect($this->referer(array('action' => 'index')));
				}
				$partners= array_unique($partners, SORT_REGULAR);
				$this->set('ids', $id_list);
				$this->set('partners', $partners);
				break;
			case "appointments_staff":
				$action = ACTION_SEND_SMS_TO_APPOINTMENT_STAFF;
				$this->loadModel('FollowUpReminder');
				$appointments = $this->FollowUpReminder->find('all',["conditions"=>["FollowUpReminder.id"=>$ids]]);
				$id_list = $partners = [];
				foreach ($appointments as $key => $appointment) {
					$staffs = $this->FollowUpReminder->get_type_staff_contacts($appointment['FollowUpReminder']['id'], false);
					foreach ($staffs as $staff) {
						if(!empty($staff)&&!empty($staff['phone'])) {
							foreach ($staff['phone'] as $phone) {
								if(!empty($phone)) {
									$has_phone = true;
									$sms_tuples[]=['phone'=>$phone, 'country_code'=>$staff['country_code'], 'ref_id'=>[\SendSMS\SendSMS::APPOINTMENT=>$appointment['FollowUpReminder']['id'], \SendSMS\SendSMS::STAFF=>$staff['id']], 'ref_type'=>null];
									$sms_records[] = array('primary_id' => $appointment['FollowUpReminder']['id'], 'secondary_id' => $staff['id'], 'param2' => $staff['name'], 'param3' => $phone, 'param6'=> \SendSMS\SendSMS::formatMessage([\SendSMS\SendSMS::APPOINTMENT=>$appointment['FollowUpReminder']['id'], \SendSMS\SendSMS::STAFF=>$staff['id']], $this->data['SmsTemplate']['body'], null));
								}
							}
							if($has_phone){
								$partners[]=$staff;
								$id_list[] = $appointment['FollowUpReminder']['id'];
							}
						}
					}
				}
				if (empty($partners)) {
					$this->flashMessage(__('Staff phone number not set', true));
					$this->redirect($this->referer(array('action' => 'index')));
				}
				$id_list= array_unique($id_list);
				$partners= array_unique($partners, SORT_REGULAR);
				$this->set('ids', $id_list);
				$this->set('partners', $partners);
				break;
		}

        $this->loadModel('SmsTemplate');
		$id_list_count = is_countable($id_list) ? count($id_list) : 0;
		if(count($ids) != $id_list_count )  $this->flashMessage(__('Some items don\'t have contact numbers', true));
		App::import('Vendor', 'PlaceHolder', array('file' => 'PlaceHolder.php'));
        $placeHolders = $this->get_placeholder_list_by_type($type);
        $this->set('PlaceHolders', $placeHolders);

        if (!empty($this->data) and !empty($this->data['SmsTemplate']['action'])) {

            if (empty($this->data['SmsTemplate']['body'])) {
                $this->SmsTemplate->validationErrors['body'] = __('Please enter Message', true);
                $error = true;
            }
            if ($error) {
                $this->flashMessage(__('Could not send SMS to clients', true));
            } else {  
				$sms_error = ''; 	 
				$sms_campaign_id = \SendSMS\SendSMS::sendMessage($sms_tuples, $this->data['SmsTemplate']['body'],$sms_error);

                if ($sms_campaign_id) {
					foreach ($sms_records as $sms_tuple) {
						$sms_tuple['param5'] = $sms_campaign_id;
						$this->add_actionline($action, $sms_tuple);
					}
                    $this->redirect(array('controller'=>'sms_campaigns', 'action' => 'view', $sms_campaign_id));
                } else {
                    $this->flashMessage(sprintf(__('Could not send SMS to the clients, %s', true), $sms_error));
                }
            }
        }
        $sms_templates = $this->SmsTemplate->getSmsTemplates(null, $type);
        $this->set('sms_templates', $sms_templates);
        $this->set('ids', $ids);
    }

    function owner_send_sms($type, $id = null) {

//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
        $this->handleSiteLimit(
            checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LINK_SMS_GATEWAYS),
            $_SERVER['HTTP_REFERER']
        );

		if(!sms_enabled()){
			$this->flashMessage(__('SMS not enabled', true));
            $this->redirect('/');
		}
		if(empty($type) || !in_array($type, ['clients', 'invoices','bookings'])){
			$this->flashMessage(sprintf(__('Invalid %s', true), __('type', true)));
            $this->redirect(array('action' => 'index'));
		}
        if (getCurrentSMSProvider() == 'WhatsAppSendSMS') {
            $this->redirect(['action' => 'owner_whatsapp_send', $type, $id]);
        }

		$phones = [];
		App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		switch ($type) {
			case 'invoices':
				$this->loadModel('Invoice');
				$client = $this->Invoice->findById($id);
				$phones = $this->get_client_phones($client);
				$country_code = $client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code');
				$ref_id = $client['Invoice']['id'];
				$ref_type = \SendSMS\SendSMS::INVOICE;
				$action = ACTION_SEND_SMS_TO_INVOICE;
				break;
			case 'clients':
				$this->loadModel('Client');
				$client = $this->Client->findById($id);
				$phones = $this->get_client_phones($client);
				$country_code = $client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code');
				$ref_id = $client['Client']['id'];
				$ref_type = \SendSMS\SendSMS::CLIENT;
				$action = ACTION_SEND_SMS_TO_CLIENT;
				break;
            case 'bookings':
                App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
                $invoiceFactory = \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
                $booking =& $invoiceFactory;
                $booking->setEntityData($id,[]);
                $client['Client'] = $booking->data['Client'];
                $phones = $this->get_client_phones($client);
                $country_code = $client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code');
                $ref_id = $booking->data['Invoice']['id'];
                $ref_type = \SendSMS\SendSMS::BOOKING;
                $action = ACTION_SEND_SMS_TO_CLIENT;
                break;
//			case 'appointments':
//				$this->loadModel('Client');
//				$this->loadModel('FollowUpReminder');
//				$appointment = $this->FollowUpReminder->findById($id);
//				if (!$appointment) {
//					$this->flashMessage(__('Appointment not found', true));
//					$this->redirect($this->referer(array('action' => 'index')));
//				}
//				$partner = $this->FollowUpReminder->get_type_contact($id, false);
//				if (!$partner) {
//					$this->flashMessage(__('Client not found', true));
//					$this->redirect($this->referer(array('action' => 'index')));
//				}
//				//$phones = $this->get_client_phones($client);
//				foreach ($partner['phone'] as $phone) {
//					if(!empty($phone)) $phones[] = $phone;
//				}
//				if (empty($phones)) {
//					$this->flashMessage(__('Client phone number not set', true));
//					$this->redirect($this->referer(array('action' => 'index')));
//				}
//				$country_code = $partner['country_code'];
//				$ref_id = $appointment['FollowUpReminder']['id'];
//				/* 
//				 * Check if type = client for later
//				 * TODO: Add support for other types when added to system
//				 */
//				if($partner['type']=="Client") $client = $this->Client->findById($partner['id']);
//				$ref_type = \SendSMS\SendSMS::APPOINTMENT;
//				$action = ACTION_SEND_SMS_TO_APPOINTMENT;
//				break;
		}
		
        $this->set('ref_id', $ref_id);
        $this->set('type', $type);
        $this->set('client_phone', array_unique($phones));
		
        $this->loadModel('SmsTemplate');

        //  $placeHolders = $this->SmsTemplate->getPlaceHoldersList('client-sms');
        $placeHolders = $this->get_placeholder_list_by_type($type);//PlaceHolder::client_place_holder_list() + PlaceHolder::site_place_holder_list() + PlaceHolder::staff_place_holder_list();
        $this->set('PlaceHolders', $placeHolders);

        if (!empty($this->data)) {
            if (empty($this->data['SmsTemplate']['to_phone'])) {
                $this->SmsTemplate->validationErrors['to_phone'] = __('Please enter client phone', true);
                $error = true;
            }
            if (empty($this->data['SmsTemplate']['body'])) {
                $this->SmsTemplate->validationErrors['body'] = __('Please enter Message', true);
                $error = true;
            }
            if ($error) {
                $this->flashMessage(__('Could not send SMS', true));
            } else {
                //$placeHolders = PlaceHolder::site_place_holder($site) + PlaceHolder::client_place_holder($client) + PlaceHolder::staff_place_holder($site['staff_id']);

				$sms_error = '';
				$sms_campaign_id = \SendSMS\SendSMS::sendMessage(array_map(function($elem) use($country_code,$ref_id,$ref_type){
					return ['phone'=>$elem, 'country_code'=>$country_code, 'ref_id'=>$ref_id, 'ref_type'=>$ref_type];
				}, explode(",", $this->data['SmsTemplate']['to_phone'])), $this->data['SmsTemplate']['body'],$sms_error);

                if ($sms_campaign_id) {
                    $this->add_actionline($action, array('primary_id' => $ref_id, 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $this->data['SmsTemplate']['to_phone'], 'param4' => $client['Client']['client_number'], 'param5' => $sms_campaign_id, 'param6'=> \SendSMS\SendSMS::formatMessage($ref_id, $this->data['SmsTemplate']['body'], $ref_type)));
                    $this->redirect(array('controller'=>'sms_campaigns', 'action' => 'view', $sms_campaign_id));
                } else {
                    $this->flashMessage(sprintf(__('Could not send SMS to the client, %s', true), $sms_error));
                }
            }
        } else {
            unset($this->data["SmsTemplate"]['id']);
        }

        $this->set('client', $client);
		
        $sms_templates = $this->SmsTemplate->getSmsTemplates(null, $type);
        $this->set('sms_templates', $sms_templates);
    }


    function owner_whatsapp_send($type, $id = null)
    {
        $phones = [];
        App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
        switch ($type) {
            case 'invoices':
                $this->loadModel('Invoice');
                $invoice = $this->Invoice->findById($id);
                $phones = $this->get_client_phones($invoice);
                $country_code = $invoice['Client']['country_code'] ?: getCurrentSite('country_code');
                $ref_id = $invoice['Invoice']['id'];
                $ref_type = \SendSMS\SendSMS::INVOICE;
                $action = ACTION_SEND_SMS_TO_INVOICE;
                break;
            case 'clients':
                $this->loadModel('Client');
                $client = $this->Client->findById($id);
                $phones = $this->get_client_phones($client);
                $country_code = $client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code');
                $ref_id = $client['Client']['id'];
                $ref_type = \SendSMS\SendSMS::CLIENT;
                $action = ACTION_SEND_SMS_TO_CLIENT;
                break;
            case 'bookings':
                App::import('vendor','InvoiceV2',['file'=>'Invoice/autoload.php']);
                $invoiceFactory = \InvoiceV2\InvoiceFactory::create(\InvoiceV2\Base::INVOICE_TYPE_BOOKING, null, []);
                $booking =& $invoiceFactory;
                $booking->setEntityData($id,[]);
                $client = [];
                $client['Client'] = $booking->data['Client'];
                $phones = $this->get_client_phones($client);
                $country_code = $client['Client']['country_code']?$client['Client']['country_code']:getCurrentSite('country_code');
                $ref_id = $booking->data['Invoice']['id'];
                $ref_type = \SendSMS\SendSMS::BOOKING;
                $action = ACTION_SEND_SMS_TO_CLIENT;
                break;
        }

        $this->set('ref_id', $ref_id);
        $this->set('type', $type);
        $this->set('client_phone', array_unique($phones));

        $this->loadModel('WhatsAppTemplate');

        //  $placeHolders = $this->SmsTemplate->getPlaceHoldersList('client-sms');
        $placeHolders = $this->get_placeholder_list_by_type($type);//PlaceHolder::client_place_holder_list() + PlaceHolder::site_place_holder_list() + PlaceHolder::staff_place_holder_list();
        $this->set('PlaceHolders', $placeHolders);

        if (!empty($this->data)) {
            if (empty($this->data['SmsTemplate']['to_phone'])) {
                $this->SmsTemplate->validationErrors['to_phone'] = __('Please enter client phone', true);
                $error = true;
            }
            if (empty($this->data['SmsTemplate']['body'])) {
                $this->SmsTemplate->validationErrors['body'] = __('Please enter Message', true);
                $error = true;
            }
            if ($error) {
                $this->flashMessage(__('Could not send SMS', true));
            } else {
                //$placeHolders = PlaceHolder::site_place_holder($site) + PlaceHolder::client_place_holder($client) + PlaceHolder::staff_place_holder($site['staff_id']);

                $sms_error = '';
                $sms_campaign_id = \SendSMS\SendSMS::sendMessage(array_map(function($elem) use($country_code,$ref_id,$ref_type){
                    return ['phone'=>$elem, 'country_code'=>$country_code, 'ref_id'=>$ref_id, 'ref_type'=>$ref_type];
                }, explode(",", $this->data['SmsTemplate']['to_phone'])), $this->data['SmsTemplate']['body'],$sms_error);

                if ($sms_campaign_id) {
                    $this->add_actionline($action, array('primary_id' => $ref_id, 'secondary_id' => $client['Client']['id'], 'param2' => $client['Client']['business_name'], 'param3' => $this->data['SmsTemplate']['to_phone'], 'param4' => $client['Client']['client_number'], 'param5' => $sms_campaign_id, 'param6'=> \SendSMS\SendSMS::formatMessage($ref_id, $this->data['SmsTemplate']['body'], $ref_type)));
                    $this->redirect(array('controller'=>'sms_campaigns', 'action' => 'view', $sms_campaign_id));
                } else {
                    $this->flashMessage(sprintf(__('Could not send SMS to the client, %s', true), $sms_error));
                }
            }
        } else {
            unset($this->data["SmsTemplate"]['id']);
        }

        $this->set('client', $client);

        $whatsappTemplateRepo = new WhatsAppTemplateRepository();
        $sms_templates = $whatsappTemplateRepo->getAllTemplates();
        $this->set('sms_templates', $sms_templates);

        $whatsapp_sms_templates = $whatsappTemplateRepo->getAllTemplates();
        $this->set('whatsapp_sms_templates', $whatsapp_sms_templates);


    }

	private function get_client_phones($client) {
		$phones = [];
		
		if (!$client) {
			$this->flashMessage(__('Client not found', true));
			$this->redirect($this->referer(array('action' => 'index')));
		}
//        die(debug($client));
		if ((empty($client['Client']['phone1'])&&empty($client['Client']['phone2']))||!sms_enabled()) {
			$this->flashMessage(__('Client phone number not set', true));
			$this->redirect($this->referer(array('action' => 'index')));
		}

		if(!empty($client['Client']['phone1'])) $phones[]=$client['Client']['phone1'];
		if(!empty($client['Client']['phone2'])) $phones[]=$client['Client']['phone2'];
		
		return $phones;
	}
	
	function owner_external_sms() {
//        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
//            if(IS_REST) $this->cakeError('error403');
//            $this->flashMessage(__('You are not allowed to view this page', TRUE));
//            $this->redirect('/');
//        }
		if (empty($this->data)) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('External API data', true)),true));
			$this->redirect(array('action'=>'settings','#'=>"external_sms"));
		}
		\App::import('Vendor', 'settings');
		\App::import('Vendor','SendSMS', array('file' => 'SendSMS/autoload.php'));
		$external = empty($this->data['external']) ? 0 : 1;
		\settings::setValue(0, "sms_external", $external,false);
		$apis = array_keys(\SendSMS\APIs::getExternalCountryAPIs(getCurrentSite("country_code")));
		$sms_settings = array_intersect_key($this->data, array_flip(array_merge(["api"],$apis)));
		$sms_settings["api"] = in_array($sms_settings["api"], $apis)? $sms_settings["api"]:"";
		if(!empty($sms_settings["api"]) && class_exists("\\SendSMS\\".$sms_settings["api"])) {
			$class_name = "\\SendSMS\\".$sms_settings["api"];
			$class = new $class_name($sms_settings);
			if(!$class->verify_credentials()){
				$this->flashMessage(__(sprintf (__('Invalid %s', true), __('Credentials', true)),true));
				$this->redirect(array('action'=>'settings','#'=>"external_sms"));
			}
		} else {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('External API data', true)),true));
			$this->redirect(array('action'=>'settings','#'=>"external_sms"));
		}
		\settings::setValue(0, "sms_settings", json_encode($sms_settings),false);
		$this->flashMessage(__('Data Saved', true), 'Sucmessage');
		$this->redirect(array('action'=>'settings','#'=>"external_sms"));
	}
}
