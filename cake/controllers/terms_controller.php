<?php

use Izam\Attachment\Service\AttachmentsService;

class TermsController extends AppController {

    var $name = 'Terms';

    /**
     * @var Term
     */
    var $Term;
    var $helpers = array('Html', 'Form');
    var $titleAlias = 'documents';

    function owner_index() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
        $this->Term->recursive = 0;
        $conditions = $this->_filter_params();
        $conditions['Term.site_id'] = getAuthOwner('id');
        $this->set('terms', $this->paginate('Term', $conditions));
        $this->set('title_for_layout',  __('Terms and Conditions', true));

        $this->crumbs = array();

        $this->crumbs[1]['title'] = __('Documents', true);
        $this->crumbs[1]['url'] = array('action' => 'index', 'controller' => 'items');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';


        $this->set('content', $this->get_snippet('terms'));
    }

    function owner_load($id = null) {
        $this->autoRender = false;
        $site_id = getAuthOwner('id');
        $term = $this->Term->find(array('Term.id' => $id, 'Term.site_id' => $site_id));

        if (!$term) {
            die(json_encode(array('error' => true, 'text' => '')));
        }

        die(json_encode(array('error' => false, 'text' => $term['Term']['content'])));
    }

    function owner_add() {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }        
        $site_id = getAuthOwner('id');
        $this->loadModel('Site');
        $check = $this->Site->check_add_terms_limit();
        if (!$check['status']) {
            $this->_flashLimitMessage($check['message'], h(__('terms & conditions', true)));
            $this->redirect(array('action' => 'index'));
        }
        if (!empty($this->data)) {
            $this->Term->create();
            $this->data['Term']['site_id'] = $site_id;
            if ($this->Term->save($this->data)) {
                
                $attachments = $this->data['Term']['attachments'];
                if(!empty($attachments)){
                    $this->attachS3imageToTerms($attachments,$this->Term->id);
                }

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Terms', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Terms', true)));
            }
        }
        $this->helpers[] = 'Fck';


        $this->crumbs = array();

        $this->crumbs[1]['title'] = __('Documents', true);
        $this->crumbs[1]['url'] = array('action' => 'index', 'controller' => 'items');

        $this->crumbs[2]['title'] = __("Terms and Conditions", true);
        $this->crumbs[2]['url'] = array('action' => 'index');

        $this->crumbs[3]['title'] = $this->pageTitle;
        $this->crumbs[3]['url'] = '#';

        $this->set('file_settings', $this->Term->getFileSettings());
    }
    
    function owner_view($id = null)
    {
        $this->redirect(array('action' => 'edit', $id));
    }

    function owner_edit($id = null) {
        $this->Term->bindAttachmentRelation('term');
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }           
        $site_id = getAuthOwner('id');
        $term = $this->Term->find(array('Term.id' => $id, 'Term.site_id' => $site_id));

        if (!$term) {
            $this->flashMessage(__('Terms not found', true));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }

        if (!empty($this->data)) {
            $this->loadModel('Site');
            $this->data['Term']['id'] = $id;
            $this->data['Term']['site_id'] = $site_id;
            if (empty($this->data['Term']['id'])) {
                $check = $this->Site->check_add_terms_limit();
                if (!$check['status']) {
                    $this->_flashLimitMessage($check['message'], h(__('terms & conditions', true)));
                    $this->redirect(array('action' => 'index'));
                }
            }
            if ($this->Term->save($this->data)) {

                $attachments = $this->data['Term']['attachments'];
                $this->attachS3imageToTerms($attachments,$this->data['Term']['id']);
                // remove normal attachment .
                if(!empty($attachments)){
                    $this->Term->saveField('file', '');
                }

                $this->flashMessage(sprintf(__('%s has been saved', true), __('Terms', true)), 'Sucmessage');
                if (!empty($_GET['box'])) {
                    $this->render('blank');
                    return;
                } else {
                    $this->redirect(array('action' => 'index'));
                }
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Terms', true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $term;
        }
        $this->set('title_for_layout',  __('Edit terms', true));

        $this->crumbs = array();

        $this->crumbs[1]['title'] = __('Documents', true);
        $this->crumbs[1]['url'] = array('action' => 'index', 'controller' => 'items');

        $this->crumbs[2]['title'] = __("Terms and Conditions", true);
        $this->crumbs[2]['url'] = array('action' => 'index');

        $this->crumbs[3]['title'] = $this->pageTitle;
        $this->crumbs[3]['url'] = '#';


        $this->helpers[] = 'Fck';
        $this->set('file_settings', $this->Term->getFileSettings());
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Edit_General_Settings)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }  
        
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('terms', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('terms', true);

        $site_id = getAuthOwner('id');
        $terms = $this->Term->find('all', array('conditions' => array('Term.id' => $id, 'Term.site_id' => $site_id)));
        if (empty($terms)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->Term->deleteAll(array('Term.id' => $_POST['ids'], 'Term.site_id' => $site_id))) {
                $this->flashMessage(sprintf(__('%s have been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  __('Delete terms', true));

        $this->crumbs = array();

        $this->crumbs[1]['title'] = __('Documents', true);
        $this->crumbs[1]['url'] = array('action' => 'index', 'controller' => 'items');

        $this->crumbs[2]['title'] = __("Terms and Conditions", true);
        $this->crumbs[2]['url'] = array('action' => 'index');

        $this->crumbs[3]['title'] = $this->pageTitle;
        $this->crumbs[3]['url'] = '#';

        $this->set('terms', $terms);
    }

    private function attachS3imageToTerms($attachments, $entity_id)
    {
        $imagesIds = explode(',', $attachments);
        if (!empty($imagesIds)) {
            return  izam_resolve(AttachmentsService::class)->save('term', $entity_id, $imagesIds);
        }
    }
}