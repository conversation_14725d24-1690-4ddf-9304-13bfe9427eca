<?php

use App\Transformers\ServiceModelDataTransformer;
use App\Validators\SalesCommissions\hasSalesCommissionsWithNonOpenStatus;
use App\Services\CreditDistribution\CreditDistributionService;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\Invoice\Services\InvoicePaymentService;
use App\Helpers\CurrencyHelper;
use Izam\Attachment\Models\EntityAttachment;

App::import('Vendor', 'notification');
App::import('Vendor', 'PayTabsPayment', array('file' => 'payments/PayTabsPayment.php'));
App::import('Controller', 'Invoices');

class InvoicePaymentsController extends AppController {

    var $name = 'InvoicePayments';

    /**
     * @var InvoicePayment
     */
    var $InvoicePayment;
    var $helpers = array('html', 'form');
    var $components = array('Lmail', 'Email', 'SysEmails');

    /**
     *
     * @var LmailComponent
     */
    var $Lmail;

//----------------------
    function owner_index() {
        
        $this->set('is_ajax',$is_ajax=$this->RequestHandler->isAjax());
        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Invoices_View_Invoices_Details) && !check_permission(Invoices_View_All_Invoices) && !check_permission(Invoices_Add_Invoice_Payments) && !check_permission(Invoices_Add_Payments_to_All)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                if(isset($_GET['iframe'])) {
                    return $this->render('../elements/not_allowed_error_message');
                }
                $this->redirect('/');
            }
        }

        $this->InvoicePayment->recursive = 1;
        if ($is_ajax) {
            $this->InvoicePayment->applyBranch['onFind'] = false;
        }

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true);
        $paymentMethods['client_credit']=__('Client Credit',true);

        $this->InvoicePayment->filters = $this->InvoicePayment->getFilters(false);
        $this->InvoicePayment->filters['payment_method']['options'] = array('options' => $paymentMethods);

        $this->loadModel('Staff');
        $staffs = $this->Staff->getList();
        $this->set('staffs', $staffs);


        if (check_permission(Invoices_View_All_Invoices) && check_permission(Invoices_View_Invoices_Details) && ifPluginActive(StaffPlugin)) {

            $this->InvoicePayment->filters['invoiced_by'] = array('input_type' => 'advanced_staff', 'more-options' => true, 'options' => array('options' => $staffs, 'type' => 'select'),"label"=>__('Invoiced By', true), "empty"=>__('Invoiced By', true));
            $this->InvoicePayment->filters['staff_id'] = array('input_type' => 'multiselect', 'more-options' => true, 'options' => array('options' => $staffs, 'type' => 'select', ), 'label' => __('Collected By', true) ,'empty' => __('Collected By', true));
        }

        $conditions = $this->_filter_params(false, $this->InvoicePayment->filters);

        if (check_permission(Invoices_View_All_Invoices) && check_permission(Invoices_View_Invoices_Details) && ifPluginActive(StaffPlugin)) {
            if (isset($conditions['InvoicePayment.invoiced_by']) and !empty($conditions['InvoicePayment.invoiced_by']) || $conditions['InvoicePayment.invoiced_by'] == '0') {
                $conditions['Invoice.staff_id'] = $conditions['InvoicePayment.invoiced_by'];
                unset($conditions['InvoicePayment.invoiced_by']);
            }
        } elseif (check_permission(Invoices_View_Invoices_Details) && ifPluginActive(StaffPlugin)) {
            $conditions['InvoicePayment.staff_id'] = $site['staff_id'];
        }

        if (!empty($conditions['InvoicePayment.name LIKE'])) {
            $conditions[] = "CONCAT (InvoicePayment.first_name,InvoicePayment.last_name) LIKE '{$conditions['InvoicePayment.name LIKE']}'";
        }

        if (!empty($conditions['InvoicePayment.no'])) {
            $conditions[] = " Invoice.no = '{$conditions['InvoicePayment.no']}'";
        }
        if (!empty($conditions['InvoicePayment.status']) && $conditions['InvoicePayment.status']==4) {
            $conditions[] = "(Invoice.summary_total-Invoice.summary_paid-COALESCE(Invoice.summary_refund,0)) <-0.05";
            unset($conditions['InvoicePayment.status']);
        }


        unset($conditions['InvoicePayment.name LIKE']);
        unset($conditions['InvoicePayment.no']);



        if ($site['staff_id'] != 0) {

            if (!check_permission(Invoices_View_All_Invoices) && (check_permission(Invoices_View_Invoices_Details))) {
                $conditions['InvoicePayment.staff_id'] = $site['staff_id'];
            }
        }

        unset($conditions['InvoicePayment.client_id']);
        if(!empty($this->params['url']['client_id'])){
        $conditions['OR'] = array('Invoice.client_id' => $this->params['url']['client_id'], 'InvoicePayment.client_id' => $this->params['url']['client_id']);
        }
 
        if(isset($this->params['url']['payment_method']) && $this->params['url']['payment_method'] == "client_credit") {
            $conditions[] = "InvoicePayment.payment_method not in ('starting_balance')";
        }else{
            $conditions[] = "InvoicePayment.payment_method not in ('client_credit', 'starting_balance')";
        }
        
		if(IS_REST){
			if($this->params['module_name']=="client") {
                $rest_module_name = 'ClientPayment';
				$conditions[]='InvoicePayment.client_id IS NOT NULL';
				$conditions[]='InvoicePayment.client_id <> 0';
			} elseif($this->params['module_name']=="invoice") {
                $rest_module_name = 'InvoicePayment';
				$conditions[]='InvoicePayment.invoice_id IS NOT NULL';
				$conditions[]='InvoicePayment.invoice_id <> 0';
			}
		} else {
//			$conditions['InvoicePayment.payment_method <>']='starting_balance';
//            if (empty($this->params['url']['payment_method'])) {
//                $conditions['InvoicePayment.payment_method <>'] = 'client_credit';
//            }
		}
        $this->paginate['InvoicePayment']['order'] = 'InvoicePayment.id DESC';

        if (!empty($conditions)) {
            $this->set('search_filter', true);
        } else {
            $this->set('search_filter', false);
        }

        $this->InvoicePayment->bindAttachmentRelation('invoice_payment');
		$invoicePayments = $this->paginate('InvoicePayment', $conditions);

        $ids = [];

        foreach ($invoicePayments as $invoicePayment) {
            $ids[] = $invoicePayment['InvoicePayment']['id'];
        }

        $this->loadModel('ActionLine');
        $invoicePaymentActions = [];

        if ($ids) {
            $ids = implode(',', $ids);
            $actions = $this->ActionLine->find('all', ['applyBranchFind'=> false, 'conditions' => [ 'OR' => [
                ['ActionLine.action_key' => ACTION_ADD_INVOICE_PAYMENT, "ActionLine.param5 IN ($ids)"],
                ['ActionLine.action_key' => ACTION_ADD_CLIENT_PAYMENT, "ActionLine.param3  IN ($ids)"],
            ]], 'fields' => ['param3', 'param5', 'staff_id']]);

            foreach ($actions as $action) {
                if ($action['ActionLine']['param5']) {
                    $invoicePaymentActions[$action['ActionLine']['param5']] = $action['ActionLine']['staff_id'];
                } else {
                    $invoicePaymentActions[$action['ActionLine']['param3']] = $action['ActionLine']['staff_id'];
                }
            }
        }

        $this->set('invoicePaymentActions', $invoicePaymentActions);
        $this->set('invoicePayments', $invoicePayments);
        $this->setup_nav_data($invoicePayments);
        $getPaymentStatus=$this->InvoicePayment->getPaymentStatus();
        $getPaymentStatus[4]=__("Overpaid",true);
        $this->set('statuses', $getPaymentStatus);

        $conditions_key = 'invoicePayments_' . md5(serialize($conditions));
        $this->Session->write($conditions_key, $conditions);
        $this->set('conditions_key', $conditions_key);

        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, false, true);
        $this->set('payment_methods', $paymentMethods);

        $this->set('title_for_layout',  __('Payments', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'invoices';

        //get the list of printable templates for this products
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('invoice_payment');

        if(!empty($printableTemplates))
            $this->set(compact('printableTemplates'));
		if(IS_REST){
			foreach ($invoicePayments as $key => $value) {
				$invoicePayments[$key][$rest_module_name] = $invoicePayments[$key]["InvoicePayment"];
				if ($rest_module_name != "InvoicePayment")
				    unset($invoicePayments[$key]["InvoicePayment"]);
			}
			$this->set('rest_items', $invoicePayments);
			$this->set('rest_model_name', $rest_module_name);
			$this->render("index");
		}
    }

//    function _filter_params($params = false, $filters = false) {
//        $conditions = array();
//        if (!empty($this->params['url']['client_id'])) {
//            $conditions['Invoice.client_id'] = $this->params['url']['client_id'];
//        }
//        return array_merge($conditions, parent::_filter_params($params, $filters));
//    }

    function owner_print($id = null) {
        if (!$id) {
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('payment', true)), true));
            $this->redirect(array('action' => 'index'));
        }

        $this->loadModel('Client');
        $conditions = array();
        $conditions['InvoicePayment.id'] = $id;
        $invoicepayment = $this->InvoicePayment->find('first', array('conditions' => $conditions));

        $this->set('invoicepayment', $invoicepayment);

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(0, true, false);
        $this->set('paymentMethods', $paymentMethods);

        $this->add_actionline($this->params['url']['ext'] == 'pdf' ? ACTION_DOWNLOAD_RECEIPT : ACTION_PRINT_RECEIPT, array('primary_id' => $invoicepayment['Invoice']['id'], 'param3' => $invoicepayment['InvoicePayment']['id'], 'param2' => $invoicepayment['Invoice']['no']));
    }

//---------------------
    function owner_view($id = null) {
        $this->InvoicePayment->bindAttachmentRelation('invoice_payment');

        $site = getAuthOwner();
        if ($site['staff_id'] != 0) {
            if (!check_permission(Invoices_View_Invoices_Details) && !check_permission(Invoices_View_All_Invoices) && !check_permission(Invoices_Add_Invoice_Payments) && !check_permission(Invoices_Add_Payments_to_All)) {
				if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        }
		App::import('Vendor', 'notification_2');
        if (!empty($id))
            NotificationV2::view_notificationbyref($id, NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);
        if (!$id) {
			if(IS_REST) $this->cakeError('error400', array('message' => __(sprintf(__('Invalid %s', true), __('payment', true)), true)));
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('payment', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $conditions = array();
        $conditions['InvoicePayment.id'] = $id;
		if(IS_REST){
			if($this->params['module_name']=="client") {
				$conditions[]='InvoicePayment.client_id IS NOT NULL';
				$conditions[]='InvoicePayment.client_id <> 0';
			} elseif($this->params['module_name']=="invoice") {
				$conditions[]='InvoicePayment.invoice_id IS NOT NULL';
				$conditions[]='InvoicePayment.invoice_id <> 0';
			}
		}
        $invoicepayment = $this->InvoicePayment->find('first', array('conditions' => $conditions));
        $invoicepayment=modifyArray($invoicepayment);
        if (!$invoicepayment['InvoicePayment']['id']) {
			if(IS_REST) $this->cakeError('error404', array('message' => __(sprintf(__('Invalid %s', true), __('payment', true)), true)));
            $this->flashMessage(__(sprintf(__('Invalid %s', true), __('payment', true)), true));
            $this->redirect(array('action' => 'index'));
        }
        $this->set_journal($id);
        $this->set('invoicePayment', $invoicepayment);
        $this->loadModel ( 'Treasury' ) ;
        
        $this->set ( 'treasuries' , $this->Treasury->get_list () ) ;
        
//        $this->set('treasuries', $treasury_list);

        if ($invoicepayment['InvoicePayment']['invoice_id'] && $invoicepayment['InvoicePayment']['payment_method'] == 'client_credit'){
            $dist_result = CreditDistributionService::getClientPayemntID($invoicepayment['InvoicePayment']['id'], $invoicepayment['InvoicePayment']['invoice_id']);
            if ($dist_result['result']) {
                $this->set('client_payemnt_id', $dist_result['client_payment_id']);
            }
        }
        
        $this->set('statuses', $this->InvoicePayment->getPaymentStatus());
        $this->set('title_for_layout',  __('View Payment', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(0, true, false);
        $paymentMethods['client_credit']=__('Client Credit',true);
        $this->set('paymentMethods', $paymentMethods);

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'invoices';

        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('invoice_payment');

        $this->set('has_templates', false);
        if (!empty($printableTemplates)) {
            $this->set('has_templates', true);
        }

        $this->set('printableTemplates', $printableTemplates);

		if(IS_REST){
			$this->set('rest_item', $invoicepayment);
			$this->set('rest_model_name', "Payment");
			$this->render("view");
		}
        $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
        $viewTemplates = $repo->getEntityViewTemplates('invoice_payment');
        if (count($viewTemplates['local']) > 0 || count($viewTemplates['global']) > 0) {
            $this->set('has_templates', true);
        }
        $this->set('view_templates', $viewTemplates);
    }

//---------------------
    function owner_add() {
        if (!empty($this->data)) {
            $this->InvoicePayment->create();
            if (IS_REST && isset($this->data['InvoicePayment']) && is_array($this->data['InvoicePayment'])) {
                $owner = getAuthOwner();
                $this->loadModel('Invoice');
                $payments=$this->data['InvoicePayment'];
                if(!isset($payments[0]['invoice_id'])){
                    $payments = array(0=>$this->data['InvoicePayment']);
                }

                if (!empty($payments[0]['pos_shift_id'])) {
                    $is_valid = $this->InvoicePayment->checkOverPaidPosRefundPayment($payments);
                    if (!$is_valid) {
                        return $this->cakeError('error400', [
                            'message' => __("You can't refund more than the amount paid in the original invoice", true),
                            'validation_errors' => ["amount" => __("You can't refund more than the amount paid in the original invoice", true)]
                        ]);
                    }
                    $is_posShiftOpen = GetObjectOrLoadModel('PosShift')->isPosShiftOpen($payments[0]['pos_shift_id']);
                    if (!$is_posShiftOpen) {
                        return $this->cakeError('error400', [
                            'message' => __("Action denied. This session has been marked as closed", true),
                            'validation_errors' => ["amount" => __("Action denied. This session has been marked as closed", true)]
                        ]);
                    }
                }

                foreach ($payments as $paymentx) {
                    $payment['InvoicePayment'] = array('manual_payment' => 1, 'invoice_id' => $paymentx['invoice_id'], 'amount' => $paymentx['amount'], 'status' => 1, 'date' => $paymentx['date'] ?? date('Y-m-d'), 'payment_method' => $paymentx['payment_method'], 'transaction_id' => $paymentx['transaction_id'], 'added_by' => 1, 'staff_id' => $owner['staff_id']);
                    $payment['InvoicePayment']['pos_shift_id'] = isset($paymentx['pos_shift_id']) ? $paymentx['pos_shift_id'] : null;
                    if(isset($paymentx['treasury_id'])){
                        $payment['InvoicePayment']['treasury_id'] = $paymentx['treasury_id'];
                    }
                    $status = $this->Invoice->ownerAddPayment($payment);
                    if (isset($status['payment_id'])) {
                        $invoicePayment = $this->InvoicePayment->read(null, $status['payment_id']);
                        $this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
                    }
                }

                if (false === $status['status']) {
                    return $this->cakeError('error400', [
                        'message' => null,
                        'validation_errors' => $this->InvoicePayment->validationErrors
                    ]);
                }

                if(!isset($payments[0]['invoice_id'])){
                    $this->set('id', $this->data['InvoicePayment'][0]['invoice_id']);
                }else{
                    $this->set('id', $status['payment_id']);
                }

	            if(IS_REST){
		            $invoice_id = $invoicePayment['Invoice']['id'] ?: $invoicePayment['InvoicePayment']['invoice_id'];
		            if(strpos($_SERVER['HTTP_REFERER'],'pos')) {
			            $InvoicesController = new InvoicesController();
			            $this->set('invoice_pos_html', $InvoicesController->owner_preview($invoice_id, false, true));
		            }
		            $this->set('id', $this->InvoicePayment->id);
		            $this->set('invoice_number', $invoicePayment['Invoice']['no']);
		            $this->set('invoice_id', $invoicePayment['Invoice']['id']);
	            }

                $this->render('created');
                return;
            } else {
                if ($this->InvoicePayment->save($this->data)) {
                    $invoicePayment = $this->InvoicePayment->read(null, $this->InvoicePayment->id);
                    $this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
	                if (IS_REST) {
		                $this->set('id', $this->InvoicePayment->id);
		                $this->render('created');
		                return;
	                }
					$this->flashMessage(sprintf(__('%s has been saved', true), __('Payment', true)), 'Sucmessage');
                    $this->redirect(array('action' => 'index'));
                } else {
                    if (IS_REST) $this->cakeError("error400", ["message" => null, "validation_errors" => $this->InvoicePayment->validationErrors]);
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Payment', true)));
                }
            }
        }
        $invoices = $this->InvoicePayment->Invoice->find('list');
        $this->set(compact('invoices'));

        $this->set('staffs', $this->InvoicePayment->Staff->getList());
        $this->set('title_for_layout',  __('Add Payment', true));
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));
        $this->set('paymentMethods', $paymentMethods);
        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'invoices';
    }

//---------------------
    function owner_edit($id = null) {
        $this->InvoicePayment->bindAttachmentRelation('invoice_payment');
        $this->InvoicePayment->recursive = 1;
        $payment = $this->InvoicePayment->find(array('InvoicePayment.id' => $id));


        if (!$payment) {
			if(IS_REST) $this->cakeError('error404', array('message' => __('Payment not found', true)));
            $this->flashMessage(__('Payment not found', true));
            $this->redirect($this->referer(array('action' => 'index')));
        }

        if(!empty($payment["InvoicePayment"]["invoice_id"])) {
            (new hasSalesCommissionsWithNonOpenStatus($payment["InvoicePayment"]["invoice_id"], $this, "update_payment"))->validate();
        }

        $this->validate_open_day($payment['InvoicePayment']['date']);

        $owner = getAuthOwner();
        if (!check_permission(INVOICE_PAYMENTS_EDIT_REMOVE_ALL) && !(check_permission(INVOICE_PAYMENTS_EDIT_REMOVE_HIS_OWN) && $payment['InvoicePayment']['staff_id'] == $owner['staff_id'])) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('you are not allowed to edit this payment', true));
            $this->redirect($this->referer(array('action' => 'view', $id)));
        }

        if ($payment['InvoicePayment']["payment_method"] == "client_credit"
            && settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')
        ) {
            $this->flashMessage(sprintf(__("If you want to %s auto payment you must cancel the auto payment option from sales settings", true), __('edit', true)));
            $this->redirect($this->referer());
        }
        
		if(IS_REST) $this->data['InvoicePayment']['id'] = $id;
        if (!empty($this->data)) {
            if ($payment['Invoice']['type'] == Invoice::Refund_Receipt) {
                $this->data['InvoicePayment']['amount'] = abs($this->data['InvoicePayment']['amount']) * -1;
            }
            App::import('Vendor', 'AutoNumber');
            /** Validate Invoice Payment Distributions **/
            if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && check_permission(Invoices_Add_Payments_to_All) && !empty($this->data['paymentInvoices'])) {
                $validation_result = CreditDistributionService::validateDistributionForm($this->data['paymentInvoices'], $this->data['InvoicePayment']['amount']);
                if (!$validation_result['result']) {
                    $this->flashMessage($validation_result['message'], 'Errormessage', 'secondaryMessage');
                }
            } else {
                $validation_result['result'] = true;
            }

            if ($validation_result['result']) {
                $this->data['InvoicePayment']['id'] = $id;
                if ($this->data['InvoicePayment']['payment_method'] == "starting_balance") {
                    if ($this->data['InvoicePayment']['amount'] == 0) {
                        $this->loadModel('Client');
                        $this->flashMessage(sprintf(__('%s  has been saved', true), __('Payment', true)), 'Sucmessage');
                        $result = $this->Client->deleteOpenBalance($id);

                        $url = array('controller' => 'clients', 'action' => 'statement', $result['data']['client_id']);
                        $this->redirect($url);
                    }
                    $this->data['InvoicePayment']['amount'] = (float)$this->data['InvoicePayment']['amount'] * -1;
                }
                if($payment['InvoicePayment']['payment_method']!=$this->data['InvoicePayment']['payment_method']){

                    $this->data['InvoicePayment']['extra_details']="";
                }
                $attachments = explode(',',$this->data['InvoicePayment']['attachment']);
                unset($payment['InvoicePayment']['attachment']);
                unset($this->data['InvoicePayment']['attachment']);

                if(in_array($payment['Invoice']['type'], [Invoice::Invoice, Invoice::DEBIT_NOTE])){
                    \AutoNumber::set_validate(\AutoNumber::TYPE_INVOICE_PAYMENT);
                }elseif($payment['Invoice']['type'] == Invoice::Refund_Receipt){
                    \AutoNumber::set_validate(\AutoNumber::TYPE_REFUND_PAYMENT);
                }
                if ($this->InvoicePayment->editPayment($this->data)) {
                   
                    if(!empty($attachments))
                    {
                        izam_resolve(AttachmentsService::class)->save('invoice_payment', $id, $attachments); 
                    }
                  
                    $invoicePayment = $this->InvoicePayment->findById($id);
                    $this->InvoicePayment->update_journals($invoicePayment);
                    $paymentalies = $this->InvoicePayment->alias;

                    /** Check Client Credit Distributions **/
                    if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && check_permission(Invoices_Add_Payments_to_All) && $invoicePayment['InvoicePayment']['client_id']) {
                        $client_payment_id = $invoicePayment['InvoicePayment']['id'];
                        $dist_service = new CreditDistributionService();
                        if (!empty($this->data['paymentInvoices']) && $invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED && $payment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                            /** Update Invoice Payments & Distributions **/
                            $distribution_data = $dist_service->updateSubPayments($client_payment_id, $this->data['paymentInvoices']);
                            $dist_service->updateDistributions($client_payment_id, $distribution_data);
                        } elseif (!empty($this->data['paymentInvoices']) && $invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED && $payment['InvoicePayment']['status'] != PAYMENT_STATUS_COMPLETED) {
                            /** Insert New Invoice Payments & Distributions **/
                            $distribution_data = $dist_service->addSubPayments($this->data['paymentInvoices'], $invoicePayment);
                            if ($distribution_data)
                                $dist_service->addDistributions($invoicePayment['InvoicePayment']['id'], $distribution_data);
                        } else {
                            /** Delete Invoice Payments & Distributions **/
                            $dist_service->deleteSubPayments($client_payment_id);
                            $dist_service->deleteDistributions($client_payment_id);
                        }
                    }

                    if ($this->data['InvoicePayment']['status'] != PAYMENT_STATUS_PENDING) {
                        App::import('Vendor', 'notification_2');
                        NotificationV2::delete_notification(NotificationV2::NOTI_CLIENT_PENDING_PAYMENT, $id);
                    }

                    if ($invoicePayment['InvoicePayment']['payment_method'] == "starting_balance") {
                        $this->add_actionline(ACTION_EDIT_OPENING_BALANCE, array('primary_id' => $invoicePayment['InvoicePayment']['id'], 'secondary_id' => $invoicePayment['InvoicePayment']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'] * -1, 'param2' => $invoicePayment['InvoicePayment']['status'], 'param2' => $invoicePayment['Client']['business_name'], 'param4' => $invoicePayment['Client']['client_number']));
                    } else if (!empty($invoicePayment['InvoicePayment']['client_id'])) {
                        $this->add_actionline(ACTION_EDIT_CLIENT_CREDIT, array('primary_id' => $invoicePayment['InvoicePayment']['id'], 'secondary_id' => $invoicePayment['InvoicePayment']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param6' => $invoicePayment['InvoicePayment']['status'], 'param2' => $invoicePayment['Client']['business_name'], 'param4' => $invoicePayment['Client']['client_number'], 'param5' => $invoicePayment['InvoicePayment']['payment_method']));
                    } else {
                        $this->add_actionline(ACTION_UPDATE_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment[$paymentalies]['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment[$paymentalies]['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment[$paymentalies]['status'], 'param8' => $invoicePayment[$paymentalies]['payment_method'], 'param9' => $invoicePayment[$paymentalies]['transaction_id']));
                    }

                    if ($invoicePayment['InvoicePayment']['client_id']) {
                        $client = $invoicePayment['InvoicePayment']['client_id'];
                        $currency_code = $invoicePayment['InvoicePayment']['currency_code'];
                    } else {
                        $client = $invoicePayment['Invoice']['client_id'];
                        $currency_code = $invoicePayment['InvoicePayment']['currency_code'];
                    }
                    $this->InvoicePayment->Client->adjust_and_pay($client);
                    $this->InvoicePayment->Client->pay_invoices_from_credit($client);
                    $updateParent = false;
                    if ($payment['InvoicePayment']['status'] != PAYMENT_STATUS_COMPLETED && $this->data['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                        $payment = $this->InvoicePayment->findById($id);
                        $client = $this->InvoicePayment->Invoice->Client->findById($payment['Invoice']['client_id']);

                        $this->loadModel('Invoice');
                        $invoice = $this->Invoice->getInvoice($payment['Invoice']['id']);
                        $enable_requisitions = settings::getValue(InventoryPlugin,"enable_requisitions") ;

                        if ($enable_requisitions) {
                            $this->loadModel('Requisition');
                            $requisition_type = (
                            $invoice['Invoice']['type'] == Invoice::Refund_Receipt?Requisition::ORDER_TYPE_INVOICE_REFUND : (
                            $invoice['Invoice']['type'] ==Invoice::Credit_Note?Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE :
                                Requisition::ORDER_TYPE_INVOICE
                            )
                            );
                            $this->Requisition->updateForOrder($invoice, $requisition_type);
                        }

                        $this->SysEmails->paymentCompleteClient($payment['Payment'], $payment['Invoice'], $client['Client'], $owner);
                        $updateParent = true;
                    }

                    if (IS_REST) {
                        $this->render('success');
                        return;
                    }

                    $this->flashMessage(sprintf(__('%s  has been saved', true), __('Payment', true)), 'Sucmessage');
                    $url = array('action' => 'view',$id);
                    if (!empty($this->params['url']['from_matching'])) {
                        $url = array('action' => 'edit', $id, '?' => 'box=1');
                        $this->redirect($url);
                    }
                    if (!empty($this->params['url']['box'])) {
                        $url = array('action' => 'edit', $id, '?' => 'box=1&update_parent=1');
                        $this->redirect($url);
                    }
                    if (!empty($this->params['url']['back']) and $this->params['url']['back'] == "invoice") {
                        $url = array('controller' => 'invoices', 'action' => 'view', $payment['Invoice']['id'], '#' => 'PaymentsBlock');
                    }
                    if ($invoicePayment['InvoicePayment']['payment_method'] == "starting_balance") {
                        $url = array('controller' => 'clients', 'action' => 'statement', $invoicePayment['InvoicePayment']['client_id']);
                    }
                    $this->redirect($url);
                } else {
                    if (IS_REST) $this->cakeError("error400", ["message" => null, "validation_errors" => $this->Supplier->InvoicePayment]);
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Payment', true)));
                }
            }
        } else {
            $this->data = $payment;
            if($this->data['InvoicePayment']['payment_method']=="starting_balance"){
            $this->data['InvoicePayment']['amount']=$this->data['InvoicePayment']['amount']*-1;
            }
            $this->data['InvoicePayment']['amount'] = round($this->data['InvoicePayment']['amount'], CurrencyHelper::getFraction($payment['Invoice']['currency_code']));
            $this->data['InvoicePayment']['date'] = format_date($this->data['InvoicePayment']['date']);
        }

        if (!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices') && check_permission(Invoices_Add_Payments_to_All)) {
            if (empty($this->data['paymentInvoices'])) {
                $this->loadModel('ClientCreditDistribution');
                $distributions = $this->ClientCreditDistribution->find('all', array('conditions' => array('ClientCreditDistribution.client_credit_invoice_payment_id' => $id)));
                foreach ($distributions as $old_distribution) {
                    $data['invoice_id'] = $old_distribution['ClientCreditDistribution']['invoice_id'];
                    $data['distribution_amount'] = $old_distribution['ClientCreditDistribution']['distribution_amount'];
                    $this->data['paymentInvoices'][] = $data;
                }
            }

            $invoicesOptions = CreditDistributionService::getInvoicesOptions($payment['InvoicePayment']['client_id'], $payment['InvoicePayment']['currency_code'], $id);
            $this->set('invoicesOptions', $invoicesOptions);
            if($invoicesOptions)
                $this->set('hasInvoices', true);
        }

        $this->loadModel('SitePaymentGateway');
        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'stripe')));
        $this->set('Stripe', $Stripe);
        if(!empty($Stripe['SitePaymentGateway']['username']) && $payment['InvoicePayment']['payment_method'] =! "starting_balance") {
            $this->set('customerSessionClientSecret', $this->InvoicePayment->create_stripe_customer_session($Stripe['SitePaymentGateway']['username'], ($payment['Invoice']['client_id'] ?? $payment['Client']['id'])));
        }
        $Square = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'square')));
        $this->set('Square', $Square);
        $this->set('statuses', InvoicePayment::getPaymentStatus());

        $this->set('payment_treasury',$payment_treasury=$this->SitePaymentGateway->PaymentTreasury());
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'));

        $this->loadModel('Invoice');

            if(!settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')){
                if(!empty($payment['InvoicePayment']['client_id'])) {
                    $client_id = $payment['InvoicePayment']['client_id'];
                    $this->data['InvoicePayment']['client_id'] = $payment['InvoicePayment']['client_id'];
                }else{
                    $client_id = $payment['Invoice']['client_id'];
                }
            $client_credit = $this->Invoice->Client->client_credit($payment['Invoice']['client_id'], $payment['InvoicePayment']['currency_code'])+$payment['InvoicePayment']['amount'];

            if ($client_credit <= 0) {
                unset($paymentMethods['client_credit']);
            } else {


                $this->set('client_credit', $client_credit);
                unset($paymentMethods['client_credit']);
                $paymentMethods['client_credit'] = sprintf(__('Client Credit (%s)', true), $client_credit);
            }
         }
         $this->set('paymentMethods', $paymentMethods);
        $this->set('file_settings', $this->InvoicePayment->getFileSettings());
        $this->set('staffs', $this->InvoicePayment->Staff->getList());
        $this->set(array('payment' => $payment, 'invoice' => $payment));

        if (ifPluginActive(ExpensesPlugin))
        {
            $this->loadModel ( 'Treasury');
            $this->loadModel ( 'ItemPermission');
            $this->set ( 'treasuries' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT) + $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_WITHDRAW) ) ;
            $this->set ( 'primary_treasury_id' , $this->Treasury->get_primary () ) ;
        }
        $this->set('title_for_layout',  __('Edit Payment', true));
        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->titleAlias = 'invoices';
        if($payment['InvoicePayment']['payment_method']=="starting_balance"){
            $this->loadModel('Currency');
            $this->set('currencies',$this->Currency->getCurrencyList());
        $this->render('edit_starting_balance');
        }
    }
function owner_add_open_balance($client_id){
    $error=false;
    $this->pageTitle=__('Add Opening Balance',true);
    $payment=$this->Client->find(array('Client.id'=>$client_id));
    if(!empty($this->data)){

        if(empty($this->data['InvoicePayment']['date'])) {
            $this->InvoicePayment->validationErrors['date_date'] = __('Required',true);
            $this->InvoicePayment->validationErrors['date_time'] = __('Required',true);
            $error=true;
        }
        if($this->data['InvoicePayment']['amount'] != (float)$this->data['InvoicePayment']['amount']) {
            $this->InvoicePayment->validationErrors['amount'] = __(sprintf('%s must be a number', __('amount', true)),true);
            $error=true;
        }
        if(empty($this->data['InvoicePayment']['amount'])) {
            $this->InvoicePayment->validationErrors['amount'] = __('Required',true);
            $error=true;
        }
        if(empty($this->data['InvoicePayment']['currency_code'])) {
            $this->InvoicePayment->validationErrors['currency_code'] = __('Required',true);
            $error=true;
        }
        $this->validateOpenDayWithValidationError($this->data, 'InvoicePayment' ,'date');
        if($error==false && empty($this->InvoicePayment->validationErrors)) {

            $payment = $this->data['InvoicePayment'];

            // function addOpenBalance($client_id,$balance,$staff_id,$currency_code,$date = null)
            $result = $this->Client->addOpenBalance($client_id,$payment['amount'] * -1,getAuthOwner('staff_id'),$payment['currency_code'],$payment['date']);
            $invoicePayment = $this->InvoicePayment->read(null,$result['date']['InvoicePayment']['id']);
            $this->add_actionline(ACTION_ADD_CLIENT_OPENING_BALANCE,array('primary_id' => $invoicePayment['InvoicePayment']['id'],'secondary_id' => $invoicePayment['InvoicePayment']['client_id'],'param1' => $invoicePayment['InvoicePayment']['amount'] * -1,'param2' => $invoicePayment['InvoicePayment']['status'],'param2' => $invoicePayment['Client']['business_name'],'param4' => $invoicePayment['Client']['client_number']));

            $url = array('controller' => 'clients','action' => 'statement',$client_id);
            $this->redirect($url);
        }
    }else{
        $this->data['InvoicePayment']['currency_code']=$payment['Client']['default_currency_code'];
    }
    $this->loadModel('Currency');
    $this->set('currencies',$this->Currency->getCurrencyList());
    $this->set('payment',$payment);
    $this->render('edit_starting_balance');
}
    function owner_approve($id = false) {
        if ($this->RequestHandler->isAjax()) {
            $this->_owner_approve_ajax($id);
        } else {
            $payment = $this->InvoicePayment->find(array('InvoicePayment.id' => $id));
            if (!$payment) {
                $this->flashMessage(__('Payment not found', true));
                $this->redirect($this->referer(array('action' => 'index')));
            }

            if ($payment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {
                $this->flashMessage(__('Payment is already completed', true), 'Notemessage');
                $this->redirect($this->referer(array('action' => 'index')));
            }
            $this->validate_open_day($payment['PurchaseOrderPayment']['date']);
            $payment['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;
           
               if(($payment['InvoicePayment']['amount']+$payment['Invoice']['summary_paid']) ==$payment['Invoice']['summary_total']*2 and $payment['Invoice']['summary_paid']!=0){
                $this->flashMessage(sprintf(__("Failed to approve the payment, the invoice #%s with total %s is already paid, and you are trying to duplicate the payment.", true),$payment['Invoice']['no'],format_price($payment['Invoice']['summary_total'],$payment['Invoice']['currency_code'])), 'Errormessage', 'secondaryMessage');
                $this->redirect(array('action' => 'view',$id));                      
            }
             
            if ($this->InvoicePayment->save($payment, array('fieldList' => array('id', 'status'), 'validate' => false, 'callbacks' => false))) {
                $this->InvoicePayment->update_journals($payment);
                $result = $this->InvoicePayment->Invoice->updateInvoicePayments($payment['Invoice']['id']);

                if (ifPluginActive(\Izam\Daftra\Common\Utils\PluginUtil::InventoryPlugin)) {
                    $this->loadModel('Invoice');
                    $invoice = $this->Invoice->getInvoice($payment['Invoice']['id']);
                    StockTransaction::updateForInvoice($invoice);
                }


                $invoicePayment = $this->InvoicePayment->read(null, $id);
                App::import('Vendor', 'notification_2');
                NotificationV2::delete_notificationbyref($id, NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);

                    if ($invoicePayment['Payment']['client_id']) {
                    $this->Client->pay_invoices_from_credit($invoicePayment['Payment']['client_id']);
                    $this->add_actionline(ACTION_CLIENT_ADD_CLIENT_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Payment']['client_id'], 'param1' => $invoicePayment['Payment']['amount'], 'param2' => $invoicePayment['Payment']['status'], 'param3' => $invoicePayment['Payment']['id'], 'param4' => $invoicePayment['Payment']['payment_method'], 'param5' => $invoicePayment['Payment']['transaction_id']));
                    if ($mail == 'complete') {
                        $this->SysEmails->creditpaymentCompleteClient($payment['InvoicePayment'], $payment['Client'], $site['Site']);
                        $this->SysEmails->creditpaymentCompleteOwner($payment['InvoicePayment'], $payment['Client'], $site['Site']);
                    }
                    $this->redirect(array('action' => 'view', $payment['InvoicePayment']['id']));
                }else{
                    $this->add_actionline(ACTION_UPDATE_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['Payment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['Payment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['Payment']['status'], 'param8' => $invoicePayment['Payment']['payment_method'], 'param9' => $invoicePayment['Payment']['transaction_id']));
                $owner = getAuthOwner();
                $client = $this->InvoicePayment->Invoice->Client->findById($payment['Invoice']['client_id']);
                
                $this->SysEmails->paymentCompleteClient($payment['InvoicePayment'], $result['invoice'], $client['Client'], $owner);                    
                }
                
                if ($invoicePayment['InvoicePayment']['client_id']) {
                    $client=$invoicePayment['InvoicePayment']['client_id'];
                    }else{
                    $client=$invoicePayment['Invoice']['client_id'];
                    }     
                    $currency_code=$invoicePayment['InvoicePayment']['currency_code'];    
                       $this->InvoicePayment->Client->adjust_balance($client,$currency_code);
                       
                       $this->InvoicePayment->Client->pay_invoices_from_credit($client);
                

                

                $this->flashMessage(__('Payment has been approved', true), 'Sucmessage');
                $this->redirect($this->referer(array('action' => 'index')));
            }
        }
    }

    function _owner_approve_ajax($id = null) {

        $payment = $this->InvoicePayment->find(array('InvoicePayment.id' => $id));
        
        $return = array('status' => false);
        if (!$payment) {
            $return['message'] = __('Payment not found', true);
            $return['messageClass'] = 'Errormessage';
            die(json_encode($return));
        }

        if ($payment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {
            $return['message'] = __('Payment is already completed', true);
            $return['messageClass'] = 'Notemessage';
            die(json_encode($return));
        }
        $payment['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;
               if(($payment['InvoicePayment']['amount']+$payment['Invoice']['summary_paid']) ==$payment['Invoice']['summary_total']*2 and $payment['Invoice']['summary_paid']!=0){
            $return['message'] =sprintf(__("Failed to add the payment, the invoice #%s with total %s is already paid, and you are trying to duplicate the payment.", true),$payment['Invoice']['no'],format_price($payment['Invoice']['summary_total'],$payment['Invoice']['currency_code']));
            $return['messageClass'] = 'Errormessage';                   
            die(json_encode($return));
               }

        $isOpened = $this->validateOpenDayWithValidationError($payment, 'PurchaseOrderPayment', 'date');
        if($isOpened !== true)
        {
            $return['message'] = $isOpened;
            $return['messageClass'] = 'Errormessage';
            die(json_encode($return));
        }
               
        if ($this->InvoicePayment->save($payment, array('fieldList' => array('id', 'status'), 'validate' => false, 'callbacks' => false))) {
            $this->InvoicePayment->update_journals($payment);
            $result = $this->InvoicePayment->Invoice->updateInvoicePayments($payment['Invoice']['id']);
            $invoicePayment = $this->InvoicePayment->read(null, $id);
            
            App::import('Vendor', 'notification_2');
            NotificationV2::delete_notificationbyref($id, NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);

            $this->add_actionline(ACTION_UPDATE_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['Payment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['Payment']['id'], 'param6' => $invoicePayment['Payment']['summary_total'], 'param7' => $invoicePayment['Payment']['status'], 'param8' => $invoicePayment['Payment']['payment_method'], 'param9' => $invoicePayment['Payment']['transaction_id']));
            $owner = getAuthOwner();
            $client = $this->InvoicePayment->Invoice->Client->findById($payment['Invoice']['client_id']);
            $this->SysEmails->paymentCompleteClient($payment['InvoicePayment'], $result['invoice'], $client['Client'], $owner);

            $return['status'] = true;
            $return['message'] = __('Payment has been approved', true);
            $return['messageClass'] = 'Sucmessage';
            die(json_encode($return));
        } else {
            $return['message'] = __('Could not approve payment', true);
            $return['messageClass'] = 'Errormessage';
            die(json_encode($return));
        }
    }

//---------------------
    function owner_delete($id = null, $invoice_id = false) {
        ini_set('memory_limit', '4G');
        set_time_limit(3600);
        $this->loadModel('Invoice');
        $site = getAuthOwner();
        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
			if(IS_REST){
				$this->cakeError("error400", ["message"=>sprintf(__('Invalid %s', true), __('payment', true))]);
			}else{
				$this->flashMessage(sprintf(__('Invalid %s', true), __('payment', true)));
				$this->redirect(array('action' => 'index'));
			}
        }
        if (!empty($invoice_id)) {
            (new hasSalesCommissionsWithNonOpenStatus($invoice_id, $this, "update_payment"))->validate();
        }
        $module_name = __('payment', true);
        $verb = __('has', true);
        $verb_view=__t('this');
        if (is_array($id) && count($id) > 1) {
            $module_name = __('payments', true);
            $verb = __('have', true);
            $verb_view=__t('these');
        }
        $this->set('verb_view', $verb_view);

		$conditions = array('InvoicePayment.id' => $id);
		if(IS_REST){
			if($this->params['module_name']=="client") {
				$conditions[]='InvoicePayment.client_id IS NOT NULL';
				$conditions[]='InvoicePayment.client_id <> 0';
			} elseif($this->params['module_name']=="invoice") {
				$conditions[]='InvoicePayment.invoice_id IS NOT NULL';
				$conditions[]='InvoicePayment.invoice_id <> 0';
			}
		}
        $invoicePaymentsCount = $this->InvoicePayment->find('count', array('conditions' => $conditions));
        if($invoicePaymentsCount > 500){
            $this->flashMessage(sprintf(__("Cannot delete more than 1000 invoice payment record at a time", true)));
            $this->redirect($this->referer());
        }
        $invoicePayments = $this->InvoicePayment->find('all', array('conditions' => $conditions));
        $invoicePaymentEntities = [];
        foreach($invoicePayments as $k => $invoice_payment){
            if ($invoice_payment['InvoicePayment']["payment_method"] == "client_credit"
                && settings::getValue(InvoicesPlugin, 'automatic_pay_invoices')
            ) {
                $this->flashMessage(sprintf(__("If you want to %s auto payment you must cancel the auto payment option from sales settings", true), __('delete', true)));
                $this->redirect($this->referer());
            }
            
            if (!(check_permission(INVOICE_PAYMENTS_EDIT_REMOVE_ALL) ||
                (!check_permission(INVOICE_PAYMENTS_EDIT_REMOVE_ALL) && check_permission(INVOICE_PAYMENTS_EDIT_REMOVE_HIS_OWN) && $invoice_payment['InvoicePayment']['staff_id'] == $site['staff_id'] ))) {
                if(IS_REST) $this->cakeError('error403');
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
            $invoicePaymentEntities[$invoice_payment['InvoicePayment']['id']] = getRecordWithEntityStructure('invoice_payment',$invoice_payment['InvoicePayment']['id'],2)->toArray();
			$this->validate_open_day($invoice_payment['InvoicePayment']['date'], ['action' => 'index']);
		}

        if (empty($invoicePayments)) {
			if(IS_REST){
				$this->cakeError("error404", ["message"=>sprintf(__('%s not found', true), ucfirst($module_name))]);
			}else{
				$this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
				$this->redirect($this->referer(array('action' => 'index'), true));
			}
        }

		if(IS_REST){
			$_POST['submit_btn'] = true;
			$_POST['ids'] = [$id];
		}
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            foreach ($invoicePayments as $invoicePayment) {
                if (!empty($invoicePayment['InvoicePayment']['invoice_id'])) {

                    $installment = $this->Invoice->getInstallmentAgreement($invoicePayment['InvoicePayment']['invoice_id']);
                    if (ifPluginInstalled(INSTALLMENT_AGREEMENT_PLUGIN) && $installment) {
                        if ($invoicePayment['InvoicePayment']['created'] <= $installment['created']) {
                            $this->flashMessage(__('You cannot delete or update the Invoice payments that created before the Installment agreement and If you need to update or delete the payment you should delete the Installment agreement', true), 'Errormessage', 'secondaryMessage');
                            $this->redirect(array('controller' => 'invoices', 'action' => 'view', $invoicePayment['InvoicePayment']['invoice_id']));
                        }
                    }
                }
            }

            if ($_POST['submit_btn'] == 'yes' && $this->InvoicePayment->deleteAll(array('InvoicePayment.id' => $id))) {
               
                foreach ($invoicePayments as $invoicePayment) {
                izam_resolve(InvoicePaymentService::class)->delete($invoicePaymentEntities[$invoicePayment['InvoicePayment']['id']]);
                   $this->after_delete_payment($invoicePayment,$invoicePayment['InvoicePayment']['client_id']?ACTION_DELETE_CLIENT_PAYMENT:ACTION_DELETE_INVOICE_PAYMENT);

                     
                    if ($invoicePayment['InvoicePayment']['client_id']) {
                    $client=    $invoicePayment['InvoicePayment']['client_id'];
                    $currency_code=$invoicePayment['InvoicePayment']['currency_code'];
                    }else{
                    $client=$invoicePayment['Invoice']['client_id'];
                    $currency_code=$invoicePayment['InvoicePayment']['currency_code'];    
                    }  

                       $this->InvoicePayment->Client->adjust_balance($client,$currency_code);
                
                       $this->InvoicePayment->Client->pay_invoices_from_credit($client);
                       
                    EntityAttachment::where(['entity_key' => 'invoice_payment', 'entity_id' => $invoicePayment['InvoicePayment']['id']])->delete();

//                    if ($invoicePayment['InvoicePayment']['client_id']) {
//                      
//                        do{
//                       
//                            $client_credit = $this->InvoicePayment->Client->client_credit($invoicePayment['InvoicePayment']['client_id'], $invoicePayment['InvoicePayment']['currency_code']);
//                           
//                            if ($client_credit < 0) {
//                                $this->InvoicePayment->alias='InvoicePayment';
//                              $last_row=$this->InvoicePayment->find('all',array('order'=>'InvoicePayment.id desc','conditions'=>array('Invoice.client_id'=>$invoicePayment['InvoicePayment']['client_id'],'InvoicePayment.payment_method'=>'client_credit','InvoicePayment.currency_code'=>$invoicePayment['InvoicePayment']['currency_code'])));
//                              
//                              $this->InvoicePayment->delete($last_row[0]['InvoicePayment']['id']);
//                              $this->after_delete_payment($last_row[0],ACTION_DELETE_INVOICE_PAYMENT);
//                            $client_credit = $this->InvoicePayment->Client->client_credit($invoicePayment['InvoicePayment']['client_id'], $invoicePayment['InvoicePayment']['currency_code']);
//                            if($client_credit>0){
//                                
//                            }
//                     
//                            }
//                            
//                        } while ($client_credit < 0);
//                        
//                        //$client_credit = $this->InvoicePayment->Client->client_credit($invoicePayment['InvoicePayment']['client_id'], $invoicePayment['InvoicePayment']['currency_code']);
//                        
//                    }
                       
               
                }
               
                //ACTION_DELETE_INVOICE_PAYMENT (primary_id => invoice_id, sec => client_id, p1=>total, p2=> invoice_status,p3=>invoice_summary_paid, p4=>is_internal (true if it is addd by staff/admin, if by client it will be false ), p5=>payment_id, p6=>payment_amount,                                                                                             p7=>payment_status, p8=>payment_method )), p4=> total_paid, p5=>payment_amount, p6=>payment_status )
                //NotificationV::delete_notification(NOTI_CLIENT_PENDING_PAYMENT, $id);

				if(IS_REST){
					$this->set("message", sprintf(__('%s %s been deleted', true), ucwords($module_name), $verb));
					$this->render("success");
					return;
				}
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');

                if(count($invoicePayments)==1){
                $payment=$invoicePayments[0]['InvoicePayment'];    
                $invoice=$invoicePayments[0]['Invoice'];
                
                if(!empty($payment['invoice_id'])){
                    if($invoice['type']==Invoice::Refund_Receipt ){
                        $this->redirect(array('controller'=>'invoices','action' => 'view_refund',$payment['invoice_id']));    
                    }else{
                        $this->redirect(array('controller'=>'invoices','action' => 'view',$payment['invoice_id']));        
                    }
                }
                if(!empty($payment['client_id'])){
                    $this->redirect(array('controller'=>'clients','action' => 'view',$payment['client_id']));    
                }
                
                }
                
                       $this->redirect(array('action' => 'index'));
                
                
            } else {
				if(IS_REST) $this->cakeError("error500", ["message"=>__("Item was not deleted. If you see this again, please contact customer support.", true)]);
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('invoice_id', $invoice_id);

        $this->set('title_for_layout',  __('Delete Payment', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->set('invoicePayments', $invoicePayments);
        $this->set('module_name', $module_name);

        $this->titleAlias = 'invoices';
    }

    function after_delete_payment($invoicePayment, $action)
    {
        $this->loadModel('Client');
        $this->loadModel('Invoice');
        if (!$invoicePayment['InvoicePayment']['client_id']) {
            $this->InvoicePayment->Invoice->updateInvoicePayments($invoicePayment['InvoicePayment']['invoice_id']);
            $invoice = $this->Invoice->read(null, $invoicePayment['InvoicePayment']['invoice_id']);
            $invoicePayment['Invoice'] = $invoice['Invoice'];
            if(isset($invoicePayment['Invoice']) && $invoicePayment['Invoice']['type']==Invoice::Refund_Receipt){
                $this->Invoice->updateInvoicePayments($invoicePayment['Invoice']['subscription_id']);
                $this->Client->adjust_balance($invoice['Invoice']['client_id'], $invoice['Invoice']['currency_code']);
                $this->Client->pay_invoices_from_credit($invoice['Invoice']['client_id']);
            }

            App::import('Vendor', 'notification_2');
            NotificationV2::delete_notificationbyref($invoicePayment['Invoice']['id'], NotificationV2::NOTI_CLIENT_PENDING_PAYMENT);
        }


        if ($action == ACTION_DELETE_INVOICE_PAYMENT) {
            $primary_id = $invoicePayment['Invoice']['id'];
            $secondary_id = $invoicePayment['Invoice']['client_id'];
            $dist_service = new CreditDistributionService();
            $dist_service->deleteInvoicePaymentDistributionRecord($invoicePayment['InvoicePayment']['id']);
            if ($invoicePayment['InvoicePayment']['payment_method'] == "tamara") {
                $instoreService = new \App\Services\PaymentGateways\Tamara\TamaraInStoreService();
                $instoreService->voidTransaction($invoicePayment['InvoicePayment']);
            }
        } else if ($action == ACTION_DELETE_CLIENT_PAYMENT) {
            $primary_id = $invoicePayment['InvoicePayment']['id'];
            $secondary_id = $invoicePayment['InvoicePayment']['client_id'];
            $dist_service = new CreditDistributionService();
            $dist_service->deleteSubPayments($invoicePayment['InvoicePayment']['id']);
            $dist_service->deleteDistributions($invoicePayment['InvoicePayment']['id']);
        } else {
            $primary_id = $invoicePayment['InvoicePayment']['id'];
            $secondary_id = $invoicePayment['InvoicePayment']['client_id'];
        }

        $this->add_actionline($action, array('primary_id' => $primary_id, 'secondary_id' => $secondary_id, 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));
        $this->InvoicePayment->delete_auto_journals($invoicePayment['InvoicePayment']['id']);
    }

//----------------------------
    function client_index() {
        $client_id = getAuthClient('id');


        $this->InvoicePayment->recursive = 0;

        $this->InvoicePayment->filters = $this->InvoicePayment->getFilters(true);


        $conditions = $this->_filter_params(false, $this->InvoicePayment->filters);
        if (!empty($conditions['InvoicePayment.name'])) {
            $conditions[] = "CONCAT (InvoicePayment.first_name,InvoicePayment.last_name) LIKE '{$conditions['InvoicePayment.name']}'";
            unset($conditions['InvoicePayment.name']);
        }
        //$conditions['Invoice.draft <>'] = 1;
        $conditions['InvoicePayment.amount >='] = 0;
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, false);
        $this->set('payment_methods', $paymentMethods);

        
        //$conditions['Invoice.client_id'] = getAuthClient('id');
        $conditions[]='InvoicePayment.payment_method <> \'client_credit\'';
        $conditions['InvoicePayment.payment_method <>']='starting_balance';
       $conditions2=array($conditions);
       $conditions2['OR'] = array(array('Invoice.draft <> 1','Invoice.client_id' => getAuthClient('id')), 'InvoicePayment.client_id' => getAuthClient('id'));
        
        
        $this->paginate['InvoicePayment']['order'] = 'InvoicePayment.id DESC';
        $this->set('invoicePayments', $this->paginate('InvoicePayment', $conditions2));
        $this->set('statuses', $this->InvoicePayment->getPaymentStatus());

        $this->set('title_for_layout',  __('Payments', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->titleAlias = 'payments';
    }

//---------------------
    function client_view($id = null) {
        $client_id = getAuthClient('id');
        $payment = $this->InvoicePayment->find('first', array('conditions' => array('InvoicePayment.id' => $id), 'recursive' => 1));

        if ($payment['InvoicePayment']['invoice_id'] && $payment['InvoicePayment']['payment_method'] == 'client_credit'){
            $dist_result = CreditDistributionService::getClientPayemntID($payment['InvoicePayment']['id'], $payment['InvoicePayment']['invoice_id']);
            if ($dist_result['result']) {
                $this->set('client_payemnt_id', $dist_result['client_payment_id']);
            }
        }

        $this->set('invoicePayment', $payment);

        if (!$payment) {
            $this->flashMessage(__('Payment not found', true));
            $this->redirect(array('action' => 'index'));
        }

        if ($this->RequestHandler->isPost()) {
            $this->_processPayment($payment);
        }
		$invoice_id = $payment['InvoicePayment']['invoice_id'];
        if ($_SESSION['website_front_'.$invoice_id] && ifPluginActive(WebsiteFrontPlugin)) {
			unset($_SESSION['website_front_'.$invoice_id]);
			$this->redirect(Router::url(array('controller' => 'contents', 'action' => 'after_payment_success', $invoice_id)));
		}
		
        $this->loadModel ( 'ItemPermission' ) ;
        $this->loadModel ( 'Treasury' ) ;
        
        $this->set ( 'treasuries' , $this->Treasury->get_list () ) ;
        
//        $this->set('treasuries', $treasury_list);
        $this->set('title_for_layout',  __('View Payment', true));

        $this->crumbs = array();
        $this->crumbs[0]['title'] = __('Invoices & Subscriptions', true);
        $this->crumbs[0]['url'] = array('action' => 'index');

        $this->crumbs[1]['title'] = __('Payments', true);
        $this->crumbs[1]['url'] = array('action' => 'index');

        $this->crumbs[2]['title'] = $this->pageTitle;
        $this->crumbs[2]['url'] = '#';

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(0, true, false);
        $this->set('paymentMethods', $paymentMethods);
        $this->set('statuses', $this->InvoicePayment->getPaymentStatus());
        $this->set(compact('payment'));



        $this->titleAlias = 'payments';
    }

    function client_offline($method = false) {
        if (!$method)
            $method = 'offline';
        $this->set('method', $method);

        $params = $this->params['url'];
        unset($params['url'], $params['ext']);

        if (!empty($params['order_id'])) {
            $payment = $this->InvoicePayment->findById($params['order_id']);
            if (!$payment || $payment['Invoice']['client_id'] != getAuthClient('id')) {
                $this->flashMessage(__('Payment not found.', true));
                $this->redirect(array('controller' => 'invoices', 'action' => 'index'));
            }

            $this->Session->write('OfflinePayment', $params);
//$this->redirect(array('action' => 'offline'));
        } elseif ($this->Session->check('OfflinePayment')) {
            $params = $this->Session->read('OfflinePayment');

            $this->set(compact('params'));

            $payment = $this->InvoicePayment->findById($params['order_id']);
            $this->set(compact('payment'));

            if (!$payment || $payment['Invoice']['client_id'] != getAuthClient('id')) {
                $this->flashMessage(__('Payment not found.', true));
//$this->redirect(array('controller' => 'invoices', 'action' => 'index'));
            }

            if (!empty($this->data)) {
                if ($this->InvoicePayment->saveOffline($this->data)) {
                    $invoicePayment = $this->InvoicePayment->read(null, $id);
                    
                    $this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param8' => $invoicePayment['InvoicePayment']['transaction_id']));
                    $this->set('results', $this->data['InvoicePayment']);
//					$this->Session->del('OfflinePayment');
                    $this->render('offline_proceed');
                    return;
                } else {
                    $this->flashMessage(__('Could not continue payment', true));
                }
            }

            $this->render('offline');
        } else {
//$this->redirect(array('controller' => 'invoices', 'action' => 'index'));
        }

        $this->titleAlias = 'payments';
    }

    function _processPayment($payment, $ipn = false) {

        $paymentClass = getPaymentClass($payment['Payment']['payment_method']);
        
        $paymentObject = new $paymentClass;
        /* @var $paymentObject PaypalPayment */
        $results = $paymentObject->processResult();
        
        $site = getCurrentSite();
        $this->loadModel('Client');
        $client = $this->Client->find('first', array('conditions' => array('Client.id' => $payment['Invoice']['client_id'])));

        if (empty($site)) {
            $new_site = $this->Site->find('first', array('conditions' => array('Site.id' => $payment['Invoice']['site_id'], 'Site.active' => 1)));
            $site = $new_site['Site'];
        }

        if ($results['status']) {
            switch ($results['status']) {
                case 'completed':
//					$subject = $this->Lmail->subject = '[' . $site['business_name'] . '] ' . sprintf(__('Payment #%d has been completed', true), $payment['Payment']['id']);
//					$template = $this->Lmail->template = 'payment_complete';
                    $mail = 'complete';
                    $this->flashMessage(__('Thank you, Your payment has been completed.', true), 'Sucmessage');
                    break;
                case 'pending':
//					$subject = $this->Lmail->subject = '[' . $site['business_name'] . '] ' .  sprintf(__('Payment #%d is pending', true), $payment['Payment']['id']);
//					$template = $this->Lmail->template = 'payment_pending';

                    $mail = 'pending';
                    $this->flashMessage(__('Your payment is still pending. You will be notified once it is accepted.', true), 'Notemessage');
                    break;
                default:
//					$subject = $this->Lmail->subject = '[' . $site['business_name'] . ']' .  sprintf(__('Payment #%d has failed', true), $payment['Payment']['id']);
//					$template = $this->Lmail->template = 'payment_failed';

                    $mail = 'failed';
                    $this->flashMessage(__('Sorry, Your payment has failed', true));
                    break;
            }
            $oldpayment = $this->InvoicePayment->read(null, $payment['Payment']['id']);
            //mail("<EMAIL>", "Old Payment:" . __FUNCTION__, print_r($oldpayment,true)."<br>".print_r($payment,true));
            if ($oldpayment['Payment']['status'] != $results['data']['status']) {
                $data = $results['data'];

                $payment['Payment'] = array_merge($payment['Payment'], $data);
                $payment['Payment']['processed'] = 1;


                $this->InvoicePayment->save($payment, array('callbacks' => false, 'validate' => false));
                

             

               
                $invoicePayment = $this->InvoicePayment->read(null, $payment['Payment']['id']);
                $this->InvoicePayment->update_journals($invoicePayment);

                if ($invoicePayment['Payment']['client_id']) {
                    $this->Client->pay_invoices_from_credit($invoicePayment['Payment']['client_id']);
                    $this->add_actionline(ACTION_CLIENT_ADD_CLIENT_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Payment']['client_id'], 'param1' => $invoicePayment['Payment']['amount'], 'param2' => $invoicePayment['Payment']['status'], 'param3' => $invoicePayment['Payment']['id'], 'param4' => $invoicePayment['Payment']['payment_method'], 'param5' => $invoicePayment['Payment']['transaction_id']));
                    if ($mail == 'complete') {
                        $this->SysEmails->creditpaymentCompleteClient($payment['Payment'], $payment['Client'], $site['Site']);
                        $this->SysEmails->creditpaymentCompleteOwner($payment['Payment'], $payment['Client'], $site['Site']);
                    }
                    $this->redirect(array('action' => 'view', $payment['Payment']['id']));
                    
                }else{
                        $invoice = $this->InvoicePayment->Invoice->getInvoice($payment['Payment']['invoice_id']);  
                       $this->InvoicePayment->Invoice->updateInvoicePayments($payment['Payment']['invoice_id']);
                     $invoice = $this->InvoicePayment->Invoice->getInvoice($payment['Payment']['invoice_id']);  
                }

                $this->add_actionline(ACTION_PROCCESS_INVOICE_PAYMENT, array('staff_id' => -2, 'primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['Payment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['Payment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['Payment']['status'], 'param8' => $invoicePayment['Payment']['payment_method'], 'param9' => $invoicePayment['Payment']['transaction_id']));

                $this->set(compact('payment', 'invoice'));

                $siteModel = ClassRegistry::init(array('class' => "Site", 'ds' => 'portal'));
                $site = $siteModel->find(array('Site.id' => $invoice['Invoice']['site_id']), false, false, -1);
                if ($mail == 'complete') {
                    $this->SysEmails->paymentCompleteClient($payment['Payment'], $invoice['Invoice'], $invoice['Client'], $site['Site']);
                    $this->SysEmails->paymentCompleteOwner($payment['Payment'], $invoice['Invoice'], $invoice['Client'], $site['Site']);
                } elseif ($mail == 'pending') {
                    $this->SysEmails->paymentPendingClient($payment['Payment'], $invoice['Invoice'], $invoice['Client'], $site['Site']);
                    $this->SysEmails->paymentPendingOwner($payment['Payment'], $invoice['Invoice'], $invoice['Client'], $site['Site']);
                } else {
                    $this->SysEmails->paymentFailedClient($payment['Payment'], $invoice['Invoice'], $invoice['Client'], $site['Site']);
                    $this->SysEmails->paymentFailedOwner($payment['Payment'], $invoice['Invoice'], $invoice['Client'], $site['Site']);
                }

                //  $invoicepayment = $this->InvoicePayment->read(null, $payment['Payment']['id']);
                //   $this->add_actionline(ACTION_CLIENT_PAY, array('secondary_id' => $invoicepayment['Invoice']['client_id'], 'primary_id' => $invoicepayment['Invoice']['id'], 'param1' => $invoicepayment['Invoice']['summary_total'], 'param2' => $invoicepayment['Invoice']['payment_status'], 'param3' => $invoicepayment['Invoice']['summary_paid'], 'param4' => $invoicepayment['Payment']['id'], 'param5' => $invoicepayment['Payment']['amount']));
//				$this->Lmail->to = empty ($client['Client']['email'])?$payment['Payment']['email']:$client['Client']['email'];
//				$this->Lmail->from = $site['business_name'] . ' <' . $site['email'] . '>';
//				$this->Lmail->sendAs = 'html';
//				$this->Lmail->language = $this->InvoicePayment->Invoice->Client->get_client_language($invoice['Invoice']['client_id']);
//
//
//				$this->Lmail->send();
//				$this->Lmail->reset();
//				$this->Lmail->from = $site['business_name'] . ' <' . $site['email'] . '>';
//				$this->Lmail->to = $site['email'];
//				$this->Lmail->subject = $subject;
//				$this->Lmail->sendAs = 'html';
//				$this->Lmail->template = 'owner_' . $template;
//				$this->Lmail->language = $this->InvoicePayment->Invoice->Site->get_site_language($invoice['Invoice']['site_id']);
//				$this->Lmail->send();
            }
        } else {
            if ($results['message']) {
                $msg = $results['message'];
            } else {
                $msg = __('An error has occurred while processing payment', true);
            }

            $this->flashMessage($msg);
        }

        if ($ipn) {
            
            $this->set($results);
            $this->set('id', $payment['Payment']['id']);
            return $this->render('payment_ipn');
        }

        $invoicepayment = $this->InvoicePayment->read(null, $payment['Payment']['id']);



        $this->redirect(array('action' => 'view', $payment['Payment']['id']));
    }

    function payment_ipn($id = false) {

        $this->InvoicePayment->alias = 'Payment';
        $payment = $this->InvoicePayment->find('first', array('conditions' => array('Payment.id' => $id ? $id : $_POST['item_number']), 'recursive' => 1));
        if (!$payment) {
            
            return false;
        }

        $this->layout = false;
        if ($this->RequestHandler->isPost()) {
            
            $this->_processPayment($payment, true);
        }
    }

    function owner_export_myob() {
        $this->autoRender = false;
        $this->InvoicePayment->myobExport();
        exit();
    }

    function owner_strip_confirm($id = null)
    {
        $this->client_strip_confirm($id);
        $this->render('client_strip_confirm');

    }

    function client_strip_confirm($id = null)
    {

        App::import('Vendor','StripeScaPayment',array('file' => 'payments' . DS . 'StripeScaPayment.php'));
        $balance_transaction = null;
        //  Configure::write('debug', 2);
        $this->loadModel('SitePaymentGateway');

        $Stripe = $this->SitePaymentGateway->find('first',array('conditions' => array('SitePaymentGateway.active'=>1,'payment_gateway' => 'stripe')));

        require_once dirname(dirname(__FILE__)) . '/lib/StripeSca.php';
        $intent = \Stripe\Stripe::setApiKey($Stripe['SitePaymentGateway']['username']);

        $this->set('pk',$Stripe['SitePaymentGateway']['option1']);

        if (!empty($id)) {

            try {
                $StripeTokenPayment = new StripeScaPayment();
                $rr = $StripeTokenPayment::read($id);
                $payment_id = $rr->charges->data[0]['metadata']->order_id;
                $status = $rr->charges->data[0]['status'];
                $balance_transaction = $rr->charges->data[0]['balance_transaction'];


            } catch (Exception $e) {
                $this->flashMessage(__('Sorry, Your payment has failed',true));
                $this->redirect('/');
                die();
            }


            $order = $this->InvoicePayment->findById($payment_id);


            if (!isset($order['InvoicePayment']['payment_method'])) {
                $this->flashMessage(__('Payment not found !!!',true));
                $this->redirect('/');
            }

            if ($order['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED) {

                $this->redirect(array('action' => 'view',$payment_id));
            }
            $client_id = getAuthClient('id');
            $this->InvoicePayment->alias = 'Payment';
            $payment = $this->InvoicePayment->find('first',array('conditions' => array('Payment.id' => $payment_id),'recursive' => 1));

            if ($status == 'succeeded') {
                $_POST['status'] = 1;
                $_POST['transaction_id'] = $balance_transaction;
                $_POST['response_message'] = null;
                $this->_processPayment($payment);
            } else {
                $_POST['status'] = 0;
                $_POST['response_message'] = 'failed';
                $client_id = getAuthClient('id');
                $this->InvoicePayment->alias = 'Payment';
                $payment = $this->InvoicePayment->find('first',array('conditions' => array('Payment.id' => $payment_id),'recursive' => 1));
                $this->_processPayment($payment);

            }
        }

    }

    public function payumoney($id = null) {
        $order = $this->InvoicePayment->findById($id);

        if (!$order || 'payumoney' !== $order['InvoicePayment']['payment_method']) {
            $this->flashMessage(__('Payment not found', true));
            $this->redirect('/');
        }

        $this->set('params', $this->Session->read("PaymentParams.$id"));
    }
    
    public function client_ccavenue_return($id = null) {
          $this->InvoicePayment->alias = 'Payment';
        $payment = $this->InvoicePayment->find('first', array('conditions' => array('Payment.id' => $id ? $id : $_POST['item_number']), 'recursive' => 1));
        if (!$payment) {
            
            return false;
        }
        
        $this->layout = false;
        if ($this->RequestHandler->isPost()) {
            
            $this->_processPayment($payment, false);
        }
        
       
    }
    public function client_ccavenue_cancel($id = null) {
          $order = $this->InvoicePayment->findById($id);
      if (!$order) {
            $this->flashMessage(__('Payment not found !', true));
            $this->redirect('/');
        }
        $this->flashMessage(__('Payment has been canceled', true));
          $this->redirect(array('client'=>true,'controller'=>'invoices','action' => 'view', $order['Invoice']['id']));
        die();
    }
    public function ccavenue($id = null) {
        $merchant_data='';
        $order = $this->InvoicePayment->findById($id);
       // echo "<pre>";print_r($order);
        if (!$order) {
            $this->flashMessage(__('Payment not found !', true));
            $this->redirect('/');
        }
        
        require_once dirname(dirname(__FILE__)) . '/lib/Crypto.php';
        $this->loadModel('SitePaymentGateway');
        
        $Ccavenue = $this->SitePaymentGateway->find('first', array('conditions' => array('payment_gateway' => 'ccavenue')));
        
        $merchant_id=$Ccavenue['SitePaymentGateway']['username'];
        $access_code=$Ccavenue['SitePaymentGateway']['option1'];
        $working_key=$Ccavenue['SitePaymentGateway']['option2'];
        
        $merchant_data['merchant_id']=$merchant_id;
        $merchant_data['order_id']=$id;
        $merchant_data['currency']=$order['InvoicePayment']['currency_code'];
        $merchant_data['amount']=$order['InvoicePayment']['amount'];
        $merchant_data['redirect_url']=Router::url(array('client'=>true,'action'=>'ccavenue_return',$id),true);
        $merchant_data['cancel_url']=Router::url(array('client'=>true,'action'=>'ccavenue_cancel',$id),true);
        $merchant_data['language']='en';
        $merchant_data['billing_name']=$order['InvoicePayment']['first_name'].' '.$order['InvoicePayment']['last_name'];
        $merchant_data['billing_address']=$order['InvoicePayment']['address1'];
        $merchant_data['billing_city']=$order['InvoicePayment']['city'];
        $merchant_data['billing_state']=$order['InvoicePayment']['state'];
        $merchant_data['billing_zip']=$order['InvoicePayment']['postal_code'];
        
        $this->loadModel('Country');
        $Country=$this->Country->find('first', array('conditions' => array('Country.code' => $order['InvoicePayment']['country_code'])));
        
        $merchant_data['billing_country']=$Country['Country']['country'];
        $merchant_data['billing_tel']=$order['InvoicePayment']['phone1'];
        $merchant_data['billing_email']=$order['InvoicePayment']['email'];
        
        	foreach ($merchant_data as $key => $value){
		$xmerchant_data.=$key.'='.$value.'&';
	}

	$encrypted_data=encrypt($xmerchant_data,$working_key); // Method for encrypting the data.
        
        $this->set('access_code',$access_code);        
        $this->set('merchant_data',$merchant_data);
        
        $this->set('encrypted_data',$encrypted_data);
        
        $this->set('params', $this->Session->read("PaymentParams.$id"));
    }

    public function authorizenet($id = null) {
        $order = $this->InvoicePayment->findById($id);

        if (!$order || 'authorizenet' !== $order['InvoicePayment']['payment_method']) {
            $this->flashMessage(__('Payment not found', true));
            $this->redirect('/');
        }

        $this->set('params', $this->Session->read("PaymentParams.$id"));
    }

    public function two_checkout() {
        if (empty($_POST) || empty($_POST['merchant_order_id'])) {
            $this->cakeError('error404');
        }

        file_put_contents('/tmp/2c0.log', print_r($_POST, 1));

        $oid = $_POST['merchant_order_id'];
        $order = $this->InvoicePayment->findById($oid);

        if (!$order) {
            $this->flashMessage(__('Payment not found', true));
            $this->redirect('/');
        }

        $this->payment_ipn($oid);

        $this->redirect('/client/invoice_payments/view/' . $oid);
    }

    public function paytabs_ipn_listener()
    {
        $payment_id = isset($_POST['reference_id']) ? $_POST['reference_id'] : null;
        if (!$payment_id)
            die;

        $this->InvoicePayment->alias = 'Payment';
        $payment = $this->InvoicePayment->find('first', array('conditions' => array('Payment.id' => $payment_id), 'applyBranchFind' => false));
        if (!$payment)
            die;

        $this->_processPayment($payment, true);
        die("IPN Received Successfully");
    }

    public function tap_payment_callback($payment_id){
        $this->loadModel('SitePaymentGateway');
        $this->loadModel('Invoice');
        $invoice_payment = $this->InvoicePayment->read(null, $payment_id);
        if($invoice_payment['InvoicePayment']['payment_method'] !== 'tap' && $invoice_payment['InvoicePayment']['status'] != 1){
            $this->cakeError('404');
        }
        $invoice = $this->Invoice->read(null, $invoice_payment['InvoicePayment']['invoice_id']);
        $charge_id = $_GET['tap_id'];
        App::import('Vendor','TapPayment',array('file'=>'payments/TapPayment.php'));
        $pg = $this->SitePaymentGateway->find('first',
            ['conditions' => ['SitePaymentGateway.payment_gateway' => $invoice_payment['InvoicePayment']['payment_method']]]
        );
        $paymentMethod = new TapPayment();
        try{
            $charge_request = $paymentMethod->retrieve_charge_request($charge_id);
        }catch(Exception $e){
            $this->cakeError('error404');
        }
        if($charge_request['status'] == 'CAPTURED'){
            $this->InvoicePayment->set('status',1);
            $this->InvoicePayment->set('transaction_id',$charge_request['id']);
            $this->InvoicePayment->save();
            $this->handleInvoicePaymentSuccess($invoice_payment);
            if($invoice_payment['InvoicePayment']['source'] == 'owner_add_payment' || $invoice_payment['InvoicePayment']['source'] == 'client_add_payment'){
                $this->flashMessage(__('The payment has been added to the invoice', true), 'Sucmessage');
            } elseif ($invoice_payment['InvoicePayment']['source'] == 'owner_add_client_credit' || $invoice_payment['InvoicePayment']['source'] == 'client_add_payment_credit') {
                $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
            }
        }else{
            $this->sendFailedInvoicePaymentEmails($invoice_payment);
            if($invoice_payment['InvoicePayment']['source'] == 'website_front' || $invoice_payment['InvoicePayment']['source'] == 'webfront_reservation'){
                $this->redirect(Router::url(array('controller' => 'contents', 'client' => true, 'action' => 'error')));
            }
            $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.' . ' ' .$charge_request['response']['message'] , true));
        }
        if($invoice_payment['InvoicePayment']['source'] == 'owner_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoices', 'action' => 'view', 'owner' => true, $invoice_payment['InvoicePayment']['invoice_id'])));
        }elseif($invoice_payment['InvoicePayment']['source'] == 'client_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoice_payment['InvoicePayment']['id'])));
        }elseif($invoice_payment['InvoicePayment']['source'] == 'owner_add_client_credit'){
            $this->redirect(Router::url(array('controller' => 'clients', 'action' => 'view', 'owner' => true, $invoice_payment['InvoicePayment']['client_id'])));
        }elseif($invoice_payment['InvoicePayment']['source'] == 'website_front'){
            $this->Session->delete('Message.flash');
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'after_payment_success', 'client' => true, $invoice_payment['InvoicePayment']['invoice_id'])));
        } elseif ($invoice_payment['InvoicePayment']['source'] == 'webfront_reservation') {
            $this->afterShopFrontReservation($invoice_payment['InvoicePayment']['invoice_id'], $invoice_payment['Invoice']['source_id']);
        } elseif($invoice_payment['InvoicePayment']['source'] == 'client_add_payment_credit'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoice_payment['InvoicePayment']['id'])));
        }
    }

    private function handleInvoicePaymentSuccess($invoicePayment) {
        $id = $invoicePayment['Invoice']['id'];
        if($id){
            $this->loadModel('Invoice');
            if($invoicePayment['Invoice']['type']==Invoice::TEMPINVOICE && $invoicePayment['InvoicePayment']['status']==PAYMENT_STATUS_COMPLETED){
                $this->Invoice->updateInvoiceType($id,Invoice::Invoice);
                $this->Invoice->updateNextInvoiceNo($id,Invoice::Invoice);
            }
            $this->Invoice->update_draft($id,0);
            $NewInvoice = $this->Invoice->getInvoice($id, array('Invoice.type' => array(Invoice::Invoice,Invoice::TEMPINVOICE)));
            $this->Invoice->updateInvoicePayments($id);
            $this->Invoice->updateInvoice($NewInvoice);
            $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions');
            if ($enable_requisitions && $invoicePayment['Invoice']['draft'] == 1 && $NewInvoice['Invoice']['draft'] != 1) {
                $this->loadModel('Requisition');
                $invoiceRequisitionTypes = [
                    Invoice::Invoice => Requisition::ORDER_TYPE_INVOICE,
                    Invoice::Refund_Receipt => Requisition::ORDER_TYPE_INVOICE_REFUND,
                    Invoice::Credit_Note => Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE
                ];
                $this->Requisition->updateForOrder($NewInvoice, $invoiceRequisitionTypes[$NewInvoice['Invoice']['type']], false , false);
            }
            $this->Invoice->InvoicePayment->alias = 'InvoicePayment';
            $invoicePayment = $this->InvoicePayment->read(null, $invoicePayment['InvoicePayment']['id']);

            $this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => empty($invoicePayment['Invoice']['draft']) ? $invoicePayment['Invoice']['payment_status'] : -1, 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));

            if(IS_REST){
                $invoice_id = $NewInvoice['Invoice']['id'] ?: $this->Invoice->id;
                if(strpos($_SERVER['HTTP_REFERER'],'pos')) {
                    $this->set('invoice_pos_html', $this->owner_preview($invoice_id, false, true));
                }
                $this->set('id', $invoice_id);
                $this->set('invoice_number', $invoicePayment['Invoice']['no']);
                $this->render('created');
                return;
            }

        }else{
            $this->add_actionline(ACTION_ADD_CLIENT_PAYMENT, array('primary_id' => 0, 'secondary_id' => $invoicePayment['InvoicePayment']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['InvoicePayment']['status'], 'param3' => $invoicePayment['InvoicePayment']['id'], 'param4' => $invoicePayment['InvoicePayment']['payment_method'], 'param5' => $invoicePayment['InvoicePayment']['transaction_id']));
        }
    }

    private function sendSuccessInvoicePaymentEmails($invoicePayment)
    {
        $this->SysEmails->creditpaymentCompleteClient($invoicePayment['InvoicePayment'], $invoicePayment['Client'], getAuthOwner());
        $this->SysEmails->creditpaymentCompleteOwner($invoicePayment['InvoicePayment'], $invoicePayment['Client'], getAuthOwner());
    }

    private function sendFailedInvoicePaymentEmails($invoicePayment)
    {
        $this->SysEmails->paymentFailedClient($invoicePayment['InvoicePayment'], $invoicePayment['Invoice'], $invoicePayment['Client'], getAuthOwner());
        $this->SysEmails->paymentFailedOwner($invoicePayment['InvoicePayment'], $invoicePayment['Invoice'], $invoicePayment['Client'], getAuthOwner());
    }
    
    public function owner_securepay($invoicePaymentId) {
        $this->loadModel('SitePaymentGateway');
        $this->loadModel('InvoicePayment');
        $invoicePayment = $this->InvoicePayment->read(null, $invoicePaymentId);
        if (!$invoicePayment) $this->cakeError('error404');

        $SecurePay = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active' => 1 , 'payment_gateway' => 'securepay')));
        $this->set('SecurePay', $SecurePay);
        $this->set('invoicePayment', $invoicePayment);
        if($invoicePayment['InvoicePayment']['invoice_id']){
            $this->loadModel('Invoice');
            $invoice = $this->Invoice->read(null, $invoicePayment['InvoicePayment']['invoice_id']);
            $this->set('invoice', $invoice);
        }else{
            $this->loadModel('Client');
            $client = $this->Client->read(null, $invoicePayment['InvoicePayment']['client_id']);
            $this->set('client', $client);
        }

        App::import('Vendor', 'SecurePayPayment', array('file' => 'payments/SecurePayPayment.php'));

        $paymentClass = new SecurePayPayment();
//        $access_token = $paymentClass->authenticate($SecurePay['SitePaymentGateway']['username'],$SecurePay['SitePaymentGateway']['option1']);
//        $orderResponse = $paymentClass->initOrder($access_token, ['amount' => $invoicePayment['InvoicePayment']['amount'],'merchantCode' => $SecurePay['SitePaymentGateway']['option2'], 'orderRef'  => $invoice['Invoice']['no'], 'orderType' => 'DYNAMIC_CURRENCY_CONVERSION',]);
//        $this->set('orderToken', $orderResponse['body']['orderToken']);
        if (!empty($this->data)) {
            if($this->data['error']){
                $this->flashMessage(__('Please Verify your credentials in payment methods', true));
            }
            App::import('Vendor', 'SecurePayPayment', array('file' => 'payments/SecurePayPayment.php'));
            try{
                $access_token = $paymentClass->authenticate($SecurePay['SitePaymentGateway']['username'],$SecurePay['SitePaymentGateway']['option1']);
                $payment = $paymentClass->create_payment($access_token,$this->data['tokenisedCard'],$invoicePayment, $orderResponse['body']['orderToken']);
            }catch(Exception $e){
                $this->flashMessage(__($e->getMessage(), true));
                return;
            }
            if($payment['status'] && $payment['body']['status'] == 'paid'){
                $this->InvoicePayment->set('status',1);
                $this->InvoicePayment->save();
                $this->handleInvoicePaymentSuccess($invoicePayment);
                if($invoicePayment['InvoicePayment']['invoice_id']){
                    $this->flashMessage(__('The payment has been added to the invoice', true), 'Sucmessage');
                    $this->redirect(array('controller' => 'invoices', 'action' => 'view', $invoicePayment['InvoicePayment']['invoice_id']));
                }else{
                    $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $invoicePayment['InvoicePayment']['client_id']));
                }
            } else {
                $this->sendFailedInvoicePaymentEmails($invoicePayment);
                $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.', true));
                if($invoicePayment['InvoicePayment']['invoice_id']){
                    $this->redirect(array('controller' => 'invoices', 'action' => 'view', $invoicePayment['InvoicePayment']['invoice_id']));
                } else {
                    $this->redirect(array('controller' => 'clients', 'action' => 'view', $invoicePayment['InvoicePayment']['client_id']));
                }
            }
        }
    }

    private function paymobGetHmacData($data)
    {
        $fields = [
            'amount_cents',
            'created_at',
            'currency',
            'error_occured',
            'has_parent_transaction',
            'id',
            'integration_id',
            'is_3d_secure',
            'is_auth',
            'is_capture',
            'is_refunded',
            'is_standalone_payment',
            'is_voided',
            'order.id',
            'owner',
            'pending',
            'source_data.pan',
            'source_data.sub_type',
            'source_data.type',
            'success',
        ];
        $values = [];
        foreach ($fields as $field) {
            if (strpos($field, '.')) {
                $f = explode('.', $field);
                if (isset($data[$f[0]][$f[1]])) {
                    $values[] = $data[$f[0]][$f[1]];
                } elseif (isset($data[$f[0]])) {
                    $values[] = $data[$f[0]];
                } else {
                    $values[] = $data[str_replace('.', '_', $field)];
                }
            } else {
                $values[] = $data[$field];
            }
        }
        return implode('', $values);
    }

    private function paymobAuthenticateHmac($data): bool
    {
        $PaymentGateway = ClassRegistry::init('SitePaymentGateway');
        $deprecated_paymob = $PaymentGateway->find(array('payment_gateway' => 'paymob'), null, null, -1);
        $paymob = $PaymentGateway->find(array('payment_gateway' => 'paymob2'), null, null, -1);

        $hmacData = $this->paymobGetHmacData($data);
        $deprecated_paymob_hash = hash_hmac('sha512', $hmacData, $deprecated_paymob['SitePaymentGateway']['option3']);
        $paymob_hash = hash_hmac('sha512', $hmacData, $paymob['SitePaymentGateway']['option3']);
        return ($deprecated_paymob_hash == $this->params['url']['hmac'] || $paymob_hash == $this->params['url']['hmac']);
    }

    public function paymob_response($id = null)
    {
        $this->InvoicePayment->applyBranch['onFind'] = false;
        $invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['OR' => ['InvoicePayment.transaction_id' => $this->params['url']['order'], 'InvoicePayment.id' => $id]]]);
        $this->InvoicePayment->read(null, $invoicePayment['InvoicePayment']['id']);
        if(!in_array($invoicePayment['InvoicePayment']['payment_method'], ['paymob', 'paymob2'])){
            $this->cakeError('404');
        }
        if (!$this->paymobAuthenticateHmac($this->params['url'])){
            $this->cakeError('404');
        }else{
            if ($this->params['url']['success'] == 'true') {
                $this->loadModel('InvoicePayment');
                $this->InvoicePayment->set(['status' => PAYMENT_STATUS_COMPLETED]);
                $this->InvoicePayment->save();
                $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
                $this->handleInvoicePaymentSuccess($invoicePayment);
                if($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment' ){
                    $this->flashMessage(__('The payment has been added to the invoice', true), 'Sucmessage');
                } elseif($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit'){
                    $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
                }
            } else {
                $this->InvoicePayment->set(['status' => PAYMENT_STATUS_FAILED]);
                $this->InvoicePayment->save();
                $this->sendFailedInvoicePaymentEmails($invoicePayment);
                if($invoicePayment['InvoicePayment']['source'] == 'website_front' || $invoicePayment['InvoicePayment']['source'] == 'webfront_reservation'){
                    $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.', true), 'alert alert-danger m-t');
                    $this->redirect(Router::url(array('controller' => 'contents', 'client' => true, 'action' => 'error')));
                }
                $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.', true));
            }
        }
        if($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoices', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
        }elseif($invoicePayment['InvoicePayment']['source'] == 'client_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
        }elseif($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit'){
            $this->redirect(Router::url(array('controller' => 'clients', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['client_id'])));
        }elseif($invoicePayment['InvoicePayment']['source'] == 'website_front'){
            $this->Session->delete('Message.flash');
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'after_payment_success', 'client' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
        } elseif ($invoicePayment['InvoicePayment']['source'] == 'webfront_reservation') {
            $this->afterShopFrontReservation($invoicePayment['InvoicePayment']['invoice_id'], $invoicePayment['Invoice']['source_id']);
        } elseif($invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
        }
    }

    public function paytabs_return($paymentId){
        // sleep for 2 secs for the callback to take place
        sleep(2);
        $this->loadModel('InvoicePayment');
        $this->InvoicePayment->applyBranch['onFind']= false;
        $invoicePayment = $this->InvoicePayment->read(null, $paymentId);
        if($invoicePayment['InvoicePayment']['payment_method'] !== 'paytabs2'){
            $this->cakeError('404');
        }
        if($invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_COMPLETED){
            if($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment'){
                $this->flashMessage(__('The payment has been added to the invoice', true), 'Sucmessage');
            } elseif ($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit') {
                $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
            }
        }elseif($invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_NOT_COMPLETED || $invoicePayment['InvoicePayment']['status'] == PAYMENT_STATUS_PENDING){
            $this->flashMessage(__('The payment has been added, but maybe still being processed', true), 'Sucmessage');
        }else{
            if($invoicePayment['InvoicePayment']['source'] == 'website_front'|| $invoicePayment['InvoicePayment']['source'] == 'webfront_reservation'){
                $this->redirect(Router::url(array('controller' => 'contents', 'client' => true, 'action' => 'error')));
            }
            $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.', true));
        }
        if($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoices', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
        }elseif($invoicePayment['InvoicePayment']['source'] == 'client_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
        }elseif($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit'){
            $this->redirect(Router::url(array('controller' => 'clients', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['client_id'])));
        }elseif($invoicePayment['InvoicePayment']['source'] == 'website_front'){
            $this->Session->delete('Message.flash');
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'after_payment_success', 'client' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
        } elseif ($invoicePayment['InvoicePayment']['source'] == 'webfront_reservation') {
          $this->afterShopFrontReservation($invoicePayment['InvoicePayment']['invoice_id'], $invoicePayment['Invoice']['source_id']);
        }elseif($invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
        }
    }

    private function afterShopFrontReservation($invoice_id, $reservation_order)
    {
        $this->Session->delete('Message.flash');
        $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'client_after_payment_success_for_booking', 'client' => true, $invoice_id, $reservation_order)));
    }

    public function paytabs_callback()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        if (isset($_POST['tran_ref']))
            $data = $_POST;

        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO, 'paytabasCallbackDebug', [
            'data' => $data,
            'is_post_vars' => isset($_POST['tran_ref']),
        ]);
        $this->loadModel('InvoicePayment');
        $invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['InvoicePayment.id' => $data['cart_id'], 'InvoicePayment.transaction_id' => $data['tran_ref']]]);

        $paymentStatusMap = [
            'A' => PAYMENT_STATUS_COMPLETED,
            'H' => PAYMENT_STATUS_PENDING,
            'P' => PAYMENT_STATUS_PENDING,
            'E' => PAYMENT_STATUS_NOT_COMPLETED,
            'D' => PAYMENT_STATUS_FAILED,
        ];
        $invoicePayment['InvoicePayment']['status'] = $paymentStatusMap[$data['payment_result']['response_status']];
        $this->loadModel('Invoice');
        $this->Invoice->read(null, $invoicePayment['InvoicePayment']['invoice_id']);
        $this->InvoicePayment->update($data['cart_id'], $invoicePayment);
        if (!in_array($invoicePayment['InvoicePayment']['status'], [$paymentStatusMap['D'], $paymentStatusMap['E']])) {
            $this->handleInvoicePaymentSuccess($invoicePayment);
        } else {
            $this->sendFailedInvoicePaymentEmails($invoicePayment);
        }
        die(json_encode(['message' => 'status updated']));
    }

    public function paypal_callback($invoicePaymentId){
        $invoicePayment = $this->InvoicePayment->read(null, $invoicePaymentId);
        if($invoicePayment['InvoicePayment']['payment_method'] !== 'paypalV2' && $invoicePayment['InvoicePayment']['status'] != 1){
            $this->cakeError('404');
        }
        if (!$invoicePayment) $this->cakeError('error404');
        $this->loadModel('SitePaymentGateway');
        $pg = $this->SitePaymentGateway->find('first', ['conditions' => ['SitePaymentGateway.payment_gateway' => 'paypalV2']]);
        
        App::import('Vendor','PayPalPaymentV2',array('file'=>'payments/PayPalPaymentV2.php'));
        $paymentMethod = new PayPalPaymentV2();
        try{
            $access_token = $paymentMethod->authenticate($pg['SitePaymentGateway']['username'],$pg['SitePaymentGateway']['option1']);
            $order = $paymentMethod->capture_order($_GET['token'],$access_token);
        }catch(Exception $e){
            $error = $e->getMessage();
        }
        if(isset($order['status']) && $order['status'] == 'COMPLETED'){
            $this->InvoicePayment->set('status',1);
            $this->InvoicePayment->set('first_name',$order['payer']['name']['given_name']);
            $this->InvoicePayment->set('last_name',$order['payer']['name']['surname']);
            $this->InvoicePayment->set('email',$order['payer']['email_address']);
            $this->InvoicePayment->set('country_code',$order['payer']['address']['country_code']);
            $this->InvoicePayment->set('transaction_id',$order['id']);
            $this->InvoicePayment->save();
            $this->handleInvoicePaymentSuccess($invoicePayment);
            if($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment'){
                $this->flashMessage(__('The payment has been added to the invoice', true), 'Sucmessage');
            }elseif($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit'){
                $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
            }
        } else {
            $this->sendFailedInvoicePaymentEmails($invoicePayment);
            if($invoicePayment['InvoicePayment']['source'] == 'website_front'|| $invoicePayment['InvoicePayment']['source'] == 'webfront_reservation'){
                $this->redirect(Router::url(array('controller' => 'contents', 'client' => true, 'action' => 'error')));
            }
            $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.', true));
        }
        if ($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment') {
            $this->redirect(Router::url(array('controller' => 'invoices', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
        } elseif ($invoicePayment['InvoicePayment']['source'] == 'client_add_payment'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
        } elseif ($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit'){
            $this->redirect(Router::url(array('controller' => 'clients', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['client_id'])));
        } elseif ($invoicePayment['InvoicePayment']['source'] == 'website_front'){
            $this->Session->delete('Message.flash');
            $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'after_payment_success', 'client' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
        } elseif ($invoicePayment['InvoicePayment']['source'] == 'webfront_reservation') {
            $this->afterShopFrontReservation($invoicePayment['InvoicePayment']['invoice_id'], $invoicePayment['Invoice']['source_id']);
        }elseif($invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit'){
            $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
        }
    }

    public function square_webhook(){
        $this->loadModel('SitePaymentGateway');
        $pg = $this->SitePaymentGateway->find('first', ['conditions' => ['SitePaymentGateway.payment_gateway' => 'square']]);
        $signature_key = $pg['SitePaymentGateway']['option3'];
        $headers = apache_request_headers();
        $signature = $headers["X-Square-Hmacsha256-Signature"];

        $body = '';
        $handle = fopen('php://input', 'r');
        while(!feof($handle)) {
            $body .= fread($handle, 1024);
        }
        $data = json_decode($body, true);
        $hash = hash_hmac("sha256", 'https://' . str_replace('.daftara.', '.daftra.',getCurrentSite('subdomain')) . '/invoice_payments/square_webhook'.$body, $signature_key, true);
        $hash_beta = hash_hmac("sha256", 'https://' . str_replace('.daftra.', '.daftara.',getCurrentSite('subdomain')) . '/invoice_payments/square_webhook'.$body, $signature_key, true);
        \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO, 'square_webhook', [
            'square_body' => $body,
            'subdomain_url' => 'https://' . getCurrentSite('subdomain') . '/invoice_payments/square_webhook',
            'square_hash' => base64_encode($hash),
            'square_test' => [
                'square_signature' => $signature
            ],
            'square_signature_key' => $signature_key,
        ]);
        if(base64_encode($hash) == $signature || base64_encode($hash_beta) == $signature){
            $this->InvoicePayment->applyBranch['onFind'] = false;
            $invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['InvoicePayment.transaction_id' => $data['data']['id']]]);
            $paymentStatusMap = [
                'COMPLETED' => PAYMENT_STATUS_COMPLETED,
                'APPROVED' => PAYMENT_STATUS_PENDING,
                'CANCELED' => PAYMENT_STATUS_NOT_COMPLETED,
                'FAILED' => PAYMENT_STATUS_FAILED,
            ];
            if($invoicePayment){
                $invoicePayment['InvoicePayment']['status'] = $paymentStatusMap[$data['data']['object']['payment']['status']];
                $this->InvoicePayment->applyBranch['onSave'] = false;
                $this->InvoicePayment->update($invoicePayment['InvoicePayment']['id'], $invoicePayment);
                $this->handleInvoicePaymentSuccess($invoicePayment);
                die(json_encode(['message' => 'status updated']));
            }else{
                $this->sendFailedInvoicePaymentEmails($invoicePayment);
                http_response_code(404);
                die(json_encode(['message' => 'payment not found']));
            }
        }else{
            http_response_code(403);
            die(json_encode(['message' => 'Signature is invalid']));
        }
    }

    function owner_multi_pdf() {
        $this->loadModel('Country');
        $ids = $_POST['ids'];
        $conditions = [];
        if($_POST['index_action_select_type'] === 'all') {
            $conditions = $this->getCachedPaginationConditions('InvoicePayment');
            if(isset($conditions['Payment.id'])){
                unset($conditions['Payment.id']);
            }
        } else {
            $conditions['Payment.id'] = $ids;
            if(is_countable($ids) && count($ids) > 200){
                $this->flashMessage(sprintf(__("Can't print more than %s %s", true), 200, __('Invoice Payment', true)));
                $this->redirect('index');
            }
        }

        $this->InvoicePayment->recursive = 2;
        $this->InvoicePayment->alias = 'Payment';

        $conditions = array_map(function($condition) {
            return str_replace('InvoicePayment.', 'Payment.', $condition);
        }, $conditions);  //to handle condition comes from cache 
        
        $selected_invoices_payments = $this->InvoicePayment->find('all', ['conditions' => $conditions]);
        
        if(is_countable($selected_invoices_payments) && count($selected_invoices_payments) > 200){
            $this->flashMessage(sprintf(__("Can't print more than %s %s", true), 200, __('Invoice Payment', true)));
            $this->redirect('index');
        }

        foreach ($selected_invoices_payments as $k => $vP) {
            $selected_invoices_payments[$k] = $vP;
            $selected_invoices_payments[$k]['clientCountry'] = $this->Country->field('country', array('Country.code' => $vP['Payment']['country_code']));
        }
        $this->set('selected_invoices_payments', $selected_invoices_payments);

        $ownerCountry = $this->Country->get_country_code(getCurrentSite('country_code'));
        $this->set('ownerCountry', $ownerCountry);

        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(0, true, false);
        $this->set('paymentMethods', $paymentMethods);

        $this->loadModel('Staff');
        $this->set('staffs', $this->Staff->getList());
        
        $this->loadModel('PrintableTemplate');
        $printableTemplates = $this->PrintableTemplate->getPrintableTemplatesList('invoice_payment', false, true);
        $defaultPrintableTemplate = $printableTemplates[0] ?? null;
        $typeData = $this->PrintableTemplate->getTypesList('invoice_payment');

        $this->set('typeData', $typeData);
        $this->set('defaultPrintableTemplate', $defaultPrintableTemplate);
    }

    public function stripe_return()
    {
        $this->loadModel('SitePaymentGateway');
        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active' => 1, 'payment_gateway' => 'stripe')));
        $this->set('Stripe', $Stripe);
        if (!empty($Stripe['SitePaymentGateway']['username'])) {
            require_once dirname(dirname(__FILE__)) . '/lib/StripeSca.php';
            $stripe = new \Stripe\StripeClient($Stripe['SitePaymentGateway']['username']);
            try {
                $intent = $stripe->paymentIntents->retrieve($this->params['url']['payment_intent']);
            } catch (Exception $e) {
                $this->cakeError('error404');
            }
            if ($invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['InvoicePayment.transaction_id' => $this->params['url']['payment_intent']]])) {
                if ($intent->status == 'succeeded') {
                    if ($invoicePayment['InvoicePayment']['status'] != PAYMENT_STATUS_COMPLETED) {
                        $invoicePayment['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;
                        $this->InvoicePayment->save($invoicePayment, ['fieldList' => ['status'], 'validate' => false]);
                        $this->handleInvoicePaymentSuccess($invoicePayment);
                    }
                    if ($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment') {
                        $this->flashMessage(__('The payment has been added to the invoice', true), 'Sucmessage');
                    } elseif ($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit' || $invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit') {
                        $this->flashMessage(__('The credit has been added to the client', true), 'Sucmessage');
                    }
                }elseif($intent->status == 'payment_failed' || $intent->status == 'canceled'){
                    $invoicePayment['InvoicePayment']['status'] = PAYMENT_STATUS_FAILED;
                    $this->InvoicePayment->save($invoicePayment);
                    $this->sendFailedInvoicePaymentEmails($invoicePayment);
                    if ($invoicePayment['InvoicePayment']['source'] == 'website_front' || $invoicePayment['InvoicePayment']['source'] == 'webfront_reservation') {
                        $this->redirect(Router::url(array('controller' => 'contents', 'client' => true, 'action' => 'error')));
                    }
                    $this->flashMessage(__('Payment Has been added but was not complete on the gateway side.', true));
                } else {
                    $invoicePayment['InvoicePayment']['status'] = PAYMENT_STATUS_PENDING;
                    $this->InvoicePayment->save($invoicePayment);
                    if ($invoicePayment['InvoicePayment']['source'] == 'website_front' || $invoicePayment['InvoicePayment']['source'] == 'webfront_reservation') {
                        $this->redirect(Router::url(array('controller' => 'contents', 'client' => true, 'action' => 'error')));
                    }
                    $this->flashMessage(__('Payment Has been added but it is still pending', true));
                }
                if ($invoicePayment['InvoicePayment']['source'] == 'owner_add_payment') {
                    $this->redirect(Router::url(array('controller' => 'invoices', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
                } elseif ($invoicePayment['InvoicePayment']['source'] == 'client_add_payment') {
                    $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
                } elseif ($invoicePayment['InvoicePayment']['source'] == 'owner_add_client_credit') {
                    $this->redirect(Router::url(array('controller' => 'clients', 'action' => 'view', 'owner' => true, $invoicePayment['InvoicePayment']['client_id'])));
                } elseif ($invoicePayment['InvoicePayment']['source'] == 'website_front') {
                    $this->Session->delete('Message.flash');
                    $this->redirect(Router::url(array('controller' => 'contents', 'action' => 'after_payment_success', 'client' => true, $invoicePayment['InvoicePayment']['invoice_id'])));
                } elseif ($invoicePayment['InvoicePayment']['source'] == 'webfront_reservation') {
                    $this->afterShopFrontReservation($invoicePayment['InvoicePayment']['invoice_id'], $invoicePayment['Invoice']['source_id']);
                } elseif ($invoicePayment['InvoicePayment']['source'] == 'client_add_payment_credit') {
                    $this->redirect(Router::url(array('controller' => 'invoice_payments', 'action' => 'view', 'client' => true, $invoicePayment['InvoicePayment']['id'])));
                }
            } else {
                $this->cakeError('error404');
            }
        }
    }

    public function stripe_webhook(){
        $this->loadModel('SitePaymentGateway');
        $Stripe = $this->SitePaymentGateway->find('first', array('conditions' => array('SitePaymentGateway.active' => 1, 'payment_gateway' => 'stripe')));
        require_once dirname(dirname(__FILE__)) . '/lib/StripeSca.php';
        \Stripe\Stripe::setApiKey($Stripe['SitePaymentGateway']['username']);
        $payload = @file_get_contents('php://input');
        $endpoint_secret = settings::getValue(0, 'stripe_webhook_secret');
        try {
            $event = \Stripe\Event::constructFrom(
                json_decode($payload, true)
            );
        } catch(\UnexpectedValueException $e) {
            // Invalid payload
            echo '⚠️  Webhook error while parsing basic request.';
            http_response_code(400);
            die();
        }

        if ($endpoint_secret) {
            // Only verify the event if there is an endpoint secret defined
            // Otherwise use the basic decoded event
            $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
            try {
                $event = \Stripe\Webhook::constructEvent(
                    $payload, $sig_header, $endpoint_secret
                );
            } catch(\Stripe\Exception\SignatureVerificationException $e) {
                // Invalid signature
                echo '⚠️  Webhook error while validating signature.';
                http_response_code(400);
                die();
            }
        }

        // Handle the event
        if(strpos($event->type, 'payment_intent') === 0){
            $paymentIntent = $event->data->object;
            \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO, 'stripe_webhook', [
                'paymentIntent' => $paymentIntent,
                'paymentIntent.status' => $paymentIntent->status,
                'paymentIntent.id' => $paymentIntent->id,
                'IS_REST' => IS_REST
            ]);
            if($invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['InvoicePayment.transaction_id' => $paymentIntent->id]])){
                if ($paymentIntent->status == 'succeeded') {
                    if ($invoicePayment['InvoicePayment']['status'] != PAYMENT_STATUS_COMPLETED) {
                        $invoicePayment['InvoicePayment']['status'] = PAYMENT_STATUS_COMPLETED;
                        $this->InvoicePayment->save($invoicePayment, ['fieldList' => ['status'], 'validate' => false]);
                        $this->handleInvoicePaymentSuccess($invoicePayment);
                    }
                } elseif ($paymentIntent->status == 'payment_failed' || $paymentIntent->status == 'canceled'){
                    $invoicePayment['InvoicePayment']['status'] = PAYMENT_STATUS_FAILED;
                    $this->InvoicePayment->save($invoicePayment);
                } else {
                    $invoicePayment['InvoicePayment']['status'] = PAYMENT_STATUS_PENDING;
                    $this->InvoicePayment->save($invoicePayment);
                }
                echo 'Success';
                http_response_code(200);
                die();
            }else{
                echo "couldn't find payment";
            }
        }else{
            echo "Received unknown event type";
        }
        echo 'failed';
        http_response_code(400);
        die();
    }
}
