<?php
class EmailLogsController extends AppController {

	var $name = 'EmailLogs';

	/**
	 * @var EmailLog
	 */
	var $EmailLog;
	var $helpers = array('Html', 'Form');

	

	function owner_index() {
		$this->EmailLog->recursive = 0;
		$conditions = $this->_filter_params();
		$conditions['EmailLog.site_id'] = getAuthOwner('id');
		$this->paginate['EmailLog']['order'] = 'EmailLog.sent_date DESC';
		$this->set('emailLogs', $this->paginate('EmailLog', $conditions));
	}

	function owner_view($id = null) {
		
		$emailLog = $this->EmailLog->find(array('EmailLog.id' => $id));
		if (!$emailLog) {
			$this->flashMessage(__('Message not found', true));
			$this->redirect('/');
		}
		$this->set('emailLog', $emailLog);
	}
	
	public function _filter_params($params = false, $filters = [], $passedModelName = false) {
		$params = $this->params['url'];
		$conditions = array();
		
		if (!empty($params['client'])){
			$client = mysql_real_escape_string($params['client']);
			$conditions[] = "CONCAT_WS(' ', Client.business_name, Client.first_name, Client.last_name, Client.email) LIKE '%{$client}%'";
			unset($params['client']);
		}
		
		if (!empty($params['client_id'])){
			$conditions['EmailLog.client_id'] = $params['client_id'];
		}
		
		if (!empty($params['invoice_id'])){
			$conditions['EmailLog.invoice_id'] = $params['invoice_id'];
		}
		
		if (!empty($params['invoice'])){
			$no = mysql_real_escape_string($params['invoice']);
			$conditions['Invoice.no LIKE'] = "%$no%";
		}
		
		return am($conditions, parent::_filter_params($params));
	}
	
//	function owner_delete($id = null) {
//		if (empty($id) && !empty ($_POST['ids'])) {
//			$id = $_POST['ids'];
//		 } 
// 		if (!$id && empty ($_POST)) {
//			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('email log',true)));
//			$this->redirect(array('action'=>'index'));
//		}
//		$module_name= __('emailLog', true);
//		if(count($id) > 1){
//			$module_name= __('emailLogs', true);
//		 } 
//		$emailLogs = $this->EmailLog->find('all',array('conditions'=>array('EmailLog.id'=>$id)));
//		if (empty($emailLogs)){
//			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
//			$this->redirect($this->referer(array('action' => 'index'), true));
//		}
//		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
//			if ($_POST['submit_btn'] == 'yes' && $this->EmailLog->deleteAll(array('EmailLog.id'=>$_POST['ids']))) {
//				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
//				$this->redirect(array('action'=>'index'));
//			}
//			else{
//				$this->redirect(array('action'=>'index'));
//			}
//		}
//		$this->set('emailLogs',$emailLogs);
//	}
}
?>