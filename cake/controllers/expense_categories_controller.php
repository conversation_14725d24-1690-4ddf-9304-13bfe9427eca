<?php

/**
 * @property Expense $Expense
 */
class ExpenseCategoriesController extends AppController {

    var $name = 'ExpenseCategories';

    function owner_index($type = "1") {
        if (!check_permission (ADD_EXPENSE_CATEGORY )) {
            $this->flashMessage(__("You do not have sufficient permissions to access this page.", true));
            $this->redirect('/');
        }
        $ec_types=$this->ExpenseCategory->gettypes();
        if(!in_array($type, array_keys($ec_types))){
        $type=1;    
        }
        $conditions['ExpenseCategory.type'] = $type;
        $this->paginate['ExpenseCategory'] = array('conditions' => $conditions,
            'order' => array('ExpenseCategory.name'=>'asc'),
        );
        $expenseCategories = $this->paginate('ExpenseCategory');
        $this->setup_nav_data($expenseCategories);
        $this->set('all_cat', $expenseCategories);
        $this->set('ec_types', $ec_types);
        $this->set('list_type',$type);
        $this->set('title_for_layout',  $type == 1 ? __("Expense Categories",true) : __("Income Categories",true));
    }

    function owner_add($type = null) {
        if (!check_permission (ADD_EXPENSE_CATEGORY )) {
            $this->flashMessage(__("You do not have sufficient permissions to access this page.", true));
            $this->redirect('/');
        }
        if (!empty($this->data)) {
            $this->ExpenseCategory->create();
            $this->data['ExpenseCategory']['staff_id'] = getAuthOwner('staff_id');
            
            if ($this->ExpenseCategory->save($this->data)) {
                $this->add_actionline(ACTION_ADD_EXPENSECATEGORY, array('primary_id' => $this->ExpenseCategory->id, 'param1' => $this->data['ExpenseCategory']['name']));
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Category', true)), 'Sucmessage');
                $redirect = array('action' => 'index',$this->data['ExpenseCategory']['type']);
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Category', true)));
            }
        }else{
          $this->data['ExpenseCategory']['type']=$type;  
        }
        $this->set('ec_types', $this->ExpenseCategory->gettypes());
        $this->set('list_type',$type);
        $this->set('title_for_layout',  __('Add Category', true));
    }

    function owner_edit($id = null) {
        if (!check_permission (ADD_EXPENSE_CATEGORY )) {
            $this->flashMessage(__("You do not have sufficient permissions to access this page.", true));
            $this->redirect('/');
        }
        $cat = $this->ExpenseCategory->find(array('ExpenseCategory.id' => $id));
        if (!$cat) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Category', true)));
            $this->redirect('/');
        }
        
        if (!empty($this->data)) {

            $this->data['ExpenseCategory']['staff_id'] = getAuthOwner('staff_id');
            if ($this->ExpenseCategory->save($this->data)) {

                $this->add_actionline(ACTION_UPDATE_EXPENSECATEGORY, array('primary_id' => $this->ExpenseCategory->id, 'param1' => $this->data['ExpenseCategory']['name']));
                $this->flashMessage(sprintf(__('%s has been updated', true), __('Category', true)), 'Sucmessage');
                $redirect = array('action' => 'index',$this->data['ExpenseCategory']['type']);
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be updated. Please, try again', true), __('Category', true)));
            }
        } else {
            $this->data = $this->ExpenseCategory->read(null, $id);
        }
        $this->set('ec_types', $this->ExpenseCategory->gettypes());
        $this->set('title_for_layout',  __('Edit Category', true));
        $this->render('owner_add');
    }

    function owner_delete($id = null) {
        if (!check_permission (ADD_EXPENSE_CATEGORY )) {
            $this->flashMessage(__("You do not have sufficient permissions to access this page.", true));
            $this->redirect('/');
        }
        $site = getAuthOwner();

        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('terms', true)));
            $this->redirect(array('action' => 'index'));
        }
        
        $module_name = __('Category', true);

        $site_id = getAuthOwner('id');
        $expenses_category = $this->ExpenseCategory->find('all', array('conditions' => array('ExpenseCategory.id' => $id)));
  
        $this->loadModel('Expense');
        $expenses_count = $this->Expense->find('count', array('conditions' => array('Expense.expense_category_id' => $id)));
        
        if ($expenses_count > 0 and !empty($id)) {
            
            $gettypes=$this->ExpenseCategory->gettypes();
            $t=$expenses_category[0]['ExpenseCategory']['type'];
            $this->flashMessage(sprintf(__('You can only deleted %s with no %s', true), __(ucfirst($gettypes[$t]).' Categories',true),__($gettypes[$t],true)));
            
            $this->redirect($this->referer(array('action' => 'index',$expenses_category[0]['ExpenseCategory']['type']), true));
        }
        if (empty($expenses_category)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index',$expenses_category[0]['ExpenseCategory']['type']), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->ExpenseCategory->deleteAll(array('ExpenseCategory.id' => $_POST['ids']))) {
                $this->flashMessage(sprintf(__('%s have been deleted', true), ucfirst($module_name)), 'Sucmessage');
                $this->redirect(array('action' => 'index',$expenses_category[0]['ExpenseCategory']['type']));
            } else {
                $this->redirect(array('action' => 'index',$expenses_category[0]['ExpenseCategory']['type']));
            }
        }

        $this->set('title_for_layout',  __('Delete terms', true));

        $this->crumbs = array();

        $this->crumbs[1]['title'] = __('Documents', true);
        $this->crumbs[1]['url'] = array('action' => 'index', 'controller' => 'items');

        $this->crumbs[2]['title'] = __("Category", true);
        $this->crumbs[2]['url'] = array('action' => 'index');

        $this->crumbs[3]['title'] = $this->pageTitle;
        $this->crumbs[3]['url'] = '#';

        $this->set('expenses', $expenses_category);
    }

}

?>
