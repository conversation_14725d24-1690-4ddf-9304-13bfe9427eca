<?php

use App\Utils\TrackStockUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;
use Izam\Daftra\Common\Utils\PluginUtil;
use Izam\Daftra\Common\Utils\SettingsUtil;

App::import('Vendor', 'settings');
class SettingsController extends AppController {

    var $name = 'Settings';

    function owner_index2(){
        $this->Setting->applyBranch= ['onFind' => false, 'onSave' => false];
        $structure = $this->Setting->find('all',['order'=>'Setting.id desc','limit'=>100000] ) ;
        foreach ( $structure as $k => $arr ) {
            $structure[$k]['Setting']['value'] = substr(htmlspecialchars($arr['Setting']['value']) , 0 , 20) ;
        }
        $this->set ( 'structure' , $structure ) ;
    }

	function owner_list() {
		$this->loadModel('Setting');
		$this->Setting->applyBranch = ['onFind' => false, 'onSave' => false];
		if (!empty($_GET['key'])) {
			$structure = $this->Setting->find('all', ['conditions' => ['Setting.key' => $_GET['key']], 'order' => 'Setting.id desc', 'limit' => 100000]);
		} else {
			$structure = $this->Setting->find('all', ['conditions' => ['Setting.key' => $this->Setting->getAllowedKeysForAppVersions()], 'order' => 'Setting.id desc', 'limit' => 100000]);
		}
		foreach ($structure as $k => $arr) {
			$structure[$k]['Setting']['value'] = $arr['Setting']['value'];
		}
		die(json_encode($structure));
	}
    function owner_add(){
        if ( !empty($this->data )){
            $this->Setting->create ( ) ;
            $this->Setting->save($this->data);
            settings::removeCache($this->data['Setting']['plugin_id'] ) ;
            $this->flashMessage(sprintf(__('%s has been saved', TRUE) , $this->data['Settinfung']['key'] ) , 'Sucmessage' );
            $this->redirect(['action'=> 'index2']);
        } else {
            $site = getCurrentSite();
            $this->loadModel('SitePlugin');
            $plugins = $this->SitePlugin->find('list', array('fields' => 'plugin_id,plugin_id', 'conditions' => array('SitePlugin.active' => 1, 'SitePlugin.site_id' => $site['id'])));
            $plugins[0] = 0;
            $this->set('plugins', $plugins);
        }
    }
    function owner_edit($id = null){
        $settings_row = $this->Setting->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $id]] );
        if ( empty($settings_row)){
            $this->flashMessage(__('You have followed an incorrect link to this page', TRUE));
            $this->redirect ( '/');
        }
        if ( !empty($this->data )){
            $this->Setting->save($this->data);
            settings::removeCache($this->data['Setting']['plugin_id'] ) ;
            $this->flashMessage(sprintf(__('%s has been saved', TRUE) , $this->data['Setting']['key'] ) , 'Sucmessage' );
            $this->redirect(['action'=> 'index2']);
        }else {
            $site = getCurrentSite();
            $this->loadModel('SitePlugin');
            $plugins = $this->SitePlugin->find('list', array('fields' => 'plugin_id,plugin_id', 'conditions' => array('SitePlugin.active' => 1, 'SitePlugin.site_id' => $site['id'])));
            $plugins[0] = 0;
            $this->set('plugins', $plugins);

            $this->data = $settings_row ; 
        }
        $this->render ('owner_add') ;
    }
    function owner_delete ( $id ) {
        $setting = $this->Setting->find('first' , ['recursive' => -1 , 'conditions' => ['id' => $id] ]);
        $this->Setting->delete($id );
        settings::removeCache($setting['Setting']['plugin_id'] ) ;
        $this->flashMessage(sprintf (__('%s has been deleted', TRUE) , $setting['Setting']['key'] ) , 'Sucmessage' ,'secondaryMessage');
        $this->redirect(['action'=> 'index2']);
                
    }
    function owner_test() {
        
        if (!empty($this->data)) {
            settings::setData($this->data);
        }

        $this->set('form_data', settings::formData(InvoicesPlugin));
    }

    function owner_purchase_invoices() {
        if (!check_permission(Edit_General_Settings)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel('Post');
        if (!empty($this->data)) {

			unset($this->data[InventoryPlugin]['next_po_number']);
            settings::setData($this->data);
            $this->flashMessage(__('Purchase Invoice Settings has been saved', true), 'Sucmessage');
			$this->redirect('/v2/owner/entity/purchase_order/list');
        }
        $formdata =  ( settings::formData(InventoryPlugin)) ;
        $this->data[InventoryPlugin]['update_product_prices'] = $formdata['update_product_prices']['value'];
        $this->data[InventoryPlugin]['automatic_pay_po'] = $formdata['automatic_pay_po']['value'];
        $this->data[InventoryPlugin]['product_code'] = $formdata['product_code']['value'];
        $this->data[InventoryPlugin]['po_always_paid'] = $formdata['po_always_paid']['value'];
        $this->data[InventoryPlugin]['po_received'] = $formdata['po_received']['value'];
        $this->data[InventoryPlugin]['enable_purchase_request_manual_status'] = $formdata['enable_purchase_request_manual_status']['value'];
        $this->data[InventoryPlugin]['enable_quotation_request_manual_status'] = $formdata['enable_quotation_request_manual_status']['value'];
        $this->data[InventoryPlugin]['enable_purchase_quotation_manual_status'] = $formdata['enable_purchase_quotation_manual_status']['value'];
        $this->data[InventoryPlugin]['enable_purchase_order_manual_status'] = $formdata['enable_purchase_order_manual_status']['value'];
        $this->data[InventoryPlugin]['enable_purchase_manual_status'] = $formdata['enable_purchase_manual_status']['value'];
        $this->data[InventoryPlugin]['custom_journal_description'] = $formdata['custom_journal_description']['value'];
        $this->data[InventoryPlugin]['purchase_discount_option'] = $formdata['purchase_discount_option']['value'];
        $this->data[InventoryPlugin]['enable_purchases_adjustment'] = $formdata['enable_purchases_adjustment']['value'];
        $this->data[InventoryPlugin]['enable_purchase_credit_note'] = $formdata['enable_purchase_credit_note']['value'];

		App::import("Vendor", "AutoNumber");
        $this->data[InventoryPlugin]['next_po_number'] = AutoNumber::get_auto_serial(AutoNumber::TYPE_PURCHASE_INVOICE);
        $this->set('form_data', $formdata);
        $this->render('/settings/owner_purchase_orders');
    }
    function owner_inventory() { 
        if (!check_permission(Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->loadModel('Invoice');
        $this->loadModel('Product');
        $this->loadModel('Site');
        $this->loadModel('Store');
        $this->loadModel('GroupPrice');
        $trackedProductsCount = $this->Product->find('count',array('recursive' => -1 ,'conditions' => ['Product.track_stock ='=>'1','Product.tracking_type <>'=> TrackStockUtil::QUANTITY_ONLY]));
        $SettingsStructure=settings::getStructure();
        if (!empty($this->data)) {

            $this->data[InventoryPlugin]['disable_overdraft'] = (empty($this->data[InventoryPlugin]['disable_overdraft'])?1:0);
	        $this->data[InventoryPlugin]['allow_negative_bundle_products'] = (empty($this->data[InventoryPlugin]['allow_negative_bundle_products']) ? 0 : 1);
            if(!isset($this->data[InventoryPlugin]['enable_tracking_numbers']) && !empty($this->data[ProductTracking]['allow_negative_tracking'])){
                $this->data[InventoryPlugin]['enable_tracking_numbers'] = 1;
            }
            if(!empty($this->data[InventoryPlugin]['disable_overdraft']) || $this->data[InventoryPlugin]['enable_tracking_numbers'] === 0) {
                $this->data[ProductTracking][Settings::ALLOW_NEGATIVE_TRACKING] = 0;
            }
			if (!empty($this->data[InventoryPlugin]['allow_negative_bundle_products']) && $this->data[InventoryPlugin]['bundle_type'] == Settings::OPTION_BUNDLE_COMPOUND) {
				$this->data[InventoryPlugin]['allow_negative_bundle_products'] = 0;
			}
            foreach($this->data as $key=>$data) {
                foreach ($data as $data_key => $data_value) {
                    if($data_key=='enable_tracking_numbers' && !$trackedProductsCount){
                        $status = $data_value? 'true':'false';
                        $this->Site->owner_update_plugin(PRODUCT_TRACKING_PLUGIN,$status,false,false);
                    }
                    // This will cause non-exist values to be added to activity logs, not only changed values
					$current_value = settings::getValue($key,$data_key);
					$exist = $current_value !== null;
                    if ($current_value != $data_value || !$exist) {
                    $array['primary_id']=$key;
                    $array['secondary_id']=0;
                    $array['param1']=isset($SettingsStructure[$key][$data_key]['label'])?$SettingsStructure[$key][$data_key]['label']:__(Inflector::humanize($data_key),true);
                        if(isset($SettingsStructure[$key][$data_key]['options'])) {
                            $options=$SettingsStructure[$key][$data_key]['options'];
                            $array['param2'] = $options[$current_value];
                            $array['param3'] = $options[$data_value];
                        }else{
                            $array['param2'] = $current_value ?? 'Nothing';
                            $array['param3'] = $data_value;
                        }
                    $this->add_actionline(ACTION_UPDATE_INVENTORY_SETTINGS,$array);
                    setLastUpdatedAt(PluginUtil::InventoryPlugin, SettingsUtil::INVENTORY_SETTINGS_UPDATED_AT);

                    }
                }
            }
            settings::setData($this->data);
            $this->flashMessage(__('Product Settings have been saved', true), 'Sucmessage');
        }
        $formdata =  ( settings::formData(InventoryPlugin)) ;
        $this->set('tracking_cannot_be_deactivated',$trackedProductsCount);
        $this->loadModel('ItemPermission');
        $this->set ( 'store_list' , $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE , ItemPermission::PERMISSION_STOCK_UPDATING));
        $this->data[InventoryPlugin]['advanced_pricing_options'] = $formdata['advanced_pricing_options']['value'];
        $this->data[InventoryPlugin]['disable_overdraft'] = (empty($formdata['disable_overdraft']['value'])?1:0);
        $this->data[InventoryPlugin]['enable_multi_units'] = $formdata['enable_multi_units']['value'];
        $this->data[InventoryPlugin]['enable_bundles'] = $formdata['enable_bundles']['value'];
        $this->data[InventoryPlugin]['default_raw_store'] = $formdata['default_raw_store']['value'];
        $this->data[InventoryPlugin]['default_warehouse_for_branch'] = $formdata['default_warehouse_for_branch']['value'];
        $this->data[InventoryPlugin][Settings::ENABLE_STOCK_REQUESTS] = settings::getValue(InventoryPlugin, \Settings::ENABLE_STOCK_REQUESTS);
        $this->data[InventoryPlugin]['enable_requisitions'] = settings::getValue(InventoryPlugin, 'enable_requisitions');
        $this->data[InventoryPlugin]['enable_requisitions_po'] = settings::getValue(InventoryPlugin, 'enable_requisitions_po');
        $this->data[InventoryPlugin]['stock_transactions_default_journal_account_id'] = $formdata['stock_transactions_default_journal_account_id']['value'];
        $this->data[ProductTracking][Settings::ALLOW_NEGATIVE_TRACKING] = settings::getValue(ProductTracking, \Settings::ALLOW_NEGATIVE_TRACKING);
        $this->data[InventoryPlugin][Settings::CALCULATE_STOCKTAKING_BASED_ON_STOCKTAKING_DATE] = $formdata[Settings::CALCULATE_STOCKTAKING_BASED_ON_STOCKTAKING_DATE]['value'];
        $this->data[InventoryPlugin][Settings::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS] = $formdata[Settings::ENABLE_SHOW_BOTH_ON_HAND_AND_AVAILABLE_STOCK_OF_PRODUCTS]['value'];
        $this->data[InventoryPlugin][Settings::VALIDATE_STOCK_OF_PRODUCTS_BY_AVAILABLE] = $formdata[Settings::VALIDATE_STOCK_OF_PRODUCTS_BY_AVAILABLE]['value'];
        $this->data[InventoryPlugin]['default_price_list'] = settings::getValue(InventoryPlugin, 'default_price_list' , null , false , true);
        $this->data[InventoryPlugin][Settings::REFUND_RECEIPT_CALCULATION_METHOD] = settings::getValue(InventoryPlugin, Settings::REFUND_RECEIPT_CALCULATION_METHOD , null , false , true);
        if(IS_REST) {
            echo  json_encode([
                'success' => "successful",
                'code' => 200,
                'data' => $this->data[InventoryPlugin]
            ]);
            die;
         }
        $this->set('form_data', $formdata);
        $this->set('active_warehouses', $this->Store->get_store_list_transactions());
        $this->set('GroupPrices', $this->GroupPrice->getActiveList());
        $this->set('GroupPrices', $this->GroupPrice->getActiveList());

    }
	
	function owner_change_system_curr($new_curr)
	{
            if(!$new_curr)
                $new_curr = getCurrentSite ('currency_code');
		set_time_limit(99999999);
		if(!$new_curr)
			$new_curr = getCurrentSite ('currency_code');
		
		
		if(!empty($new_curr))
		{
			$this->loadModel('Currency');
			$new_curr = $this->Currency->findByCode($new_curr);
			if($new_curr)
			{
				
				$this->data[ExpensesPlugin]['journals_local_currency_code'] = $new_curr['Currency']['code'];
				settings::setData($this->data);
				$this->loadModel('Journal');
				$this->loadModel('StockTransaction');
				
				$id = 0 ;
				while ($journals = $this->Journal->find('all',arraY('conditions' => ['Journal.id > '.$id ],'limit' => 100,'order' => 'ID ASC')))
				{
					foreach($journals as $k => $journal)
					{
						$this->Journal->save_journal($journal,false,true);
						$id = $journal['Journal']['id'];
					}
				}
				$id = 0 ;
				$this->StockTransaction->average_on_all();
			
				
			}
		} 
		die('111');
	
	}
	
	
	
    
    
    function owner_statements ($type = null)
    {
        
        $this->loadModel('Site');
        if ( !empty ( $this->data ) ) {
            if(is_null($type) || $type == 'client'){
                settings::setValue(0, "transcation_columns_setting", $this->data ['Settings']['transcation_columns']);
                settings::setValue(0, "statement_header_html", $this->data ['Settings']['statement_header_html']);
                settings::setValue(0, "statement_footer_html", $this->data ['Settings']['statement_footer_html']);
            }else{
                settings::setValue(0, $type . "_transcation_columns_setting", $this->data ['Settings']['transcation_columns']);
                settings::setValue(0, $type . "_statement_header_html", $this->data ['Settings']['statement_header_html']);
                settings::setValue(0, $type . "_statement_footer_html", $this->data ['Settings']['statement_footer_html']);
            }
            if ( !empty( $_SESSION["origURL"] ) ){
                $origin = $_SESSION["origURL"];
                unset ($_SESSION["origURL"]);
                $this->redirect ($origin);die;
            }
        }else {
            if ( empty( $_SESSION["origURL"] ) && !empty ($_SERVER["HTTP_REFERER"]) ){
                $_SESSION["origURL"] = $_SERVER["HTTP_REFERER"];
            }
        }
//        $this->set ( 'client_placeholders', PlaceHolder::client_place_holder_list() ) ;
//        $this->set ( 'staff_placeholders', PlaceHolder::staff_place_holder(getAuthOwner('staff_id')) ) ;
//        $this->set ( 'site_placeholders', PlaceHolder::site_place_holder() ) ;
        $this->set ('transcation_columns_setting_options', [0 => __("Single",true), 1 => __("Amount / Payments",true)]);
        if(is_null($type) || $type == 'client'){
            $transcation_columns_setting = settings::getValue(0, "transcation_columns_setting", null, false);
            $this->data['Settings']['transcation_columns_setting'] = intval($transcation_columns_setting) ;
            $this->set('transcation_columns_setting', $transcation_columns_setting);
            $this->set('statement_header_html', settings::getValue(0, "statement_header_html", null, false));
            $this->set('statement_footer_html', settings::getValue(0, "statement_footer_html", null, false));
        }else{
            $transcation_columns_setting = settings::getValue(0, $type . "_transcation_columns_setting", null, false);
            $this->data['Settings']['transcation_columns_setting'] = intval($transcation_columns_setting) ;
            $this->set('transcation_columns_setting', $transcation_columns_setting);
            $this->set('statement_header_html', settings::getValue(0, $type . "_statement_header_html", null, false));
            $this->set('statement_footer_html', settings::getValue(0, $type . "_statement_footer_html", null, false));
        }
        $this->set('type', $type);
    }

    /*
     * map the client and workorder keys for using in the workorder interface
     */
    function workorder_mapping()
    {
        
        $this->loadModel('Setting');

        //supposed to get from the post
        $clients = [
            "client_first_name"=>"Ali",
            "client_last_name"=>"Moka",
            "client_some_field"=>"some_field",
            "client_address"=>"Address"
        ];

        //supposed to get from the post
        $workorders = [
            "workorder_first_name"=> "Optinal",
            "workorder_last_name"=> "Second",
            "workorder_some_field"=> "SomeField",
            "workorder_address"=> "Address"
        ];

        $clients_json = array_keys($clients);
        $workorders_json = array_keys($workorders);

        $json_array = [];
        for( $i = 0 ; $i < count($clients_json) ; $i++ )
        {
            $json_array[$clients_json[$i]] = $workorders_json[$i];
        }

        settings::setValue(0, 'client_workorder_mapping' , json_encode($json_array));

        $json = json_decode(settings::getValue(0, 'client_workorder_mapping' ));
        dd($json);

    }
	
	function owner_hidden_settings()
	{
		
		if (!empty($this->data)) {
//			$this->data[ClientsPlugin]['client_extra_fields'] = json_encode($this->data[ClientsPlugin]);
//			sdie(debug($this->data));
            settings::setData($this->data['settings']);
            $this->flashMessage(__('Hidden Settings has been saved', true), 'Sucmessage');
        }
		$formdata =  ( settings::formData(ClientsPlugin)) ;
		$this->data['settings'][ClientsPlugin]['focus_on_posts'] = settings::getValue(ClientsPlugin, 'focus_on_posts');
		$this->data['settings'][0]['SMS_ENABLE'] = settings::getValue(0, 'SMS_ENABLE');
		
//        $this->data[ClientsPlugin]['prevent_client_edit_profile'] = $formdata['prevent_client_edit_profile']['value'];
        $this->set('form_data', $formdata);
	}
    function owner_pos ()
    {
		if (!empty($_GET['branch_id']) && is_numeric($_GET['branch_id'])) {
			setRequestCurrentBranch($_GET['branch_id']);
		}
		$this->loadModel('PosShift');
        $this->pageTitle  = __('Point of Sale Settings', true);
        if (!empty($this->data)) {
            $this->data['settings'][PosPlugin]["pos_payment_cash"] = '1';
            if (
	            ($this->data['settings'][PosPlugin]["pos_accounting_settings"] != settings::getValue(PosPlugin, 'pos_accounting_settings',null,false) || $this->data['settings'][PosPlugin]["pos_enable_trackings_items"] != settings::getValue(PosPlugin, 'pos_enable_trackings_items',null,false))
	            &&  $this->PosShift->isThereAnyPosSessionOpen()
            ) {
	            $this->flashMessage(__('You cannot change in this setting while there’s an open session', TRUE));
	            $this->redirect(['action'=>'pos']);
            }

			if (!$this->data['settings'][PosPlugin]["pos_accounting_settings"]) {
	            $this->data['settings'][PosPlugin]["pos_partial_payment"] = 0;
	            $this->data['settings'][PosPlugin]["pos_enable_trackings_items"] = 0;
            }
            if (isset($this->data['settings'][PosPlugin]["categories"]) && !empty($this->data['settings'][PosPlugin]["categories"]))
                $this->data['settings'][PosPlugin]["categories"] = implode(',', $this->data['settings'][PosPlugin]["categories"]);
            if(settings::getValue(PosPlugin, 'pos_accounting_settings') != $this->data['settings'][PosPlugin]["pos_accounting_settings"]) {
                $array['primary_id']="pos_accounting_settings";
                $array['secondary_id']=0;
                $array['param1']=$this->data['settings'][PosPlugin]["pos_accounting_settings"];
                $this->add_actionline(ACTION_UPDATE_POS_ACCOUNTING_SETTINGS, $array);
            }
            if(settings::getValue(PosPlugin, 'pos_partial_payment') != $this->data['settings'][PosPlugin]["pos_partial_payment"]) {
                $array['primary_id']="pos_partial_payment";
                $array['secondary_id']=0;
                $array['param1']=$this->data['settings'][PosPlugin]["pos_partial_payment"];
                $this->add_actionline(ACTION_UPDATE_POS_PARTIAL_PAYMENT, $array);
            }
            settings::setData($this->data['settings']);
            $this->flashMessage(__('Settings have been saved', true), 'Sucmessage');
        }

        //Get Default Settings
	    $this->data['settings'][PosPlugin]['pos_default_payment'] = settings::getValue(PosPlugin, 'pos_default_payment');
        $this->data['settings'][PosPlugin]['pos_show_images'] = settings::getValue(PosPlugin, 'pos_show_images');
        $this->data['settings'][PosPlugin]['pos_default_layout'] = settings::getValue(PosPlugin, 'pos_default_layout', null, false);
        $this->data['settings'][PosPlugin]['pos_default_client'] = settings::getValue(PosPlugin, 'pos_default_client', null, false);
        $this->data['settings'][PosPlugin]['enable_multi_units'] = settings::getValue(InventoryPlugin, 'enable_multi_units');
        $this->data['settings'][PosPlugin]['enable_auto_print'] = settings::getValue(PosPlugin, 'enable_auto_print');
        $this->data['settings'][PosPlugin]['minimum_price_calculation'] = empty(settings::getValue(PluginUtil::SalesPlugin, 'minimum_price_calculation')) ? 'tax': settings::getValue(PluginUtil::SalesPlugin, 'minimum_price_calculation');
        $default_client_id = settings::getValue(PosPlugin, 'pos_default_client', null, false);
        if (!empty($default_client_id)) {
            $this->loadModel('Client');
            $client = $this->Client->find('all', ['conditions' => ['Client.id' => $default_client_id]]);
            $this->data['settings'][PosPlugin]['pos_default_client_data'] = $this->Client->processClients($client);
        }
        $this->data['settings'][PosPlugin]['pos_enable_num_pad'] = settings::getValue(PosPlugin, 'pos_enable_num_pad');
        $this->data['settings'][PosPlugin]['validate_pos_invoice_more_details'] = settings::getValue(PosPlugin, 'validate_pos_invoice_more_details');
        $this->data['settings'][PosPlugin]['pos_auto_adjustment'] = settings::getValue(PosPlugin, 'pos_auto_adjustment');
        $this->data['settings'][PosPlugin]['pos_partial_payment'] = settings::getValue(PosPlugin, 'pos_partial_payment');
        $this->data['settings'][PosPlugin]['pos_accounting_settings'] = settings::getValue(PosPlugin, 'pos_accounting_settings', null ,false);
	    $this->data['settings'][PosPlugin]['pos_enable_trackings_items'] = settings::getValue(PosPlugin, 'pos_enable_trackings_items');
	    $this->data['settings'][PosPlugin]['pos_profit_journal_account'] = settings::getValue(PosPlugin, 'pos_profit_journal_account');
        $this->data['settings'][PosPlugin]['pos_losses_journal_account'] = settings::getValue(PosPlugin, 'pos_losses_journal_account');
        $this->data['settings'][PosPlugin]['allowed_categories'] = settings::getValue(PosPlugin, 'allowed_categories', null ,false);
        $this->data['settings'][PosPlugin]['categories'] = explode(',', settings::getValue(PosPlugin, 'categories', null ,false));
        $this->data['settings'][PosPlugin]['default_price_group'] = settings::getValue(InventoryPlugin, 'default_price_list' , null , false , true);
        $this->data['settings'][PosPlugin]['pos_assign_sales_person_per_item'] = settings::getValue(PosPlugin, 'pos_assign_sales_person_per_item');
        $this->data['settings'][PosPlugin]['disable_shipping'] = settings::getValue(InvoicesPlugin, 'disable_shipping' , null , false , true);
        $this->data['settings'][PosPlugin]['not_selling_below_average_cost'] = settings::getValue(InvoicesPlugin, 'not_selling_below_average_cost');
        $this->data['settings'][PosPlugin]['enable_estimate'] = (
            !settings::getValue(InvoicesPlugin, 'disable_estimate_module' , null , false , true) &&
            (check_permission(ESTIMATES_VIEW_ALL_ESTIMATES) || check_permission(ESTIMATES_VIEW_HIS_OWN_ESTIMATES))
        );
        $this->data['settings'][PosPlugin]['enable_sales_order'] = (
            settings::getValue(InvoicesPlugin, 'enable_sales_order' , null , false , true) &&
            (check_permission(SALES_ORDER_VIEW_ALL_SALES_ORDER) || check_permission(SALES_ORDER_VIEW_HIS_OWN_SALES_ORDER))
        );
        $this->data['settings'][PosPlugin]['change_price_list'] = settings::getValue(InvoicesPlugin, 'change_price_list', null , false , true);
        $this->data['settings'][PosPlugin]['pos_enable_sales_adjustment'] = settings::getValue(PosPlugin, 'pos_enable_sales_adjustment');
        if($this->data['settings'][PosPlugin]['pos_auto_adjustment'] === null){
            $this->data['settings'][PosPlugin]['pos_auto_adjustment'] = true;
        }
        if($this->data['settings'][PosPlugin]['allowed_categories'] === null){
            $this->data['settings'][PosPlugin]['allowed_categories'] = 'all';
        }

        $this->data['settings'][PosPlugin]['can_view_all_clients'] = check_permission(Clients_View_All_Clients);
        $this->data['settings'][PosPlugin]['can_view_his_own_clients'] = check_permission(Clients_View_his_own_Clients);
        $this->data['settings'][PosPlugin]['can_view_all_products'] = check_permission(View_All_Products);

        $this->data['settings'][PosPlugin]['can_invoice_all_products'] = check_permission(View_All_Products);

        $staffId = getAuthOwner('staff_id');
        $this->data['settings'][PosPlugin]['pos_interface_settings'] = settings::getValue(PosPlugin, 'pos_interface_settings_' . $staffId, null , false , true);

        //Get Payment Methods
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'), true, true,false,true);
        if (in_array('client_credit', array_keys($paymentMethods))) {
            unset($paymentMethods['client_credit']);
        }
        if (!in_array('cash', array_keys($paymentMethods))) {
            $paymentMethods['cash'] = 'Cash';
        }
        $this->loadModel('Category');
        $this->set('categories', $this->Category->find('list', ['conditions'=> ['Category.category_type' => Category::CATEGORY_TYPE_PRODUCT]]));
        $this->set(compact('paymentMethods'));

        $settings['Settings'] = $this->data['settings'][PosPlugin];
        $settings['Settings']['paymentMethods'] = [['id' => 'cash', 'title' => $paymentMethods['cash'] ?: 'Cash']];
        $this->data['settings'][PosPlugin]["pos_payment_cash"] = '1';
        foreach ($paymentMethods as $key => $name) {
            $this->data['settings'][PosPlugin]["pos_payment_{$key}"] = settings::getValue(PosPlugin, "pos_payment_{$key}");
            if ($key == 'cash')
                continue;
            if (settings::getValue(PosPlugin, "pos_payment_{$key}") == 1)
                $settings['Settings']['paymentMethods'][] = ['id' => $key, 'title' => $name];
        }
        $settings['Settings']['paymentMethods'][] = ['id' => 'client_credit', 'title' => __('Client Credit', true)];
        $this->loadModel('InvoiceLayout');
        $this->set('layouts',$this->InvoiceLayout->find('list'));
        $journalAccount = GetObjectOrLoadModel('JournalAccount');
        $profit_account = $journalAccount->find('first', ['conditions' => ['JournalAccount.id' => $this->data['settings'][PosPlugin]['pos_profit_journal_account']]]);
        $debit_account = $journalAccount->find('first', ['conditions' => ['JournalAccount.id' => $this->data['settings'][PosPlugin]['pos_losses_journal_account']]]);
        if($profit_account) {
            $profit_account = array(
                'name' => $profit_account['JournalAccount']['code'] . ', ' . $profit_account['JournalAccount']['name'],
                'id' => $profit_account['JournalAccount']['id'],
                'details' => $profit_account['JournalAccount']['parent_cat_ids']
            );
        }
        if($debit_account) {
            $debit_account = array(
                'name' => $debit_account['JournalAccount']['code'] . ', ' . $debit_account['JournalAccount']['name'],
                'id' => $debit_account['JournalAccount']['id'],
                'details' => $debit_account['JournalAccount']['parent_cat_ids']
            );
        }


        $this->loadModel("InvoicePayment");
        $most_used_payments_gateways_pos_orderd = Set::extract('{n}.limited.payment_method', $this->InvoicePayment->query("SELECT payment_method, count(payment_method) as total FROM (SELECT payment_method FROM invoice_payments WHERE invoice_payments.payment_method NOT IN ('client_credit', 'starting_balance') order by date DESC LIMIT 100) as limited GROUP BY payment_method ORDER BY TOTAL DESC;", false));
        $payment_gateway = [];
        foreach ($most_used_payments_gateways_pos_orderd as $payment_gateway_name) {
            if (settings::getValue(PosPlugin,'pos_payment_' . $payment_gateway_name, false)) {
                $payment_gateway[] = $payment_gateway_name;
            }
            if (count($payment_gateway) == 2) {
                break;
            }
        }
        $this->loadModel("SitePaymentGateway");
        $frequently_used_payments = $this->SitePaymentGateway->find('all', ['fields' => ['id', 'payment_gateway', 'label'], 'conditions' => ['SitePaymentGateway.payment_gateway' => $payment_gateway, 'SitePaymentGateway.active' => 1]]) ?? [];
        switch (count($frequently_used_payments)) {
            case 1:
                $settings['Settings']['frequently_used'] = [$frequently_used_payments[0]['SitePaymentGateway']];
                break;
            case 2:
                $settings['Settings']['frequently_used'] = [$frequently_used_payments[0]['SitePaymentGateway'], $frequently_used_payments[1]['SitePaymentGateway']];
                break;
            default:
                $settings['Settings']['frequently_used'] = [];
                break;
        }

        $this->set("profit_account",$profit_account);
        $this->set("debit_account",$debit_account);
        if ( 2270462 == getCurrentSite('id') && 31 == getAuthOwner('staff_id')) {
            $logMsg = "Fetch POS SETTING FOR SITE  2270462 && STAFF 31 IN BRANCH " . getCurrentBranchID();
            \Rollbar\Rollbar::log(\Rollbar\Payload\Level::INFO , $logMsg,  $this->data);
        }
        if(IS_REST){
            $this->set('rest_item', $settings);
            $this->set('rest_model_name', "Settings");
            $this->render("view");
        }
	
	}
    function owner_sales ( )
    {
    
        $this->loadModel('Site');
        if ( !empty ( $this->data ) ) {
            if ( !empty ($this->data ['Settings']['enable_maximum_discount']) && !empty ( $this->data ['Settings']['maximum_discount_admin'] )&& ($this->data ['Settings']['maximum_discount_admin'] > 100 || $this->data ['Settings']['maximum_discount_admin'] < 1 )){
                $this->flashMessage(sprintf ( __("Value must be between %s",true) , "1-100" )) ;
            }else {
                settings::setValue(InventoryPlugin, "maximum_discount_admin", $this->data ['Settings']['maximum_discount_admin']);
                settings::setValue(InventoryPlugin, "enable_maximum_discount", $this->data ['Settings']['enable_maximum_discount']);
                $this->flashMessage(sprintf(__('%s  has been saved', true) , __("Sales Settings",true) ), 'Sucmessage') ;
                if ( !empty( $_SESSION["origURL"] ) ){
                    $origin = $_SESSION["origURL"];
                    unset ($_SESSION["origURL"]);
                    $this->redirect ($origin);die;
                }else {
                    $this->redirect ( ['settings' , 'sales']);
                }
            }
            
        }else {
            if ( empty( $_SESSION["origURL"] ) && !empty ($_SERVER["HTTP_REFERER"]) ){
                $_SESSION["origURL"] = $_SERVER["HTTP_REFERER"];
            }
        }
//        $this->set ( 'client_placeholders', PlaceHolder::client_place_holder_list() ) ;
//        $this->set ( 'staff_placeholders', PlaceHolder::staff_place_holder(getAuthOwner('staff_id')) ) ;
////        $this->set ( 'site_placeholders', PlaceHolder::site_place_holder() ) ;
//        $this->set ( 'statement_header_setting_options', [0=> __("Default Header",true),1 => __("Custom Header",true)] );
//        $statement_header_setting = settings::getValue(0, "statement_header_setting"  );
//        $this->data['Settings']['statement_header_setting'] = intval($statement_header_setting ) ;
//        $this->set ('statement_header_setting',$statement_header_setting  );
//        $this->set ('statement_header_html', settings::getValue(0, "statement_header_html"  ) );
        $maximum_discount_admin= settings::getValue(InventoryPlugin, "maximum_discount_admin"  );
        $enable_maximum_discount= settings::getValue(InventoryPlugin, "enable_maximum_discount"  );
        $this->set ('maximum_discount_admin', $maximum_discount_admin );
        $this->set ('enable_maximum_discount', $enable_maximum_discount );
        $this->data['Settings']['maximum_discount_admin'] = $maximum_discount_admin;
        $this->data['Settings']['enable_maximum_discount'] = $enable_maximum_discount;
    }
	
	public function owner_numbering($type=null) {
        if (!check_permission(PermissionUtil::Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
		if(is_null($type)) $this->redirect('/');
		App::import('Vendor','AutoNumber');
		$next_number = \AutoNumber::get_auto_serial($type, 0, true);
		$rule = \AutoNumber::get_rule($type);
		$next_number_options = [\AutoNumber::ContinousOverallPrefixes => __("Continue on last number", true), \AutoNumber::SeparatedForPrefixes => __("Create a separate serial for each prefix", true), \AutoNumber::ResetWhenPrefixChanges => __("Start from beginning", true)];
		$next_numbers = \AutoNumber::get_next_number_for_all_prefixes($type);
		$next_numbers[$next_number[0]] = $next_number[1];
		if(isset($next_numbers[''])) {
			$next_numbers["{{".__("empty", true)."}}"] = $next_numbers[''];
			unset($next_numbers['']);
		}
		foreach ($next_numbers as $key => $value) {
			$next_numbers[$key] = ['hidden_value'=>$value, 'value'=>$value];
		}
		if(empty($this->data)){
			$this->data = $rule;
			if(preg_match('/^([0-9]*)d$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=0;$this->data['digit_no']=intval($pattern_matches[1]);}
			elseif(preg_match('/^([0-9]*)x$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=1;$this->data['digit_no']=intval($pattern_matches[1]);}
			elseif(preg_match('/^([0-9]*)X$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=2;$this->data['digit_no']=intval($pattern_matches[1]);}
			elseif(preg_match('/^([0-9]*)c$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=3;$this->data['char_no']=intval($pattern_matches[1]);}
			elseif(preg_match('/^([0-9]*)C$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=4;$this->data['char_no']=intval($pattern_matches[1]);}
			elseif(preg_match('/^([0-9]*)c([0-9]*)d$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=5;$this->data['char_no']=intval($pattern_matches[1]);$this->data['digit_no']=intval($pattern_matches[2]);}
			elseif(preg_match('/^([0-9]*)C([0-9]*)d$/', $rule["number_pattern"], $pattern_matches)){$this->data['mode']=6;$this->data['char_no']=intval($pattern_matches[1]);$this->data['digit_no']=intval($pattern_matches[2]);}
		} else {
			$errors = [];
			switch ($this->data['mode']) {
				case 0:
					$regex = '/^[0-9]{'.intval($this->data['digit_no']).',}$/';
					$role = intval($this->data['digit_no']).'d';
                    $mode = "numeric digits";
					break;
				case 1:
					$regex = '/^[0-9a-f]{'.intval($this->data['digit_no']).',}$/';
					$role = intval($this->data['digit_no']).'x';
                    $mode = "lowercase hex numbers";
					break;
				case 2:
					$regex = '/^[0-9A-F]{'.intval($this->data['digit_no']).',}$/';
					$role = intval($this->data['digit_no']).'X';
                    $mode = "uppercase hex numbers";
					break;
				case 3:
					$regex = '/^[a-z]{'.intval($this->data['char_no']).',}$/';
					$role = intval($this->data['char_no']).'c';
                    $mode = "lowercase letters";
					break;
				case 4:
					$regex = '/^[A-Z]{'.intval($this->data['char_no']).',}$/';
					$role = intval($this->data['char_no']).'C';
                    $mode = "uppercase letters";
					break;
				case 5:
					$regex = '/^[a-z]{'.intval($this->data['char_no']).'}[0-9]{'.intval($this->data['digit_no']).',}$/';
					$role = intval($this->data['char_no']).'c'.intval($this->data['digit_no']).'d';
                    $mode = "lowercase letters followed by numeric digit";
					break;
				case 6:
					$regex = '/^[A-Z]{'.intval($this->data['char_no']).'}[0-9]{'.intval($this->data['digit_no']).',}$/';
					$role = intval($this->data['char_no']).'C'.intval($this->data['digit_no']).'d';
                    $mode = "uppercase letters followed by numeric digits";
					break;
				default:
					$errors['mode'] = __("Invalid pattern mode", true);
			}
			$values = json_decode($this->data['values'], true);
			if($this->data['prefix_serial_option']== \AutoNumber::SeparatedForPrefixes){
				foreach ($values as $key => $value) {
					if(!preg_match($regex, $value['value'])){
						$errors['old_prefix'] = __("One of the prefixes contain invalid number", true);
						break;
					}
				}
			} elseif($this->data['prefix_serial_option']== \AutoNumber::ContinousOverallPrefixes || $this->data['prefix_serial_option']== \AutoNumber::ResetWhenPrefixChanges) {
				$key = $this->data['old_prefix'];
				$value = $values[$key];
				if(!preg_match($regex, $value['value'])){
					$errors['old_prefix'] = __("Number must match the format chosen", true);
				}
			} else {
				$errors['prefix_serial_option'] = __("Invalid prefix option", true);
			}
			if(empty($errors)){
				\AutoNumber::set_rule($type, $role,0, $this->data["prefix_format"], $this->data['prefix_serial_option'], $this->data['require_unique']);
				if($this->data['prefix_serial_option']== \AutoNumber::SeparatedForPrefixes){
					foreach ($values as $key => $value) {
						if($value['hidden_value'] !== $value['value']){
							\AutoNumber::set_last_serial([$key!=="{{".__("empty", true)."}}"?$key:'', $value['value']], $type);
						}
					}
				} else {
					$eval_prefix = \AutoNumber::eval_prefix($this->data["prefix_format"]);
					$key = $this->data['old_prefix'];
					$value = $values[$key];
					if("{{".__("empty", true)."}}" == $key) $key = "";
					if($eval_prefix !== $key){
						\AutoNumber::set_last_serial([$eval_prefix, $this->data['next_number']], $type);
					} else {
						if($value['hidden_value'] !== $value['value']){
							\AutoNumber::set_last_serial([$key, $value['value']], $type);
						}
					}
					
				}
                $constants = \AutoNumber::getOptions();
                if(strpos($rule['number_pattern'], 'C') && strpos($rule['number_pattern'], 'd')){
                    $oldMode = "uppercase letters followed by numeric digits";
                    $oldCharCount = substr($rule['number_pattern'], 0, -3);
                    $oldDigitCount = substr($rule['number_pattern'], 2, -1);
                }elseif(strpos($rule['number_pattern'], 'c') && strpos($rule['number_pattern'], 'd')){
                    $oldMode = "lowercase letters followed by numeric digit";
                    $oldCharCount = substr($rule['number_pattern'], 0, -3);
                    $oldDigitCount = substr($rule['number_pattern'], 2, -1);
                }elseif(strpos($rule['number_pattern'], 'C')){
                    $oldMode = "uppercase letters";
                    $oldCharCount = substr($rule['number_pattern'], 0, -1);
                }elseif(strpos($rule['number_pattern'], 'c')){
                    $oldMode = "lowercase letters";
                    $oldCharCount = substr($rule['number_pattern'], 0, -1);
                }elseif(strpos($rule['number_pattern'], 'X')){
                    $oldMode = "uppercase hex numbers";
                    $oldDigitCount = substr($rule['number_pattern'], 0, -1);
                }elseif(strpos($rule['number_pattern'], 'x')){
                    $oldMode = "lowercase hex numbers";
                    $oldDigitCount = substr($rule['number_pattern'], 0, -1);
                }elseif(strpos($rule['number_pattern'], 'd')){
                    $oldMode = "numeric digits";
                    $oldDigitCount = substr($rule['number_pattern'], 0, -1);
                }
                if($value['value'] != $value['hidden_value']) {
                    $array['primary_id']="autonumber_".$constants[$type]['label']."_settings";
                    $array['secondary_id']=0;
                    $array['param1'] = $value['value'];
                    $array['param2'] = $value['hidden_value'];
                    $array['param3'] = $constants[$type]['label'];
                    $this->add_actionline(ACTION_UPDATE_AUTONUMBER_SETTINGS, $array);
                }
                if($this->data['require_unique'] != $rule['require_unique']){
                    $array['primary_id']="autonumber_".$constants[$type]['label']."_settings";
                    $array['secondary_id']=0;
                    $array['param1'] = $this->data['require_unique'];
                    $array['param2'] = $rule['require_unique'];
                    $array['param3'] = $constants[$type]['label'];
                    $this->add_actionline(ACTION_UPDATE_AUTONUMBER_SETTINGS_UNIQUE_STATUS, $array);
                }
                if($oldMode != $mode){
                    $array['primary_id']="autonumber_".$constants[$type]['label']."_settings";
                    $array['secondary_id']=0;
                    $array['param1'] = $mode;
                    $array['param2'] = $oldMode;
                    $array['param3'] = $constants[$type]['label'];
                    $this->add_actionline(ACTION_UPDATE_AUTONUMBER_SETTINGS_PATTERN, $array);
                }elseif($rule['number_pattern'] != $role){
                    if(isset($oldCharCount) && $this->data['char_no'] != $oldCharCount){
                        $array['primary_id']="autonumber_".$constants[$type]['label']."_settings";
                        $array['secondary_id']=0;
                        $array['param1'] = $this->data['char_no'];
                        $array['param2'] = $oldCharCount;
                        $array['param3'] = $constants[$type]['label'];
                        $this->add_actionline(ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_CHARACTERS, $array);
                    }
                    if(isset($oldDigitCount) && $this->data['digit_no'] != $oldDigitCount){
                        $array['primary_id']="autonumber_".$constants[$type]['label']."_settings";
                        $array['secondary_id']=0;
                        $array['param1'] = $this->data['digit_no'];
                        $array['param2'] = $oldDigitCount;
                        $array['param3'] = $constants[$type]['label'];
                        $this->add_actionline(ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_DIGITS, $array);
                    }
                }
                if($this->data['old_prefix'] != $this->data['prefix_format'] && !empty($this->data['prefix_format'])){
                    $array['primary_id']="autonumber_".$constants[$type]['label']."_settings";
                    $array['secondary_id']=0;
                    $array['param1'] = $this->data['prefix_format'];
                    $array['param2'] = $this->data['old_prefix'];
                    $array['param3'] = $constants[$type]['label'];
                    $this->add_actionline(ACTION_UPDATE_AUTONUMBER_SETTINGS_PREFIX, $array);
                }

				$this->flashMessage(__('Numbering format has been saved', true), 'Sucmessage');

				$this->redirect($this->here);
			} else {
				$this->set('errors', $errors);
			}
		} 
		$this->set('type', $type);
		$this->set('rule', $rule);
		$this->set('next_number', $next_number);
		$this->set('next_numbers', $next_numbers);
		$this->set('next_number_options', $next_number_options);
	}
	
	public function owner_numbering_eval_prefix() {
		App::import('Vendor','AutoNumber');
		echo \AutoNumber::eval_prefix($this->data['prefix_format']);
		exit;
	}
	
	public function owner_invoices_journals(){
		if (!userHavePermissionToAddInvoice()) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
        $site = getAuthOwner();
        $this->set('form_data', settings::formData(AccountingPlugin));
        if (!empty($this->data)) {
            
//				die(debug($this->data));
                if (isset($this->data['disable_invoice_auto_journal']) && ($this->data['disable_invoice_auto_journal'] !== "")){
                    settings::setValue(AccountingPlugin, 'disable_invoice_auto_journal', $this->data['disable_invoice_auto_journal']);
                }


                $this->flashMessage(__('Your settings have been updated', true), 'Sucmessage');
//                $this->redirect(array('action' => 'settings'));
           
        } else {
			
            $this->data['disable_invoice_auto_journal'] = settings::getValue(AccountingPlugin, 'disable_invoice_auto_journal');
        }
    }

	
	public function owner_expenses_journals(){
        //todo remove this function
        $this->redirect('/');
    }

    /**
     * Deprecated action left for redirecting only
     * the new action is clients => settings
     */
	function owner_clients() {
        file_put_contents("deprecated.txt", "owner_clients action is deprecated, called from $_SERVER[HTTP_HOST]$_SERVER[HTTP_REFERER]".PHP_EOL, FILE_APPEND);
        $this->redirect(['controller'=>'clients','action'=>'settings']);
    }
	
	/**
	 * Deprecated action left for redirecting only
	 * the new action is clients => settings
	 */
	public function owner_client_system_settings(){
		file_put_contents("deprecated.txt", "owner_client_system_settings action is deprecated, called from $_SERVER[HTTP_HOST]$_SERVER[HTTP_REFERER]".PHP_EOL, FILE_APPEND);
		$this->redirect(['controller'=>'clients','action'=>'settings']);
    }
	/**
	 * Deprecated action left for redirecting only
	 * the new action is clients => permission_settings
	 */
	public function owner_client_dashboard_settings(){
		file_put_contents("deprecated.txt", "owner_client_dashboard_settings action is deprecated, called from $_SERVER[HTTP_HOST]$_SERVER[HTTP_REFERER]".PHP_EOL, FILE_APPEND);
		$this->redirect(['controller'=>'clients','action'=>'permission_settings']);
    }

    public function api_view($plugin_id) {
        return $this->{"owner_$plugin_id"}();
    }

    public function owner_product_settings() {

        App::import('Vendor', 'barcode_gen/bootstrap');

        if( ! empty($this->data) )
        {
            settings::setValue(ProductsPlugin , 'barcode_type', $this->data[ProductsPlugin]['barcode_type']);
            settings::setValue(ProductsPlugin , 'embedded_barcode', $this->data[ProductsPlugin]['embedded_barcode']);
            settings::setValue(ProductsPlugin , 'embedded_barcode_format', $this->data[ProductsPlugin]['embedded_barcode_format']);
            settings::setValue(ProductsPlugin , 'weight_unit_divider', $this->data[ProductsPlugin]['weight_unit_divider']);
            settings::setValue(ProductsPlugin , 'currency_divider', $this->data[ProductsPlugin]['currency_divider']);

            $this->flashMessage(__('Products settings have been saved', TRUE) , 'Sucmessage' );
            $this->redirect(['action'=> 'product_settings']);

        }


        $generatorJPG = new Picqer\Barcode\BarcodeGeneratorJPG();

        $product_types = [
            $generatorJPG::TYPE_CODE_128 => __("Code 128",true),
            $generatorJPG::TYPE_EAN_13 => __("EAN 13", true)
        ];

        $product_settings = settings::getPluginValues(ProductsPlugin);

        $this->set('product_settings', $product_settings);
        $this->set('product_types', $product_types);
    }

    public function owner_accounts_routing($groupEntityName = null){
        if (!check_permission(Edit_General_Settings)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
	    $this->loadModel('JournalAccountRoute');
        $this->loadModel('JournalAccount');
        $entitiesData = [];
        $entities = $this->JournalAccountRoute->getRelatedEntities($groupEntityName);
        if(!$entities)
        {
            $this->flashMessage(sprintf(__('Wrong entity type',true)));
            $this->redirect('/');
        }
        if(!empty($this->data))
        {
            if($this->JournalAccountRoute->saveRoutingTypeSettings($this->data)){
                $this->flashMessage(__('Account routing has been saved', TRUE) , 'Sucmessage' );
            }else{
                $this->flashMessage(__("Account routing Couldn't be saved", TRUE)  );
            }
        }

        $this->loadModel('JournalAccount');
        foreach($entities as $k => $entityType)
        {
            $this->JournalAccountRoute->setRoutingEntityData($entityType);
            $journalAccountEntityData = JournalAccountRoute::$journalAccountsRoutingData[$entityType];
            $entityTitle = $journalAccountEntityData['name'];

            $routingType = $this->JournalAccountRoute->getEntityAccountRoutingType();
            $routedAccount = $this->JournalAccountRoute->getRoutedAccount(0, true);
            $routingOptions = $this->JournalAccountRoute->getRoutingOptions();
            $defaultAccountId = null;
            switch($entityType) {
                case Journal::ADJUSTMENT_ACCOUNT_ENTITY_TYPE:
                    $this->loadModel('Journal');
                    $conditions = [
                        'JournalAccount.entity_type' => Journal::DISCOUNT_ALLOWED_ENTITY_TYPE,
                        'JournalAccount.entity_id' => 0
                        ];
                    $defaultAccount = $this->JournalAccount->find('first', ['conditions' => $conditions]);
                    if(!$defaultAccount) {
                       $defaultAccount['JournalAccount']['id'] =  $this->Journal->create_auto_account(['entity_type' => 'discount_allowed', 'entity_id' => 0]);
                    }
                    if($defaultAccount) {
                        $defaultAccountId = $defaultAccount['JournalAccount']['id'];
                        if($routedAccount['JournalAccount']['entity_type'] == "adjustment") {
                            $routedAccount = [$defaultAccount['JournalAccount']];
                        }
                    }
                break;
                case Journal::PURCHASES_ADJUSTMENT_ACCOUNT_ENTITY_TYPE:
                    $this->loadModel('Journal');
                    $conditions = [
                        'JournalAccount.entity_type' => Journal::DISCOUNT_RECEIVED_ENTITY_TYPE,
                        'JournalAccount.entity_id' => 0
                        ];
                    $defaultAccount = $this->JournalAccount->find('first', ['conditions' => $conditions]);
                    if(!$defaultAccount) {
                       $defaultAccount['JournalAccount']['id'] =  $this->Journal->create_auto_account(['entity_type' => 'discount_received', 'entity_id' => 0]);
                    }
                    if($defaultAccount) {
                        $defaultAccountId = $defaultAccount['JournalAccount']['id'];
                        if($routedAccount['JournalAccount']['entity_type'] == "purchases_adjustment") {
                            $routedAccount = [$defaultAccount['JournalAccount']];
                        }
                    }
                break;
            }
            $entitiesData[] = [
                'account_type' => $journalAccountEntityData['account_type'],
                'entity_title' => $entityTitle,
                'routing_type' => $routingType,
                'entity_name' => $this->JournalAccountRoute->routingEntityname,
                'routed_account' => $routedAccount,
                'routing_options' => $routingOptions,
                'default_account' => $defaultAccountId
            ];
        }
        $this->set('entitiesData', $entitiesData);
    	$this->set('entityName', $groupEntityName);

    }

    public function owner_staff_routing()
    {
        if (!in_array(90, get_plugin_array()))
            $this->redirect('/');

        if(!empty($this->data['staff_account_routing']))
        {
            Settings::setValue(HRM_PAYROLL_PLUGIN, 'staff_account_routing', $this->data['staff_account_routing']);
            $this->flashMessage(__('Staff account routing has been saved', TRUE) , 'Sucmessage' );
        }
       $value = Settings::getValue(HRM_PAYROLL_PLUGIN, 'staff_account_routing');
       $this->set('value', $value);
    }



    public function owner_cheques_routing()
    {
        if (!in_array(ChequeCyclePlugin, get_plugin_array()))
            $this->redirect('/');

        if(!empty($this->data['payable_notes_account_routing']))
        {
            Settings::setValue(ChequeCyclePlugin, 'payable_notes_account_routing', $this->data['payable_notes_account_routing']);
            $this->flashMessage(__('Routing has been saved', TRUE) , 'Sucmessage' );
        }
        if(!empty($this->data['receivable_notes_account_routing']))
        {
            Settings::setValue(ChequeCyclePlugin, 'receivable_notes_account_routing', $this->data['receivable_notes_account_routing']);
            $this->flashMessage(__('Routing has been saved', TRUE) , 'Sucmessage' );
        }
       $value = Settings::getValue(ChequeCyclePlugin, 'payable_notes_account_routing');
       $this->set('payable_value', $value);
       $this->set('title', __('Cheques account routing',true));
       $this->set('settingsTitle', __('Payable cheque account',true));

       $value = Settings::getValue(ChequeCyclePlugin, 'receivable_notes_account_routing');
       $this->set('receivable_value', $value);
    }

    function owner_remove_cache_all()
    {
        //LAZY CODE
        for($i=0;$i<1000;$i++)
            settings::removeCache($i) ;
        $this->autoRender=false;
        $this->layout=false;
    }

    /**
     * this function is used by portal to save data into website settings table
     * @param $key
     * @param string $op
     * @param string $value
     */
    function remote_settings($key,$op="read",$value=""){
	    if($_GET['portal_key']=="78cde2917b000740fb0ce22cbdf28e4e8e10cb6c"){
	        if($op=="read"){
                echo json_encode(['status'=>true,"value"=>settings::getValue(0,$key)]);
            }else {
                settings::setValue(0, $key, $value);
            }
            echo json_encode(['status'=>true]);
            die();
        }else{
            echo json_encode(['status'=>false]);
            die();
        }

    }

    protected function getBlockedPageMessage()
    {
        switch ($this->params['url']['url']) {
            case 'api2/settings/pos':
                return __("You have been blocked from accessing some required features to run POS, please contact the account admin", true);
            default:
                return parent::getBlockedPageMessage(); // TODO: Change the autogenerated stub
        }
    }

    public function owner_export() {
        $this->set('title_for_layout',  __('Export Settings', true));
        include(dirname(__FILE__) . DS . 'setting_export.php');
    }

    public function owner_accounting_general()
    {
        $this->layout = "";
        if(!check_permission(Edit_General_Settings)) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if($this->RequestHandler->isPost()) {
            settings::setValue(AccountingPlugin, "tax_in_journals", ($_POST['tax_in_journals']));
            settings::setValue(AccountingPlugin, "display_cost_centers_in_journals", ($_POST['display_cost_centers_in_journals']));
            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL, (isset($_POST[SettingsUtil::ENABLE_TAGS_IN_JOURNAL]) && $_POST[SettingsUtil::ENABLE_TAGS_IN_JOURNAL] == "1" ));
            settings::setValue(AccountingPlugin, SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES, ($_POST[SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES]));
            
            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_INVOICE_ITEM_JOURNAL_ACCOUNT, ($_POST[SettingsUtil::ENABLE_INVOICE_ITEM_JOURNAL_ACCOUNT]));
            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT, ($_POST[SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT]));
            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_JOURNAL_ACCOUNT, ($_POST[SettingsUtil::ENABLE_REQUISITION_ITEM_JOURNAL_ACCOUNT]));

            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION, ($_POST[SettingsUtil::ENABLE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION]));
            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION, ($_POST[SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION]));
            settings::setValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION, ($_POST[SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION]));

            $this->flashMessage(sprintf(__('%s has been saved', TRUE) , __('Settings', true) ) , 'success' );
        }
        $taxInJournals = settings::getValue(AccountingPlugin, 'tax_in_journals');
        $displayCostCentersInJournals = settings::getValue(AccountingPlugin, 'display_cost_centers_in_journals');
        $tagsInJournal = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_TAGS_IN_JOURNAL);
        $updateJournalEntriesCurrencyRates = settings::getValue(AccountingPlugin, SettingsUtil::UPDATE_JOURNAL_ENTRIES_CURRENCY_RATES);

        $enableInvoiceItemJournalAccount = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_INVOICE_ITEM_JOURNAL_ACCOUNT);
        $enablePurchaseInvoiceItemJournalAccount = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_JOURNAL_ACCOUNT);
        $enableRequisitionItemJournalAccount = settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_JOURNAL_ACCOUNT);

        $enableInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
        $enablePurchaseInvoiceItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_PURCHASE_INVOICE_ITEM_COST_CENTER_DISTRIBUTION);
        $enableRequisitionItemCostCenterDistribution = Settings::getValue(AccountingPlugin, SettingsUtil::ENABLE_REQUISITION_ITEM_COST_CENTER_DISTRIBUTION);

        $this->setDefaultViewData();
        $this->view = 'izam';
        $this->set('taxInJournals', $taxInJournals);
        $this->set('display_cost_centers_in_journals', $displayCostCentersInJournals);
        $this->set(SettingsUtil::ENABLE_TAGS_IN_JOURNAL, $tagsInJournal);
        $this->set("updateJournalEntriesCurrencyRates", $updateJournalEntriesCurrencyRates);
        $this->set("enableInvoiceItemJournalAccount", $enableInvoiceItemJournalAccount);
        $this->set("enablePurchaseInvoiceItemJournalAccount", $enablePurchaseInvoiceItemJournalAccount);
        $this->set("enableRequisitionItemJournalAccount", $enableRequisitionItemJournalAccount);

        $this->set("enableInvoiceItemCostCenterDistribution", $enableInvoiceItemCostCenterDistribution);
        $this->set("enablePurchaseInvoiceItemCostCenterDistribution", $enablePurchaseInvoiceItemCostCenterDistribution);
        $this->set("enableRequisitionItemCostCenterDistribution", $enableRequisitionItemCostCenterDistribution);

        $this->set('title_for_layout', __('Accounting General Settings', true));
        $this->render('accounting/general-settings');
    }

    public function owner_pos_interface_settings()
    {
        if (isset($this->data['pos_interface_settings'])) {
            $staffId = getAuthOwner('staff_id');
            settings::setValue(PosPlugin, 'pos_interface_settings_' . $staffId, $this->data['pos_interface_settings']);
            echo json_encode(['status' => true]);
            die();
        }
        echo json_encode(['status' => false]);
        die();
    }
}
?>
