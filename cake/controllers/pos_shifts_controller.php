<?php

use App\Helpers\CurrencyHelper;
use App\Helpers\UsersHelper;
use App\Services\Report\ReportFactory;
use App\Services\Report\ReportUtil;
use Izam\Daftra\Common\Utils\PermissionUtil;

class PosShiftsController extends AppController {

    var $name = 'PosShifts';
    var $components = ['BranchValidation'];
    /**
     * @var PosShift
     */
    var $PosShift;
    var $helpers = array('Html', 'Form');

    function owner_index() {
        $this->set('title_for_layout',  __('POS Sessions',true));
        if(!check_permission(VIEW_OWN_POS_SESSIONS)){
            $this->cakeError('error401');
        }
        $conditions = ['PosShift.staff_id' => getAuthOwner('staff_id')];
        if(check_permission(VIEW_ALL_POS_SESSIONS)){
            $conditions = [];
        }
        $this->loadModel('ItemPermission');
        $availableStores = array_keys($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING, null, true));
        $conditions['PosShift.warehouse_id'] = $availableStores;
        $conditions = array_merge($conditions, $this->_filter_params());
        $this->PosShift->recursive = 0;
        $this->paginate['PosShift']['order'] = 'PosShift.open_time DESC, PosShift.id DESC';
        $posShifts = $this->paginate('PosShift', $conditions);
        $this->loadModel('Invoice');
        $this->loadModel('InvoicePayment');
        $this->Invoice->recursive = -1;
        //Add new join with invoice payments to guarantee data integrity with payments to match the pos summaries
        $this->InvoicePayment->unbindModel(['belongsTo' => ['Staff', 'Client']], false);
        $shift_ids = array_map(function($shift){return $shift['PosShift']['id'];}, $posShifts);
        $siteDefaultCurrencyCode = $this->InvoicePayment->get_default_currency();
        $precision = getAllowedNumberOfDigitsAfterFraction($siteDefaultCurrencyCode) ?? 2;
        $shiftsSalesResult = $this->InvoicePayment->find('all',[['joins' => [['table' => 'Invoice', 'type'=> 'inner' , 'conditions' => ['InvoicePayment.invoice_id = Invoice.id']]]],'conditions' => ['not' => ['Invoice.pos_shift_id' => null],'Invoice.type' => Invoice::Invoice, 'InvoicePayment.pos_shift_id' => $shift_ids],'fields' => ["SUM(ROUND(amount, $precision)) as total_sale",'pos_shift_id','currency_code'],'group' => ['InvoicePayment.pos_shift_id']]);
        $shiftsRefundResult = $this->InvoicePayment->find('all',[['joins' => [['table' => 'Invoice', 'type'=> 'inner' , 'conditions' => ['InvoicePayment.invoice_id = Invoice.id']]]],'conditions' => ['not' => ['Invoice.pos_shift_id' => null],'Invoice.type' => Invoice::Refund_Receipt, 'InvoicePayment.pos_shift_id' => $shift_ids],'fields' => ["SUM(ROUND(amount, $precision)) as total_refund",'pos_shift_id','currency_code'],'group' => ['InvoicePayment.pos_shift_id']]);

        $shiftsTotals = [];

        foreach ($shiftsSalesResult as $shiftsTotal){
            $shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']] = $shiftsTotal[0];
            $shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']]['currency'] = $shiftsTotal['InvoicePayment']['currency_code'];
        }
        foreach ($shiftsRefundResult as $shiftsTotal){
            if(!$shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']])
                $shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']] = [];
            $shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']] = array_merge($shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']],$shiftsTotal[0]);
            $shiftsTotals[$shiftsTotal['InvoicePayment']['pos_shift_id']]['currency'] = $shiftsTotal['InvoicePayment']['currency_code'];
        }

        $this->set(compact('posShifts','shiftsTotals'));
        $this->set('staff_id', getAuthOwner('staff_id'));
        $this->set('posShiftStatuses',$this->PosShift->getStatuses());
        $this->set('staffs',$this->PosShift->Staff->getList());
        $shifts = $this->PosShift->Shift->find('list',['conditions' => ['Shift.category_type' => Category::CATEGORY_TYPE_Shift]]);
        $this->PosShift->PosDevice->applyBranch['onFind'] = false;
        $pos = $this->PosShift->PosDevice->find('list',['conditions' => ['PosDevice.category_type' => Category::CATEGORY_TYPE_POS_Device]]);
        $this->PosShift->PosDevice->applyBranch['onFind'] = true;
        $this->set(compact('shifts','pos'));
        $this->set('statuses',[PosShift::STATUS_OPENED => __('Opened',true),PosShift::STATUS_CLOSED => __('Closed',true),PosShift::STATUS_VALIDATED => __('Validated',true)]);
    }


    function owner_view($id = null) {
        ini_set('memory_limit', '2G');
        $this->set('title_for_layout',  __('View Session Details',true));
        if(!check_permission(VIEW_OWN_POS_SESSIONS)){
            $this->cakeError('error401');
        }

        if (!$id) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('POS Session', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $this->PosShift->unbindModel(["hasMany"=>['Invoice']], false);
        $shift = $this->PosShift->read(null, $id);

        if(!check_permission(VIEW_ALL_POS_SESSIONS) && $shift['PosShift']['staff_id'] != getAuthOwner('staff_id')) {
            $this->cakeError('error401');
        }
        if(!$shift) {
            $this->flashMessage(__(sprintf (__('Invalid %s', true), __('POS Session', true)),true));
            $this->redirect(array('action'=>'index'));
        }
        $isCalculatedPerInvoice = $shift['PosShift']['is_calculated_per_invoice'] = $this->PosShift->checkPosShiftIsCalculatedPerInvoice($shift);
        $this->PosShift->bindModel(['hasMany' => ['Invoice']]);
        $this->BranchValidation->validatePosBranch($shift);
        $this->loadModel('ItemPermission');
        $availableStores = array_keys($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING, null, true));
        if (!in_array($shift['PosShift']['warehouse_id'],$availableStores)) {
            $this->flashMessage(sprintf(__('You don\'t have permission to view this %s', true), __('POS Session',true)));
            $this->redirect(['action'=>'index']);
        }
        $this->loadModel('Invoice');
        $this->set('invoice_count',$this->Invoice->find('count',['conditions' => ['Invoice.type'=>Invoice::Invoice,'Invoice.pos_shift_id' => $id]]));

	    $pos_shift_invoices = GetObjectOrLoadModel('Invoice')->find('all', ['recursive' => -1, 'conditions' => ['Invoice.pos_shift_id' => $id]]);
	    $pos_shift_invoice_ids = [];
	    foreach ($pos_shift_invoices as $pos_shift_invoice) {
		    $pos_shift_invoice_ids[] = $pos_shift_invoice['Invoice']['id'];
	    }
	    $this->set('requisition_count', GetObjectOrLoadModel('Requisition')->find('count',['recursive' => -1, 'conditions' => ['Requisition.order_id' => $pos_shift_invoice_ids, 'Requisition.order_type' => [Requisition::ORDER_TYPE_INVOICE, Requisition::ORDER_TYPE_INVOICE_REFUND]]]));
		$sales_journal = GetObjectOrLoadModel('Journal')->find('first', ['conditions' => ['Journal.entity_type' => Journal::JOURNAL_ENTITY_TYPE_POS_SHIFT_SALES, 'Journal.entity_id' => $id]]);
		$refunds_journal = GetObjectOrLoadModel('Journal')->find('first', ['conditions' => ['Journal.entity_type' => Journal::JOURNAL_ENTITY_TYPE_POS_SHIFT_REFUND, 'Journal.entity_id' => $id]]);
	    $validate_journal = GetObjectOrLoadModel('Journal')->find('first', ['conditions' => ['Journal.entity_type' => Journal::JOURNAL_ENTITY_TYPE_POS_SHIFT_VALIDATE, 'Journal.entity_id' => $id]]);
        $this->set('sales_journal', $sales_journal);
		$this->set('validate_journal', $validate_journal);
		$this->set('refunds_journal', $refunds_journal);
	    $this->set('refund_count',$this->Invoice->find('count',['conditions' => ['Invoice.type'=>Invoice::Refund_Receipt,'Invoice.pos_shift_id' => $id]]));
		$this->set('requisitions_count', count(GetObjectOrLoadModel('Requisition')->getPosShiftRequisitions($id)));
        $shift['PosShiftTransaction'] = $this->PosShift->PosShiftTransaction->find('all',['conditions' => ['pos_shift_id' => $id]]);
        $this->set('shift', $shift);
        $this->loadModel('InvoicePayment');
        $this->set('invoice_payment_count', $this->InvoicePayment->find('count', ['conditions' => ['InvoicePayment.pos_shift_id' => $id]]));
        $payments = $this->InvoicePayment->find('all',['conditions' => ['InvoicePayment.pos_shift_id' => $id, 'Invoice.pos_shift_id' => $id], 'limit' => 100]);
        $this->set('payments', $payments);
        $this->set('invoicePaymentStatus', InvoicePayment::getPaymentStatus());
        $this->loadModel('SitePaymentGateway');
        $paymentMethods = $this->SitePaymentGateway->getPaymentGateways();
        $this->set('invoicePaymentMethods', $paymentMethods);
        $this->set('shiftData',$this->PosShift->getSessionData($id));
        $this->loadModel('SitePaymentGateway');
        $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'),true,true,true));
        $this->set('canClose',check_permission(CLOSE_ALL_POS_SESSIONS) || (check_permission(CLOSE_OWN_POS_SESSIONS) && $shift['PosShift']['staff_id'] == getAuthOwner('staff_id')));
        $this->set('canValidate',check_permission(VALIDATE_ALL_POS_SESSIONS) || (check_permission(VALIDATE_OWN_POS_SESSIONS) && $shift['PosShift']['staff_id'] == getAuthOwner('staff_id')));
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PosShift', array('primary_id' => $id));
        $action_list = $timeline->PosShiftActionList();
        $timeline->init(array('primary_id' => $id), $action_list);
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $pos_actions[$key] = $action;
            }
        }
        $this->set('actions', $pos_actions);
        $this->set('data', $timeline->getDataArray());
        $this->set('staffs',$this->PosShift->Staff->getList());
        if(isset($_GET['print'])) {
            $this->render('owner_print');
        }
    }

    function owner_add()
    {
        $openedSessionID = $this->PosShift->getOpenedSessionID();
        if($openedSessionID){
            $this->flashMessage(sprintf (__('You already have an opened %s.', true), __('pos shift',true)));
            $this->redirect(array('action'=>'index'));
        }
        $this->set('title_for_layout',  __('Start Session',true));
        $shifts = $this->PosShift->Shift->getCategoriesWithBranch('list', ['conditions' => ['Shift.category_type' => Category::CATEGORY_TYPE_Shift]]);
        $shifts = [0 => __t('Please Select')] + $shifts;

        $this->loadModel('ItemPermission');
        $availableStores = array_keys($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING));
        $conditions = ['PosDevice.category_type' => Category::CATEGORY_TYPE_POS_Device , 'PosDevice.status' => Category::POS_DEVICE_ACTIVE];
        if (!isSuperAdmin() && !isOwner() && !empty($availableStores)) {
            $conditions['PosDevice.classification_id1'] = $availableStores;
        }
        $pos = $this->PosShift->PosDevice->getCategoriesWithBranch('list', ['conditions' => $conditions]);

        if (!empty($this->data)) {

            if (!isSuperAdmin() && !isOwner() && ifPluginActive(BranchesPlugin) && !in_array(getCurrentBranchID(), getStaffBranchesIDs())) {
                $this->flashMessage(sprintf(__('You don\'t have permission to start a session on this Branch', true)));
                $this->redirect(array('action' => 'index'));
            }

            $this->PosShift->create();

            $this->data['PosShift']['warehouse_id'] = $this->PosShift->PosDevice->getCategoriesWithBranch('first', [
                'conditions' => [
                    'PosDevice.category_type' => Category::CATEGORY_TYPE_POS_Device,
                    'PosDevice.id' => $this->data['PosShift']['pos_id'],
                ],
                'fields' => [
                    'classification_id1'
                ]
            ])['PosDevice']['classification_id1'];
            $pos_accounting_settings = settings::getValue(PosPlugin, "pos_accounting_settings", null, false);
            $this->data['PosShift']['is_calculated_per_invoice'] = $pos_accounting_settings == 1;
            $this->data['PosShift']['open_time'] = date("Y-m-d H:i:s", strtotime('now'));
            $this->data['PosShift']['staff_id'] = getAuthOwner('staff_id');
            $this->data['PosShift']['currency_code'] = getCurrentSite('currency_code');
            if ($this->PosShift->save($this->data)) {
                $session_id = $this->PosShift->getInsertID();
                $date = date('Y/m/d',strtotime($this->data['PosShift']['open_time']));
                $action_line = [];
                $action_line['primary_id'] = $session_id;
                $action_line['param1'] = "{$pos[$this->data['PosShift']['pos_id']]}/{$date}/{$session_id}";
                $action_line['param2'] = $pos[$this->data['PosShift']['pos_id']];
                $action_line['param3'] = $shifts[$this->data['PosShift']['shift_id']];
                $this->PosShift->add_actionline(ACTION_POS_SESSION_START,$action_line);
                $this->owner_open($session_id);
            } else {
                $this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('POS Session',true)));
            }
        }
        $maxIdRow = $this->PosShift->find('first', [
            'fields' => ['MAX(PosShift.id) as nextExpectedID'],
            'applyBranchFind' => false
        ]);
        $nextExpectedID = (int)$maxIdRow[0]['nextExpectedID'] + 1;
        $this->set('user_name',getAuthOwner('business_name'));
        $this->set('nextExpectedID', $nextExpectedID);
        $this->set(compact('shifts','pos'));
    }

    function owner_edit($id = null) {
        if (!$id && empty($this->data)) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'pos shift',true)));
            $this->redirect(array('action'=>'index'));
        }
        $posShift = $this->PosShift->read(null, $id);
        if (!empty($this->data)) {
            if ($this->PosShift->save($this->data)) {
                $this->flashMessage(sprintf (__('The %s  has been saved', true), __('POS Session',true)), 'Sucmessage');
                $this->redirect(array('action'=>'index'));
            } else {
                $this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('POS Session',true)));
            }
        }
        if (empty($this->data)) {
            $this->data = $posShift;
        }
        $this->render('add');
    }

    function owner_delete($id = null) {
        if (empty($id) && !empty ($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty ($_POST)) {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('POS Session',true)));
            $this->redirect(array('action'=>'index'));
        }
        $module_name= __('posShift', true);
        if(is_array($id) && count($id) > 1){
            $module_name= __('posShifts', true);
        }
        $posShifts = $this->PosShift->find('all',array('conditions'=>array('PosShift.id'=>$id)));
        if (empty($posShifts)){
            $this->flashMessage(sprintf(__('%s not found', true), $module_name));
            $this->redirect($this->referer(array('action' => 'reports','index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes') {
                if (!$this->PosShift->hasTransactions($_POST['ids'])) {
                    if ($this->PosShift->deleteAll(array('PosShift.id' => $_POST['ids']))) {
                        if (!check_permission(DELETE_POS_SESSION)) {
                            $this->flashMessage(__('You are not allowed to view this page', TRUE));
                            $this->redirect(array('action' => 'index'));
                        }
                        foreach ($posShifts as $posShift) {
                            $session_id = $posShift['PosShift']['id'];
                            $shifts = $this->PosShift->Shift->find('first',['conditions' => ['Shift.category_type' => Category::CATEGORY_TYPE_Shift, 'Shift.id' => $posShift['PosShift']['shift_id']]]);
                            $pos = $this->PosShift->PosDevice->find('first',['conditions' => ['PosDevice.category_type' => Category::CATEGORY_TYPE_POS_Device, 'PosDevice.id' => $posShift['PosShift']['pos_id']]]);
                            $shift_name = $shifts['Shift']['name'] ?? __t('Please Select');
                            $pos_name = $pos['PosDevice']['name'];
                            $date = date('Y/m/d',strtotime($this->data['PosShift']['open_time']));
                            $action_line = [];
                            $action_line['primary_id'] = $session_id;
                            $action_line['param1'] = "{$pos_name}/{$date}/{$session_id}";
                            $action_line['param2'] = $pos_name;
                            $action_line['param3'] = $shift_name;
                            $this->PosShift->add_actionline(ACTION_POS_SESSION_REMOVE,$action_line);
                        }
                        $this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
                        $this->redirect(array('action' => 'index'));
                    } else {
                        $this->flashMessage(sprintf(__('Could not delete %s', true), $module_name));
                        $this->redirect(array('action' => 'index'));
                    }
                } else {
                    $this->flashMessage(sprintf(__('%s #%s can not be deleted because it has transactions', true), $module_name, implode(',', $_POST['ids'])));
                    $this->redirect(array('action' => 'index'));
                }
            }
            else{
                $this->redirect(array('action'=>'index'));
            }
        }
        $this->set('posShifts',$posShifts);
    }

    function owner_timeline($id = null) {
        $this->set('invoice_id', $id);
        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PosShift', array('primary_id' => $id));
        $action_list = $timeline->PosShiftActionList();
        $timeline->init(array('primary_id' => $id), $action_list);
        $this->set('data', $timeline->getDataArray());
        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $action_list)) {
                $pos_actions[$key] = $action;
            }
        }
        $this->set('actions', $pos_actions);
    }

    function owner_timeline_row($id = null) {
        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('PosShift', array('primary_id' => $id));
        $this->set('data', $timeline->getDataArray());
        echo $timeline->view_action($id);
        die();
    }

    function owner_open($id = null) {
        if (!check_permission([OPEN_OWN_POS_SESSIONS, OPEN_ALL_POS_SESSIONS])) {
            if (IS_REST) $this->cakeError('error403');
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        if($id) {
            $shift = $this->PosShift->read(null, $id);
            $this->BranchValidation->validatePosBranch($shift);
            if ($shift['PosShift']['status'] != PosShift::STATUS_OPENED) {
                $this->flashMessage(sprintf(__('%s not opened', true), __('POS Session',true)));
                $this->redirect(array('action'=>'index'));
            }
            $this->loadModel('ItemPermission');
            $availableStores = array_keys($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING, null, true));
            if (!in_array($shift['PosShift']['warehouse_id'],$availableStores)) {
                $this->flashMessage(sprintf(__('You don\'t have permission to open this %s', true), __('POS Session',true)));
                $this->redirect(array('action'=>'index'));
            }
            $date = date('Y/m/d',strtotime($shift['PosShift']['open_time']));
            $action_line = [];
            $action_line['primary_id'] = $id;
            $action_line['param1'] = "{$shift['PosDevice']['name']}/{$date}/{$id}";
            $this->PosShift->add_actionline(ACTION_POS_SESSION_OPEN,$action_line);
            $this->Session->write('shift_id',$id);
            $this->redirect('/pos/?lang='.(is_rtl() ? 'ar' : 'en'));
        }else {
            $openedSessionID = $this->PosShift->getOpenedSessionID();
            if($openedSessionID) {
                $this->redirect('/pos/?lang='.(is_rtl() ? 'ar' : 'en'));
            }
            $this->redirect(array('action'=>'add'));
        }
    }

    function owner_close($shift_id){
        if($shift_id) {
            $shift = $this->PosShift->read(null, $shift_id);
            $this->BranchValidation->validatePosBranch($shift);
            if ($shift['PosShift']['status'] == PosShift::STATUS_OPENED) {
                $staff_id = getAuthOwner('staff_id');
                if (check_permission(CLOSE_ALL_POS_SESSIONS) || ($shift['PosShift']['staff_id'] == $staff_id && check_permission(CLOSE_OWN_POS_SESSIONS))) {
                    $this->PosShift->id = $shift_id;
                    $this->data['PosShift']['status'] = PosShift::STATUS_CLOSED;
                    $this->data['PosShift']['close_time'] = $this->PosShift->formatDateTime($this->data['PosShift']['close_time']);
                    $shift['PosShift']['open_time'] = date('Y-m-d H:i', strtotime($shift['PosShift']['open_time']));
                    $close_time = "'" . $this->data['PosShift']['close_time'] . "'";
                    $invoices_after_close_time = $this->PosShift->query("SELECT count(id) as count FROM invoices WHERE date > $close_time AND pos_shift_id = $shift_id", false)[0][0]['count'];
                    if($this->data['PosShift']['close_time'] >= $shift['PosShift']['open_time']){
                        if (!$invoices_after_close_time) {
                            if ($this->PosShift->save($this->data)) {
                                $this->Session->delete('shift_id');
                                $date = date('Y/m/d',strtotime($shift['PosShift']['open_time']));
                                $action_line = [];
                                $action_line['primary_id'] = $shift_id;
                                $action_line['param1'] = "{$shift['PosDevice']['name']}/{$date}/{$shift_id}";
                                $action_line['param2'] = $shift['PosDevice']['name'];
                                $action_line['param3'] = $shift['Shift']['name'];
                                $this->PosShift->add_actionline(ACTION_POS_SESSION_CLOSE,$action_line);
                                $this->flashMessage(sprintf (__('The %s was closed successfully', true), __('POS Session',true)), 'Sucmessage');
                                if (check_permission(VALIDATE_ALL_POS_SESSIONS) || ($shift['PosShift']['staff_id'] == $staff_id && check_permission(VALIDATE_OWN_POS_SESSIONS))) {
                                    $this->redirect(['action' => 'validate',$shift_id]);
                                }
                            } else {
                                $this->flashMessage(sprintf (__('Couldn\'t update this %s', true), __('POS Session',true)));
                            }
                        } else {
                            $this->flashMessage(__("you can't close the session on a date before the dates of invoices in the same session", true));
                        }
                    }else{
                        $this->flashMessage(__('The closing date and time of the point of sale (POS) should be greater than or equal to the opening session time of the POS', true));
                    }
                } else {
                    $this->flashMessage(sprintf (__('You don\'t have permission to close this %s', true), __('POS Session',true)));
                }
            } else {
                $this->flashMessage(sprintf (__('this %s is already closed', true), __('POS Session',true)));
            }
        } else {
            $this->flashMessage(sprintf (__('Invalid id for %s', true), __('POS Session',true)));
        }
        $this->redirect($_SERVER["HTTP_REFERER"]);
    }

    function owner_validate($shift_id){
        $shifts = $this->PosShift->Shift->getCategoriesWithBranch('list', ['conditions' => ['Shift.category_type' => Category::CATEGORY_TYPE_Shift]]);
        $shifts = [0 => __t('Please Select')] + $shifts;

        $this->set('title_for_layout',  __('Validate Session',true));
        if (!$shift_id) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'pos shift',true)));
            $this->redirect(array('action'=>'index'));
        }
        $shift = $this->PosShift->read(null, $shift_id);
        $this->BranchValidation->validatePosBranch($shift);
        $shift['PosShiftTransaction'] = $this->PosShift->PosShiftTransaction->find('all',['conditions' => ['pos_shift_id' => $shift_id]]);
        if ($shift['PosShift']['status'] == PosShift::STATUS_CLOSED) {
            $staff_id = getAuthOwner('staff_id');
            if (check_permission(VALIDATE_ALL_POS_SESSIONS) || ($shift['PosShift']['staff_id'] == $staff_id && check_permission(VALIDATE_OWN_POS_SESSIONS))) {
                $this->PosShift->id = $shift_id;
            } else {
                $this->flashMessage(sprintf (__('You don\'t have permission to validate this %s', true), __('POS Session',true)));
                $this->redirect(array('action'=>'index'));
            }
        } else {
            $this->flashMessage(sprintf (__('this %s can\'t be validated make sure it\'s closed', true), __('POS Session',true)));
            $this->redirect(array('action'=>'index'));
        }
        if (!empty($this->data)) {
            $this->loadModel('PosShiftSummary');
            $this->loadModel('SitePaymentGateway');
            $paymentTreasury = $this->SitePaymentGateway->PaymentTreasury();

            $shiftId = $this->data['PosShiftSummary']['shift_id'];
            unset($this->data['PosShiftSummary']['shift_id']);

            $this->data['PosShiftSummary'] = array_map(function ($item) use ($shift_id,$paymentTreasury){
                $item['key'] = array_keys($item)[0];
                $item['value'] = array_values($item)[0];
                $item['pos_shift_id'] = $shift_id;
                $item['type'] = 'PaymentMethod';
                if($item['partner_type']) {
                    if (($item['partner_type'] == PosShiftTransaction::PARTNER_STAFF) && (getAuthOwner('staff_id') != $item['partner_id'] && getAuthOwner('staff_id') != Staff::OWNER_STAFF_ID) && !authStaff($item['partner_id'], $item['password'])) {
                        $this->flashMessage(sprintf(__('The %s could not be saved. Wrong Staff password', true), __('Transaction', true)));
                        $this->redirect(array('action' => 'validate', $item['pos_shift_id']));
                    }
                    $item['journal_entity'] = $item['partner_type'] == PosShiftTransaction::PARTNER_STAFF ? 'staff_petty_cash' : 'treasury';
                    $item['entity_id'] = $item['partner_id'];
                    unset($item['partner_type']);
                    unset($item['partner_id']);
                } else {
                    $item['journal_entity'] = 'treasury';
                    $this->loadModel('ItemPermission');
                    $this->loadModel('SitePaymentGateway');
                    $staffId = $this->PosShift->data['Staff']['id'] ?? null;
                    if ($staffId) {
                        $itemPermissions = $this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY, ItemPermission::PERMISSION_DEPOSIT, $staffId);
                        $sitePaymentGateway = $this->SitePaymentGateway->getPaymentTreasury($item['key']);
                        if (isset($itemPermissions[$sitePaymentGateway])) {
                            $item['entity_id'] = $paymentTreasury[$item['key']];
                        } else {
                            $this->loadModel('Treasury');
                            $item['entity_id'] = $this->Treasury->getPrimaryForStaff(ItemPermission::PERMISSION_DEPOSIT, $staffId);
                        }
                    } else {
                        $item['entity_id'] = $paymentTreasury[$item['key']];
                    }
                }
                unset($item[array_keys($item)[0]]);
                return $item;
            },$this->data['PosShiftSummary']);

            $shiftData = $this->PosShift->getSessionData($shift_id);

            /**
             * This directly matches up the session payments with the corresponding payments in the pos shift summary.
             * It then adjusts the values to eliminate small fractional discrepancies (e.g., due to rounding errors).
             */
            $allowedNumberOfDigitsAfterFraction = getAllowedNumberOfDigitsAfterFraction($shift['PosShift']['currency_code']);
            foreach ($this->data['PosShiftSummary'] as $paymentIndex => &$paymentData) {
                $sessionPayment = $shiftData['payment'][$paymentData['key']];
                $abs_diff = abs($sessionPayment['total'] - $paymentData['value']);
                $rounded_diff = round(abs($abs_diff), $allowedNumberOfDigitsAfterFraction);
                $smallest_fraction = pow(10, CurrencyHelper::getFraction($shift['PosShift']['currency_code']) * -1);
                if ($abs_diff > 0 && $rounded_diff < $smallest_fraction) {
                    $paymentData['value'] += $sessionPayment['total'] - $paymentData['value'];
                }
            }

            if($this->PosShiftSummary->saveAll($this->data['PosShiftSummary'])) {
                $this->data = $shift;
                $this->data['PosShift']['status'] = PosShift::STATUS_VALIDATED;
                $this->data['PosShift']['shift_id'] = $shiftId;
                if($this->PosShift->save($this->data)){
                    $shiftData = $this->PosShift->getSessionData($shift_id);
                    $date = date('Y/m/d',strtotime($shift['PosShift']['open_time']));
                    $action_line = [];
                    $action_line['primary_id'] = $shift_id;
                    $action_line['param1'] = "{$shift['PosDevice']['name']}/{$date}/{$shift_id}";
                    $action_line['param2'] = $shiftData['totalCounted'];
                    $action_line['param3'] = $shiftData['totalCounted'] - $shiftData['total'];
                    $action_line['param4'] = $shift['PosShift']['currency_code'];
                    $this->PosShift->add_actionline(ACTION_POS_SESSION_VALIDATE,$action_line);
                    $this->flashMessage(sprintf(__('The %s has been verified', true), __('POS Session', true)), 'Sucmessage');
                    $this->redirect(array('action' => 'view', $shift_id));
                }
            } else {
                $this->flashMessage(sprintf (__('The %s could not be verified. Please, try again', true), __('POS Session',true)));
            }
        }
        $this->set(compact('shift', 'shifts'));
        $this->set('shiftData',$this->PosShift->getSessionData($shift_id));
        $this->loadModel('SitePaymentGateway');
        $this->set('payment_methods', $this->SitePaymentGateway->getPaymentGateways(getCurrentSite('id'),true,true,true));
        $this->loadModel('ItemPermission');
        $this->loadModel('Staff');
        $this->set('treasuries',$this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_TREASURY,ItemPermission::PERMISSION_DEPOSIT));
        $this->set('staffs',UsersHelper::getInstance()->getList());
    }

    function owner_unvalidate($shift_id){
        if (!$shift_id) {
            $this->flashMessage(sprintf (__('Invalid %s.', 'pos shift',true)));
            $this->redirect(array('action'=>'index'));
        }
        $shift = $this->PosShift->read(null, $shift_id);
        $this->BranchValidation->validatePosBranch($shift);
        if ($shift['PosShift']['status'] != PosShift::STATUS_VALIDATED) {
            $this->flashMessage(sprintf(__('The %s could not be unvalidated because it\'s not validated.', true), __('POS Shift', true)));
            $this->redirect(array('action'=>'view',$shift_id));
        }
        if (!(check_permission(VALIDATE_ALL_POS_SESSIONS) || (check_permission(VALIDATE_OWN_POS_SESSIONS) && $shift['PosShift']['staff_id'] == getAuthOwner('staff_id')))) {
            $this->flashMessage(sprintf (__('You do not have permission to unvalidated this %s.', 'pos shift',true)));
            $this->redirect(array('action'=>'view',$shift_id));
        }
        $shiftData = $this->PosShift->getSessionData($shift_id);
        if (getAuthOwner('staff_id') != Staff::OWNER_STAFF_ID && ($shiftData['payment']['cash']['journal_entity'] == 'staff_petty_cash' && $shiftData['payment']['cash']['entity_id'] != getAuthOwner('staff_id'))) {
            if (empty($_POST['password'])) {
                $this->flashMessage(sprintf (__('The %s could not be unvalidated. You have to enter the staff password.',true), __('pos shift', true)));
                $this->redirect(array('action'=>'view',$shift_id));
            }
            if (!authStaff($shiftData['payment']['cash']['entity_id'], $_POST['password'])) {
                $this->flashMessage(sprintf(__('The %s could not be unvalidated. Wrong Staff password', true), __('pos shift', true)));
                $this->redirect(array('action' => 'view', $shift_id));
            }
        }
        $this->PosShift->id = $shift_id;
        $this->data = [];
        $this->data['PosShift'] = [];
        $this->data['PosShift']['id'] = $shift_id;
        $this->data['PosShift']['status'] = PosShift::STATUS_CLOSED;
        if ($this->PosShift->save($this->data)) {
            $this->loadModel('PosShiftSummary');
            if ($this->PosShiftSummary->deleteAll(['PosShiftSummary.pos_shift_id' => $shift_id])) {
                $date = date('Y/m/d',strtotime($shift['PosShift']['open_time']));
                $action_line = [];
                $action_line['primary_id'] = $shift_id;
                $action_line['param1'] = "{$shift['PosDevice']['name']}/{$date}/{$shift_id}";
                $this->PosShift->add_actionline(ACTION_POS_SESSION_UN_VALIDATE,$action_line);
                $this->flashMessage(sprintf(__('The %s has been Unvalidated', true), __('POS Session', true)), 'Sucmessage');
                $this->redirect(array('action' => 'view',$shift_id));
            }
        }
    }

    function owner_reopen($shift_id){
        if (!$shift_id) {
            $this->flashMessage(sprintf (__('Invalid %s.', true), __('pos shift',true)));
            $this->redirect(array('action'=>'index'));
        }
        $shift = $this->PosShift->read(null, $shift_id);
        $this->BranchValidation->validatePosBranch($shift);
        if ($shift['PosShift']['status'] != PosShift::STATUS_CLOSED) {
            $this->flashMessage(sprintf (__('The %s could not be unvalidated because it\'s not validated.', true), __('pos shift',true)));
            $this->redirect(array('action'=>'view',$shift_id));
        }
        if (!(check_permission(CLOSE_ALL_POS_SESSIONS) || (check_permission(CLOSE_OWN_POS_SESSIONS) && $shift['PosShift']['staff_id'] == getAuthOwner('staff_id')))) {
            $this->flashMessage(sprintf (__('You do not have permission to reopen this %s.', true), __('pos shift',true)));
            $this->redirect(array('action'=>'view',$shift_id));
        }
        $openedSessionID = $this->PosShift->getOpenedSessionID();
        if($openedSessionID){
            $this->flashMessage(sprintf (__('You already have an opened %s.', true), __('pos shift',true)));
            $this->redirect(array('action'=>'view',$shift_id));
        }
        $this->loadModel('ItemPermission');
        $availableStores = array_keys($this->ItemPermission->getAuthenticatedList(ItemPermission::ITEM_TYPE_STORE,ItemPermission::PERMISSION_INVOICING, null, true));
        if (!in_array($shift['PosShift']['warehouse_id'],$availableStores)) {
            $this->flashMessage(sprintf(__('You don\'t have permission to open this %s', true), __('POS Session',true)));
            $this->redirect(array('action'=>'index'));
        }
        $this->PosShift->id = $shift_id;
        $this->data = [];
        $this->data['PosShift'] = [];
        $this->data['PosShift']['id'] = $shift_id;
        $this->data['PosShift']['status'] = PosShift::STATUS_OPENED;
        if ($this->PosShift->save($this->data)) {
            $this->loadModel('PosShiftSummary');
            if ($this->PosShiftSummary->deleteAll(['PosShiftSummary.pos_shift_id' => $shift_id])) {
                $date = date('Y/m/d',strtotime($shift['PosShift']['open_time']));
                $action_line = [];
                $action_line['primary_id'] = $shift_id;
                $action_line['param1'] = "{$shift['PosDevice']['name']}/{$date}/{$shift_id}";
                $this->PosShift->add_actionline(ACTION_POS_SESSION_REOPEN,$action_line);
                $this->Session->write('shift_id',$shift_id);
                $this->flashMessage(sprintf(__('The %s has been reopened', true), __('POS Session', true)), 'Sucmessage');
                $this->redirect(array('action' => 'view',$shift_id));
            }
        }
    }

    function owner_translate() {
        $pos_labels = [
            'Refund Discount',
            'Previous Invoice Total',
            'Products Information',
            'Cancel',
            'CONFIRM',
            'Print',
            'Selling Price',
            'Stock Balance',
            'Barcode',
            'Product Code',
            'Product Name',
            'Product Information',
            'Search Clients',
            'Search Products',
            'Search Invoices',
            'Add New Client',
            'Phone Number',
            'Mobile',
            'Street Address 1',
            'Street Address 2',
            'City',
            'State',
            'Postal Code',
            'Country',
            'CONFORM',
            'Refund Invoice',
            'Back',
            'Payment Method',
            'In Stock',
            'Payment amount',
            'Transaction Type',
            'Client',
            'Payment List',
            'Payment',
            'Refund',
            'Subtotal',
            'Discount',
            'Total',
            'Change',
            'Total Refund',
            'Previous Net Payable',
            'Net Payable',
            'Add Payment',
            'Add Refund Payment',
            'Order Details',
            'With discount',
            'Options',
            'Add Sales Note',
            'Qty',
            'Price',
            'Paid',
            'Remove',
            'Void',
            'Hold',
            'UnHold',
            'Go to Dashboard',
            'Add Cash In',
            'Take Cash Out',
            'Keyboard Shortcuts',
            'Show Payment',
            'Show Clients',
            'Show Product Information',
            'Show Invoices',
            'Show Order Source',
            'Show Products',
            'New Order',
            'Search',
            'Discard Order',
            'Hold Order',
            'Remove Order Item',
            'Choose Quantity',
            'Choose Discount (Percentage)',
            'Choose Discount (Currency)',
            'Choose Price',
            'Choose Previous Order',
            'Choose Next Order',
            'Choose Previous Order Item',
            'Choose Next Order Item',
            'Navigate Through Items',
            'Are you sure you want to discard order?',
            'Add Item',
            'Unit',
            'Unit(s)',
            'Products',
            'Invoices',
            'Please Select Client Or Add One First',
            'You cannot choose any product with tracking type in the POS',
            'Print Last Invoice',
	        'Product Not Found',
	        'You are not allowed to edit this client details',
	        'Shipping Options',
	        'Shipping',
	        'Dis',
            'Client Credit',
            'Select Category',
            'Order Panel Shortcuts',
            'Items Panel Shortcuts',
            'Order Controllers',
            'Items Selection Controllers',
            'Item Adjustment Controllers',
            'Payment Controllers',
            'Items Navigation',
            'Information Panels Switchers',
            'Search Estimates',
            'Estimates',
            'Search Sales Order',
            'Sales Order',
        ];
        $this->js_lang_labels = array_merge($this->js_lang_labels, $pos_labels);
        $this->layout = false;
    }

    function owner_pos_js() {
        $this->set('pos_js',settings::getValue(PosPlugin, 'pos_js'));
        $this->layout = false;
    }



    function api_get_session(){
        $user_id = getAuthOwner('staff_id');
        $user_name = $user_id === 0 ? 'Owner' : getAuthStaff('name');
        $headers = getallheaders();
        $shift_id = $this->Session->read('shift_id') ?:$headers['Posshift'];
        $posShift = $this->PosShift->read(null, $shift_id);
        $check_offers = false;
        if (ifPluginActive(OffersPlugin)) {
            $this->loadModel('Offer');
            $check_offers = $this->Offer->find('count',['conditions'=>['active'=>1]]) > 0;
        }
        $default_payment = settings::getValue(PosPlugin, 'pos_default_payment') ?: 'cash';
	    $enable_multiple_addresses = settings::getValue(ClientsPlugin,'multiple_addresses');
		$disable_shipping = settings::getValue(SalesPlugin,'disable_shipping');
		$stopSellingExpiredItems = settings::getValue(PRODUCT_TRACKING_PLUGIN,'stop_selling_expired_tracking_items');
        $shift_data = [
            'Shift' => [
                'lang' => CurrentSiteLang(),
                'default_payment' => $default_payment,
	            'multiple_addresses_enabled' => $enable_multiple_addresses == "1",
                'branch_id' => $posShift['PosShift']['branch_id'],
                'shift_id' => $shift_id,
                'staff_id' => $user_id,
                'staff_name' => $user_name,
                'store_id' => $posShift['PosShift']['warehouse_id'],
                'cash_in_url' => Router::url(['owner' => true, 'controller' => 'pos_shift_transactions','action' => 'view_session', $shift_id, '?' => ['action' => 'cash_in']]),
                'cash_out_url' => Router::url(['owner' => true, 'controller' => 'pos_shift_transactions','action' => 'view_session', $shift_id,'?' => ['action' => 'cash_out']]),
                'can_edit_price' => check_permission(EDIT_PRODUCT_PRICE_POS),
                'can_add_discount' => check_permission(ADD_DISCOUNT_POS),
                'can_add_invoice_details' => $this->PosShift->can_add_invoice_details(),
                'check_offers' => $check_offers,
	            'disable_shipping' => (bool) $disable_shipping,
                'stop_selling_expired_items' => $stopSellingExpiredItems,
                'share_products' => !ifPluginActive(BranchesPlugin) || settings::getValue(BranchesPlugin, 'share_products'),
                'enable_log_rocket' => (bool) settings::getValue(PosPlugin, 'enable_log_rocket', null, false, false),
            ]
        ];
        $embedded_barcode_enabled = settings::getValue(ProductsPlugin, 'embedded_barcode');
        if ($embedded_barcode_enabled) {
            $shift_data['Shift']['embedded_barcode_length'] = strlen(settings::getValue(ProductsPlugin, 'embedded_barcode_format'));
	        $shift_data['Shift']['embedded_product_barcode_length'] = substr_count(settings::getValue(ProductsPlugin, 'embedded_barcode_format'), 'X') ?: substr_count(settings::getValue(ProductsPlugin, 'embedded_barcode_format'), 'x');
        }
        if (ifPluginActive(INSURANCE_PLUGIN)) {
            $this->loadModel('InsuranceAgent');
            $this->loadModel('InsuranceAgentClass');
            $insuranceAgents = $this->InsuranceAgent->find('list',['conditions' => ['InsuranceAgent.active' => 1]]);
            $insuranceAgentClasses = $this->InsuranceAgentClass->find('list',['fields' => ['InsuranceAgentClass.id','InsuranceAgentClass.name','InsuranceAgentClass.insurance_agent_id']]);
            $shift_data['Shift']['applyInsurance'] = true;
            $shift_data['Shift']['insuranceAgents'] = $insuranceAgents;
            $shift_data['Shift']['insuranceAgentClasses'] = $insuranceAgentClasses;
        }

        $enable_requisitions = settings::getValue(InventoryPlugin, 'enable_requisitions');
        $showTracking = !$enable_requisitions && ifPluginActive(ProductTracking);
        $shift_data['Shift']['show_tracking'] = $showTracking;
        $calculatedPerInvoice = $this->PosShift->checkPosShiftIdIsCalculatedPerInvoice($shift_data['Shift']['shift_id']);
        $shift_data['Shift']['disable_tracking_products'] =  !$calculatedPerInvoice || !settings::getValue(PosPlugin, 'pos_enable_trackings_items');

        //Check if the session is opened and the user of the session is the same user who created it or can open all session
        if($posShift['PosShift']['status'] == PosShift::STATUS_OPENED && (($posShift['PosShift']['staff_id'] == $user_id && check_permission(OPEN_OWN_POS_SESSIONS)) || check_permission(OPEN_ALL_POS_SESSIONS)) && $shift_id){
            $this->set('rest_item', $shift_data);
            $this->set('rest_model_name', "Shift");
            $this->render("view");
        }elseif(!$posShift && !check_permission(OPEN_OWN_POS_SESSIONS) && !check_permission(OPEN_ALL_POS_SESSIONS)){
            $this->cakeError('error403', ['message' => __('You are not allowed to access this page', true)]);
        }else{
            $this->cakeError('error401');
        }
    }

    function owner_reports($active_report = null){
        if(!check_permission([PermissionUtil::VIEW_ALL_POS_SESSIONS,PermissionUtil::VIEW_OWN_POS_SESSIONS])){
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect($this->referer());
        }

        $this->set('title_for_layout',  __('POS Reports',true));
        $reports = [
            'total_pos_categories_sales' => array('icon' => 'ico-bycategory', 'report_name' => 'PosCategories', 'title' => __('Total POS Categories Sales', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','total_pos_categories_sales'])),
            'total_pos_product_sales' => array('icon' => 'ico-byproduct', 'report_name' => 'PosProducts', 'title' => __('Total POS Products Sales', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','total_pos_product_sales'])),
            'pos_shifts_sales' => array('icon' => 'ico-staff', 'report_name' => 'PosSales', 'title' => __('POS Shifts Sales', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','pos_shifts_sales'])),
            'detailed_pos_shifts_transactions' => array('icon' => 'ico-prof', 'report_name' => 'PosTransactions', 'title' => __('Detailed POS Shifts Transactions', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','detailed_pos_shifts_transactions'])),
            'pos_shifts_profit' => array('icon' => 'ico-prof', 'report_name' => 'PosProfits', 'title' => __('POS Shifts Profit', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','pos_shifts_profit'])),
            'pos_categories_profit' => array('icon' => 'ico-prof', 'report_name' => 'PosCategoriesProfits', 'title' => __('Total POS Categories Profit', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','pos_categories_profit'])),
            'pos_product_profit' => array('icon' => 'ico-prof', 'report_name' => 'PosProductsProfits', 'title' => __('Total POS Products Profit', true), 'url' => Router::url(['controller' => 'pos_shifts','action' => 'reports','pos_product_profit']))
        ];
        $chunked_report = array_chunk($reports,4);
        $this->set('sales_reports',$chunked_report[0]);
        $this->set('profit_reports',$chunked_report[1]);
        $this->set('active_report', $active_report);
        if ($active_report) {
            App::import('vendor','ReportV2',['file'=>'ReportV2/autoload.php']);
            $params = $this->params['url'];
            unset($params['ext'],$params['url'],$params['debug']);
            foreach($params as $k => $v){
                if(is_array($v) && count($v) == 1){
                    $params[$k] = $v[0];
                }
            }
            $report =& \ReportV2\ReportFactory::create($reports[$active_report]['report_name'],$params);
            $report->setResponseType($this->RequestHandler->responseType());

            $report_data = $report->get_report_data();
            $report_meta = $report->get_report_meta_data();
            $filters = $report->get_filter_inputs();
            $header_filters = $report->get_filters_key_value($filters);
            $this->set('report_data', $report_data);
            $this->set('report_meta', $report_meta);
            $this->set('report_name', $active_report);
            $this->set('filters', $filters);
            $this->set('header_filters',$header_filters);
            $params = $report->get_params();
            $this->data = $params;
            $this->set('Obj',$report);
            $this->set('params', $params);
        }
    }

    function owner_json_find()
    {
        $owner = getAuthOwner();
        $value = trim($_GET['q']);
        if (!empty($value)) {

            $value = $this->PosShift->getDataSource()->value('%' . $value . '%', 'string');
            $conditions = ['PosShift.id LIKE ' . $value];
            $result = $this->PosShift->find('list', ['limit' => 30, 'conditions' => $conditions]);
            echo json_encode($result);
            die();
        }
        echo json_encode([]);
        die();
    }

        public function beforeRender()
    {
        parent::beforeRender();
        if (!empty($this->params['url']['ext']) && $this->params['url']['ext'] == ReportUtil::XLSX) {
            $this->autoRender = false;
            $this->theme = false;
            $this->autoLayout = $this->layout = false;
            $reportAction = $this->params['action'];
            $reportName = str_replace('owner_', '', $reportAction);
            $reportType = $this->params['url']['report_type'];
            $element = ReportFactory::element('owner_report', $reportType);
            ReportFactory::init(ReportUtil::XLSX)->export($reportName, $this, $element, $reportType ? $reportType : $this->params['pass'][0]);
        }
    }

    public function owner_convert_sessions_to_per_session() {
        if(!empty($_POST) && !empty($_POST['id'])) {
            $this->loadModel('Journal');
            define('IGNORE_POS_STATUS', 1);
            $posShifts = $this->PosShift->find('all', ['conditions' => ['PosShift.id' => $_POST['id'], 'PosShift.is_calculated_per_invoice' => 1]]);
            foreach ($posShifts as $posShift) {
                $this->PosShift->id = $posShift['PosShift']['id'];
                $this->PosShift->saveField('is_calculated_per_invoice', 0, ['callbacks' => false]);
                $posShift['PosShift']['is_calculated_per_invoice'] = 0;
                Journal::$disableLogs = 1;
                $this->loadModel('Invoice');
                $invoices = $this->Invoice->find('all', ['conditions' => ['Invoice.pos_shift_id' => $posShift['PosShift']['id']]]);
                foreach ($invoices as $invoice) {
                    $invoice = $this->Invoice->getInvoice($invoice['Invoice']['id']);
                    $this->Invoice->update_journals($invoice);
                }
                $this->PosShift->update_journals($posShift);
            }
        }
        $posShifts = $this->PosShift->find('all', ['conditions' => ['PosShift.is_calculated_per_invoice' => 1]]);
        $this->set('posShifts', $posShifts);
    }

    public function owner_update_pos_shifts_invoices_journals() {
        set_time_limit(0);
        ini_set('memory_limit', '2G');
        $query = "Select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select distinct pos_shift_id from invoices where (type = 0 or type = 6) and summary_total > 0 and draft = 0 and id not in (select entity_id from journals where entity_type = 'invoice' or entity_type = 'refund'))";
        $posShifts = $this->PosShift->query($query);
        $this->loadModel('Invoice');
        $this->loadModel('Journal');
        Journal::$disableLogs = 1;
        foreach ($posShifts as $posShift) {
            $invoices = $this->Invoice->find('all', ['conditions' => ['Invoice.pos_shift_id' => $posShift['pos_shifts']['id']]]);
            foreach ($invoices as $invoice) {
                $invoice = $this->Invoice->getInvoice($invoice['Invoice']['id']);
                $this->Invoice->update_journals($invoice);
            }
        }
        $this->flashMessage(__('journals generated', TRUE), 'Sucmessage');
        return $this->redirect($this->referer());
    }

    public function owner_remove_pos_shift_journals_for_per_invoice_shifts() {
        set_time_limit(0);
        ini_set('memory_limit', '2G');
        $query = "select * from pos_shifts where is_calculated_per_invoice = 1 and id in (select entity_id from journals where entity_type = 'pos_shift_sales' or entity_type = 'pos_shift_refund');";
        $posShifts = $this->PosShift->query($query);
        $this->loadModel('Journal');
        define('IGNORE_POS_STATUS', 1);
        Journal::$disableLogs = 1;
        foreach ($posShifts as $posShift) {
            $posShift = $this->PosShift->findById($posShift['pos_shifts']['id']);
            $this->PosShift->update_journals($posShift);
        }
        $this->flashMessage(__('pos shifts fixed'.count($posShifts), TRUE), 'Sucmessage');
        return $this->redirect($this->referer());
    }

    public function owner_fix_invoice_payments_journals() {
        $query = "select * from invoice_payments where id not in (select entity_id from journals where entity_type = 'invoice_payment') and invoice_id in (select id from invoices where pos_shift_id in (select id from pos_shifts where is_calculated_per_invoice = 1));";
        $results = $this->PosShift->query($query);
        $this->loadModel('InvoicePayment');
        $this->loadModel('Journal');
        Journal::$disableLogs = 1;
        foreach ($results as $result) {
            $invoicePayment = $this->InvoicePayment->find('first', ['conditions' => ['InvoicePayment.id' => $result['invoice_payments']['id']]]);
            $this->InvoicePayment->update_journals($invoicePayment);
        }
        $this->flashMessage(__('payments fixed'.count($results), TRUE), 'Sucmessage');
        return $this->redirect($this->referer());
    }

    function owner_autocomplete() {
        Configure::write('debug', 0);

        if (!$this->RequestHandler->isAjax()) {
            $this->redirect(['action' => ' index']);
        }

        if (empty($this->params['url']['term'])) {
            die(json_encode([]));
        }
        $keywords = trim($this->params['url']['term']);
        $dbNos = $this->PosShift->find('all', ['limit' => 15,
        'joins' => [
            [
                'table' => 'categories',
                'alias' => 'PosDevice',
                'type' => 'INNER',
                'conditions' => [
                    'PosDevice.id = PosShift.pos_id',
                    'PosDevice.category_type' => Category::CATEGORY_TYPE_POS_Device
                ]
            ]
        ],
        'conditions' => ['CONCAT(PosDevice.name, "/", DATE_FORMAT(PosShift.open_time,"%Y/%m/%d"), "/", PosShift.id) LIKE' => '%' . $keywords . '%',], 'recursive' => -1, 'fields' => 'PosShift.*, PosDevice.*']);
        $nos = [];
        foreach ($dbNos as $shift) {
            $nos[] = ['label' => $shift['PosDevice']['name'] . '/' . date('Y/m/d', strtotime($shift['PosShift']['open_time'])) . '/' . $shift['PosShift']['id'], 'value' => $shift['PosShift']['id']];
        }
        die(json_encode($nos));
    }
}
