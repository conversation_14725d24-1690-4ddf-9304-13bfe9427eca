<?php

use Izam\IzamTafket\Models\Language as PortalLanguage;

class LanguagesController extends AppController {

	var $name = 'Languages';

	/**
	 * @var Language
	 */
	var $Language;
	var $helpers = array('Html', 'Form');

	function admin_index() {
		$this->Language->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('languages', $this->paginate('Language', $conditions));
	}

	function admin_view($id = null) {
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('language', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('language', $this->Language->read(null, $id));
	}

	function admin_add() {
		if (!empty($this->data)) {
			if (PortalLanguage::create($this->data['Language'])) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('language',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('language',true)));
			}
		}
		$parents = $this->Language->Parent->find('list');
		$this->set(compact('parents'));
	}

	function admin_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'language',true)));
			$this->redirect(array('action'=>'index'));
		}
		$language = $this->Language->read(null, $id);
		if (!empty($this->data)) {
			if (PortalLanguage::where('id', $id)->update($this->data['Language'])) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('language',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('language',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $language;
		}
		$parents = $this->Language->Parent->find('list');
		$this->set(compact('parents'));
		$this->render('admin_add');
	}

	function admin_delete($id = null) {
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('language',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('language', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('languages', true);
		 } 
		$languages = $this->Language->find('all',array('conditions'=>array('Language.id'=>$id)));
		if (empty($languages)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->Language->deleteAll(array('Language.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('languages',$languages);
	}
}
?>