<?php

class OrdersController extends AppController {

	var $name = 'Orders';

	/**
	 * @var Order
	 */
	var $Order;
	var $helpers = array('Html', 'Form');

	function owner_index() {
		$this->Order->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('orders', $this->paginate('Order', $conditions));
	}

	function owner_view($id = null) {
		if (!$id) {
			$this->flashMessage(sprintf(__('Invalid %s', true), __('order', true), true));
			$this->redirect(array('action' => 'index'));
		}
		$this->set('order', $this->Order->read(null, $id));
	}

	function owner_add() {
		if (!empty($this->data)) {
			$this->Order->create();
			if ($this->Order->save($this->data)) {
				$this->flashMessage(sprintf(__('The %s has been saved', true), __('order', true)), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('order', true)));
			}
		}
	}

	function owner_edit($id = null) {
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf(__('Invalid %s.', 'order', true)));
			$this->redirect(array('action' => 'index'));
		}
		$order = $this->Order->read(null, $id);
		if (!empty($this->data)) {
			if ($this->Order->save($this->data)) {
				$this->flashMessage(sprintf(__('The %s  has been saved', true), __('order', true)), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('order', true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $order;
		}
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (empty($id) && !empty($_POST['ids'])) {
			$id = $_POST['ids'];
		}
		if (!$id && empty($_POST)) {
			$this->flashMessage(sprintf(__('Invalid id for %s', true), __('order', true)));
			$this->redirect(array('action' => 'index'));
		}
		$module_name = __('order', true);
		if (is_countable($id) && count($id) > 1) {
			$module_name = __('orders', true);
		}
		$orders = $this->Order->find('all', array('conditions' => array('Order.id' => $id)));
		if (empty($orders)) {
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->Order->deleteAll(array('Order.id' => $_POST['ids']))) {
				$this->flashMessage(sprintf(__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action' => 'index'));
			} else {
				$this->redirect(array('action' => 'index'));
			}
		}
		$this->set('orders', $orders);
	}

	function owner_upgrade() {
		$this->loadModel('Plan');

		$currentPlan = getAuthOwner('plan_id');
		$currPrice = $this->Plan->field('price', array('Plan.id' => $currentPlan));
		$plans = $this->Plan->find('all', array('conditions' => array('Plan.price >' => floatval($currPrice)), 'order' => 'Plan.display_order'));
		$this->set(array('plans' => $plans, 'currentPlan' => $currentPlan));

		$this->set('title_for_layout',  __('Upgrade your account', true));
	}

	function owner_payment() {
		$this->loadModel('Plan');

		if ($this->RequestHandler->isPost()) {
			if (empty($_POST['plan_id']) && empty($this->data['Order']['plan_id'])) {
				$this->flashMessage(__('Please choose your upgrade plan', true), 'Notemessage');
				$this->redirect(array('action' => 'upgrade'));
			} elseif (!empty($this->data['Order']['plan_id'])) {
				$newPlanId = $this->data['Order']['plan_id'];
				$op = $this->data['Order']['plan_id'];
			} else {
				$newPlanId = $_POST['plan_id'];
				$op = 'upgrade';
			}
		} else {
			$newPlanId = getAuthOwner('plan_id');
			$currentPlan = $this->Plan->find(array('Plan.id' => $newPlanId));
			if (!$currentPlan['Plan']['price']) {
				$this->flashMessage(__('You cannot use this plan for upgrade', true));
				$this->redirect(array('action' => 'upgrade'));
			}
			$op = 'renew';
		}

		$this->set(compact('op'));
		$newPlan = $this->Plan->findById($newPlanId);
		if (!$newPlan) {
			$this->flashMessage(__('Please choose your plan', true));
			$this->redirect(array('action' => 'upgrade'), 'Notemessage');
		}

		if (!empty($this->data)) {

			$this->data['Order']['site_id'] = getAuthOwner('id');
			$this->data['Order']['amount'] = $newPlan['Plan']['price'];
			$this->data['Order']['currency_code'] = 'AUD';
			$this->data['Order']['status'] = 0;

			$result = $this->Order->createPayment($this->data);
			if ($result['status']) {
				$this->redirect($result['url']);
			} else {
				fb($this->Order->validationErrors);
				$this->flashMessage(__('Could not complete your payment', true));
			}
		} else {
			$owner = getAuthOwner();
			unset($owner['id'], $owner['password']);
			$this->data['Order'] = $owner;
			$this->data['Order']['op'] = $op;
			$this->data['Order']['plan_id'] = $newPlanId;
		}

		$this->loadModel('Country');
		$this->set('countryCodes', $this->Country->find('list', array('order' => 'Country.country', 'fields' => 'code, country')));
	}

	function complete($id = false) {
		if (!$this->RequestHandler->isPost()) {
			$this->redirect('/');
		}

		$order = $this->Order->find(array('Order.id' => $id, 'Order.site_id' => getCurrentSite('id')));
		if (!$order) {
			$this->redirect('/');
		}

		require_once APP . 'vendors' . DS . 'payments' . DS . 'PaypalPayment.php';

		$payment = new PaypalPayment();
		$results = $payment->processResult();
		if ($results['data']['status'] != $order['Order']['status']) {
			$data = $results['data'];
			$data['id'] = $id;
			unset($data['order_id']);
			$this->save(array('Order' => $data), array('validate' => false, 'fieldList' => array_keys($data)));
			$upgradeResult = $this->Order->processOrder($id);

		}

		$this->redirect(array('action' => 'view', $id, 'owner' => 1));
	}
}