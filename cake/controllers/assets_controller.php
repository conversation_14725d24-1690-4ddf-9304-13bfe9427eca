<?php

use App\Domain\JournalRoute\Savers\RouteSaverFactory;
use App\Domain\JournalRoute\Util;
use Izam\Database\Capsule\Service\IzamDatabaseServiceProvider;
use Izam\Attachment\Service\AttachmentsService;
use Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil;

App::import('Vendor', 'settings');

class AssetsController extends AppController  {

    var $name = 'Assets';


    function beforeFilter() {
        parent::beforeFilter();

    }

    /**
     * Listing all the assets
     */
    function owner_index ( )
    {
        if (!check_permission(ADD_NEW_ASSETS)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        if (!IS_REST) {
            if (!\Izam\Entity\Helper\EntityHasLegacyCustomFields::check('asset')) {
                return $this->redirect('/v2/owner/entity/asset/list');
            }
        }
        $this->Asset->bindAttachmentRelation('asset');
        $this->loadModel ( 'Journal') ;
        $this->loadModel ( 'JournalCat') ;
        $this->loadModel ( 'AssetLocation') ;
        $fixed_assets = $this->JournalCat->find('first' , ['fields' => 'id',  'recursive' => -1 ,  'conditions' => ['entity_type' => 'fixed_assets'] ]);
        $categories = $this->Journal->get_children_cats( $fixed_assets['JournalCat']['id']);
        $cat_list = [] ;
        foreach ( $categories as $cc ) {
            $cat_list[$cc['JournalCat']['id']] = __at($cc['JournalCat']['name']);
        }

        //Setting page properties
        $conditions = $this->_filter_params();

        $this->paginate['order'] = 'Asset.id DESC';
        $assets = $this->paginate ('Asset' , $conditions) ;
        $this->set ( 'assets' , $assets ) ;
        $this->setup_nav_data($assets);
        $this->pageTitle=__("Assets" , true );


        //Getting Filters
        $filters = $this->Asset->getFilters () ;
        $filters['journal_cat_id']['options']['options'] = $cat_list ;
        $filters['asset_location_id']['options']['options'] =  $this->AssetLocation->find('list') ;
        $filters['asset_status']['options']['options'] = $this->Asset->get_status_list () ;
        $this->set ('filters' , $filters  );
        $this->set ( 'cat_list' , $cat_list ) ;

    }
    /**
     * helper function for the add and edit so we don't have to rewrite them .
     * @param int $id id of the asset being loaded ( in edit )
     */
    private function __settings ( $id = null ) {
        $this->loadModel ( 'AssetLocation') ;
        $this->loadModel ( 'AssetGroup') ;
        $this->loadModel ( 'AssetDeprecation') ;
        $this->loadModel ( 'Currency') ;
        $this->loadModel ( 'Journal') ;
        $this->loadModel ( 'JournalCat') ;
        $this->loadModel ( 'Tax') ;

        $fixed_assets = $this->JournalCat->find('first' , ['fields' => 'id',  'recursive' => -1 ,  'conditions' => ['entity_type' => 'fixed_assets'] ]);
        $categories = $this->Journal->get_children_cats( $fixed_assets['JournalCat']['id']);
        $taxes = $this->Tax->getTaxList();
        // $detailedTaxes = $this->Tax->getTaxList('id,value,included');
        $detailedTaxesTemp = $this->Tax->find('all',['recursive' => -1, 'fields' => ['id','value','included'] ] );
        $detailedTaxes=[];
        foreach($detailedTaxesTemp as $t){
            $detailedTaxes[$t['Tax']['id']] = $t['Tax'];
        }
        $asset_groups = $this->AssetGroup->find ( 'list' ) ;
        $asset_locations = $this->AssetLocation->find ('list', ['fields' => ['id', 'name']]) ;
        $new_locations = [] ;
        foreach ( $asset_locations as $k => $l ) {
            $new_locations[$l] = $l ;
        }
        $deprecation_methods = [''=>__("Please Select",true)]+AssetDeprecation::getDeprecationMethods () ;
        $has_deprecations = false ;
        if ( !empty ( $id ) ){
            $has_deprecations = (!empty ( $this->AssetDeprecation->find ('first' , ['conditions' => ['AssetDeprecation.asset_id' => $id ]])));
        }

        $this->set ('has_deprecations'  , $has_deprecations ) ;
        $this->set ('categories' , $categories );
        $this->set ('detailedTaxes' , $detailedTaxes );
        $this->set ('taxes' , $taxes );
        $this->set ('deprecation_methods' , $deprecation_methods );
        $this->set ('asset_groups' , $asset_groups );
        $this->set ('asset_locations' , $new_locations );
        $this->set ('asset_currencies' , $this->Currency->getCurrencyList ( [] , true ) ) ;
        $this->set ('deprecation_intervals' , AssetDeprecation::getDeprecationIntervals ( ) ) ;


    }
    /**
     * Form for adding an asset
     */
    function owner_add ($restApi = false)
    {
        if (!check_permission(ADD_NEW_ASSETS)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->Asset->bindAttachmentRelation('asset');
        App::import('Vendor', 'AutoNumber');
        if ( !empty ( $this->data ) ){
            if ( $this->data['Asset']['journal_cat_id'] == -1 ){
                $this->data['Asset']['journal_cat_id'] = NULL ;
            }
            if ( !empty ( $this->data['Asset']['asset_location_id']))
            {
                $this->loadModel('AssetLocation');
                $this->data['Asset']['asset_location_id'] = $this->AssetLocation->findOrCreate($this->data['Asset']['asset_location_id'][0]);
            } else {
                $this->data['Asset']['asset_location_id'] = 0;
            }
            if (!isset($this->data['Asset']['asset_current_value'])) {
                $this->data['Asset']['asset_current_value'] = $this->data['Asset']['asset_purchase_value'];
            }
            $this->Asset->set($this->data);
            $validateDeprecationData = $this->Asset->validateDeprecationData($this->data);
            \AutoNumber::set_validate(\AutoNumber::TYPE_ASSET);
            if ($validateDeprecationData['valid'] && $this->check_date_validity ( ) && $additionalFieldsFormHandler->validate($this->data) &&  $asset_id = $this->Asset->add ( $this->data , $validateDeprecationData ) ) {
                \AutoNumber::update_auto_serial(\AutoNumber::TYPE_ASSET);

                $attachments = $this->data['Asset']['attachment'];
                if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                
                $imagesIds = explode(',',$attachments);
                if(!empty($imagesIds))
                {
                    izam_resolve(AttachmentsService::class)->save('asset', $this->Asset->id, $imagesIds); 
                }
                $additionalFieldsFormHandler->store($asset_id, $this->data);

                if($this->data['Asset']['asset_deprecation_account']) 
                {
                    $assetDepressionAccountRoute =  [
                    "entity_type" => "asset_deprecation",
                    "account_id" => $this->data['Asset']['asset_deprecation_account'],
                    "account_type" => "1"
                    ];
                    RouteSaverFactory::getSaver(Util::ASSET_DEPRECATION_ACCOUNT_ENTITY_TYPE)->save($assetDepressionAccountRoute, $this->Asset->id);
                }

                if($this->data['Asset']['asset_deprecation_expense_account']) 
                {
                    $assetDepressionExpenseAccountRoute = [
                        "entity_type" => "asset_deprecation_expense",
                        "account_id" => $this->data['Asset']['asset_deprecation_expense_account'],
                        "account_type" => "1"
                    ];
                    RouteSaverFactory::getSaver(Util::ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE)->save($assetDepressionExpenseAccountRoute, $this->Asset->id);
                }



                $this->Asset->add_action ( ACTION_ASSET_ADD , $asset_id) ;
                $this->cron ( getCurrentSite('id') , $asset_id ,true) ;

                if ($restApi) {
                    return [
                        'result' => 'successful',
                        'code' => 202,
                        'id' => $asset_id,
                        'data' => $this->data
                    ];
                }

//               $this->add_actionline('ACTION_ASSET_ADD' , ['primary_id' => $asset_id , 'secondary_id' => $this->data['Asset']['journal_account_id'] , 'param1' => $this->data['Asset']['name']] ) ;
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset', true)), 'Sucmessage');
                $this->redirect (Router::url(['action' => 'view' , $asset_id ]) ) ;
            } else {
                $this->data['Asset']['purchase_date']=format_date( ($this->data['Asset']['purchase_date']));
                $this->data['Asset']['in_service_date'] = format_date($this->data['Asset']['in_service_date']);
                $this->Asset->validationErrors = array_merge($validateDeprecationData['validation_errors'] ?: [], $this->Asset->validationErrors);
                $this->Asset->validationErrors =  array_merge($this->Asset->validationErrors, $additionalFieldsFormHandler->getValidationErrors());
                if ($restApi) {
                    return [
                        'result' => 'failed',
                        'code' => 422,
                        'validation_errors' => $this->Asset->validationErrors,
                    ];
                }
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset', true)));
//                if (!empty( $this->Asset->validationErrors)) {
//                    $this->flashMessage(implode("<br/>", $this->Asset->validationErrors), 'Errormessage', 'secondaryMessage');
//                }
                if(!empty($this->data['Asset']['attachment'])){                       
                    $filesId = explode(',',$this->data['Asset']['attachment']);
                    $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                    $this->data['Attachments'] = $attachment;
                }
            }
        }else{
            $this->data['Asset']['code'] = \AutoNumber::get_auto_serial(\AutoNumber::TYPE_ASSET);
        }
        $this->set('next_deprecation_amount', 0);
        $this->set('forms', $additionalFieldsFormHandler->getCreateForms($this->data));

        $this->__settings ( ) ;
    }
    private function check_date_validity ( ) {
        if ( !empty ( $this->data['Asset']['deprecation_end_date']) && !empty ( $this->data['Asset']['in_service_date'] ) ){
            $end_date_str = $this->Asset->formatDate ($this->data['Asset']['deprecation_end_date'] ) ;
            $service_date_str = $this->Asset->formatDate ($this->data['Asset']['in_service_date'] )  ;
            if ($service_date_str > $end_date_str  )
            {
                $this->Asset->validationErrors['in_service_date'] =sprintf ( __("%s has to be older than %s",true) ,__("In Service Date",true) , __("Depreciation End Date",true));
                return false ;
            }
        }
        if ( !empty ( $this->data['Asset']['purchase_date']) && !empty ( $this->data['Asset']['in_service_date'] ) ){
            $pur_date_str = $this->Asset->formatDate ($this->data['Asset']['purchase_date'] )  ;
            $service_date_str = $this->Asset->formatDate ($this->data['Asset']['in_service_date'] )  ;
            if ($service_date_str < $pur_date_str  )
            {
                $this->Asset->validationErrors['purchase_date'] =sprintf ( __("%s has to be older than %s",true) ,__("Purchase Date",true) , __("In Service Date",true));
                return false ;
            }
        }
        if (!empty($this->data['Asset']['in_service_date']) && !empty($this->data['Asset']['next_depreciation_date'])){
            $service_date_str = $this->Asset->formatDate($this->data['Asset']['in_service_date']);
            $next_depreciation_date_str = $this->Asset->formatDate($this->data['Asset']['next_depreciation_date']);
            if ($next_depreciation_date_str < $service_date_str) {
                $this->Asset->validationErrors['next_depreciation_date'] = sprintf(__("%s has to be older than %s", true), __("In Service Date", true), __("Next Depreciation Date", true));
                return false ;
            }
        }

        if(! $this->data['Asset']['journal_cat_id']){
            $this->Asset->validationErrors['journal_cat_id'] =__("Required",true); ;
        }

        return true ;
    }
    /**
     * NO EDIT
     * Form for editing an asset
     */
    public function owner_edit ( $id )
    {
        if (!check_permission(ADD_NEW_ASSETS)) {
			if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        $this->Asset->bindAttachmentRelation('asset');
        $asset = $this->Asset->find ( 'first' , ['conditions' => ['Asset.id' => $id] ]);
        if ( empty ( $asset ) ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Asset",true)) ) ;
            $this->redirect ( 'index'); die ;
        }else {
            $next_deprecation_amount = (($asset['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_AMOUNT) ? $asset['Asset']['deprecation_amount'] : ($asset['Asset']['deprecation_percentage'] * $asset['Asset']['asset_current_value'] / 100));
            $this->set('next_deprecation_amount', $next_deprecation_amount);
            $operations = $this->Asset->count_operations($data[$this->alias]['id']);
            $deprecations= $this->Asset->count_deprecations($data[$this->alias]['id']);
            if($deprecations+$operations>1) {
                $this->flashMessage(__('The Asset Already Have Transactions', true));
                $this->redirect (Router::url(['action' => 'view' , $id ]) ) ; die ;
            }

            if ( $asset['Asset']['asset_status'] == Asset::STATUS_SOLD)
            {
                $this->flashMessage(__('You already sold this asset', true));
                $this->redirect (Router::url(['action' => 'view' , $id ]) ) ; die ;
            }
            
            if(!$this->validate_open_day($this->data['Asset']['purchase_date']??$asset['Asset']['purchase_date'],false)){
                $this->set('is_force_edit', true);
            }

            $operations = $this->Asset->count_operations($id);
            $deprecations = $this->Asset->count_deprecations($id);
            
            $disable_accounts = false;
            if(($deprecations+$operations) > 0) {
                $disable_accounts = true;
            }

            if ( !empty ( $this->data ) ){
                $this->data['Asset']['id'] = $id  ;
                if (empty($this->data['Asset']['asset_location_id'])) {
                    $this->data['Asset']['asset_location_id'] = [];
                }
                if ( !empty ( $this->data['Asset']['asset_location_id']))
                {
                    $this->loadModel('AssetLocation');
                    $this->data['Asset']['asset_location_id'] = $this->AssetLocation->findOrCreate($this->data['Asset']['asset_location_id'][0]);
                }
                if ($operations) {
                    $this->data['Asset']['quantity'] = $asset['Asset']['quantity'];
                }
                if (!isset($this->data['Asset']['asset_current_value'])) {
                    $this->loadModel('AssetDeprecation');
                    $total_deps = $this->AssetDeprecation->find('first' , ['fields' => "SUM(cost) as calculated_total_deprecation",'recursive' => -1 ,'conditions' => ['AssetDeprecation.asset_id' => $id] ])[0]['calculated_total_deprecation']??0;
                    $this->data['Asset']['asset_current_value'] = !empty($this->data['Asset']['asset_purchase_value']) ? round($this->data['Asset']['asset_purchase_value'] - $total_deps, 3) : ($asset['Asset']['asset_purchase_value'] ?? 0);

                }
	            $validateDeprecationData = $this->Asset->validateDeprecationData($this->data);
                if($asset['Asset']['current_deprecation'] > ($this->data['Asset']['asset_purchase_value'] ?? $asset['Asset']['asset_purchase_value'])) {
                    $validateDeprecationData['valid'] = false;
                    CustomValidationFlash([__('Current depreciation amount exceeds purchase amount', true)]);
                }
                \AutoNumber::set_validate(\AutoNumber::TYPE_ASSET);
                if ($validateDeprecationData['valid'] && $this->check_date_validity ( ) && $additionalFieldsFormHandler->validate($this->data)&& $asset_id = $this->Asset->update ( $id , $this->data , $validation_errors )) {
                    $this->loadModel('Journal');
                    $assetJournal = $this->Journal->get_auto_account(['entity_type' => 'asset', 'entity_id' => $this->data['Asset']['id']]);
                    if ($assetJournal['JournalAccount']['journal_cat_id'] != $this->data['Asset']['journal_cat_id']) {
                        $this->loadModel('JournalAccount');
                        $this->JournalAccount->id = $assetJournal['JournalAccount']['id'];
                        $assetJournal['JournalAccount']['journal_cat_id'] = $this->data['Asset']['journal_cat_id'];
                        $this->JournalAccount->set($assetJournal);
                        $this->JournalAccount->save();
                    }
                    $additionalFieldsFormHandler->update($id, $this->data);

                    $attachments = $this->data['Asset']['attachment'];
                    if(IS_REST && is_array($attachments)) $this->cakeError("error400", ["validation_errors"=> __("Attachments must be a string separate by ,",true)]);
                    
                    $imagesIds = explode(',',$attachments);
                    if(!empty($imagesIds))
                    {
                        izam_resolve(AttachmentsService::class)->save('asset', $id, $imagesIds); 
                    }

                    if($this->data['Asset']['asset_deprecation_account']) 
                    {
                        $assetDepressionAccountRoute =  [
                        "entity_type" => "asset_deprecation",
                        "account_id" => $this->data['Asset']['asset_deprecation_account'],
                        "account_type" => "1"
                        ];
                        RouteSaverFactory::getSaver(Util::ASSET_DEPRECATION_ACCOUNT_ENTITY_TYPE)->save($assetDepressionAccountRoute, $this->Asset->id);
                    }
    
                    if($this->data['Asset']['asset_deprecation_expense_account']) 
                    {
                        $assetDepressionExpenseAccountRoute = [
                            "entity_type" => "asset_deprecation_expense",
                            "account_id" => $this->data['Asset']['asset_deprecation_expense_account'],
                            "account_type" => "1"
                        ];
                        RouteSaverFactory::getSaver(Util::ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE)->save($assetDepressionExpenseAccountRoute, $this->Asset->id);
                    }

                    App::import('Controller', 'Assets');
                    $controller = new AssetsController();
                    $controller->cron(getCurrentSite('id'), $asset_id, true);
                    $this->Asset->add_action ( ACTION_ASSET_MODIFY , $id) ;
//                  $this->add_actionline('ACTION_ASSET_MODIFY' , ['primary_id' => $id , 'secondary_id' => $this->data['Asset']['journal_account_id'] , 'param1' => $this->data['Asset']['name']] ) ;
                    if(IS_REST){
                        $this->render('success');
                        return;
                    }
                    $this->flashMessage(sprintf(__('%s has been saved', true), __('Asset', true)), 'Sucmessage');
                    $this->redirect (Router::url(['action' => 'view' , $id ]) ) ;
                } else {
                    $this->Asset->validationErrors = array_merge($this->Asset->validationErrors, $additionalFieldsFormHandler->getValidationErrors());
                    if(!empty($this->data['Asset']['attachment'])){
                        $filesId = explode(',',$this->data['Asset']['attachment']);
                        $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
                        $this->data['Attachments'] = $attachment;
                    }
                    if (IS_REST) {
                        $this->cakeError("error400", ["message"=>__('Could not save Asset, Please fix errors below', true), "validation_errors"=>$this->Asset->validationErrors]);
                    }
                    $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Asset', true)));
//                    if (!empty( $this->Asset->validationErrors)) {
//                        $this->flashMessage(implode("<br/>", $this->Asset->validationErrors), 'Errormessage', 'secondaryMessage');
//                    }
                }
            }
            $this->loadModel('AssetOperation');
            $this->data = $asset ;  
            $this->data['Asset']['quantity'] = $this->Asset->getQuntity($asset['Asset']['id']) - $this->AssetOperation->getSoldQuantity($asset['Asset']['id']);;
            $operations = $this->Asset->count_operations($asset['Asset']['id']);
            $this->set('disable_quantity', $operations);

            $accounts = $this->GetAssetDepereciassionAccounts($id);
            $this->data['Asset']['asset_deprecation_account'] = $accounts['asset_deprecation'];
            $this->data['Asset']['asset_deprecation_expense_account'] = $accounts['asset_deprecation_expense'];
            
            if ( !empty ( $this->data['Asset']['asset_location_id'] ) ) {
                $this->loadModel('AssetLocation' );
                $this->data['Asset']['asset_location_id']  = $this->AssetLocation->find('first' , ['recursive' => -1 ,  'conditions' => ['id' =>$this->data['Asset']['asset_location_id'] ] ] )['AssetLocation']['name'] ;
            } else {
                $this->data['Asset']['asset_location_id'] = '';
            }
            $this->set('forms', $additionalFieldsFormHandler->getEditForms($id, $this->data));
            $this->set('disable_accounts', $disable_accounts);
            $this->__settings ( $id ) ;
            $this->render('owner_add');
        }

    }
    function owner_preview($id = null){

        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->Asset->bindAttachmentRelation('asset');
	    $this->loadModel('Staff');
        $asset = $this->Asset->find ( 'first' , ['conditions' => ['Asset.id' => $id] ]);
        if ( empty ( $asset ) ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Asset",true)) ) ;
            $this->redirect ( 'index'); die ;
        }else {
            $site = getCurrentSite();
            $this->loadModel('AssetLocation');
            $this->loadModel('JournalCat');
            $this->loadModel('JournalAccount');
            $this->loadModel('AssetOperation');
            $locations = $this->AssetLocation->find ( 'list'  ) ;
            $cat_name = $this->JournalCat->findById ($asset['Asset']['journal_cat_id'])['JournalCat']['name'];
            $account_name = $this->JournalAccount->findById ($asset['Asset']['journal_account_id'])['JournalAccount']['name'];
            $this->set(compact ('cat_name' , 'account_name' ));
            $asset['Asset']['quantity'] = $this->Asset->getQuntity($asset['Asset']['id']) - $this->AssetOperation->getSoldQuantity($asset['Asset']['id']);
            $this->set("asset" , $asset ) ;
            $this->set("locations" , $locations ) ;
            $owner = getAuthOwner();
            $this->set("owner" , $owner ) ;
            $this->set("site" , $site ) ;
            $asset_operations = $this->Asset->get_all_operations($id) ;
            $this->layout = '' ;
            $this->set("asset_operations" , $asset_operations ) ;
	        $this->set('staffs', $this->Staff->getList());
            $soldQuantity = $this->AssetOperation->getSoldQuantity($asset['Asset']['id'], null, true);
            $this->set('soldQuantity', $soldQuantity);

        }
    }
    /**
     * Form for viewing an asset with all its aspects
     */
    function owner_view ( $id ) {
        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
        $this->Asset->bindAttachmentRelation('asset');
        $asset = $this->Asset->find ( 'first' , ['conditions' => ['Asset.id' => $id] ]);
        if ( empty ( $asset ) ) {
            $this->flashMessage(sprintf(__("%s is not found",true),__("Asset",true)) ) ;
            $this->redirect ( 'index'); die ;
        }else {
            $this->loadModel('JournalAccount');
            $account_id = $this->JournalAccount->find ('first'  , ['recursive' => -1 , 'fields' => 'id',  'recursive' => -1,'conditions' => ['entity_id' => $id , 'entity_type' => 'asset']])['JournalAccount']['id'];
            $this->set('account_id',$account_id);
            $can_edit = true ;
            if ( $asset['Asset']['asset_current_value'] <=0 || in_array ( $asset['Asset']['asset_status']  , [ Asset::STATUS_DEPRECATED ,Asset::STATUS_SOLD ] ) )
            {
                $can_edit = false ;
            }
			if (round($asset['Asset']['asset_current_value'], 6) <= 0 && !in_array($asset['Asset']['asset_status'], [Asset::STATUS_DEPRECATED, Asset::STATUS_SOLD])) {
				$this->Asset->save([ 'id' => $id,
					'asset_status' => Asset::STATUS_DEPRECATED
				]);
				$asset['Asset']['asset_status'] = Asset::STATUS_DEPRECATED;
			}
            $this->loadModel('AssetLocation');
            $this->loadModel('JournalCat');
            $this->loadModel('JournalAccount');
            $cat_name = $this->JournalCat->findById ($asset['Asset']['journal_cat_id'])['JournalCat']['name'];
            $account_name = $this->JournalAccount->findById ($asset['Asset']['journal_account_id'])['JournalAccount']['name'];
//            die ( $cat_name .' '.$account_name );
            $locations = $this->AssetLocation->find ( 'list'  ) ;
            $e =compact ('can_edit' , 'locations' , 'asset' , 'cat_name' , 'account_name' );
//            print_r ( $e ) ;die ( $e) ;
            $this->set ( $e);
//            var_dump($this->viewVars ) ;die ; 
//            $this->set ( 'can_edit' , $can_edit );
//            $this->set ( 'asset' , $asset );
            $this->set ( 'count_operations' , $this->Asset->count_operations ( $id )  );
            $this->set ( 'count_deprecations' , $this->Asset->count_deprecations ( $id )  );
            $this->setup_nav_view($id);
            $asset_operations = $this->Asset->get_all_operations($id);
            // check if asset operation total is not equal to asset current value, in this case update the asset's current value
            $asset_operations_total = 0;
            foreach ( $asset_operations as $a ){
                $asset_operations_total += ($a['debit']-$a['credit']);
            }

            if ($asset_operations_total != $asset['Asset']['asset_current_value']) {
                $this->Asset->save([ 'id' => $id,
                    'asset_current_value' => $asset_operations_total
                ]);
            }
            $Journal = GetObjectOrLoadModel('Journal');
            $accounts = [];
            $accounts[] = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::ASSET_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);
            $accounts[] = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::ASSET_DEPRECATION_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);
            $accounts[] = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id]);
            $this->set('accounts', $accounts);

            $entityKey = ($asset['Asset']['asset_status'] == Asset::STATUS_DEPRECATED) ? 'asset_deprecation' : 'asset';

            $repo = new \Izam\Template\TemplateRepository(getPDO('portal'), getPDO());
            $viewTemplates = $repo->getEntityViewTemplates($entityKey);
            $this->set('view_templates', $viewTemplates);
            $this->loadModel('Post');
            $this->set('post_count', $this->Post->find('count', array('conditions' => ['item_id' => $id, 'item_type' => Post::ASSET_TYPE])));
            $this->set("asset_operations" , $asset_operations);
            $this->set('forms', $this->createAdditionalFieldsFormHandlerInstance()->show($id));
            $this->loadModel('Journal');
            if($asset['Asset']['asset_purchase_value'] > 0) {
                $journal = $this->Journal->find('first', ['applyBranchFind' => false, 'conditions' => ['Journal.entity_type' => 'asset', 'Journal.entity_id' => $id]]);
                if(!$journal) {
                    $this->Asset->update_journals($asset);
                }
            }
        }
    }
    function owner_delete (  $id = null ) {

        if (!check_permission(ADD_NEW_ASSETS)) {
            if(IS_REST) $this->cakeError('error403');
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }
		set_time_limit('9999');
		ini_set('memory_limit', '8G');
        if(IS_REST){
            $_POST['submit_btn'] = 'yes';
        }

        if($_POST['index_action_select_type'] === 'all' && $_POST['submit_btn'] != 'yes' ) {

            $conditions = $this->getCachedPaginationConditions('Asset');

            if(isset($conditions['Asset'.'.id'])){
                unset($conditions['Asset'.'.id']);
            }
        }else {
            if (!empty ($_POST['ids'])) {
                $ids = $_POST['ids'];
                $id = $_POST['ids'][0];
            } else {
                $ids = [$id];
                $_POST['ids'][] = $id;
            }
            $conditions = [ 'Asset.id' => $ids ];
        }
        $assets = $this->Asset->find('all', ['recursive'=>-1,'conditions' => $conditions]);
        $this->set('assets',$assets);
        $additionalFieldsFormHandler = $this->createAdditionalFieldsFormHandlerInstance();
        foreach($assets as $row) {
            if(ifPluginActive(MANUFACTURING_PLUGIN)){
                $validationError = $this->validateManufacturingRelatedData($row['Asset']['id']);
                if(!empty($validationError)){
                    if(IS_REST){
                        $this->cakeError("error400", ["message"=> $validationError]);
                    }else{
                        $this->flashMessage($validationError);
                        return $this->redirect($this->referer('/'));
                    }
                }
            }
            $this->validate_open_day($row['Asset']['purchase_date']);
        }

        if (empty ($assets)) {
            $this->flashMessage(sprintf ( __('%s not found', TRUE) , __("Assets" , true)) );
            $this->redirect(array('action' => 'index'));
        }
        if ($_POST['submit_btn'] == 'yes' && !empty ($assets) ) {
            foreach($assets as $row) {
                $this->Asset->add_action ( ACTION_ASSET_DELETE , $row['Asset']['id']) ;
                $this->Asset->delete($row['Asset']['id']) ;
                $additionalFieldsFormHandler->deleteRelatedCustomFieldRecords($row['Asset']['id']);
            }
            $message = sprintf (__('%s has been deleted', TRUE) , (count ($assets ) > 1? __("Assets",true) :$assets[0]['Asset']['name'] )  );

            if(IS_REST){
                $this->set("message", $message);
                $this->render("success");
                return;
            }
            $this->flashMessage(sprintf (__('%s has been deleted', TRUE) , (count ($assets ) > 1? __("Assets",true) :$assets[0]['Asset']['name'] )  ) , 'success' ,'secondaryMessage');
            $this->redirect(['action' => 'index']);
        }



    }

    private function validateManufacturingRelatedData($assetId) : ?string {
		$Workstation = GetObjectOrLoadModel('Workstation');
        $workstations = $Workstation->find('all', ['conditions' => ['Workstation.asset_id' => $assetId]]);
        if(!empty($workstations)){
            $errorStr = __('The asset %s has been selected in the following workstations: %s', true);
            $workstationAnchoredNumbers = array_map(function($ws) {
                return '<a href="/v2/owner/entity/workstation/' . $ws['Workstation']['id'] . '/show">#'. $ws['Workstation']['code'] . '</a>';
            }, $workstations);
            $errorStr = sprintf(
                $errorStr,
                '<a href="'. Router::url(['controller' => 'assets', 'action' => 'view', $assetId]) . '">#'. $assetId . '</a>',
                implode(', ', $workstationAnchoredNumbers)
            );
            return $errorStr;
        }
        return null;
    }

    function cron($site_id = null , $asset_id = null , $force = false ) {
        if ( IS_CRON || $force){
            App::import('Vendor','oilogger');
            $logfile = date('Y_m_d_H');
            Oilogger::initSession('assets_' . $logfile . '.txt');
            Oilogger::log('Started......................');

            $this->loadModel ( 'AssetDeprecation' ) ;
            $this->loadModel ( 'Asset' ) ;
            $this->autoRender = $this->autoLayout = false;

            App::import('Vendor', 'Recurring');
            set_time_limit(360000);
            $this->autoRender = $this->autoLayout = false;
            $siteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));
            $conditions = array('Site.status'=>SITE_STATUS_ACTIVE);

            if ($site_id) {
                $conditions['Site.id'] = $site_id;
            }

            $sites = $siteModel->find('all', array('recursive' => -1,'conditions' => $conditions));
            $this->loadModel('Timezone');
            $totalSites = count($sites);
            Oilogger::log('Found '. $totalSites .' SITES');

            IzamDatabaseServiceProvider::boot(getPDO(),getPortalConfig());

            foreach ($sites as $index => $site) {
                Oilogger::log('Current Site : ' . $site['Site']['id'].' ' .$site['Site']['subdomain']. ', Number = ' . ($index + 1) . ' / ' . $totalSites);
                try {
                    $GLOBALS['site'] = $site['Site'];
                    $config = json_decode($site['Site']['db_config'], true);
                    ConnectionManager::getDataSource('default')->swtich_db($config);
                    $this->Asset->getDataSource()->swtich_db($config);
                    $this->AssetDeprecation->getDataSource()->swtich_db($config);

                    IzamDatabaseServiceProvider::setSiteConnection(getPdo());
	                removePdo();

                    $this->loadModel('Timezone');
                    $zone = $this->Timezone->field('zone_name', ["Timezone.id" => $site['Site']['timezone']]);
                    date_default_timezone_set($zone);
                    $currDate = date('Y-m-d');
                    $conditions = ['Asset.deprecation_method' => [AssetDeprecation::METHOD_FIXED_AMOUNT, AssetDeprecation::METHOD_PERCENTAGE], 'OR' => ['Asset.deprecation_end_date' => '0000-00-00', 'AND' => ['Asset.asset_current_value > ' => 0, 'Asset.deprecation_end_date >= ' => $currDate]]];
                    if (!empty ($asset_id)) {
                        $conditions ['Asset.id'] = $asset_id;
                        unset ($conditions['OR']);
                    }
                    Oilogger::log('Searching For Depreciation');
                    $ApplicableAssets = $this->Asset->find('all', ['conditions' => $conditions]);
                    if(!is_array($ApplicableAssets)){
                        continue;
                    }
                    Oilogger::log('Current Depreciation count: ' . count($ApplicableAssets));
                    foreach ($ApplicableAssets as $Asset) {
						// Validation if Asset is valid or skipping it
	                    if (!$this->Asset->isValid($Asset)) {
							Oilogger::log("Found invalid Asset id : " . $Asset['Asset']['id'] . " for site : " . $site['Site']['subdomain'] . " - Skipping ...");
							continue;
	                    }
                        if(isset($Asset['Asset']['branch_id'])){
                            setRequestCurrentBranch($Asset['Asset']['branch_id']);
                        }
                        $id = $Asset['Asset']['id'];
                        $period = AssetDeprecation::$deprecation_intervals[$Asset['Asset']['deprecation_interval']]['sql_lt'];
                        $period_count = $Asset['Asset']['deprecation_period'];
                        $last_generated_date = $this->getLastAssetDeprecationDate($Asset);
                        $this->Asset->id = $id;
                        $previous_dates = $this->AssetDeprecation->find('list', ['fields' => ['AssetDeprecation.id', 'AssetDeprecation.date'], 'conditions' => ['AssetDeprecation.asset_id' => $id]]);
                        $fromDate = $last_generated_date;
                        $previous_dates[] = $fromDate;
                        $fromDate = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);
                        $count = 0;
                        if (strtotime($last_generated_date) > 0) {
                            $salvageValue = $Asset['Asset']['salvage_value'] ?? 0; 
                            while ($Asset['Asset']['asset_current_value'] > $salvageValue && $fromDate <= $currDate && (!strtotime($Asset['Asset']['deprecation_end_date']) || strtotime($Asset['Asset']['deprecation_end_date']) < 0 || $fromDate <= $Asset['Asset']['deprecation_end_date'])) {
                                $count++;
                                if ($count > 1000) {
                                    break;
                                }
                                $cost = (($Asset['Asset']['deprecation_method'] == AssetDeprecation::METHOD_FIXED_AMOUNT) ? $Asset['Asset']['deprecation_amount'] : ($Asset['Asset']['deprecation_percentage'] * $Asset['Asset']['asset_current_value'] / 100));

                                if($count == 1 && $fromDate != $Asset['Asset']['next_deprecation_date'] && strtotime($Asset['Asset']['next_deprecation_date']) > 0) {
                                    $diff = calculatePeriodDifference($period, $last_generated_date, $Asset['Asset']['next_deprecation_date']);
                                    if($diff != $period_count){
                                        $cost = $cost * ($diff / $period_count);
                                        $fromDate = $Asset['Asset']['next_deprecation_date'];
                                        $previous_dates[] = $fromDate;
                                    }
                                }
                                
                                // To Force To use the remaining amount of the asset as the last deprecation
                                if (round($Asset['Asset']['asset_current_value'] - $cost - $salvageValue, 6) < 0) {
                                    $cost = $Asset['Asset']['asset_current_value'] - $salvageValue;
                                }
                                $current_deprecation = ['AssetDeprecation' => [
                                    'asset_id' => $id,
                                    'date' => $fromDate,
                                    'deprecation_method' => $Asset['Asset']['deprecation_method'],
                                    'currency_code' => $Asset['Asset']['asset_currency'],
                                    'period' => $Asset['Asset']['deprecation_period'],
                                    'cost' => $cost
                                ]];
                                $next_date = Recurring::nextDate($period_count, $period, $fromDate, $previous_dates);
                                if ($next_date == $fromDate) {
                                    break;
                                }
                                debug(($Asset['Asset']['asset_current_value'] - $cost));
                                $this->Asset->id = $id;
                                $saved_asset = ['Asset' => array_merge($Asset['Asset'],[
                                    'id' => $id,
                                    'last_deprecation_date' => $fromDate,
                                    'current_deprecation' => $cost,
                                    'total_deprecation' => ($cost + $Asset['Asset']['total_deprecation']),
                                    'next_deprecation_date' => $next_date,
                                    'asset_current_value' => ($Asset['Asset']['asset_current_value'] - $cost),
                                ])];
                                if (round($Asset['Asset']['asset_current_value'] - $cost, 6) == 0) {
                                    $saved_asset['Asset']['asset_current_value'] = 1;
                                    $saved_asset['Asset']['asset_status'] = Asset::STATUS_DEPRECATED;
                                } else if (round($Asset['Asset']['asset_current_value'] - $cost, 6) <= 0) {
                                    $saved_asset['Asset']['asset_status'] = Asset::STATUS_DEPRECATED;
                                }
                                $this->Asset->actsAs['journal']['is_journal'] = false;
                                $this->Asset->save($saved_asset, ['validate' => false, 'callbacks' => false]);
                                $this->AssetDeprecation->create();
                                if (!$this->AssetDeprecation->save($current_deprecation, ['validate' => false])) {
                                    debug('darab');
                                    break;
                                }
                                $this->Asset->actsAs['journal']['is_journal'] = true;
                                $current_deprecation['AssetDeprecation']['id'] = $this->AssetDeprecation->id;
                                $this->AssetDeprecation->add_action(ACTION_ASSET_DEPRECATION_ADD, $current_deprecation);
                                $Asset['Asset']['asset_current_value'] -= $cost;
                                debug($Asset['Asset']['asset_current_value']);
                                $Asset['Asset']['total_deprecation'] += $cost;
                                $fromDate = $next_date;
                            }
                        }
                    }
                    Configure::delete('cron_branch_id');
                } catch (Throwable $exception) {
                    
                    Configure::delete('cron_branch_id');
                    Oilogger::log('Error Searching For Depreciation ' .  $exception->getMessage().' '.$exception->getFile().' '.$exception->getLine());
                }
            }
        }
    }

	private function getLastAssetDeprecationDate($asset) {
		$asset_id = $asset['Asset']['id'];
		$last_deprecation_dates = [];
		// Method 1 - Add last deprecation Date from asset.
		$last_deprecation_dates[] = $asset['Asset']['last_deprecation_date'];

		// Method 2 - Find max(date) from the already created asset deprecations.
		$last_deprecation_created = $this->AssetDeprecation->query("SELECT max(date) as max_date  FROM `asset_deprecations` AS `AssetDeprecation` WHERE `AssetDeprecation`.`asset_id` = $asset_id ORDER BY `AssetDeprecation`.`id` DESC LIMIT 1", false);
		$last_deprecation_dates[] = $last_deprecation_created[0][0]['max_date'];

        // Method 3 - Just add the in-service date of the asset incase it's a new asset that has no deprecation ever made.
		$last_deprecation_dates[] = $asset['Asset']['in_service_date'];

		// Return the lastest date from the 3 previous methods to return the correct one.
		return date('Y-m-d', max(array_map('strtotime', $last_deprecation_dates)));
	}

    function owner_timeline ( $id ){

        $this->set('is_ajax', false);
        if ($this->RequestHandler->isAjax()) {
            $this->set('is_ajax', true);
        }

        $action_line_data = $this->Asset->get_action_line_data  ( $id ) ;
        $all_actions_lists = $action_line_data['all_actions_lists'];
        $all_action_ids = $action_line_data['all_action_ids'];

        require_once APP . 'vendors' . DS . 'Timeline.php';
        $timeline = new Timeline('All', array('primary_id' => $id));
        debug ( $all_action_ids ) ;
        debug ( $all_actions_lists ) ;
        $timeline->init(array('ActionLine.id' => $all_action_ids),$all_actions_lists );
        $this->set('data', $timeline->getDataArray());



        foreach ($timeline->ActionKeys as $key => $action) {
            if (in_array($key, $all_actions_lists)) {
                $invoice_actions[$key] = $action;
            }
        }
        $this->set('actions', $invoice_actions);
    }

    private function createAdditionalFieldsFormHandlerInstance()
    {
        return new \App\Services\LocalEntityForm\AdditionalFormsHandler(EntityKeyTypesUtil::ASSET);
    }
    protected function getJsonImportRequiredData(): array
    {
        return [
            'models' => ['Asset', 'AssetDeprecation', 'AssetLocation', 'AssetOperation', 'Journal', 'JournalAccount', 'JournalCat', 'Tax', 'AssetGroup', 'Currency', 'ActionLine'],
            'ownerAddParameters' => [true] // true means owner_add() should handle this request as rest api in assets_controller
        ];
    }

    function owner_multi_delete()
    {
        $ids = $_POST['ids'];
        $conditions = [];
        if($_POST['index_action_select_type'] === 'all') {
            $conditions = $this->getCachedPaginationConditions($this->modelClass);
            $filterData = $this->{$this->modelClass}->find('all', ['conditions' => $conditions]);
            $ids = array_column(array_column($filterData, $this->modelClass), 'id');
        }
        $params = json_decode($_POST['bulk_data'], true);
        $this->set('ids', $ids);
        $this->set('url', $params['target_url']);
        $this->set('actionType', $params['action_type']);
        $this->set('entityName', $params['entity_name']);
        $this->set('back_url', $params['back_url']);
        $this->set('action', $params['_method']);
        $this->set('title_for_layout', $params['title_for_layout']);
        $this->set('_PageBreadCrumbs', json_decode($params['breadcrumbs'], true));
		$this->setDefaultViewData();
		$this->view = 'izam';
		$this->render('bulk/bulk');
    }
    public function getJsonImportSuccessMessage($entityKey, $result): string
    {
        return sprintf(__(ucfirst($entityKey) . " #%s has been successfully created.", true), "<a target='_blank' style='text-decoration: underline;' href='/owner/assets/view/".$result['id']."'>".$result['data']['Asset']['code']."</a>");
    }

    public function getJsonImportFailMessage($entityKey, $result, $record): string
    {
        return sprintf(__("Error Adding %s #%s:%s", true), __(ucfirst($entityKey), true),  $record["Asset"]['code'], implode(", ", $this->array_flatten($result["validation_errors"])));
    }

    private function GetAssetDepereciassionAccounts($id){
        $Journal = GetObjectOrLoadModel('Journal');
        $accounts['asset_deprecation'] = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::ASSET_DEPRECATION_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id])['JournalAccount']['id'];
        $accounts['asset_deprecation_expense'] = $Journal->get_auto_account(['entity_type' => \Izam\Daftra\Common\Utils\JournalUtil::ASSET_DEPRECATION_EXPENSE_ACCOUNT_ENTITY_TYPE, 'entity_id' => $id])['JournalAccount']['id'];
        return $accounts;
    }

    public function get_diff_in_months(){
        $data = $_POST;
        $start_date = $data['start_date'];
        $end_date = $data['end_date'];
        die(json_encode(['diff_in_months' => monthDifference($start_date, $end_date)]));
    }
}
