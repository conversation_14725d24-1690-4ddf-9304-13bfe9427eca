<?php
class CostCenterTransactionsController extends AppController {

	var $name = 'CostCenterTransactions';

	/**
	 * @var CostCenterTransaction
	 */
	var $CostCenterTransaction;
	var $helpers = array('Html', 'Form');


	function owner_index() {

		if (!check_permission(VIEW_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		$this->set('default_currency', $this->CostCenterTransaction->get_default_currency());
		$this->loadModel('CostCenter');
		$this->CostCenter->recursive = -1;
		$cost_centers = $this->CostCenter->find('list');
		$this->paginate['CostCenterTransaction']['order'] = ['CostCenterTransaction.id' => 'DESC'];
		$this->loadModel('JournalAccount');
		$this->JournalAccount->recursive = -1;
		$journal_accounts = $this->JournalAccount->find('list');
		$this->setup_nav_data($cost_centers);
		$this->set('journal_accounts',$journal_accounts);
		$this->set('cost_centers',$cost_centers);
		$this->loadModel('Staff');
		$staff_list = $this->Staff->getList();
		$conditions = $this->_filter_params();
		if (!empty($this->params['url']['sort_by'])) {
			$this->paginate['CostCenterTransaction']['order'] = ($this->params['url']['sort_by'] === 'DESC') ? ['CostCenterTransaction.id' => 'DESC'] : ['CostCenterTransaction.id' => 'ASC'];
		}
		if (!empty($this->params['url']['branch_id'])) {
			$branch_id = trim($this->params['url']['branch_id']);
			$conditions['Journal.branch_id'] = $branch_id;
		}
		$results = $this->paginate('CostCenterTransaction', $conditions);
		$this->set('costCenterTransactions', $results);
		if(IS_REST){
			// Prepare The Results For Hamza
			foreach ($results as $key => $value) {
				$results[$key]['CostCenterTransaction']['code'] = $results[$key]['CostCenterTransaction']['id'];
				$results[$key]['CostCenterTransaction']['cost_center_name'] = $cost_centers[$results[$key]['CostCenterTransaction']['cost_center_id']];
				$results[$key]['CostCenterTransaction']['journal_account_name'] = $journal_accounts[$results[$key]['CostCenterTransaction']['journal_account_id']];
				$results[$key]['CostCenterTransaction']['journal_account_code'] = $results[$key]['JournalAccount']['code'];
				$results[$key]['CostCenterTransaction']['journal_number'] = $results[$key]['Journal']['number'];
				$results[$key]['CostCenterTransaction']['branch_id'] = $results[$key]['Journal']['branch_id'];
				$results[$key]['CostCenterTransaction']['view_source'] = $results[$key]['Journal']['is_automatic'] ? Journal::get_link($results[$key])['link'] : false;
				$results[$key]['CostCenterTransaction']['created'] = format_datetime($results[$key]['Journal']['date']);
				if (getAuthOwner('staff_id') == $results[$key]['Journal']['staff_id']) {
					$results[$key]['CostCenterTransaction']['staff_name'] = false;
				} else {
					$results[$key]['CostCenterTransaction']['staff_name'] = $staff_list[$results[$key]['Journal']['staff_id']] ?: __('System', true);
				}
			}
			$cost_center = $this->CostCenter->find('first', ['conditions' => [
				'CostCenter.id' => $this->params['url']['cost_center_id']
			]]);
			$results_without_filters = $this->CostCenterTransaction->find('count', ['conditions' => [
				'CostCenter.id' => $this->params['url']['cost_center_id'],
			]]);
			if ($cost_center && !in_array($cost_center['CostCenter']['cost_center_id'], [0, -1])) {
				$parent_id = $cost_center['CostCenter']['cost_center_id'];
				$this->set('parent_cost_center_id', $parent_id);
			}
			$this->set('results_without_filters', empty($results) && $results_without_filters > 0);
			$this->set('rest_items', $results);
			$this->set('rest_model_name', "CostCenterTransaction");
			return $this->render("cost-centers/index");
		}
	}

	function owner_view($id = null) {

			if (!check_permission(VIEW_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('cost center transaction', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		$this->set('costCenterTransaction', $this->CostCenterTransaction->read(null, $id));
	}

	function owner_add() {
			if (!check_permission(MANAGE_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!empty($this->data)) {
			$this->CostCenterTransaction->create();
			if ($this->CostCenterTransaction->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('cost center transaction',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('cost center transaction',true)));
			}
		}
		$costCentersJournalAccounts = $this->CostCenterTransaction->CostCentersJournalAccount->find('list');
		$costCenters = $this->CostCenterTransaction->CostCenter->find('list');
		$journalAccounts = $this->CostCenterTransaction->JournalAccount->find('list');
		$journals = $this->CostCenterTransaction->Journal->find('list');
		$this->set(compact('costCentersJournalAccounts', 'costCenters', 'journalAccounts', 'journals'));
	}

	function owner_edit($id = null) {


		if (!check_permission(MANAGE_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'cost center transaction',true)));
			$this->redirect(array('action'=>'index'));
		}
		$costCenterTransaction = $this->CostCenterTransaction->read(null, $id);
		if (!empty($this->data)) {
			if ($this->CostCenterTransaction->save($this->data)) {
				$this->flashMessage(sprintf (__('The %s  has been saved', true), __('cost center transaction',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf (__('The %s could not be saved. Please, try again', true), __('cost center transaction',true)));
			}
		}
		if (empty($this->data)) {
			$this->data = $costCenterTransaction;
		}
		$costCentersJournalAccounts = $this->CostCenterTransaction->CostCentersJournalAccount->find('list');
		$costCenters = $this->CostCenterTransaction->CostCenter->find('list');
		$journalAccounts = $this->CostCenterTransaction->JournalAccount->find('list');
		$journals = $this->CostCenterTransaction->Journal->find('list');
		$this->set(compact('costCentersJournalAccounts','costCenters','journalAccounts','journals'));
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (!check_permission(MANAGE_COST_CENTERS)) {
                $this->flashMessage(__('You are not allowed to view this page', TRUE));
                $this->redirect('/');
            }
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 }
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('cost center transaction',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('Cost Center Transaction', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('costCenterTransactions', true);
		 }
		$costCenterTransactions = $this->CostCenterTransaction->find('all',array('conditions'=>array('CostCenterTransaction.id'=>$id)));
		if (empty($costCenterTransactions)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->CostCenterTransaction->deleteAll(array('CostCenterTransaction.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				if (isset($_POST['box']) && $_POST['box']) {
					$this->handleResponseIframe('transaction_deleted_yes');
					return;
				}
				if(isset($_GET['redirect']) && $_GET['redirect'] == 'journals') {
					$this->redirect(array('controller' => 'journals','action'=>'view', $costCenterTransactions[0]['CostCenterTransaction']['journal_id']));
				}
				$this->redirect(array('action'=>'index'));
			}
			else{
				if (isset($_POST['box']) && $_POST['box']) {
					$this->handleResponseIframe('transaction_deleted_no');
					return;
				}
				if(isset($_GET['redirect']) && $_GET['redirect'] == 'journals') {
					$this->redirect(array('controller' => 'journals','action'=>'view', $costCenterTransactions[0]['CostCenterTransaction']['journal_id']));
				}
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('costCenterTransactions',$costCenterTransactions);
	}
}
?>
