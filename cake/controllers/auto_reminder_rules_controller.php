<?php

use Izam\Limitation\Utils\LimitationUtil;
use Izam\Daftra\Workflow\Repositories\WorkflowTypeRepository;
App::import('vendor','AutoReminder',['file'=>'AutoReminder/autoload.php']);
class AutoReminderRulesController extends AppController {

	var $name = 'AutoReminderRules';

	/**
	 * @var AutoReminderRule
	 */
	var $AutoReminderRule;
	var $helpers = array('Html', 'Form');

	function owner_index() {
		if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Edit_General_Settings)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->AutoReminderRule->recursive = 0;
		$conditions = $this->_filter_params();
		$this->set('autoReminderRules', $this->paginate('AutoReminderRule', $conditions));
        $this->set('actives',[__('Inactive',true), __('Active',true)]);
		$entity_types = array_keys(\AutoReminder\Base::getEntitySettings());
        $entitySettings = \AutoReminder\Base::getEntitySettings();
        foreach ($entity_types as $key => $entity_type) {
            if (isset($entitySettings[$entity_type]['plugin']) && !ifPluginActive($entitySettings[$entity_type]['plugin']))
                unset($entity_types[$key]);
        }
        $channels = [
            \AutoReminder\Base::SMS => __("SMS", true),
            \AutoReminder\Base::EMAIL => __("Email", true),
            \AutoReminder\Base::SYSTEM => __("System", true),
        ];
        $this->set('entityTypes',$entity_types);
        $this->set('entitySettings', $entitySettings);
        $this->set('channels',$channels);
	}

	function owner_view($id = null) {
		if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Edit_General_Settings)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$reminderRule = $this->AutoReminderRule->read(null, $id);
		if (empty($reminderRule)) {
			$this->flashMessage(__(sprintf (__('Invalid %s', true), __('auto reminder rule', true)),true));
			$this->redirect(array('action'=>'index'));
		}
		try {
			$rule = \AutoReminder\AutoReminderFactory::create($reminderRule);
		} catch (Exception $e) {
			$this->flashMessage($e->getMessage());
			$this->redirect(array('action' => 'index'));
		}
		$class_name = \AutoReminder\Base::getEntitySettings()[$rule->getEntityName()]["class"];
        $this->loadModel('Timezone');
        $zone = $this->Timezone->field('zone_name', array("Timezone.id" => getCurrentSite()['timezone']));

        date_default_timezone_set($zone);
        $pendingMatches = $rule->findMatches(false,$zone, $rule->getEntityName());
		$this->loadModel("ReminderMessage");
		$this->paginate = array(
			'conditions' => array('ReminderMessage.auto_reminder_rule_id' => $id),
		);
		$reminderMessages = $this->paginate('ReminderMessage');

        $channels = [
            \AutoReminder\Base::SMS => __("SMS", true),
            \AutoReminder\Base::EMAIL => __("Email", true),
            \AutoReminder\Base::SYSTEM => __("System", true),
        ];
        $sent_status = [
            \AutoReminder\Base::SENDING_PENDING => __("Pending", true),
            \AutoReminder\Base::SENDING_SUCCEEDED => __("Dispatched", true),
            \AutoReminder\Base::SENDING_FAILED => __("Failed", true),
        ];
		$this->set(compact("pendingMatches", "class_name", "reminderMessages", "reminderRule", "channels"));
		$this->set('sent_status', $sent_status); 
		$this->set('entitySettings', \AutoReminder\Base::getEntitySettings());
		$this->set('reminderCount', $this->params['paging']['ReminderMessage']['count']);

	}

	function owner_add($type='') {

		if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Edit_General_Settings)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		$this->handleSiteLimit(
			checkSiteLimitV2(getCurrentSite('id'), LimitationUtil::LINK_SMS_GATEWAYS),
			'index'
		);

		if(empty($type)) $this->redirect(array('action'=>'add', 'appointments'));
		if(empty(\AutoReminder\Base::getEntitySettings()[$type])){
			$this->flashMessage(sprintf (__('Invalid %s.', 'auto reminder rule',true), $type));
			$this->redirect(array('action'=>'index'));
		}
		if (!empty($this->data)) {
			// $this->AutoReminderRule->create();
			if ($result = \AutoReminder\AutoReminderFactory::save($this->data["AutoReminderRule"], $validation_errors)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('auto reminder rule',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('auto reminder rule',true)));
				$this->AutoReminderRule->validationErrors = $validation_errors;
			}
		}
		$this->set("type",$type);
	}

	function owner_edit($id = null) {
		if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Edit_General_Settings)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (!$id && empty($this->data)) {
			$this->flashMessage(sprintf (__('Invalid %s.', 'auto reminder rule',true)));
			$this->redirect(array('action'=>'index'));
		}
		$autoReminderRule = $this->AutoReminderRule->read(null, $id);
		if (!empty($this->data)) {
			if ($result = \AutoReminder\AutoReminderFactory::save($this->data["AutoReminderRule"], $validation_errors)) {
				$this->flashMessage(sprintf (__('The %s has been saved', true), __('auto reminder rule',true)), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			} else {
				$this->flashMessage(sprintf(__('The %s could not be saved. Please, try again', true), __('auto reminder rule',true)));
				$this->AutoReminderRule->validationErrors = $validation_errors;
			}
		}
		if (empty($this->data)) {
			$this->data = $autoReminderRule;
		}
		$this->set("type", $this->data["AutoReminderRule"]["entity_type"]);
		$this->render('owner_add');
	}

	function owner_delete($id = null) {
		if (!check_permission(\Izam\Daftra\Common\Utils\PermissionUtil::Edit_General_Settings)) {
			if(IS_REST) $this->cakeError('error403');
			$this->flashMessage(__('You are not allowed to view this page', TRUE));
			$this->redirect('/');
		}
		if (empty($id) && !empty ($_POST['ids'])) {
			$id = $_POST['ids'];
		 } 
 		if (!$id && empty ($_POST)) {
			$this->flashMessage(sprintf (__('Invalid id for %s', true), __('auto reminder rule',true)));
			$this->redirect(array('action'=>'index'));
		}
		$module_name= __('autoReminderRule', true);
		if(is_countable($id) && count($id) > 1){
			$module_name= __('autoReminderRules', true);
		 } 
		$autoReminderRules = $this->AutoReminderRule->find('all',array('conditions'=>array('AutoReminderRule.id'=>$id)));
		if (empty($autoReminderRules)){
			$this->flashMessage(sprintf(__('%s not found', true), $module_name));
			$this->redirect($this->referer(array('action' => 'index'), true));
		}
		if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
			if ($_POST['submit_btn'] == 'yes' && $this->AutoReminderRule->deleteAll(array('AutoReminderRule.id'=>$_POST['ids']))) {
				$this->flashMessage(sprintf (__('%s deleted', true), $module_name), 'Sucmessage');
				$this->redirect(array('action'=>'index'));
			}
			else{
				$this->redirect(array('action'=>'index'));
			}
		}
		$this->set('autoReminderRules',$autoReminderRules);
	}


	function owner_check_auto_reminder_message()
	{
		$entity_type = $this->params['entity_type'];
		App::import('vendor', 'AutoReminder', ['file' => 'AutoReminder/autoload.php']);
		$this->loadModel('AutoReminderRule');
		$this->loadModel('ReminderMessage');
		$rules = $this->AutoReminderRule->find('all', ["recursive" => -1, "conditions" => ["send_on" => 1, 'entity_type' => $entity_type]]);
		foreach ($rules as $rule) {
			$autoReminderRule = \AutoReminder\AutoReminderFactory::create($rule);
			$autoReminderRule->addMessages($autoReminderRule->findMatches());
		}
		echo json_encode([
			"result" => "success",
			"code" => 200,
		]);
		die;
	}
}
?>
