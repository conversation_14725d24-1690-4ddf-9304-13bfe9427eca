<?php

class GroupPricesController extends AppController {

    var $name = 'GroupPrices';

    function owner_index() {
        $site = getAuthOwner();
	    /** Based On: DAFTRA-6927 **/
//        if (!check_permission(View_All_GroupPrice) and !check_permission(View_His_GroupPrice)) {
        if (!check_permission(View_Price_List) && !str_contains($this->referer(), '/pos/')) {
            $this->flashMessage(__('You are not allowed to view this page', TRUE));
            $this->redirect('/');
        }

        $this->GroupPrice->recursive = 0;
        if(IS_REST){
            $groupPrices = $this->GroupPrice->getActiveList();
            echo json_encode($groupPrices, JSON_UNESCAPED_SLASHES);
            die();
        }
        $conditions = $this->_filter_params();
	
	    /** Based On: DAFTRA-6927 **/
//        if (!check_permission(View_All_GroupPrice)) {
//            $conditions['GroupPrice.staff_id'] = $site['staff_id'];
//        }

        $this->paginate['GroupPrice'] = array('conditions' => $conditions,
            'order' => array('GroupPrice.name asc'),
        );
        $groupPrices  = $this->paginate();
        $this->setup_nav_data($groupPrices);
        $this->set('groupprices', $groupPrices);
        $this->set('content', $this->get_snippet('groupprices'));
        $this->set('title_for_layout',  h(__('Price Groups', true)));
    }

    function owner_add() {
        if (!check_permission(Add_Edit_Price_List)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }
        if (!empty($this->data)) {
            $this->GroupPrice->create();
            $this->data['GroupPrice']['staff_id'] = getAuthOwner('staff_id');
            if ($this->GroupPrice->save($this->data)) {

                $this->add_actionline(ACTION_ADD_GROUPPRICE, array('primary_id' => $this->GroupPrice->id, 'param1' => $this->data['GroupPrice']['name']));
                $this->flashMessage(sprintf(__('%s has been saved', true), __('Price Group', true)), 'Sucmessage');
                $redirect = array('action' => 'index');
                if (!empty($this->params['url']['box'])) {
                    $redirect = array('action' => 'add', '?' => array('box' => 1, 'success' => 1));
                }
                $this->redirect($redirect);
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Price Group', true)));
            }
        }

        $this->set('title_for_layout',  __('Add Price Group', true));
    }

    function owner_edit($id = null) {
        $site = getAuthOwner();
	    /** Based On: DAFTRA-6927 **/
//	    if (!check_permission(Edit_Delete_all_GroupPrices) and !check_permission(Edit_And_delete_his_own_added_GroupPrices)) {
	    if (!check_permission(Add_Edit_Price_List)) {
            $this->flashMessage(__("You are not allowed to access this page", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }


        $conditions = array('GroupPrice.id' => $id);
	
	    /** Based On: DAFTRA-6927 **/
//        if (!check_permission(Edit_Delete_all_GroupPrices)) {
//            $conditions['GroupPrice.staff_id'] = $site['staff_id'];
//        }
	    
        $groupprice = $this->GroupPrice->find($conditions);
        if (!$groupprice) {
            $this->flashMessage(sprintf(__("%s not found.", true), __('Price groups', true)));
            $this->redirect($this->referer(array('action' => 'index')));
        }



        if (!empty($this->data)) {
            $this->data['GroupPrice']['id'] = $id;


            if ($this->GroupPrice->save($this->data)) {
                $this->add_actionline(ACTION_EDIT_GROUPPRICE, array('primary_id' => $this->GroupPrice->id, 'param1' => $this->data['GroupPrice']['name']));
                $this->flashMessage(sprintf(__('%s  has been saved', true), __('Price Group', true)), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->flashMessage(sprintf(__('%s could not be saved. Please, try again', true), __('Price groups', true)));
            }
        } else {
            $this->data = $groupprice;
        }
        $this->set('title_for_layout',  h(sprintf(__('%s - Price groups', true), $groupprice['GroupPrice']['name'])));

        $this->render('owner_add');
    }

    function owner_delete($id = null) {

        $owner = getAuthOwner();
	    /** Based On: DAFTRA-6927 **/
//        if (!check_permission(Edit_Delete_all_GroupPrices) and !check_permission(Edit_And_delete_his_own_added_GroupPrices)) {
        if (!check_permission(Delete_Price_List)) {
            $this->flashMessage(__("You are not allowed to delete  price groups", true), 'Errormessage', 'secondaryMessage');
            $this->redirect(array('action' => 'index'));
        }

        if (empty($id) && !empty($_POST['ids'])) {
            $id = $_POST['ids'];
        }
        if (!$id && empty($_POST)) {
            $this->flashMessage(sprintf(__('Invalid %s', true), __('project', true)));
            $this->redirect(array('action' => 'index'));
        }
        $module_name = __('price group', true);
        $verb = __('has', true);
        if (is_countable($id) && count($id) > 1) {
            $verb = __('have', true);
            $module_name = __('price groups', true);
        }
        $conditions = array();

        $conditions['GroupPrice.id'] = $id;
	    /** Based On: DAFTRA-6927 **/
//        if (!check_permission(Edit_Delete_all_GroupPrices)) {
//            $conditions['GroupPrice.staff_id'] = $owner['staff_id'];
//        }
	    
        $groupprices = $this->GroupPrice->find('all', array('conditions' => $conditions));
        if (empty($groupprices)) {
            $this->flashMessage(sprintf(__('%s not found', true), ucfirst($module_name)));
            $this->redirect($this->referer(array('action' => 'index'), true));
        }
        if (!empty($_POST['submit_btn']) && !empty($_POST['ids'])) {
            if ($_POST['submit_btn'] == 'yes' && $this->GroupPrice->deleteAll($conditions)) {
                foreach ($groupprices as $groupprice) {
                    $this->add_actionline(ACTION_DELETE_GROUPPRICE, array('primary_id' => $groupprice['GroupPrice']['id'], 'param1' => $groupprice['GroupPrice']['name']));
                }
                $this->flashMessage(sprintf(__('%s %s been deleted', true), ucfirst($module_name), $verb), 'Sucmessage');
                $this->redirect(array('action' => 'index'));
            } else {
                $this->redirect(array('action' => 'index'));
            }
        }

        $this->set('title_for_layout',  h(__('Delete Price Groups', true)));

        $this->set('groupprices', $groupprices);
        $this->set('module_name', $module_name);
    }

}

?>