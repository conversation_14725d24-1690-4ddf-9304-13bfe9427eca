<?php

use Izam\Daftra\Portal\Models\SupplierDirectory as PortalSupplierDirectory;

class SupplierDirectoriesController extends AppController {

    var $name = 'SupplierDirectories';
    var $helpers = array('Fck');
//	var $components = array('Email', 'Lmail');
    var $components = array('Cookie', 'Email', 'SysEmails');

    function owner_index() {
		
		
        $find = $this->SupplierDirectory->find('first', array('conditions' => array('site_id' => getCurrentSite('id'))));
        if (isset($find['SupplierDirectory']['registered_status']) and $find['SupplierDirectory']['registered_status'] != SupplierDirectory::Automatic_Register) {
            $this->set('is_register', 1);
        } else {
            $this->set('is_register', 0);
        }

        $this->set('content', $content=$this->get_snippet('supplier-directories',false,false));
		
		$this->loadModel('SupplierDirectory');
        $this->loadModel('QuoteRequest');
        $supplierid = $this->SupplierDirectory->GetSupplier(getCurrentSite('id'));
        $send_quote_request = $this->QuoteRequest->find('count', array('conditions' => array('QuoteRequest.site_id' => getCurrentSite('id'))));
        $received_quote_request = $this->QuoteRequest->find('count', array('conditions' => array('QuoteRequest.supplier_directory_id' => $supplierid)));
        $this->set('send_quote_request', $send_quote_request);
        $this->set('received_quote_request', $received_quote_request);
		
        $this->set('title_for_layout',  __('Suppliers Directory', true));
    }

    function owner_search() {
        $params = $this->params['url'];
        $this->set('params', $params);
        if (isset($params['country']) and !empty($params['country'])) {
            $conditions['country_code'] = $params['country'];
        }
        if (isset($params['industries']) and !empty($params['industries'])) {
            if (is_array($params['industries'])) {
                $indus = $params['industries'];
                foreach ($indus as $indu) {
                    $conditions['OR']['FIND_IN_SET(\'' . $indu . '\',industry_id) >'] = 0;
                }
            }
        }
        if (isset($params['keywords']) and !empty($params['keywords'])) {
            if ($params['mode'] == "and") {
                $keywords = '+' . str_replace(" ", ' +', trim($params['keywords']));
            } else {
                $keywords = trim($params['keywords']);
            }
            $conditions[' match(company_name,website,trading_as,market_message,product_service) AGAINST(\'' . $keywords . '\' IN BOOLEAN MODE)'] = true;
        }
//$find = $this->SupplierDirectory->find('first', array('conditions' => $conditions));
        if (!empty($conditions)) {
            //$conditions['site_id !='] = getCurrentSite('id');
            //$conditions['SupplierDirectory.deleted !='] = 1;
            $conditions[] = '(SupplierDirectory.deleted  IS NULL OR SupplierDirectory.deleted  = 0)';
			  $conditions[] = '(SupplierDirectory.status = 1)';

            $data = $this->paginate('SupplierDirectory', $conditions);
            debug($data);
            $this->set('data', $data);
            $this->set('search', true);
        } else {
            $this->set('search', false);
        }
		$this->set('title_for_layout',  __('Search Suppliers Directory', true));
        $this->_settings();
    }

    function owner_view($id = null) {
        $this->_settings();
        $row = $this->SupplierDirectory->read(null, $id);
        $site_info = $this->Site->findById($row['Site']['id']);
        $this->set('site_info', $site_info);

        if (empty($row)) {
            $this->flashMessage(__('Supplier not found', true));
            $this->redirect(array('action' => 'search'));
        }
        $this->set('row', $row);
		
		$this->set('title_for_layout',  __('View Supplier', true));
    }

    function owner_register() {
        $find = $this->SupplierDirectory->find('first', array('conditions' => array('site_id' => getCurrentSite('id'))));
        if (false&&isset($find['SupplierDirectory']['registered_status']) and $find['SupplierDirectory']['registered_status'] != SupplierDirectory::Automatic_Register) {
            $this->redirect(array('action' => 'update'));
        }

        if (!empty($this->data)) {
            if (isset($find['SupplierDirectory']['registered_status'])) {
                $this->data['SupplierDirectory']['id'] = $find['SupplierDirectory']['id'];
                $this->data['SupplierDirectory']['registered_status'] = SupplierDirectory::Automatic_Manual_Register;
            } else {
                $this->data['SupplierDirectory']['registered_status'] = SupplierDirectory::Manual_Register;
            }

            $this->data['SupplierDirectory']['site_id'] = getCurrentSite('id');

            $this->data['SupplierDirectory']['industry_id'] = implode(",", $this->data['SupplierDirectory']['industry_id'] ?? []);
            $this->data['SupplierDirectory']['status']=0;
            PortalSupplierDirectory::create($this->data['SupplierDirectory']);
			$this->_send_notification_email(empty($this->data['SupplierDirectory']['id'])?$this->SupplierDirectory->getLastInsertId():$this->data['SupplierDirectory']['id']);
            $this->flashMessage(__('Thank you for joining our supplier directory, your details will be added to the directory once it is reviewed and approved', true), 'Sucmessage');
            $this->redirect(array('action' => 'index'));
        } else {

            $site = $this->SupplierDirectory->Site->read(null, getCurrentSite('id'));
			debug($site);
            
            unset($site['Site']['id']);
            $this->data['SupplierDirectory'] = $site['Site'];
			unset($site['SupplierDirectory']['id']);
			
            $this->data['SupplierDirectory']['company_name'] = $site['Site']['business_name'];
            $this->data['SupplierDirectory']['abn'] = $site['Site']['bn1'];
            $this->data['SupplierDirectory']['phone'] = $site['Site']['phone1'];
            $this->data['SupplierDirectory']['postcode'] = $site['Site']['postal_code'];
			$this->data['SupplierDirectory']['suburb'] = $site['Site']['city'];
			$this->data['SupplierDirectory']['contact_person_fname'] = $site['Site']['first_name'];
			$this->data['SupplierDirectory']['contact_person_lname'] = $site['Site']['last_name'];
            $this->data['SupplierDirectory']['address'] = $site['Site']['address1'] . "\n" . $site['Site']['address2'];
        }
        $this->set('action', 'register');
		$this->set('title_for_layout',  __('Register a new supplier profile', true));
        $this->_settings();
    }

    function owner_update() {

        $this->_settings();
        $find = $this->SupplierDirectory->find('count', array('conditions' => array('registered_status' => array(SupplierDirectory::Manual_Register, SupplierDirectory::Automatic_Manual_Register), 'site_id' => getCurrentSite('id'))));
        $row = $this->SupplierDirectory->find('first', array('conditions' => array('registered_status' => array(SupplierDirectory::Manual_Register, SupplierDirectory::Automatic_Manual_Register), 'site_id' => getCurrentSite('id'))));
        if ($find == 0) {
            $this->redirect(array('action' => 'register'));
        }
        if (!empty($this->data)) {
            $this->data['SupplierDirectory']['id'] = $row['SupplierDirectory']['id'];
            $this->data['SupplierDirectory']['industry_id'] = implode(",", $this->data['SupplierDirectory']['industry_id'] ?? []);
            $this->data['SupplierDirectory']['status']=0;
            PortalSupplierDirectory::where('id', $this->data['SupplierDirectory']['id'])->update($this->data['SupplierDirectory']);
            $this->data['SupplierDirectory']['industry_id'] = explode(",", $this->data['SupplierDirectory']['industry_id']);
			$this->_send_notification_email($this->data['SupplierDirectory']['id']);
            $this->flashMessage(__('Thank you for updating your company details, your update will be published once it is reviewed and approved', true),'Sucmessage');
			$this->redirect(array('action' => 'index'));
        } else {
            $this->data = $row;
            $this->data['SupplierDirectory']['industry_id'] = explode(',', $this->data['SupplierDirectory']['industry_id']);
        }
        $this->set('action', 'update');
		$this->set('title_for_layout',  __('Update your supplier details', true));
        $this->render('owner_register');
    }
	
	function _send_notification_email($supplier_id) {
			
			$owner = getAuthOwner();
            $this->Email->from = $this->config['txt.admin_mail'];
            $this->Email->to = $this->config['txt.admin_mail'];
            $this->Email->subject = 'New Supplier Update';
            $this->Email->send('Supplier ID: '.$supplier_id."\r\n".'Site : '.$owner['business_name'].' (#'.$owner['id'].') '."\r\n".'Subdomin: '.$owner['subdomain'].' ');
    }

    function _settings() {
        $this->loadModel('Country');
        $this->set('countries', $this->Country->getCountryListName());
        $this->loadModel('Industry');
        $this->set('industries', $this->Industry->find('list'));
    }

}

?>