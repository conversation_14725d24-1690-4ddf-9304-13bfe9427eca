<?php


namespace App\Utils;


class ChartOfAccountsUtil
{
    public static function getSaveAccountRedirectUrl($data) {
        $folderId = '';
        $page = '';
        if(isset($_POST['folderid'])) {
            $folderId = $_POST['folderid'];
        } else if(isset($data['JournalCat']['folderid'])) {
            $folderId = $data['JournalCat']['folderid'];
        } else if(isset($data['JournalAccount']['folderid'])) {
            $folderId = $data['JournalAccount']['folderid'];
        }

        if(isset($data['pageNumber'])){
            $page = '?page='.$data['pageNumber'];
        }
        
        return '/v2/owner/chart-of-accounts/cats/'.$folderId.$page;
    }

    public static $accountsLimit = 100;
}