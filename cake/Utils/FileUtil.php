<?php

namespace App\Utils;


class FileUtil
{
    const B = 'b';
    const KB = 'kb';
    const MB = 'mb';
    const GB = 'gb';

    /**
     * @var array rates from every unit to bytes
     */
    public static $rates = [
        self::B => 1,
        self::KB => 1 * 1024,
        self::MB => 1 * 1024 * 1024,
        self::GB => 1 * 1024 * 1024 * 1024,
    ];

    /**
     * @param $size
     * @param string $from
     * @param string $to
     * @return float|int
     * transforms file size from unit to unit
     */
    public static function transformSize($size, $from = self::B, $to = self::MB) {
        return (self::$rates[$from] / self::$rates[$to]) * $size;
    }

    public static function formatSizeUnits($bytes)
    {
        if ($bytes >= 1073741824) {
            $bytes = number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            $bytes = number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            $bytes = number_format($bytes / 1024, 2) . ' KB';
        } elseif ($bytes > 1) {
            $bytes = $bytes . ' bytes';
        } elseif ($bytes == 1) {
            $bytes = $bytes . ' byte';
        } else {
            $bytes = '0 bytes';
        }

        return $bytes;
    }
}
