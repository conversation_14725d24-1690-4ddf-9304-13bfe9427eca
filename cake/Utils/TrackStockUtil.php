<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 25/11/19
 * Time: 03:04 م
 */

namespace App\Utils;


class TrackStockUtil
{
    const REQUISITION = 'Requisition';
    const REQUISITION_ITEM = 'RequisitionItem';
    const INVOICE = 'Invoice';
    const INVOICE_ITEM = 'InvoiceItem';
    const PURCHASE_ORDER = 'PurchaseOrder';
    const PURCHASE_ORDER_ITEM = 'PurchaseOrderItem';
    const MODE_ADD = 'add';
    const MODE_DEDUCT = 'deduct';
    const MODE_REFUND = 'refund';
    const MODE_CREDIT = 'credit';
    const MODE_TRANSFER = 'transfer';

    const TYPE_SERIAL = 'serial';
    const TYPE_LOT = 'lot';
    const TYPE_EXPIRY = 'expiry_date';
    const TYPE_LOT_EXPIRY = 'lot_and_expiry_date';
    const QUANTITY_ONLY = 'quantity_only';

    static function getItemTypeFromParent($parent_type) {
	    $item_map = [
		    TrackStockUtil::INVOICE => TrackStockUtil::INVOICE_ITEM,
		    TrackStockUtil::PURCHASE_ORDER => TrackStockUtil::PURCHASE_ORDER_ITEM,
		    TrackStockUtil::REQUISITION => TrackStockUtil::REQUISITION_ITEM
	    ];
	    return $item_map[$parent_type];
    }

    static function getTrackingTypeFieldName($trackingType)
    {
        $mapping = [
            self::TYPE_SERIAL => 'serial',
            self::TYPE_LOT => 'lot',
            self::TYPE_EXPIRY => 'expiry_date',
            self::TYPE_LOT_EXPIRY => 'lot',
            self::QUANTITY_ONLY => 'none',
        ];
        return $mapping[$trackingType];
    }

    static function getTrackingTypes()
    {
        return  array(
            self::TYPE_SERIAL =>__('Serial Number',true),
            self::TYPE_LOT =>__('Lot Number',true),
            self::TYPE_EXPIRY =>__('Expiry Date',true),
            self::TYPE_LOT_EXPIRY =>__('Lot Number And Expiry Date',true),
            self::QUANTITY_ONLY =>__('Quantity Only',true)
        );

    }

    static function getRequisitionTypeValidationMode($RequisitionType)
    {
        GetObjectOrLoadModel('Requisition');
        $mapping = [
            \Requisition::ORDER_TYPE_MANUAL_INBOUND => self::MODE_ADD,
            \Requisition::ORDER_TYPE_MANUAL_OUTBOUND => self::MODE_DEDUCT,
            \Requisition::ORDER_TYPE_TRANSFER_REQUISITION => self::MODE_TRANSFER,
            \Requisition::ORDER_TYPE_PURCHASE_ORDER => self::MODE_ADD,
            \Requisition::ORDER_TYPE_PURCHASE_REFUND => self::MODE_DEDUCT,
            \Requisition::ORDER_TYPE_INVOICE => self::MODE_DEDUCT,
            \Requisition::ORDER_TYPE_INVOICE_REFUND => self::MODE_REFUND,
            \Requisition::ORDER_TYPE_INVOICE_CREDIT_NOTE => self::MODE_CREDIT,
            \Requisition::ORDER_TYPE_PURCHASE_DEBIT_NOTE => self::MODE_DEDUCT,
            \Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP => self::MODE_ADD,
            \Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT => self::MODE_ADD,
            \Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL => self::MODE_DEDUCT,
            \Requisition::ORDER_TYPE_MANUFACTURE_ORDER_OUTBOUND_MATERIAL_REFUND => self::MODE_ADD,

        ];
        return $mapping[$RequisitionType];
    }

    public static function getPurchaseOrderValidationType($purchaseOrderType)
    {
        GetObjectOrLoadModel('PurchaseOrder');
        $mapping = [
            \PurchaseOrder::PURCHASE_INVOICE => self::MODE_ADD,
            \PurchaseOrder::Purchase_Refund => self::MODE_DEDUCT,
            \PurchaseOrder::DEBIT_NOTE => self::MODE_DEDUCT,
        ];
        return $mapping[$purchaseOrderType];
    }

    static function getInvoiceValidationType($invoiceType)
    {
        GetObjectOrLoadModel('Invoice');
        $mapping = [
            \Invoice::Invoice => self::MODE_DEDUCT,
            \Invoice::Refund_Receipt => self::MODE_REFUND,
            \Invoice::Credit_Note => self::MODE_REFUND,
        ];
        return isset($mapping[$invoiceType]) ? $mapping[$invoiceType] : false;
    }

}
