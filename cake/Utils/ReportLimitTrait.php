<?php

namespace App\Utils;

trait ReportLimitTrait{

    	/**
	 * This function is used to rate limit the report results based on a threshold limit variable
	 * It takes 2 params 
	 * QueryData (Report result)
	 * UrlPath which is related to the current report
	 * @return void 
	 */
	function displayLimitThreshold($queryData, $limit = false, $handler = 'v1') {

		$actual_link = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
        $urlParams = parse_url($actual_link);
        $thresholdLimit = $limit ? $limit : (defined('REPORT_DATA_THRESHOLD_LIMIT') ? REPORT_DATA_THRESHOLD_LIMIT : 7000);
        if(is_countable($queryData) && ($records_count=count($queryData)) > $thresholdLimit && strpos($actual_link, '.csv') === false) {
			parse_str($urlParams['query'], $params);
            if(!isset($_GET['csv_confirmed']))
            {
				$params['auto_csv'] = 1;
				$params['threshold_limit'] = $records_count;
				$params['handler'] = $handler;
                $query = http_build_query($params);
				$actual_link = $urlParams['scheme'].'://'.$urlParams['host'].$urlParams['path'].'?'.$query;
            }

            return  redirect($actual_link);
        }
		
	}

    function displayLimitThresholdByCount($count, $limit = false, $handler = 'v1') {

		$actual_link = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
        $urlParams = parse_url($actual_link);
        $thresholdLimit = $limit ? $limit : (defined('REPORT_DATA_THRESHOLD_LIMIT') ? REPORT_DATA_THRESHOLD_LIMIT : 7000);
        if($count > $thresholdLimit && strpos($actual_link, '.csv') === false) {
			parse_str($urlParams['query'], $params);
            if(!isset($_GET['csv_confirmed']))
            {
				$params['auto_csv'] = 1;
				$params['threshold_limit'] = $count;
				$params['handler'] = $handler;
                $query = http_build_query($params);
				$actual_link = $urlParams['scheme'].'://'.$urlParams['host'].$urlParams['path'].'?'.$query;
            }
        
            return  redirect($actual_link);
        }
		
	}


	function redirectToConfirmation() {
		if(isset($_GET['auto_csv']))
        {
            $actual_link = "https://{$_SERVER['HTTP_HOST']}{$_SERVER['REQUEST_URI']}";
            $parsed_path = parse_url($actual_link, PHP_URL_PATH);
            $referer_path = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_PATH);
            $records_count=$_GET['threshold_limit'];
            $handler = $_GET['handler'] ?? 'v1';
            if($referer_path == str_replace('.csv','', $parsed_path)) {
                $parsed = parse_url($actual_link); 
                parse_str($parsed['query'], $params);
                foreach(['auto_csv','threshold_limit','handler'] as $_key) {
                    unset($params[$_key]);
                }
                $paramsToString = http_build_query($params);
                $download_url = $parsed['scheme'].'://'.$parsed['host'].$parsed['path'].'.csv?'.$paramsToString;
                $this->redirect('/owner/reports/confirm_csv?records_count='.$records_count.'&handler='.$handler.'&download_url='.base64_encode($download_url).'&report_url='.base64_encode($actual_link));
            }
        }
	}


}