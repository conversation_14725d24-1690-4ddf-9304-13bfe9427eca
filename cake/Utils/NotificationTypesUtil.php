<?php

namespace App\Utils;



/**
 * CakeNotificationTypesUtil Class set of all cake notification key
 * @package App\Utils
 * <AUTHOR> <<EMAIL>>
 */
class NotificationTypesUtil
{
    /**
     * get all cake notification name spaces
     * @return array
     */
    public static function getAllCakeNotificationNameSpaces()
    {
        return [
            AppNotificationUtil::NOTI_NO_INVOICE_CREATED=> "App\\Notifications\\NoInvoiceCreatedNotification",
            AppNotificationUtil::NOTI_NO_SMTP_SETTING => "App\\Notifications\\NoSmtpSettingNotification",
            AppNotificationUtil::NOTI_CLIENT_PENDING_PAYMENT=> "App\\Notifications\\ClientPendingPaymentNotification",
            AppNotificationUtil::NOTI_CLIENT_ACCEPT_ESTIMATE=>  "App\\Notifications\\ClientPendingPaymentNotification",
            AppNotificationUtil::NOTI_CLIENT_DECLINED_ESTIMATE=>  "App\\Notifications\\ClientDeclinedEstimateNotification",
            AppNotificationUtil::RECURRING_INVOICE_GENERATED=>  "App\\Notifications\\RecurrentInvoiceGeneratedNotification",
            AppNotificationUtil::Product_Low_Stock =>  "App\\Notifications\\ProductLowStockNotification",
            AppNotificationUtil::NOTI_INVOICE_DUE_AFTER =>  "App\\Notifications\\InvoiceDueDateAfter",
            AppNotificationUtil::NOTI_ASSIGN_CLIENT =>  "App\\Notifications\\AssignClientNotification",
            AppNotificationUtil::NOTI_UPDATE_NOTE=>  "App\\Notifications\\UpdateNoteNotification",
            AppNotificationUtil::NOTI_UPDATE_APPOINTMENT =>  "App\\Notifications\\UpdateAppointmentNotification",
            AppNotificationUtil::NOTI_UPDATE_WORK_ORDER =>  "App\\Notifications\\UpdateWorkOrderNotification",
            AppNotificationUtil::NOTI_CLIENT_SHARED_NOTE=>  "App\\Notifications\\ClientSharedNoteNotification",
            AppNotificationUtil::NOTI_UPDATE_APPOINTMENT_CLIENT=>  "App\\Notifications\\UpdateAppointmentClientNotification",
            AppNotificationUtil::NOTI_UPDATE_REQUISITION =>  "App\\Notifications\\UpdateRequisitionNotification",
            AppNotificationUtil::NOTI_ADD_REQUISITION =>  "App\\Notifications\\AddRequisitionNotification",
            AppNotificationUtil::NOTI_ORDER_DELIVERY_UPDATE =>  "App\\Notifications\\OrderDeliveryUpdateNotification",
            AppNotificationUtil::ADD_LEAVE_APPLICATION => "App\\Notifications\\AddLeaveApplicationNotification",
            AppNotificationUtil::APPROVE_LEAVE_APPLICATION => "App\\Notifications\\ApproveLeaveApplicationNotification",
            AppNotificationUtil::REJECT_LEAVE_APPLICATION => "App\\Notifications\\RejectLeaveApplicationNotification",
            AppNotificationUtil::ADD_MEMBERSHIP => "App\\Notifications\\AddMembershipNotification",


        ];
    }


    /** get laravel class name space with cake key
     * @param string $cakeKey
     * @return string
     */
    public static function getLaravelClassNamespaceWithCakeKey($cakeKey)
    {
        return isset(self::getAllCakeNotificationNameSpaces()[$cakeKey])? self::getAllCakeNotificationNameSpaces()[$cakeKey] : $cakeKey;
    }


    /**
     * get all cake notification Entity name
     * @return array
     */

    public static function getAllCakeNotificationEntityNames()
    {
        return [
            AppNotificationUtil::Product_Low_Stock => __('Product', true),
            AppNotificationUtil::NOTI_UPDATE_APPOINTMENT => __('Appointment', true),
            AppNotificationUtil::NOTI_NO_SMTP_SETTING => __('Configure SMTP', true),
            AppNotificationUtil::NOTI_ADD_REQUISITION => __('Requisition', true),
            AppNotificationUtil::NOTI_UPDATE_REQUISITION => __('Requisition', true),
            AppNotificationUtil::NOTI_UPDATE_WORK_ORDER => __('Work Order', true),
            AppNotificationUtil::NOTI_CLIENT_PENDING_PAYMENT => __('Payment', true),
        ];
    }

    /** get laravel entity name  with cake key
     * @param string $cakeKey
     * @return string
     */
    public static function getLaravelEntityNameWithCakeKey($cakeKey)
    {
        return isset(self::getAllCakeNotificationEntityNames()[$cakeKey]) ? self::getAllCakeNotificationEntityNames()[$cakeKey] : $cakeKey;
    }
}
