<?php

namespace App\Utils;

use Izam\Daftra\Common\Utils\SettingsUtil;

class InvoiceTypeUtil
{
    public static function getSource($advancePaymentCount = 0)
    {
        $invoice = GetObjectOrLoadModel('Invoice');
        $invoiceTypes =  [
            array(
                'name'  => __('Sales Invoice', true),
                'value' => $invoice::Invoice,
            ),
            array(
                'name'  => __('Debit Note', true),
                'value' => $invoice::DEBIT_NOTE,
            ),
        ];
        $isClientHasAdvancePayment = $advancePaymentCount > 0;
        if ($isClientHasAdvancePayment) {
            $advancePaymentInvoice = [
                'name'  => __('Advance Payment Invoice', true),
                'value' => $invoice::ADVANCE_PAYMENT,
            ];

            // Insert at index 1
            array_splice($invoiceTypes, 1, 0, [$advancePaymentInvoice]);
        }
        return $invoiceTypes;
    }
}