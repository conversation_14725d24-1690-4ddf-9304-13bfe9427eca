<?php

namespace App\Utils\Dashboard;

class DashboardWidgetUtil
{
    const ATTENDANCE_SUMMARY = 'attendance_summary';
    const PENDING_REQUESTS = 'pending_requests';
    const LEAVES_SUMMARY = 'leaves_summary';
    const CONTRACTS_SUMMARY = 'contracts_summary';
    const ATTENDANCE_FLAGS_SUMMARY = 'attendance_flags_summary';
    const FIRST_CONTRACTS_TO_EXPIRE = 'first_contracts_to_expire';
    const RESIDENCIES_EXPIRING_SOON = 'residencies_expiring_soon';

    const PAYROLL_SUMMARY = 'payroll_summary';
    const LEAVE_APPLICATIONS = 'leave_applications';

    private static $widget_permissions = [
        self::ATTENDANCE_SUMMARY => [
            VIEW_ALL_THE_ATTENDANCE_LOG,
            VIEW_HIS_OWN_ATTENDANCE_LOG
        ],
        self::PENDING_REQUESTS => [
            MANAGE_REQUESTS
        ],
        self::LEAVES_SUMMARY => [
            VIEW_ALL_THE_ATTENDANCE_LOG,
            VIEW_HIS_OWN_ATTENDANCE_LOG
        ],
        self::CONTRACTS_SUMMARY => [
            VIEW_PAYROLL_CONTRACT
        ],
        self::ATTENDANCE_FLAGS_SUMMARY => [
            VIEW_ALL_THE_ATTENDANCE_LOG,
            VIEW_HIS_OWN_ATTENDANCE_LOG
        ],
        self::FIRST_CONTRACTS_TO_EXPIRE => [
            VIEW_PAYROLL_CONTRACT
        ],
        self::RESIDENCIES_EXPIRING_SOON => [
            Staff_View_Staffs
        ],
        self::PAYROLL_SUMMARY => [
            CREATE_PAY_RUN,
            VIEW_PAY_RUN,
        ],
        self::LEAVE_APPLICATIONS => [
            VIEW_ALL_LEAVE_APPLICATION,
            VIEW_HIS_OWN_LEAVE_APPLICATIONS,
        ]
    ];

    public static function getWidgetPermissions($widget)
    {
        return !empty(self::$widget_permissions[$widget]) ? self::$widget_permissions[$widget] : [];
    }
}
