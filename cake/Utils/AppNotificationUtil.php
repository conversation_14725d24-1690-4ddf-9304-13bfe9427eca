<?php

namespace App\Utils;



use Router;

/**
 * AppNotificationUrlNamesUtil Class set of all notifications that deal with cake or laravel
 * @package App\Utils
 * <AUTHOR> <<EMAIL>>
 */
class AppNotificationUtil
{
    /**
     * @const AppNotificationUrlNamesUtil::CONTRACT_EXPIRED_NOTIFICATION contract expired notification type
     */
 
    const CONTRACT_EXPIRED_NOTIFICATION = "App\\Notifications\\ContractExpiredNotification";
    const CONTRACT__WILL_EXPIRE_NOTIFICATION = "App\\Notifications\\ContractWillExpireNotification";
    const NOTI_NO_INVOICE_CREATED = 1;
    const NOTI_NO_SMTP_SETTING = 2;
    const NOTI_CLIENT_PENDING_PAYMENT = 7;
    const NOTI_CLIENT_ACCEPT_ESTIMATE = 8;
    const NOTI_CLIENT_DECLINED_ESTIMATE = 9;
    const RECURRING_INVOICE_GENERATED = 10;
    const Product_Low_Stock = 11;
    const NOTI_INVOICE_DUE_AFTER = 25;
    const NOTI_ASSIGN_CLIENT = 12;
    const NOTI_UPDATE_NOTE = 13;
    const NOTI_UPDATE_APPOINTMENT = 14;
    const NOTI_UPDATE_WORK_ORDER = 15;
    const NOTI_CLIENT_SHARED_NOTE = 16;
    const NOTI_UPDATE_APPOINTMENT_CLIENT = 18;
    const NOTI_UPDATE_REQUISITION = 19;
    const NOTI_ADD_REQUISITION = 20;
    const NOTI_ORDER_DELIVERY_UPDATE = 21;
    const ADD_LEAVE_APPLICATION = 22;
    const APPROVE_LEAVE_APPLICATION = 23;
    const REJECT_LEAVE_APPLICATION = 24;
    const ADD_MEMBERSHIP = 25;


    /**
     * @return array
     */
    static public function getAllTypes()
    {
        return [
            self::CONTRACT_EXPIRED_NOTIFICATION => 'owner.contracts.show',
            self::CONTRACT__WILL_EXPIRE_NOTIFICATION => 'owner.contracts.show',
            "App\\Notifications\\UpdateAppointmentNotification" => '/client/appointments/view/',
            "App\\Notifications\\UpdateWorkOrderNotification" => Router::url(['prefix' => 'owner','controller' => 'work_orders', 'action' => 'view']),
        ];
    }

    /**
     * @return array
     */
    static public function getAllNames()
    {
        return [
            self::CONTRACT_EXPIRED_NOTIFICATION => __t('Contract'),
            self::CONTRACT__WILL_EXPIRE_NOTIFICATION =>__t('Contract'),
            "App\\Notifications\\NoSmtpSettingNotification"=>__t('SMTP Settings'),
            "App\\Notifications\\ProductLowStockNotification"=>__t('Product'),
            "App\\Notifications\\InvoiceDueDateAfter"=>__t('Invoice'),
            "App\\Notifications\\AssignClientNotification" => __t('Client'),
            "App\\Notifications\\RecurrentInvoiceGeneratedNotification"=>__t('Invoice'),
            "App\\Notifications\\UpdateNoteNotification"=>__t('Note'),
            "App\\Notifications\\UpdateRequisitionNotification"=>__t('Requisition'),
            "App\\Notifications\\AddRequisitionNotification"=>__t('Requisition'),
            "App\\Notifications\\UpdateWorkOrderNotification"=>__t('Work Order'),
            "App\\Notifications\\UpdateAppointmentNotification" => __t('Appointment'),
            "App\\Notifications\\ClientSharedNoteNotification"=>__t('Note'),
            "App\\Notifications\\UpdateAppointmentClientNotification"=>__t('Appointment'),
            "App\\Notifications\\NoInvoiceCreatedNotification"=>__t('Invoice'),
            "App\\Notifications\\ClientPendingPaymentNotification"=>__t('Payment'),
            "App\\Notifications\\AddRequestNotification"=>__t('Request'),
            "App\\Notifications\\AddLeaveApplicationNotification"::class=>__t('Leave Application'),
            "App\\Notifications\\ApproveLeaveApplicationNotification"::class=>__t('Leave Application'),
            "App\\Notifications\\RejectLeaveApplicationNotification"::class=>__t('Leave Application'),
            "App\\Notifications\\AddMembershipNotification"=>__t('New  Membership'),

        ];
    }



    /**
     * get url alias name for notification
     * @param string $notificationType
     * @return string
     */
    static public function getUrlNameUsingNotificationType( $notificationType)
    {

        return isset(self::getAllTypes()[$notificationType]) ? self::getAllTypes()[$notificationType] : '';
    }

    static public function getNameUsingNotificationType( $notificationType)
    {

        return isset( self::getAllNames()[$notificationType]) ? self::getAllNames()[$notificationType] : '';
    }
}
