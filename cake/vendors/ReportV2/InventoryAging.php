<?php

namespace ReportV2;

use DateTime;
use Izam\Daftra\Common\Utils\StockTransactionUtil;
use Izam\Daftra\Common\Utils\StoreStatusUtil;

class InventoryAging extends Base
{
    function __construct($name, $params = null)
    {
        $this->report = [
            'modelName' => 'Product',
            'title' => 'Inventory Aging Report',
            'permissions' => [
                Track_Inventory,
                View_His_Own_Reports,
            ],
            'table' => 'products P',
            'fields' => [
                'id' => ['query' => '`P`.`id`', 'type' => ''],
                'product_code' => ['query' => '`P`.`product_code`', 'type' => ''],
                'name' => ['query' => '`P`.`name`', 'type' => '', 'link' => ['url' => '/owner/products/view/$id', 'params' =>['$id' => 'id']]],
                "bucket_0_30" => ['type' => self::FIELD_TYPE_NUMBER],
                "bucket_31_60" => ['type' => self::FIELD_TYPE_NUMBER],
                "bucket_61_90" => ['type' => self::FIELD_TYPE_NUMBER],
                "bucket_90_plus" => ['type' => self::FIELD_TYPE_NUMBER],
                "avg_age_days" => ['type' => self::FIELD_TYPE_NUMBER],
            ],
            'display_fields' => [
                'product_code' => __('Code', true),
                'name' => __('Name', true),
                'available_qty' => __('Available Quantity', true),
                'bucket_0_30' => sprintf(__("%s Days", true), "0-30"),
                'bucket_31_60' => sprintf(__("%s Days", true), "31-60"),
                'bucket_61_90' => sprintf(__("%s Days", true), "61-90"),
                'bucket_90_plus' => sprintf(__("%s Days", true), "90+"),
                'avg_age_days' => __('Average Age (in days)', true),
            ],
            'filters' => [
                'product' => [
                    'type' => 'text',
                    'input' => [
                        'label' => __('Search', true),
                        'name' => 'product',
                        'class' => 'form-control',
                        'div' => 'form-group col-md-3',
                        'placeholder' => __('Search by Code or Name', true),
                    ],
                    'condition' => "(P.name LIKE '%\$product%' OR P.product_code LIKE '%\$product%')"
                ],

                // Branch filter comes automatically if branchesConditionAlias is set below.

                'warehouse_id' => [
                    'type' => 'select',
                    'input' => [
                        'multiple' => 'multiple',
                        'label' => __('Warehouse', true),
                        'name' => 'warehouse_id',
                        'data-live-search' => true,
                        'class' => 'form-control input-select',
                        'div' => 'form-group col-md-3',
                        'dynamic_options' => [
                            'model' => 'Store',
                            'conditions' => [],
                            'fields' => ['Store.id', 'Store.name']
                        ]
                    ],
                    'condition' => "ST.store_id IN (\$warehouse_id)"
                ],

                'to_date' => [
                    'type' => 'date',
                    'input' => [
                        'name' => 'to_date',
                        'type' => 'text',
                        'label' => __('Date', true),
                        'class' => 'form-control hasDate',
                        'div' => 'form-group col-md-3'
                    ],
                    // cap considered movements up to the chosen date
                    'condition' => "ST.received_date <= '\$to_date'"
                ],
            ],

            'joins' => [
                'LEFT JOIN stock_transactions ST ON ST.product_id = P.id',
            ],

            'branchesConditionAlias' => 'ST',
            
            'conditions' => [
                "ST.ignored = 0",
                "ST.status = " . StockTransactionUtil::STATUS_PROCESSED,
                
            ],

            'group_by' => [
                __('Product', true) => 'null',
            ],
            'default_group_by' => 'P.id',
            'has_summary' => false,
            'has_details' => true,
            'order_by' => [],
            'graphs' => [],
            'allow_removed' => true,
            'removed_text' => ' ',
            'hide_subtotals' => true,
            'total_fields' => [
                'avg_age_days' => self::AGGREGATE_FUNCTION_IGNORE, // we compute custom average below
            ],
            'group_fields' => []
        ];

        parent::__construct($name, $params);
    }

    public function process_query_data($rows)
    {
        // === Inputs & filters ===
        $toDate = isset($this->params['to_date']) && $this->params['to_date'] !== ''
            ? $this->Model->formatDate($this->params['to_date'])
            : date('Y-m-d'); // YYYY-MM-DD
        $toDate .= ' 23:59:59';

        $search = $this->params['product'] ?? '';
        $warehouseIds = [];
        if (!empty($this->params['warehouse_id'])) {
            $warehouseIds = is_array($this->params['warehouse_id']) ? $this->params['warehouse_id'] : [$this->params['warehouse_id']];
            $warehouseIds = array_values(array_filter($warehouseIds, fn($v) => $v !== '' && $v !== null));
        }
        $branchIds = [];
        if (!empty($this->params['branch_id'])) { // present when Branch plugin is active
            $branchIds = is_array($this->params['branch_id']) ? $this->params['branch_id'] : [$this->params['branch_id']];
            $branchIds = array_values(array_filter($branchIds, fn($v) => $v !== '' && $v !== null));
        }

        // === Build WHERE clauses ===
        $where = [
            "ST.ignored = 0",
            "ST.`received_date` <= '{$toDate}'",
            "ST.status = " . StockTransactionUtil::STATUS_PROCESSED
        ];

        // Search by code or name (matches step-5 filter)
        if ($search !== '') {
            $esc = addslashes($search);
            $where[] = "(P.name LIKE '%{$esc}%' OR P.product_code LIKE '%{$esc}%')";
        }

        // Warehouse filter
        if ($warehouseIds) {
            $in = implode(",", array_map('intval', $warehouseIds));
            $where[] = "ST.store_id IN ({$in})";
        }

        // Branch filter via stores
        if ($branchIds) {
            $in = implode(",", array_map('intval', $branchIds));
            $where[] = "ST.branch_id IN ({$in})";
        }

        $where[] = "S.active = " . StoreStatusUtil::ACTIVE;
        $where[] = "EXISTS (SELECT 1 FROM stores S2 WHERE S2.id = ST.store_id AND S2.active = " . StoreStatusUtil::ACTIVE . ")";

        $whereSql = $where ? ('WHERE ' . implode(' AND ', $where)) : '';

        // === 30-day buckets relative to $toDate ===
        // period1: (toDate - 30d, toDate]
        // period2: (toDate - 60d, toDate - 30d]
        // period3: (toDate - 90d, toDate - 60d]
        // period4: <= (toDate - 90d)
        $sql = "
        SELECT
            P.product_code            AS code,
            P.id                      AS product_id,
            P.name                    AS name,
         
            SUM(ST.quantity)          AS total_stock,
            SUM(
              CASE
                WHEN ST.`received_date` >  DATE_SUB('{$toDate}', INTERVAL 30 DAY)
                 AND ST.`received_date` <= '{$toDate}'
                AND ST.quantity > 0
                THEN ST.quantity
                ELSE 0
              END
            ) AS period1,
            SUM(
              CASE
                WHEN ST.`received_date` >  DATE_SUB('{$toDate}', INTERVAL 60 DAY)
                 AND ST.`received_date` <= DATE_SUB('{$toDate}', INTERVAL 30 DAY)
                 AND ST.quantity > 0
                THEN ST.quantity
                ELSE 0
              END
            ) AS period2,
            SUM(
              CASE
                WHEN ST.`received_date` >  DATE_SUB('{$toDate}', INTERVAL 90 DAY)
                 AND ST.`received_date` <= DATE_SUB('{$toDate}', INTERVAL 60 DAY)
                AND ST.quantity > 0
                THEN ST.quantity
                ELSE 0
              END
            ) AS period3,
            SUM(
              CASE
                WHEN ST.`received_date` <= DATE_SUB('{$toDate}', INTERVAL 90 DAY)
                AND ST.quantity > 0
                THEN ST.quantity
                ELSE 0
              END
            ) AS period4,
             SUM(
              CASE
                WHEN ST.`received_date` <= '{$toDate}'
                AND ST.quantity < 0
                THEN ST.quantity
                ELSE 0
              END
            ) AS all_neg
        FROM stock_transactions ST
        INNER JOIN products P ON P.id = ST.product_id
        LEFT JOIN stores   S ON S.id = ST.store_id
        {$whereSql}
        GROUP BY P.id
    ";


        $agg = $this->Model->query($sql);


        $product_ids = array_column(array_column($agg, 'P'), 'product_id');
        $secondQuery  = "
        SELECT
            product_id,      
            received_date,
            quantity
            from stock_transactions
            where product_id in (" . implode(',', $product_ids) . ")
            and stock_transactions.status = " . StockTransactionUtil::STATUS_PROCESSED . "
            and stock_transactions.ignored = 0
            and received_date <= '{$toDate}'
            and quantity > 0
            order by received_date desc
            ";



        $stocksRawData = $this->Model->query($secondQuery);

        foreach ($stocksRawData as $row) {
            $st = $row["stock_transactions"];
            $pid = $st["product_id"];
            $stocks[$pid][] = $st;
        }
        // Index aggregated results by product_code for easy mapping back to $rows
        $byCode = [];
        foreach ($agg as $r) {
            // Cake returns mixed shapes; normalize
            $code = $r["P"]['product_id'];
            if (!$code) {
                continue;
            }
            $total_stock = (float)($r[0]['total_stock'] ?? 0);
            $all_negative = (float)($r[0]['all_neg'] ?? 0);
            $period1 = (float)($r[0]['period1'] ?? 0);
            $period2 = (float)($r[0]['period2'] ?? 0);
            $period3 = (float)($r[0]['period3'] ?? 0);
            $period4 = (float)($r[0]['period4'] ?? 0);


            $old_neg = $all_negative;
            if ($old_neg > 0) {
                $old_neg = 0;
            }
            $all_negative+= $period4;
            $final_p4 = ($all_negative ) < 0 ? 0  : ($period4  + $old_neg);

            $old_neg = $all_negative;
            if ($old_neg > 0) {
                $old_neg = 0;
            }
            $all_negative+= $period3;
            $final_p3 = ($all_negative ) < 0 ? 0  : ($period3 + $old_neg);



            $old_neg = $all_negative;
            $all_negative+= $period2;

            if ($old_neg > 0) {
                $old_neg = 0;
            }
            $final_p2 = ($all_negative ) < 0 ? 0  : ($period2 + $old_neg );

            $old_neg = $all_negative;
            if ($old_neg > 0) {
                $old_neg = 0;
            }
            $all_negative+= $period1;
            $final_p1 = ($all_negative ) < 0 ? 0  : ($period1  + $old_neg);



            $byCode[$code] = [
                'available_qty' => $total_stock,  // net up to cutoff
                'bucket_0_30' => $final_p1,
                'bucket_31_60' => $final_p2,
                'bucket_61_90' => $final_p3,
                'bucket_90_plus' => $final_p4,
            ];


            $bucket_0_30 = $final_p1;
            $bucket_31_60 = $final_p2;
            $bucket_61_90 = $final_p3;
            $bucket_90_plus = $final_p4;
            $byCode[$code]['avg_age_days'] = 0;
            if ($total_stock <= 0) {
                continue;
            }
            $average = 0;
            foreach ($stocks[$code] as $stock) {

                $buketIndex = $this->monthBucketIndex($stock['received_date'], $toDate);
                $buket = $this->getBucketKey($buketIndex);
                if (${$buket} <= 0) {
                    continue;
                }
                $dateDiff = (float) $this->daysDiff($toDate, $stock['received_date']);

                $minBuket = min((float) $stock['quantity'], ${$buket});

               // dump($minBuket, $dateDiff);
                $average+= $minBuket * $dateDiff;
                ${$buket}-= $minBuket;
            }
            $byCode[$code]['avg_age_days'] =  $byCode[$code]['avg_age_days_num'] =   $average / $total_stock;
        }
        // Merge into rows; keep computed fields under alias R
        foreach ($rows as &$row) {
            $code = $row['P']['id'];

            if ($code && isset($byCode[$code])) {
                $row['R'] = $byCode[$code];
            } else {
                // default zeros if no transactions match filters
                $row['R'] = [
                    'available_qty' => 0,
                    'avg_age_days' => 0,
                    'bucket_0_30' => 0,
                    'bucket_31_60' => 0,
                    'bucket_61_90' => 0,
                    'bucket_90_plus' => 0,
                ];
            }
        }
        return $rows;
    }

    function getBucketKey(int $index): ?string {
        $keys = [
            0 => 'bucket_0_30',
            1 => 'bucket_31_60',
            2 => 'bucket_61_90',
            3 => 'bucket_90_plus'
        ];
        return $keys[$index] ?? null; // null if index not 0–3
    }


    public function process_final_data($final)
    {
        // Sort rows by computed avg_age_days DESC
        if (isset($final['data'])) {
            foreach ($final['data'] as &$groupRows) {
                usort($groupRows, function ($a, $b) {
                    $av = (float)($a['R']['avg_age_days_num'] ?? $a['avg_age_days_num'] ?? 0);
                    $bv = (float)($b['R']['avg_age_days_num'] ?? $b['avg_age_days_num'] ?? 0);
                    if ($av === $bv) {
                        return 0;
                    }
                    return ($av > $bv) ? -1 : 1;
                });
            }
        }

        // Total = average of row averages (sum(avg_age)/row_count)
        $sumAvg = 0.0;
        $count = 0;

        if (isset($final['data'])) {
            foreach ($final['data'] as $groupRows) {
                foreach ($groupRows as $r) {

                    if (!empty($r['avg_age_days_num'])) {
                        $sumAvg += (float)$r['avg_age_days_num'];
                        $count += 1;
                    }
                }
            }
        }
        if (!isset($final['total_fields'])) {
            $final['total_fields'] = [];
        }

        $final['total_fields']['avg_age_days'] = $count > 0 ? round($sumAvg / $count, 2) : 0;
        return $final;

        return $final;
    }

    function daysDiff($date1, $date2): int {
        $d1 = new DateTime($date1);
        $d2 = new DateTime($date2);
        return $d1->diff($d2)->days; // absolute difference in days
    }
    private function monthBucketIndex($dateInput, $referenceDate): int {
        $d = new DateTime($dateInput);
        $ref = new DateTime($referenceDate);

        $y = (int)$ref->format('Y') - (int)$d->format('Y');
        $m = (int)$ref->format('n') - (int)$d->format('n');
        $months = abs($y * 12 + $m);

        // adjust by day-of-month if needed
        $dAdjusted = (clone $d)->modify(($d <= $ref ? '+' : '-') . $months . ' months');
        if (($d <= $ref && $dAdjusted > $ref) || ($d > $ref && $dAdjusted < $ref)) {
            $months = max(0, $months - 1);
        }

        if ($months < 1) return 0;
        if ($months < 2) return 1;
        if ($months < 3) return 2;
        return 3;
    }

    public function show_report()
    {
        return true;
    }
}
