<?php

namespace ReportV2;

use ReportV2\Helper\ClientCategories;
use ReportV2\Helper\ClientLocalEntityCustomData;

class ClientPayment extends Base
{

    use ClientLocalEntityCustomData;
    use ClientCategories;

    function __construct($name, $params = null)
    {

        /*******************/
        $clientExtraJoin = $extraJoin = '';
        $CustomForm = GetObjectOrLoadModel('CustomForm');
        $result = $CustomForm->get_display_form_fields('Client');
        $localCustomFormFields = $this->getLocalCustomFormFields();
        if (!empty($localCustomFormFields)) {
            $clientExtraJoin = ' LEFT JOIN ' . "le_custom_data_client" . ' CustomData ON CustomData.' . 'reference_id' . ' = C.id ';
        } elseif (!empty($result['fields'])) {
            //If client id from payment is null then fetch client id from invoice to make a valid relation
            $extraJoin = ' LEFT JOIN ' . $result['customDataTableName'] . ' CustomData ON CustomData.' . $result['customDataForeignKey'] . ' = IF(`IP`.`client_id` IS NOT NULL, `IP`.`client_id`, `I`.`client_id`)';
        }
        /***/
        $staff = GetObjectOrLoadModel('Staff');
        $ownerName = $staff->getOwnerUserName();
        $staff->virtualFields = [
            'virtual_full_name' => "CONCAT(Staff.name, ' ', IFNULL(Staff.middle_name, ''), ' ', IFNULL(Staff.last_name, ''))"
        ];
        $this->report = [
            'modelName' => 'Invoice',
            'title' => 'Clients Payments',
            'table' => 'invoice_payments IP',
            'fields' => [
                'payment_id' => ['query' => '`IP`.`id`', 'type' => ''],
                'phone'=> ['query' => "CONCAT( IFNULL(C.phone1,''), IF(C.phone1 IS NOT NULL AND C.phone2 IS NOT NULL ,' ,', '') ,IFNULL(C.phone2,'')  ) AS phone" ,'type' => ''],
                'address'=> ['query' => "CONCAT( IFNULL(C.address1,''), IF(C.address1 IS NOT NULL AND C.address2 IS NOT NULL ,' ,', '') ,IFNULL(C.address2,'')  ) AS address" ,'type' => ''],
                'payment_invoice_id' => ['query' => '`IP`.`invoice_id`', 'type' => ''],
                'date' => ['query' => '`IP`.`date`', 'type' => self::FIELD_TYPE_DATE],
                'payment_method' => ['query' => '`IP`.`payment_method`', 'type' => ''],
                'invoice_id' => ['query' => '`I`.`id`', 'type' => ''],
                'no' => ['query' => '`I`.`no`', 'type' => ''],
                'type' => ['query' => '`I`.`type`', 'type' => ''],
                'client_number' => ['query' => '`C`.`client_number`', 'type' => ''],
                'client_name' => ['query' => 'CONCAT(`C`.`business_name`," ",CONCAT(COALESCE(`C`.`first_name`,"")," ",COALESCE(`C`.`last_name`,""))) as client_name', 'type' => ''],
                'treasury' => ['query' => 'CONCAT(`T`.`name`," #",`T`.`id`) as treasury', 'type' => ''],
                'amount' => ['query' => '`IP`.`amount`', 'type' => SELF::FIELD_TYPE_PRICE, 'colored' => true],
                'currency' => ['query' => '`IP`.currency_code'],
                "category"=>['query'=>"C.category AS category",'type'=>''],
                "assigned_staff"=>['query'=>' ( CASE WHEN GROUP_CONCAT(DISTINCT(S.full_name)) != "" THEN GROUP_CONCAT(DISTINCT(S.full_name)) ELSE CONCAT(S.name," ",S.last_name) END) as assigned_staff','type'=>''],
                "collected_by"=>[
                    'query'=> "
                                (
                                    CASE
                                        WHEN IP.added_by = 0 THEN
                                            CASE
                                                WHEN IP.client_id IS NOT NULL AND IP.client_id != '' 
                                                    THEN C.business_name
                                                WHEN IP.invoice_id IS NOT NULL AND IP.invoice_id != '' 
                                                    THEN I.client_business_name
                                            END
                                        WHEN IP.staff_id = 0 THEN '$ownerName'
                                        WHEN IP.staff_id = -3 THEN 'API'
                                        WHEN IP.staff_id IS NOT NULL AND IP.staff_id != '' THEN
                                            CASE 
                                                WHEN collectedStaff.full_name IS NOT NULL AND collectedStaff.full_name != '' THEN collectedStaff.full_name
                                            ELSE 
                                                CONCAT(collectedStaff.name, ' ', COALESCE(collectedStaff.last_name,''))
                                            END    
                                    END
                                ) AS collected_by
                    ",
                    'type'=>''
                ],
                "added_by" => [
                    'query' => "
                                (
                                    CASE
                                        WHEN IP.added_by = 0 THEN
                                            CASE
                                                WHEN IP.client_id IS NOT NULL AND IP.client_id != '' 
                                                    THEN C.business_name
                                                WHEN IP.invoice_id IS NOT NULL AND IP.invoice_id != '' 
                                                    THEN I.client_business_name
                                            END
                                 

                                        WHEN IP.staff_id IS NOT NULL AND IP.staff_id != '' THEN
                                            CASE 
                                                WHEN collectedStaff.full_name IS NOT NULL AND collectedStaff.full_name != '' THEN collectedStaff.full_name
                                            ELSE 
                                                CONCAT(collectedStaff.name, ' ', COALESCE(collectedStaff.last_name, ''))
                                            END    
                                        WHEN I.staff_id = 0 THEN '$ownerName'                                
                                        WHEN I.staff_id IS NOT NULL AND I.staff_id != '' THEN
                                            CASE 
                                                WHEN invoiceStaff.full_name IS NOT NULL AND invoiceStaff.full_name != '' THEN invoiceStaff.full_name
                                            ELSE 
                                                CONCAT(invoiceStaff.name, ' ', COALESCE(invoiceStaff.last_name, ''))
                                            END    
                                        ELSE 'MO'    
                                    END
                                ) AS added_by
                    ",
                    'type' => ''
                ],
            ],
            'currency' => ['field' => 'currency_code', 'filter_field' => '`IP`.`currency_code`', 'convert_fields' => ['amount']],
            'display_fields' => [
                'payment_id' => 'ID',
                'date' => 'Date',
                'client_number' => 'Client Number',
                'client_name' => 'Client Name',
                'type' => 'Type',
                'no' => 'Document Number',
                'payment_method' => 'Payment Method',
                'treasury' => 'Treasury',
                'amount' => 'Amount',
                "assigned_staff" => 'Assigned Staff',
                "collected_by" => 'Collected By',
                "added_by" => 'Added By',
            ],
            'allow_removed' => true,
            'removed_text' => ' ',
            'group_fields' => [
                'amount' => 'SUM',
            ],
            'total_fields' => [
                'amount' => 'SUM',
            ],
            'filters' => [
                'client_id' => [
                    'type' => 'element',
                    'element' => [
                        'name' => 'advanced_client',
                        'options' => [
                            'multiple' => 'multiple',
                            'label' => __('Client', true) ,
                            'empty' => __('All', true),
                            'width' => '100%',
                            'input_name' => 'client_id',
                            'class' => 'form-control input-select',
                            'div' => 'form-group col-md-3',
                            'selected_client_id' => $_GET['client_id'],
                        ]
                    ],
                    'condition' => "C.id IN (\$client_id) ",
                ],
                'client_category' => [
					'type' => 'element',
					'element' => [
						'name' => 'advanced_client_category',
						'options' => [
							'label' => __('category', true) ,
							'empty' => __('All', true),
							'width' => '100%',
							'input_name' => 'client_category',
							'class' => 'form-control input-select',
							'div' => 'form-group col-md-3',
							'selected_category' => $_GET['client_category'],
							'client_categories' => $this->getClientCategories()
						]
					],
					'condition' => "C.category_id IN (\$client_category) ",

				],
                'payment_method' => [
                    'type' => 'select',
                    'input' => [
                        'label' => __('Payment Method', true) ,
                        'name' => 'payment_method',
                        'multiple' => 'multiple',
                        'type' => 'select',
                        'options' => $this->getPaymentMethods(),
                        'class' => 'input-select form-control ',
                        'div' => 'form-group col-md-3'
                    ],
                    'condition' => "IP.payment_method IN (\$payment_method) "
                ],
//                'client_id' => ['type' => 'select', 'input' => ['multiple' => 'multiple', 'label' => 'Client', 'name' => 'client_id', 'data-live-search' => true, 'class' => 'form-control input-select', 'div' => 'form-group col-md-3', 'dynamic_options' => ['model' => 'Client', 'conditions' => [], 'fields' => ['Client.id', 'Client.business_name']]], 'condition' => "C.id IN (\$client_id) "],
                'from_date' => ['type' => 'date', 'input' => ['name' => 'from_date', 'type' => 'text', 'label' => 'From Date', 'class' => 'form-control hasDate', 'div' => 'form-group col-md-3'], 'condition' => "IP.date >= '\$from_date'"],
                'to_date' => ['type' => 'date', 'input' => ['name' => 'to_date', 'type' => 'text', 'label' => 'To Date', 'class' => 'form-control hasDate', 'div' => 'form-group col-md-3'], 'condition' => "IP.date <= '\$to_date' "],
                'treasury_id' => ['type' => 'select', 'input' => ['id' => 'treasury_id', 'name' => 'treasury_id', 'type' => 'select', 'label' => __('Treasury', true), 'empty' => __('All', true), 'class' => 'input-select form-control ', 'data-live-search' => true, 'div' => 'form-group col-md-3', 'dynamic_options' => ['model' => 'Treasury', 'fields' => ['Treasury.id', 'Treasury.name']]], 'condition' => "T.id = \$treasury_id"],
                'show_details' => [  'input' => ['name' => 'show_details','type' => 'checkbox', 'label' =>  'Show details','class' => 'INPUT form-control', 'div' => 'hide_zero clip-check col-md-3 check-info' ], 'condition' => "" ],

               
                'assigned_staff' => [
                    'type' => 'element',
                    'element' => [
                        'name' => 'staff_smart_search',
                        'options' => [
                            'multiple' => true,
                            'label' => __('Assigned Staff', true) ,
                            'emptyOption' => true,
                            'width' => '100%',
                            'name' => 'assigned_staff',
                            'class' => 'form-control input-select',
                            'div' => 'form-group col-md-3',
                            'value' => $_GET['assigned_staff'],
                        ]
                    ],
                    'condition' => "(S.id IN (\$assigned_staff ))",
                ],
                'details' => [ 'type' => 'text','input' => ['name' => 'details','label' => $this ->getKeywordsFieldLabel(), 'class' => 'form-control ', 'div' => 'form-group col-md-3'], 'condition' => "(C.category like '%\$details%') OR (C.phone1 like '%\$details%' OR C.phone2 like '%\$details%') OR (C.address1 like '%\$details%' OR C.address2 like '%\$details%')" ],

            ],
            'conditions' => [
                '`IP`.`status` = 1',
            ],
            'joins' => [
                'LEFT JOIN invoices I on `I`.`id` = `IP`.`invoice_id`',
                'LEFT JOIN clients C on `C`.`id` = IF(`IP`.`client_id` IS NOT NULL, `IP`.`client_id`, `I`.`client_id`)',
                'LEFT JOIN treasuries T on `T`.`id` = `IP`.`treasury_id`',
                'LEFT JOIN item_staffs ISS ON ISS.item_id = C.id AND item_type = 1',
                'LEFT JOIN staffs S ON S.id = ISS.staff_id',
                'LEFT JOIN staffs collectedStaff ON collectedStaff.id = IP.staff_id',
              
                'LEFT JOIN staffs invoiceStaff ON I.staff_id = invoiceStaff.id',
                $clientExtraJoin,
                $extraJoin
                //'LEFT JOIN categories Categories on `Categories`.`id` = `C`.`treasury_id`',
            ],
            'group_by' => [
                'Client' => "CONCAT(`C`.`business_name`,\" \",COALESCE(CONCAT(`C`.`first_name`,\" \",`C`.`last_name`)),\" #\",`C`.`client_number`)",
                'Treasury' => 'CONCAT(`T`.`name`," #",`T`.`id`)',
                'Payment Method' => 'UPPER(`IP`.`payment_method`)'
            ],
            'has_summary' => false,
            'order_by' => [
                'Date Descending' => "`IP`.`date` DESC",
                'Date Ascending' => "`IP`.`date` ASC",
                'Number Descending' => "`I`.`no` DESC",
                'Number Ascending' => "`I`.`no` ASC"
            ],
            'default_group_by' => '`IP`.`id`',
            'graphs' => [
                ['type' => 'pie', 'group_fields' => ['amount']],
            ],
        ];

        parent::__construct($name, $params);

        $this->setCustomFields($result);
        $this->setCustomFieldsFilters();
        $this->custom_fields_result = $result;
        if(isset($_GET['show_details']) && $_GET['show_details'] == 1){
            $this->report['display_fields']['phone'] = 'Phone';
            $this->report['display_fields']['address'] = 'Address';
            $this->report['display_fields']['category'] = 'Category';

            if(isset($this->custom_fields_result)){

                foreach ($this->custom_fields_result['fields'] as $value){
                    if($value['CustomFormField']['is_listing'] == 0){
                        continue;
                    }
                    $field_key = 'field_'.$value['CustomFormField']['id'];
                    $field_label = $value['CustomFormField']['label'];
                    $this->report['display_fields'][$field_key] = $field_label;
                }
            }
            if (!empty($localCustomFormFields)) {
                $this->displayLocalCustomFormFields($localCustomFormFields);
                $this->setLocalCustomFormFieldsFilters($localCustomFormFields);
            }
        }
        $totalFields = $this->report['total_fields'];
        foreach ($this->report['display_fields'] as $fieldKey => $fieldLabel) {
            if ('payment_id' == $fieldKey) {
                continue;
            }
            if (isset($this->report['total_fields'][$fieldKey])) {
                $totalFields[$fieldKey] = $this->report['total_fields'][$fieldKey];
            } else {
                $totalFields[$fieldKey] = '-';
            }
        }
        $this->report['total_fields'] = $totalFields;
        $this->report['group_fields'] = $totalFields;
        if (empty($this->params['payment_method'])||!in_array('client_credit',$this->params['payment_method'])) {
            $this->report['conditions'][] = '`IP`.`payment_method` != \'client_credit\'';
        }

    }

    function process_final_data($final_data)
    {
        $totalFields = $final_data;
        foreach ($this->report['display_fields'] as $fieldKey => $fieldLable) {
            if (isset($final_data['total_fields'][$fieldKey]) && !empty($final_data['data'])) {
                $totalFields[$fieldKey] = $final_data['total_fields'][$fieldKey];
            } else {
                $totalFields[$fieldKey] = '';
            }
        }
        $final_data['total_fields'] = $totalFields;
        return $final_data;
    }

    function process_meta_data()
    {
    }

    function process_group_data($grouped_data)
    {
        return $grouped_data;
    }

    function getPaymentMethods(){
        $SitePaymentGateway=GetObjectOrLoadModel('SitePaymentGateway');
        $paymentMethods = $SitePaymentGateway->getPaymentGateways(0, true, false);
        $paymentMethods['client_credit']=__('Client Credit',true);
        return $paymentMethods;
    }

    function process_query_data($query_data)
    {
        $I = GetObjectOrLoadModel('Invoice');
        $paymentMethods = $this->getPaymentMethods();
        $invoice_payments_id=[];
        foreach ($query_data as $k => &$data) {
            $data['IP']['payment_id'] = '<a class="link-style" href="/owner/invoice_payments/view/' . $data['IP']['id'] . '">' . '#' . $data['IP']['id'] . '</a>';
            $invoice_payments_id[]=$data['IP']['id'];
            if (isset($paymentMethods[$data['IP']['payment_method']])) {
                $data['IP']['payment_method'] =   __($paymentMethods[$data['IP']['payment_method']],true) ;
            }else{
                $data['IP']['payment_method'] = __(ucwords(str_replace("_", " ", $data['IP']['payment_method'])), true);
            }

            if ($data['I']['type'] == $I::Invoice) {
                $data['I']['type'] = __('Invoice', true);
                $data['I']['no'] = '<a class="link-style" href="/owner/invoices/view/' . $data['I']['id'] . '">' . $data['I']['no'] . '</a>';
            } else if ($data['I']['type'] == $I::Refund_Receipt) {
                $data['I']['type'] = __('Refund Receipt', true);
                $data['I']['no'] = '<a class="link-style" href="/owner/invoices/view_refund/' . $data['I']['id'] . '">' . $data['I']['no'] . '</a>';
            }

            if ($data['IP']['invoice_id'] == null ) {
                $data['I']['type'] ='-';
                $data['I']['no'] = '-';
            }
        }

        //get added by staff from action lines ,to know who added this payment
        $idsArr = array_map('intval', $invoice_payments_id);
        $actionsByIP = [];

        if (!empty($idsArr)) {//get all action lines for these invoice payments 
            $ids = implode(',', $idsArr);
            $actions = $this->Model->query(
                "SELECT param5, param3, staff_id FROM action_lines
                 WHERE (action_key = " . ACTION_ADD_INVOICE_PAYMENT . " AND param5 IN ($ids))
                OR (action_key = " . ACTION_ADD_CLIENT_PAYMENT . " AND param3 IN ($ids))"
            );
            $staffs_ids = [];
            foreach ($actions as $r) {
                $al = $r['action_lines'];
                $ip = $al['param5'] ?: $al['param3'];
                if ($ip !== null && $ip !== '') {
                    $staffs_ids[] = (int)$al['staff_id'];
                    $actionsByIP[$ip] = ['IP' => $ip, 'staff_id' => $al['staff_id']];
                }
            }
        }

        $staffs_ids = array_unique($staffs_ids);
        $staffs = GetObjectOrLoadModel('Staff')->getList(true, ['Staff.id' => $staffs_ids],false,true); //get staff names that exist in action lines  
        if (!empty($actionsByIP)) {
            foreach ($query_data as $k => &$data) {
                $ipId = $data['IP']['id'] ?? null;
                if ($ipId && isset($actionsByIP[$ipId])) {
                    $staffId = $actionsByIP[$ipId]['staff_id'];
                    $data['0']['added_by'] = $staffs[$staffId] ?? $data['0']['added_by'];
                }
            }
        }

        return $this->process_query_local_entity_data($query_data);
    }

    function process_order_by($order_by)
    {
        return $order_by;
    }

    function process_filters($filters)
    {
        return $filters;
    }

    function process_filter_conditions($filters)
    {
        $staff_id = getAuthOwner('staff_id');
        if (!check_permission(VIEW_ALL_CLIENTS_REPORTS)) {
            if (check_permission(VIEW_HIS_OWN_CLIENTS_REPORTS)) {
                $filters['where']['staff_id'] = 'C.staff_id = ' . $staff_id;
            } else {
                return ['where' => ['1 = -1']];
            }
        }
        return $filters;
    }

    function process_header_filters($filters)
    {
        return $filters;
    }

    function setCustomFieldsFilters($model = false) //php8 fix make it compatible with parent
    {
        $ClientModel = GetObjectOrLoadModel('Client');
        return parent::setCustomFieldsFilters($ClientModel);
    }

    private function getDefaultGroupBy(): string
    {
        return " `IP`.`id` ";
    }

    private function getKeywordsFieldLabel(): string
    {
        return __('Keywords', true) .'<span class="tip" title="' . __('You can search by phone number, address and department for the client', true) . '">
                    <i class="fa fa-question-circle text-muted"></i>
                </span>';
    }
    
}

?>
