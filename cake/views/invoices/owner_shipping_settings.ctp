 <?php

use Izam\Daftra\Common\Utils\PluginUtil;

echo $html->css('nav-tabs', false);
echo $html->css('bootstrap-multiselect.css', null, []);
echo $javascript->link(array('main', 'nicEdit', 'humanize', 'bootstrap-multiselect.js'));

?>

 <div class="nav-wrapper side-nav-content">
     <div class="compat">
         <div class="my-10 vstack gap-10">

             <?php echo $form->create('Site', array('id'=> "settingsForm",   'type' => 'file','url' => '/owner/invoices/estimates_settings' ,  'controller' => 'invoices', 'action' => 'settings')); ?>
             <header class="pages-head page-fixed-start bg-none p-0" data-page-start="true">
                 <div class="page-head add-page-head">
                     <div class="container container-full">
                         <div class="page-head-row">
                             <div class="start">
                                 <!-- left area -->
                                 <div class="hstack">

                                 </div>
                                 <!-- /left area -->
                             </div>
                             <div class="center">
                                 <!-- center area -->
                                 <div class="hstack">

                                 </div>
                                 <!-- /center area -->
                             </div>
                             <div class="end">
                                 <!-- right area -->
                                 <div class="hstack">

                                     <a href="#" class="btn btn-secondary btn-responsive-icon">
                                         <i class="mdi mdi-close-thick me-xl-4"></i>
                                         <span><?php __("Cancel") ?></span>
                                     </a>

                                     <button type="submit" class="btn btn-success btn-responsive-icon add-new-btn">
                                         <i class="mdi mdi-content-save me-xl-4"></i>
                                         <span><?php __("Save") ?></span>
                                     </button>

                                 </div>
                                 <!-- /right area -->
                             </div>
                         </div>
                     </div>
                 </div>
             </header>

             <div class="card mb-10">
                 <div class="card-body p-25">

                     <div class="setting-section" id="section1">
                         <h3 class="fs-13 text-black mb-4 p-0"><?= __('Shipping Configuration', true) ?></h3>
                         <p><?= __('Define shipping options, fees, handling rules, and COD payment option.', true) ?>
                         </p>
                         <div class="">

                             <!-- 
                            change note
                            inverted
                            -->
                             <div class="form-group mt-md-15 mb-0">
                                 <label class="form-label fs-8 fw-medium text-black" for="SiteDisableShipping">
                                     <?= __('Shipping & Delivery Options', true) ?>
                                 </label>
                                 <div class="d-flex gap-5">
                                     <div class="flex-grow-1">

                                         <label
                                             class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                                             <?php
                                                echo $form->input('disable_shipping', array('div' => false, 'class' => 'form-check-input', 'label' => false, 'type' => 'checkbox'));
                                             ?>
                                             <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                                         </label>
                                     </div>
                                     <div class="flex-shrink-0">
                                         <?php echo '<a target="_blank" ' . (!settings::getValue(InvoicesPlugin, 'disable_shipping') ? "style='display:none;'" : "") . ' href="/v2/owner/shipping_options" id="disable_shipping_btn" class="btn btn-secondary text-decoration-none" ><i class="fa fa fa-cog me-4"></i>' . __("Manage Options", true) . '<i class="mdi mdi-open-in-new ms-4"></i></a>'; ?>


                                     </div>
                                 </div>
                                 <div class="form-text fs-7 mt-5">
                                     <?= __('Enable shipping and delivery options to define how goods are shipped, including method names, fees, and handling rules. Use this to customize order fulfillment based on your sales workflow.', true); ?>
                                 </div>
                             </div>




                             <div class="form-group mt-md-15 mb-0">
                                 <label class="form-label fs-8 fw-medium text-black" for="SiteNextInvoiceNumber">
                                     <?= __('Cash on Delivery Fees', true) ?>
                                 </label>
                                 <div class="d-flex gap-5">
                                     <div class="flex-grow-1">

                                         <select class="form-control" placeholder="Ajax Select" data-form-input="true" data-select-input="productsSelect">
                                            <option value="1">dddd</option>
                                        </select>
                                     </div>
                                     <div class="flex-shrink-0">
                                         <?php echo '<a target="_blank"   href="/v2/owner/shipping_options" id="disable_shipping_btn" class="btn btn-secondary text-decoration-none disabled" ><i class="fa fa fa-cog me-4"></i>' . __("Manage Options", true) . '<i class="mdi mdi-open-in-new ms-4"></i></a>'; ?>


                                     </div>
                                 </div>
                                 <div class="form-text fs-7 mt-5">
                                     <?= __('Enable shipping and delivery options to define how goods are shipped, including method names, fees, and handling rules. Use this to customize order fulfillment based on your sales workflow.', true); ?>
                                 </div>
                             </div>






                         </div>
                     </div>
                 </div>
             </div>

             <?php echo $form->end(); ?>
         </div>
     </div>
 </div>


 <?php include('views/invoices/partials/settings/settings-shipping-sidebar.ctp'); ?>

 <script>
$(document).ready(function() {


    var $input = $('[data-select-input="productsSelect"]');
    $input.selectize(Object.assign({}, window.APP.VENDORS.DEFAULTS.selectize.single, {
        valueField: "id",
        labelField: "text",
        load: function(query, callback) {
            var url =
                '/v2/owner/clients/filter?allow_suspended=1&term=__q__&_type=query&q=__q__';
            if (typeof this.$input.attr('multiple') == 'undefined') {
                this.clearOptions();
            } else {
                this.clearUnselectedOptions();
            }
            if (!query.length) return callback();
            $.ajax({
                url: url.replaceAll('__q__', encodeURIComponent(query)),
                type: "GET",
                dataType: 'json',
                error: function() {
                    callback();
                },
                success: function(response) {
                    if (response.results) {
                        callback(response.results);
                    } else {
                        callback();
                    }
                },
            });
        },
    }));

    $("#SiteDisableShipping").change(function() {
        $("#disable_shipping_btn").toggle();
    })

    $(document).on('click', '#statusIdBtn', function() {
        IzamModal.closeModals();
        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" src="/owner/follow_up_statuses/index/' +
            $(this).attr('status_id') + '?box=1&frameModal=1"></iframe>',
            '',
            '',
        );
    });
    $(document).on('click', '#autoNumberSettingsBtn', function() {
        IzamModal.closeModals();

        // Build URL with multiple query params
        const srcUrl =
            '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_ESTIMATE]) ?>?box=1&frameModal=1';

        IzamModal.addHtmlModal(
            '<iframe style="width:100%; height:100%; border:none;" ' +
            'src="' + srcUrl + '"></iframe>',
            '',
            ''
        );
    });
});
 </script>