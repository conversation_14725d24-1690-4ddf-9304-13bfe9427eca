<?php

echo $html->css('nav-tabs', false);
echo $html->css('bootstrap-multiselect.css', null, []);
echo $javascript->link(array('main', 'nicEdit', 'humanize', 'bootstrap-multiselect.js'));

echo $this->element('breadcrumbs', ['breadcrumbs' => [
        ['link' => '/v2/owner/sales_settings', 'title' => __('Sales Settings', true)],
        ['link' => '#', 'title' => __("Sales Order Settings", true)],
]]);

?>
<style>
     
    #flashMessage {
        margin-inline-start: 300px;
    }
    @media (max-width: 768px) {
    #flashMessage {
        margin-inline-start: 60px;
    }
}
    
    
 
</style>
<div class="side-nav-content">
    <div class="compat">
        <div class="vstack gap-10">

            <?php echo $form->create('Site', array('id' => "settingsForm", 'type' => 'file', 'url' => '/owner/invoices/sales_order_settings', 'controller' => 'invoices', 'action' => 'settings')); ?>
            <header class="pages-head page-fixed-start bg-none p-0" data-page-start="true">
                 <div class="page-head add-page-head">
                     <div class="container container-full p-0">
                         <div class="page-head-row">
                             <div class="start">
                                 <!-- left area -->
                                 <div class="hstack">

                                 </div>
                                 <!-- /left area -->
                             </div>
                             <div class="center">
                                 <!-- center area -->
                                 <div class="hstack">

                                 </div>
                                 <!-- /center area -->
                             </div>
                             <div class="end p-0">
                                 <!-- right area -->
                                 <div class="hstack gap-0">

                                    <a href="#" class="btn btn-clear btn-responsive-icon btn-touch">
                                        <i class="mdi mdi-close-thick me-xl-4 d-xl-none"></i>
                                        <span><?php __("Discard") ?></span>
                                    </a>

                                    <button type="submit" class="btn btn-success btn-responsive-icon add-new-btn btn-touch">
                                        <i class="mdi mdi-content-save me-xl-4"></i>
                                        <span><?php __("Save") ?></span>
                                    </button>

                                </div>
                                 <!-- /right area -->
                             </div>
                         </div>
                     </div>
                 </div>
             </header>

            <div class="card mb-10">
                <div class="card-body p-10 p-md-25">
                    <?php include('views/invoices/partials/settings/sales_order/sales_order-module.ctp'); ?>
                </div>
            </div>

            <?php echo $form->end(); ?>
        </div>
    </div>
</div>

<?php include('views/invoices/partials/settings/sales_order/sidebar.ctp'); ?>

<script>
    $(document).ready(function () {

        $("#SiteEnableSalesOrderStatus").change(function () {
            $("#enable_invoice_link").toggle();
        })

        $(document).on('click', '#statusIdBtn', function () {
            IzamModal.closeModals();
            IzamModal.addHtmlModal(
                '<iframe style="width:100%; height:100%; border:none;" src="/owner/follow_up_statuses/index/' +
                $(this).attr('status_id') + '?box=1&frameModal=1"></iframe>',
                '',
                '<?= __("Manage Custom Statuses", true) ?>',
                true
            );
        });
        $(document).on('click', '#autoNumberSettingsBtn', function () {
            IzamModal.closeModals();

            // Build URL with multiple query params
            const srcUrl =
                '<?= Router::url(['controller' => 'settings', 'action' => 'numbering', AutoNumber::TYPE_SALES_ORDER]) ?>?box=1&frameModal=1';

            IzamModal.addHtmlModal(
                '<iframe style="width:100%; height:100%; border:none;" ' +
                'src="' + srcUrl + '"></iframe>',
                '',
                ''
            );
        });
    });
</script>