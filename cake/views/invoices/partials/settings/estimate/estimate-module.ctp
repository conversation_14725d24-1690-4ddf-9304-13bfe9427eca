<div class="setting-section" id="section1">
    <h3 class="fs-13 text-black mb-4 p-0"><?= __('Estimate Configuration', true) ?></h3>
    <p><?= __('Configure how estimates are created, managed, and converted into invoices.', true) ?>
    </p>
    <div class="">

        <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                   for="SiteDisableEstimateModule"><?= __('Estimates Module', true) ?></label>
            <label
                    class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?php
                echo $form->input('disable_estimate_module', array('div' => false, 'class' => 'form-check-input', 'label' => false, 'type' => 'checkbox'));
                ?>
                <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Enable the Estimates Module to allow users to create, share, manage, and convert estimates into invoices.', true); ?>
            </div>
        </div>

        <div id="estimatesOptions" <?php echo '' . (!settings::getValue(InvoicesPlugin, 'disable_estimate_module') ? "style='display:none;'" : "") . '' ?>>

            <div class="form-group mt-12 mt-md-15 mb-0">
                <label class="form-label fs-8 fw-medium text-black" for="SiteNextInvoiceNumber">
                    <?= __('Next Auto-Generated Estimate Number', true) ?>
                </label>
                <div class="d-flex gap-5 flex-column flex-md-row">
                    <div class="flex-grow-1">
                        <?php
                        $value = empty($this->data['Site']['next_estimate_number']) ? '000001' : $this->data['Site']['next_estimate_number'];
                        echo $form->input('next_estimate_number', array('div' => false, 'type' => 'number', 'class' => 'INPUT form-control required', 'value' => $value, 'readonly' => true, 'disabled' => true, 'label' => false));
                        ?>
                    </div>
                    <div class="flex-shrink-0">

                        <?php echo '<button type="button" tabindex="-1" class="btn btn-secondary text-decoration-none" id="autoNumberSettingsBtn" href="#" title="Auto Number Settings"><i class="fa fa-cog me-4"></i>  ' . __('Auto Number Settings', true) . ' </button>'; ?>
                    </div>
                </div>
                <div class="form-text fs-7 mt-5">
                    <?= __('The number the system will assign to the next estimate.', true) ?>
                </div>
            </div>


            <?php if (ifPluginActive(FollowupPlugin)) { ?>
                <div class="form-group mt-12 mt-md-15 mb-0">
                    <label class="form-label fs-8 fw-medium text-black" for="SiteEnableInvoiceStatus">
                        <?= sprintf(__('%s Custom Statuses', true), __('Estimate', true)); ?>
                    </label>
                    <div class="d-flex gap-5 flex-column flex-md-row">
                        <div class="flex-grow-1">
                            <label
                                    class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                                <?php
                                echo $form->input('enable_estimate_status', array('type' => 'checkbox', 'div' => false, 'class' => 'form-check-input ', 'label' => false,));
                                ?>
                                <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                            </label>
                        </div>
                        <div class="flex-shrink-0">
                            <?php echo '<span id="enable_invoice_link"' . (!settings::getValue(InvoicesPlugin, 'enable_estimate_status') ? "style='display:none;'" : "") . '> <button type="button" id="statusIdBtn" status_id="' . AutoNumber::TYPE_ESTIMATE . '" class="btn btn-secondary text-decoration-none" ><i class="fa fa fa-cog me-4"></i>' . __("Manage Custom Statuses", true) . '</button></span>'; ?>
                        </div>
                    </div>
                    <div class="form-text fs-7 mt-5">
                        <?= __('Create custom estimate statuses that suit your workflow, such as “Pending Response” or “Follow-up Needed”, and assign them to estimate. Use custom statuses to filter and search estimates.', true) ?>
                    </div>
                </div>
            <?php } ?>


            <div class="form-group mt-12 mt-md-15 mb-0">
                <label class="form-label fs-8 fw-medium text-black"
                    for="SiteDisableEstimateModule"><?= __('Apply Offers to Estimates', true) ?></label>
                <label
                        class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">

                    <?php
                    echo $form->input('apply_offers_on_estimates', ['checked' => !empty($this->data['Site']['apply_offers_on_estimates']), 'type' => 'checkbox', 'label' => false, 'class' => 'form-check-input', 'div' => false]);
                    ?>
                    <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                </label>
                <div class="form-text fs-7 mt-5">
                    <?= __('Automatically apply available sales offers to estimates so clients can see potential savings early. Note: Offers may still apply if the estimate is converted to an invoice after the offer expires.', true); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(function() {
    $("#SiteDisableEstimateModule").change(function() {
        if ($(this).prop('checked')) {
            $("#estimatesOptions").show();

        } else {
            $("#estimatesOptions").hide();
        }
    })
    $("#SiteDisableEstimateModule").change();
})
</script>