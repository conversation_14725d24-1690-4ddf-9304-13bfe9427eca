
    <div class="m-t-30">
        <!-- Navigation Buttons -->
        <?php // echo $this->element ( 'invoices/settings_sidemenu' , ['selected_tab' => "general"]);?>

        <div class="col-md-12 no-padding" style="background:#fff">

            <div id="tab_contents" class="tab-content tabs-content side-content responsive">
                <div class="tab-pane active" id="general_settings">

                    <div class="FormExtended row">
                        <?php //echo $form->create('Site', array('id'=> "settingsForm",   'type' => 'file','url' => '/owner/invoices/settings' ,  'controller' => 'invoices', 'action' => 'settings')); ?>
                        <!-- <div class="pages-head">
                            <div class="">
                                <div class="row-flex align-items-center">
                                    <div class="col-flex-sm-6">
                                    </div>
                                    <div class="col-flex-sm-6 d-flex justify-content-end">
                                        <button type="submit" class="btn s2020 btn-icn btn-success font-weight-medium ml-2">
                                            <i class="mdi mdi-content-save-outline fs-20"></i>
                                            <span><?php __("Save") ?></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        
                        
                        <div>
                           
                            
                            
                            
                            
                            
                            
                            
                            
                            

                            
                            
                           
                            
                            
                            
                            <div class="form-group">
                                <?php
                                //                    echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('You Sell', true))));
                                echo $form->input('disable_estimate_module', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Disable Estimates Module', true)));
                                ?>
                                <div class="clearfix"></div>
                            </div>
                            <?php if(ifPluginActive(FollowupPlugin)){ ?>
                            
                            <div class="form-group">
                                <?php
                                //                    echo $form->input('sold_item_type', array_merge($form_data['sold_item_type'], array('type' => 'select', 'div' => 'col-md-6 col-sm-12 m-b-md', 'class' => 'form-control', 'label' => __('You Sell', true))));
                                echo $form->input('enable_estimate_status', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Enable Estimate Manual Statuses', true),
                                    'after' => '<span id="enable_estimate_link"'.( !settings::getValue(InvoicesPlugin, 'enable_estimate_status') ? "style='display:none;'" : "") .'><i class="fa fa fa-cog"></i> <a href="#" status_id="'. Post::ESTIMATE_TYPE.'" class="click_status" >'. __("Edit Statuses List" , true ).'</a>
                                </span>'));
                                ?>
                                <div class="clearfix"></div>
                            </div>
                            <?php } ?>
                            <div class="form-group">
                                <?php
                                echo $form->input('disable_shipping', array('type'=> 'checkbox',  'div' => '  clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Disable Shipping Options', true),));
                                ?>
                                <div class="clearfix"></div>
                            </div>
                            
                            <?php if(count($invoiceTemplates)>1){ ?>
                            <div class="form-group">
                                <?php
                                echo $form->input('default_prefill_template',  array('empty'=>__('Select Template',true),'options'=>$invoiceTemplates,'type' => 'select', 'div' => 'col-md-6 col-sm-12', 'class' => 'form-control', 'label' => __('Default Prefilled Template', true)));
                                ?>
                                <div class="clearfix"></div>
                            </div>
                            <?php } ?>
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            

                            
                            
                            <div class="form-group" id="client-social">
                                <?php
                                echo $form->input('invoice_integrate_social_media', array('type' => 'checkbox', 'label' => __('Send Transactions Via Social Media', true), 'class' => 'form-x1', 'div' => 'clip-check check-info',
                                    'after' => '<span id="invoice_integrate_social_media"' .
                                        (!settings::getValue(InvoicesPlugin, 'invoice_integrate_social_media') ? "style='display:none;'" : "") .
                                        '><i class="fa fa fa-cog"></i> <a href="/v2/owner/settings/share-social-media/invoice_integrate_social_media" target="_blank">' .
                                        __("Manage", true) .
                                        '</a> </span>'
                                ));
                                ?>
                                <div class="clearfix"></div>
                            </div>
                            
                            
                            
                            
                            
                            
                        </div>

                        <div class="form-group">
                            <?php
                            echo $form->input('apply_offers_on_estimates', ['checked'=>!empty($this->data['Site']['apply_offers_on_estimates']), 'type' => 'checkbox', 'label'=>__('Apply Offers For Estimates',true),'class' => 'form-x1', 'div' => 'clip-check check-info']);
                            ?>
                            <div class="clearfix"></div>
                        </div>

                        

                        

                        <div class="form-group" id="exceedingTheSalesOrderQtyInTheSalesInvoices">
                            <?= $form->input('exceeding_the_sales_order_qty_in_the_sales_invoices', array('checked'=>!empty($this->data['Site']['exceeding_the_sales_order_qty_in_the_sales_invoices']),'type' => 'checkbox', 'label' => __('Exceeding The Sales Order QTY In The Sales Invoices', true), 'class' => 'form-x1', 'div' => 'clip-check check-info')); ?>
                            <div class="clearfix"></div>
                        </div>

                        
                        <?php if(ifPluginActive(PluginUtil::EINVOICE_PLUGIN) || $hasAdvancePayment) { ?>
                        <div class="form-group">
                            <?php echo $form->input('enable_advance_payment', array('type'=> 'checkbox', 'checked' => !empty($this->data['Site']['enable_advance_payment']), 'div' => 'clip-check check-info', 'class' => 'INPUT form-control ', 'label' => __('Advance Payment', true), 'id' => "advance-payment-handle")); ?>
                            <div class="clearfix"></div>
                        </div>
                        <?php } ?>
                        
                        
                    </div>
                </div>
            </div>
        </div>
        <div class="clearfix" ></div>

    </div>
    
    <?php //echo $form->end(); ?>
</div>