<div class="setting-section" id="section1">
    <h3 class="fs-13 text-black mb-4 p-0"><?= __('Sales Oder Configuration', true) ?></h3>
    <p><?= __('Set up and manage sales order creation, numbering, and statuses.', true) ?>
    </p>
    <div class="">



        <div class="form-group mt-12 mt-md-15 mb-0">
            <label class="form-label fs-8 fw-medium text-black"
                for="SiteEnableSalesOrder"><?= __('Sales Orders Module', true) ?></label>
            <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                <?= $form->input('enable_sales_order', array('checked'=>!empty($this->data['Site']['enable_sales_order']),'type' => 'checkbox', 'label' =>false, 'class' => 'form-check-input', 'div' => false)); ?>
                <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
            </label>
            <div class="form-text fs-7 mt-5">
                <?= __('Enable the Sales Orders Module to allow users to create, share, manage, and convert Sales Orders into invoices.', true); ?>
            </div>
        </div>

        <!-- <div class="form-group">
            <?= $form->input('enable_sales_order', array('checked'=>!empty($this->data['Site']['enable_sales_order']),'type' => 'checkbox', 'label' => __('Enable Sales Order', true), 'class' => 'form-x1', 'div' => 'clip-check check-info')); ?>
            <div class="clearfix"></div>
        </div> -->

        <div id="salesOrderOptions" <?php echo '' . (!settings::getValue(InvoicesPlugin, 'enable_sales_order') ? "style='display:none;'" : "") . '' ?>>

            <div class="form-group mt-12 mt-md-15 mb-0">
                <label class="form-label fs-8 fw-medium text-black" for="SiteNextInvoiceNumber">
                    <?= __('Next Auto-Generated Sales Order Number', true) ?>
                </label>
                <div class="d-flex gap-5 flex-column flex-md-row">
                    <div class="flex-grow-1">
                        <?php
                        if (empty($this->data['Site']['next_sales_order_number'])) {
                            $value = '000001';
                        } else {
                            $value = $this->data['Site']['next_sales_order_number'];
                        }
                        echo $form->input('next_sales_order_number', array('div' => false, 'type' => 'number', 'class' => 'INPUT form-control required', 'value' => $value, 'readonly' => true, 'disabled' => true, 'label' => false));
                        ?>
                    </div>
                    <div class="flex-shrink-0">

                        <?php echo '<button type="button" tabindex="-1" class="btn btn-secondary text-decoration-none" id="autoNumberSettingsBtn" href="#" title="Auto Number Settings"><i class="fa fa-cog me-4"></i>  ' . __('Auto Number Settings', true) . ' </button>'; ?>
                    </div>
                </div>
                <div class="form-text fs-7 mt-5">
                    <?= __('The number the system will assign to the next sales order.', true) ?>
                </div>
            </div>


            <?php if(ifPluginActive(FollowupPlugin)){ ?>
            <div class="form-group mt-12 mt-md-15 mb-0">
                <label class="form-label fs-8 fw-medium text-black" for="SiteEnableSalesOrderStatus">
                    <?= __('Sales Orders Custom Statuses', true) ?>
                </label>
                <div class="d-flex gap-5 flex-column flex-md-row">
                    <div class="flex-grow-1">
                        <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                            <?php
                                                    echo $form->input('enable_sales_order_status', array('type' => 'checkbox', 'div' => false, 'class' => 'form-check-input ', 'label' => false,));
                                                    ?>
                            <span class="form-check-label fw-normal"><?= __('Enabled', true) ?></span>
                        </label>
                    </div>
                    <div class="flex-shrink-0">
                        <?php  echo '<span id="enable_invoice_link"' . (!settings::getValue(InvoicesPlugin, 'enable_sales_order_status') ? "style='display:none;'" : "") . '> <button type="button" id="statusIdBtn" status_id="' . AutoNumber::TYPE_SALES_ORDER . '" class="btn btn-secondary text-decoration-none" ><i class="fa fa fa-cog me-4"></i>' . __("Manage Custom Statuses", true) . '</button></span>'; ?>
                    </div>
                </div>
                <div class="form-text fs-7 mt-5">
                    <?= __('Create custom Sales Orders statuses that suit your workflow, such as “Pending Approval” or “Confirmed”, and assign them to Sales Order. Use custom statuses to filter and search Sales Orders.', true) ?>
                </div>
            </div>
            <?php } ?>



            <div class="form-group mt-12 mt-md-15 mb-0">
                <label class="form-label fs-8 fw-medium text-black"
                    for="SiteEnablePreviewInvoice"><?= __('Allow Invoicing Item Quantities Exceeding Sales Order Quantities', true) ?></label>
                <label class="form-check form-check-custom form-switch bg-white border border-1 border-dark-1">
                    <?= $form->input('exceeding_the_sales_order_qty_in_the_sales_invoices', array('checked'=>!empty($this->data['Site']['exceeding_the_sales_order_qty_in_the_sales_invoices']),'type' => 'checkbox', 'label' => false, 'class' => 'form-check-input', 'div' => false)); ?>
                    <span class="form-check-label fw-normal"><?= __('Allowed', true) ?></span>
                </label>
                <div class="form-text fs-7 mt-5">
                    <?= __('You can still invoice quantities less than the sales order, but you cannot exceed the ordered quantities when this option is disabled.', true); ?>
                </div>
            </div>


            <!-- <div class="form-group" id="exceedingTheSalesOrderQtyInTheSalesInvoices">
                                    <?= $form->input('exceeding_the_sales_order_qty_in_the_sales_invoices', array('checked'=>!empty($this->data['Site']['exceeding_the_sales_order_qty_in_the_sales_invoices']),'type' => 'checkbox', 'label' => __('Exceeding The Sales Order QTY In The Sales Invoices', true), 'class' => 'form-x1', 'div' => 'clip-check check-info')); ?>
                                    <div class="clearfix"></div>
                                </div> -->
        </div>

    </div>
</div>
<script>
$(function() {
    $("#SiteEnableSalesOrder").change(function() {
        if ($(this).prop('checked')) {
            $("#salesOrderOptions").show();

        } else {
            $("#salesOrderOptions").hide();
            $("#SiteExceedingTheSalesOrderQtyInTheSalesInvoices").prop('checked', false);
        }
    })
    $("#SiteEnableSalesOrder").change();
})
</script>