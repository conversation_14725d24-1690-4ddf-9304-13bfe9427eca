<div class="report">
    <?php
    $dateFormats = getDateFormats('std');
    $dateFormat = $dateFormats[getAuthOwner('date_format')];
    $totals = array();
    $is_summary = $params['is_summary'];
    $show_staff = $params['group_by'] != 'staff' && check_permission(Invoices_View_All_Invoices) && ifPluginActive(StaffPlugin);
    
    $show_date = $params['group_by'] != 'daily';
    $colspan = 10 + $show_staff +  $show_date;
    foreach ($reportCurrencies as $cc => $currency) {
        $total = $paid = $unpaid = 0;
        $st_totals = array();
        $st_groups = array();
        ?>


        <div class="subreport">
            <div class="row chart-wrap">
                <?php $draw_pie_methods = ($params['group_by'] != 'payment_method' && count($reportData[$currency]['methods'] ?? []) > 1 ? true : false) ?>
                <div class="chart-col col-md-<?php echo $draw_pie_methods ? '8' : '12' ?> col-sm-12" id="chart_<?php echo $currency ?>"></div>
                <?php if ($draw_pie_methods) { ?>
                    <div class="col-md-4 col-sm-12" id="pie_chart_<?php echo $currency ?>"></div>
                <?php } ?>

            </div>
            <style>
                /* Resize on print css fix */
                @media print {
                    .chart-col > div > div {
                        max-width: 100%;
                        margin-left: auto;
                        margin-right: auto;
                    }
                    .chart-col div[aria-label="A tabular representation of the data in the chart."] {
                        display: none;
                    }
                }
            </style>
            <br/>

            <?php if (!empty($_GET['quick'])) ob_start(); ?>
            <?php if (empty($is_summary)) { ?>
                <div class="container-report-table">
                    <table cellspacing="0" cellpadding="4" width="100%" class="report reports_currency no-collapse fixed-table-head">
                        <thead>
                            <tr class="report-results-head">
                                <th   class="first-column no-sort"><?php __("ID") ?></th>
                                <th   class="first-column no-sort"><?php __("Name") ?></th>
                                <th   class="first-column no-sort"><?php __("Product Code") ?></th>

                                <?php if ($show_date) { ?>
                                    <th   class="no-sort"><?php __("Date") ?></th>
                                <? } ?>
                                <?php if ($show_staff) { ?>
                                    <th   class="no-sort"><?php __("Staff") ?></th>
                                <? } ?>
                                    <th class="no-sort"><?php __("Invoice");?></th>
                                    <th class="no-sort"><?php __("Client");?></th>


                                <th  class="no-sort"><?php __("Unit Price") ?> </th>
                                <th  class="no-sort"><?php __("Quantity") ?> </th>
                                <th  class="no-sort"><?php __("Discount") ?> </th>
                                <th  class="no-sort"><?php __("Taxes") ?> </th>
                                <th  class="no-sort"><?php __("Total") ?> </th>
                            </tr>
                        </thead>
                    <? } else { ?>
                        <table cellspacing="0" cellpadding="4" width="100%" class="report reports_currency no-collapse fixed-table-head">
                            <thead>
                                <tr class="report-results-head">
                                        <?
                                if ($params['group_by'] == 'product'){
                                ?>
                                    <th   class="first-column no-sort"><?php echo __('Product Code') ?></th>
                                <?
                                }
                                ?>
                                    <th   class="first-column no-sort"><?php echo $titles['column_title'] ?></th>

                                    <th  class="no-sort"><?php __("Unit Price") ?></th>
                                    <th   class="no-sort"><?php __("Quantity") ?></th>
                                    <th  class="no-sort"><?php __("Discount") ?> </th>
                                    <th  class="no-sort"><?php __("Taxes") ?> </th>
                                    <th  class="no-sort"><?php __("Total") ?> </th>
                                </tr>
                            </thead>
                        <? } ?>
                        <tbody>
                            <?php
                            $price_all_total = 0;
                            $discount_all_Total = 0 ;
                            $taxes_total_all = 0;
                            $taxes = 0;
                            $unit_price_all = 0;
    //                        debug ( $clients ) ;
                            foreach ($reportData[$currency] as $group => $subData) {

                                $subtotal = 0;
                                $group_title = $group;

                                if ($params['group_by'] == 'client')
                                    $group_title = $clients[$group];
                                if ($params['group_by'] == 'product') {
                                    $group_title = $products[$group];
                                    $group_code = $subData[0]['product_code'];
                                }
                                if ($params['group_by'] == 'staff' || $params['group_by'] == 'collected_by' || $params['group_by'] == 'sales_person')
                                    $group_title = $staffs[$group];
                                if ($params['group_by'] == 'payment_method')
                                    $group_title = $paymentMethods[$group];

                                if ($params['group_by'] == 'weekly' || $params['group_by'] == 'daily')
                                    $group_title = date($dateFormat, strtotime($group));
                                if (empty($group_title))
                                    $group_title = '[' . __('Not identified in the system', true) . ']';
                                ?>
                                <?php if (empty($is_summary)) { ?>
                                    <tr class="empty-row">
                                        <td class="empty-row" colspan="<?php echo $colspan ?>">&nbsp</td>
                                    </tr>
                                    <tr class="sub-head">
                                        <td colspan="<?php echo $colspan ?>" class="sub-head"><?php echo $group_title ?></td>
                                    </tr>
                                <? } ?>
                                <?php
                                $price_sub_total = 0 ;
                                $discount_Total = 0 ;
                                $taxes_total = 0;
                                $unit_price_total = 0;
                           

                                if(isset($subData[0]['quantity'])){
                                foreach ($subData as $payment) {
                                    $payment['discount_amount'] = $payment['calculated_discount'];
                                    $percentage = $payment['quantity'] * $payment['unit_price'] != 0 ? $payment['discount_amount'] / ($payment['quantity'] * $payment['unit_price']) : false;
                                    $discount_percentage = (empty($payment['discount_amount'])) ? $payment['discount'] : ($percentage * 100);
                                    $subtotal+=round($payment['quantity'], 2);
                                    $discount_amount = (($payment['quantity'] * $payment['unit_price']) * $discount_percentage/100 ) * -1;
                                    $taxes = ($payment['summary_tax1'] + $payment['summary_tax2']);
                                    if($payment['quantity'] < 0){
                                        $discount_amount *= -1;
                                        $taxes *= -1;
                                    }
                                    $discount_Total += $discount_amount;
                                    $discount_all_Total += $discount_amount;
                                    $price_sub = ($payment['unit_price'] * $payment['quantity']) + $discount_amount + $taxes;
                                    $price_sub_total += $price_sub;
                                    $taxes_total += $taxes;
                                    $taxes_total_all += $taxes;
                                  
                                    $unit_price_total = $payment['unit_price'];

                                    $unit_price_all += $unit_price_total;

                                    ?>
                                    <?php if (empty($is_summary)) { ?>
                                        <tr class="indent-td">
                                            <td><?php echo $payment['id'] ?></td>
                                            <td><?php echo $payment['name']==""?__('[Removed]',true):$payment['name'] ?></td>
                                            <td><?php echo $payment['product_code'];?></td>
                                            <?php if ($show_date) { ?>
                                                <td><?php echo date($dateFormat, strtotime($payment['date'])); ?></td>
                                            <? } ?>
                                            <?php if ($show_staff) { ?>
                                                <td><?php echo $staffs[$payment['staff_id']] ?></td>
                                            <? }
 
                                            if($payment['type']==Invoice::Invoice){
                                            $invoice_type= __("Invoice" , true );
                                            $invoice_view_link=Router::url(['controller' => 'invoices' , 'action' => 'view' , $payment['invoice_id']]);
                                            }elseif($payment['type']==Invoice::Refund_Receipt){
                                            $invoice_view_link=Router::url(['controller' => 'invoices' , 'action' => 'view_refund' , $payment['invoice_id']]);
                                            $invoice_type= __("Refund Receipt" , true );
                                            }elseif($payment['type']==Invoice::Credit_Note){
                                                $invoice_view_link=Router::url(['controller' => 'invoices' , 'action' => 'view_creditnote' , $payment['invoice_id']]);
                                                $invoice_type= __("Credit Note" , true );
                                            }elseif($payment['type']==Invoice::DEBIT_NOTE){
                                            $invoice_view_link=Router::url(['controller' => 'invoices' , 'action' => 'view_debitnote' , $payment['invoice_id']]);
                                            $invoice_type= __("Debit Note" , true );
                                            }elseif($payment['type']==Invoice::BOOKING){
                                                $invoice_view_link=Router::url(['controller' => 'bookings' , 'action' => 'view' , $payment['invoice_id']]);
                                                $invoice_type= __("Booking" , true );
                                            }
                                            ?>
                                                <td ><?php if ( !empty ( $payment['invoice_id'] )){?><a href="<?php echo sprintf('https://%s%s',getCurrentSite('subdomain'),$invoice_view_link) ;?>" ><?php echo $invoice_type." #".$payment['invoice_number'];?></a><?php }?>&nbsp;</td>
                                                <td ><?php if ( !empty ( $payment['client_id'] ) ){?><a href="<?php echo Router::url(['controller' => 'clients' , 'action' => 'view' , $payment['client_id']]) ;?>" ><?php echo $payment['business_name']." #".$payment['client_number'];?></a><?php }?>&nbsp;</td>
                                            <td><?php echo format_price_simple($payment['unit_price']) ?></td>
                                            <td class="<?php echo ($payment['quantity'] >= 0 ? 'profit' : 'loss') ?>"><?php echo $payment['quantity'] ?></td>
                                            <td><?php echo format_price_simple($discount_amount, $currency) ?></td>
                                            <td class="<?php echo ($payment['quantity'] >= 0 ? 'profit' : 'loss') ?>"><?php echo format_price_simple($taxes, $currency) ?></td>
                                            <td class="<?php echo ($payment['quantity'] >= 0 ? 'profit' : 'loss') ?>"><?php echo format_price_simple($price_sub,$currency) ?> </td>
                                        </tr>
                                    <? } ?>
                                    <?php
                                }

                                $total+=$subtotal;
                                $st_totals[] = $subtotal;

                                if ($is_periodic)
                                    $st_groups[] = $group . ($params['group_by'] == 'monthly' ? '-01' : '');
                                else
                                    $st_groups[] = $group_title;
                                ?>
                                <?php if (empty($is_summary)) { ?>
                                    <tr class="subtotal">
                                        <td colspan="<?php echo $colspan - 4 ?>"><?php echo __('Subtotal', true) ?></td>
                                        <td class=""><?php echo $subtotal ?></td>
                                        <td class=""><?php echo format_price_simple($discount_Total, $currency) ?></td>
                                        <td class=""><?php echo format_price_simple($taxes_total, $currency) ?></td>
                                        <td class=""><?php $price_all_total += $price_sub_total;echo format_price_simple($price_sub_total,$currency ) ; ?></td>
                                    </tr>

                                <? } else {
                                    $price_all_total += $price_sub_total;
                                    ?>
                                    <tr>
                                                <?
                                if ($params['group_by'] == 'product'){
                                ?>
                                        <td><?php echo $group_code ?></td>
                                <?
                                }
                                ?>
                                        <td><?php echo $group_title ?></td>

                                        <td><?php echo format_price_simple($unit_price_total, $currency)?></td>
                                        <td><?php echo $subtotal ?></td>
                                        <td class=""><?php echo format_price_simple($discount_Total, $currency) ?></td>
                                        <td class=""><?php echo format_price_simple($taxes_total, $currency) ?></td>
                                        <td class="<?php echo ($price_sub_total >= 0 ? 'profit' : 'loss') ?>"><?php echo format_price_simple($price_sub_total,$currency ) ; ?></td>
                                    </tr>
                                <? }
                                }else{
                                ?>
                                    <tr class="empty-row">
                                        <td class="empty-row" colspan="<?php echo $colspan ?>"><? echo __('No Transactions',true) ?></td>
                                    </tr>

                            <?php }} ?>

                            <tr class="section-net">
                                <?php if (empty($is_summary)) { ?>
                                    <td colspan="<?php echo $colspan - 4 ?>" class="first-column"><?php __("Quantity") ?></td>
                                    <td class="<?php echo ($total >= 0 ? 'profit' : 'loss') ?>"><?php echo $total ?></td>
                                    <td><?php echo format_price_simple($discount_all_Total) ?></td>
                                    <td class=""><?php echo format_price_simple($taxes_total_all, $currency) ?></td>
                                    <td class="<?php echo ($price_all_total >= 0 ? 'profit' : 'loss') ?>"><?php echo format_price_simple($price_all_total) ?></td>
                                <? } else { ?>

                                    <td colspan="<? echo ($params['group_by'] == 'product')?'3':'2'; ?>"  class="first-column"><? echo __('Total') ?></td>
                                    <td class="<?php echo ($total >= 0 ? 'profit' : 'loss') ?>"><?php echo $total ?></td>
                                    <td><?php echo format_price_simple($discount_all_Total) ?></td>
                                    <td class=""><?php echo format_price_simple($taxes_total_all, $currency) ?></td>
                                    <td class="<?php echo ($price_all_total >= 0 ? 'profit' : 'loss') ?>"><?php echo format_price_simple($price_all_total) ?></td>
                                <? } ?>
                            </tr>
                        </tbody>

                    </table>
                </div>

                <?php if (!empty($_GET['quick'])) ob_end_clean(); ?>
        </div>
        <br/>
            <?
    if(!isset($this->params['url']['no_graph']) || $this->params['url']['no_graph']!="1"){
    ?>
        <?php
        $chart_params = array(
            'title' => $titles['graph_title'] . ' (' . $currency . ')',
            'chartType' => ($is_periodic ? 'area' : 'bar'),
            'group' => $params['group_by'],
            'xLabels' => $st_groups,
            'values' => array(
                __('Total', true) => $st_totals,
        ));
        echo $this->element('reports/charts', array('resize_on_print' => true, 'div' => 'chart_' . $currency, 'jsonParams' => $chart_params));

        if ($draw_pie_methods) {
            $lablels = array();
            $values = array();
            foreach ($reportData[$currency]['methods'] as $method => $amount) {
                $lablels[] = $paymentMethods[$method] ? $paymentMethods[$method] : $method;
                $values[] = ($amount);
            }
            $chart_params = array(
                'title' => __('Payment Methods', true) . ' (' . $currency . ')',
                'chartType' => 'pie',
                'xLabels' => $lablels,
                'values' => array($values),
            );
            echo $this->element('reports/charts', array('div' => 'pie_chart_' . $currency, 'jsonParams' => $chart_params));
        }



        //echo $this->element('reports/charts', array('div' => 'pie_'.$currency, 'not_first'=>$cc>0, 'jsonParams'=>$chart_params)); 	
    }
    }
    ?>
</div>
