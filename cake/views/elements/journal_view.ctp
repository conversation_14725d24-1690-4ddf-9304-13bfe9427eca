<?php
$totalColspan = 3;
if($show_cost_centers_summary){
    $totalColspan++;
}
if($has_tax){
    $totalColspan++;
}
if($enable_tags && $has_tags){
    $totalColspan++;
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

        <title><?php echo __("Journal",true) ; ?> Template</title>
	<style>
		<?php if(isset($box) || isset($_GET['box'])) { ?>
			#body_box {
				background-color: #BCC5CD !important;
				padding: 40px 20px 20px 20px !important;
				margin-bottom: 0;
			}
		<? } ?>
	</style>
    </head>
	<style><?= file_get_contents(WWW_ROOT . 'css/journal_en.css')?></style>
	<?php if(CurrentSiteLang() == "ara") { ?>
	<style><?= file_get_contents(WWW_ROOT . 'css/journal_ar.css')?></style>
	<?php }?>
	<?php
	
	$dateFormats = getDateFormats('std');
		$journal['Journal']['date'] = format_date($journal['Journal']['date']);
	?>
    <body >

        <div class="invoice-wrap">
            <div class="invoice-inner">
                <h2 class="">
                    <?php echo __("Journal Entry",true)." #".$journal['Journal']['number'] ; ?>
                </h2>
                <table class="f_table">
                    <tr>
                        <td><?php echo __("Date", true). ": ".$journal['Journal']['date']; ?></td>
						<?php if(isset($journal['Journal']['show_loacal_currency']) && $journal['Journal']['show_loacal_currency'] == true){ ?>
                        <td class="pull-right"><?php echo __("Currency Code", true). ": ".$journal['JournalTransaction'][0]['currency_code']; ?></td>
						<?php } ?>
                        
                    </tr>
					<tr>
                        <td>
						<?php if(!empty($journal['Journal']['alter_description']) || !empty($journal['Journal']['description']))  { ?>
						<?php echo __("Description", true). ": ".nl2br(!empty(trim($journal['Journal']['alter_description'])) ? $journal['Journal']['alter_description'] :$journal['Journal']['description'] ); ?>
						<?php } ?>
                        </td>
                        <td class="pull-right">
						<?php if(isset($journal['Journal']['show_loacal_currency']) && $journal['Journal']['show_loacal_currency'] == true){ ?>
						<?php echo __("Exchange Rate", true). ": ". round($journal['JournalTransaction'][0]['currency_rate'], 5); ?>
						<?php } ?>
                        </td>
					</tr>
                </table>
                
                
                <table   id="listing_table" class="t_table">
                    <tr class="bold">
						<td colspan="2"><?php echo __("Account") ?></td> 
							<td ><?php echo __("Description") ?></td>
                        <?php if ($enable_tags && $has_tags) { ?>
                            <td><?= __('Tags') ?></td>
                        <?php } ?>
                        <?php if($show_cost_centers_summary){ ?>
								<td>
									<?= __('Cost Center') ?>
								</td>
							<?php } ?>
                            <?php if($has_tax) { ?>
                                <td>
                                    <?= __('Tax') ?>
                                </td>
                            <?php } ?>
							<?php if(isset($journal['Journal']['show_loacal_currency']) && $journal['Journal']['show_loacal_currency'] == true){ ?>
						<td ><?php echo __("Debit") ?></td>
						<td ><?php echo __("Credit") ?></td >
						
							<td ><?php echo __("Local Debit") ?></td>
                          <td ><?php echo __("Local Credit") ?></td>
						<?php } else { ?>
						  <td ><?php echo __("Debit") ?></td>
                          <td ><?php echo __("Credit") ?></td>
						<?php } ?>
						
					

                    </tr>
					<?php $local_totals ?>
					
					<?php
                    $debitWithoutTax = 0;
                    $creditWithoutTax = 0;
                    foreach($journal['JournalTransaction'] as $k => $transaction) {
                        $debitWithoutTax += $transaction['debit'];
                        $creditWithoutTax += $transaction['credit'];
                        ?>
                    <tr>
						<td> <?php echo $transaction['JournalAccount']['code'] ?> </td>
						<td> <?php echo  $journal['JournalTransaction'][$k]['JournalAccount']['name'] ?> </td>
							<td> <?php echo  !empty(trim($transaction['alter_description']))?$transaction['alter_description']:$transaction['description'] ?>  </td>
                        <?php if ($enable_tags && $has_tags) { ?>
                            <td> <?= implode(', ', array_column($transaction['Tags'], 'name')) ?>  </td>
                        <?php } ?>
							<?php if($show_cost_centers_summary && isset($transactions_centers[$transaction['id']])){ ?>
								<td>
									<?php echo $transactions_centers[$transaction['id']]['name']?>
								</td>
							<?php } elseif($show_cost_centers_summary) {?>
								<td>
									&nbsp;
								</td>
							<?php } ?>
                        <?php if($has_tax) {
                            $taxName = $transaction['tax_id'] ? $taxes[$transaction['tax_id']] : '-';
                        ?>
                            <td width="100px" class="tax-cell"> <span class="tax-cell-content"></span><?= $taxName ?> </td>
                        <?php } ?>
						<?php if(isset($journal['Journal']['show_loacal_currency']) && $journal['Journal']['show_loacal_currency'] == true){
							$currency_totals['credit'] += $transaction['currency_credit'];
							$currency_totals['debit']+= $transaction['currency_debit'];
							?>
						<td> <?php echo $transaction['currency_debit']== 0 ? "" :format_price_simple($transaction['currency_debit'],$transaction['currency_code']) ?> </td>
						<td><?php echo $transaction['currency_credit'] == 0 ? "" :format_price_simple($transaction['currency_credit'],$transaction['currency_code']) ?></td>

						<?php } ?>
						<td> <?php echo $transaction['debit'] == 0 ? "" :format_price_simple($transaction['debit'],$transaction['currency_code'])?> </td>
						<td><?php echo $transaction['credit']== 0 ? "" :format_price_simple($transaction['credit'],$transaction['currency_code']) ?></td>

                    </tr>
					<?php } ?>
                    <?php if($has_tax) { ?>
                        <tr>
                            <td colspan="<?= $totalColspan  ?>"><?= __('Total Without Tax') ?></td>
                            <td><?= format_price($debitWithoutTax) ?></td>
                            <td><?= format_price($creditWithoutTax) ?></td>
                        </tr>
                    <?php } ?>
                    <?php foreach ($tax_transactions as $tax_transaction) { ?>
                        <tr>
                            <?php $taxName = $taxes[$tax_transaction['JournalAccount']['entity_id']]; ?>
                            <td colspan="<?= $totalColspan  ?>"><?= $taxName . " ({$taxesPercentages[$tax_transaction['JournalAccount']['entity_id']]}%)" ?></td>
                            <td><?= format_price($tax_transaction['JournalTransaction']['debit']); ?></td>
                            <td><?= format_price($tax_transaction['JournalTransaction']['credit']); ?></td>
                        </tr>
                    <?php } ?>
                    <tr bgcolor="#e5e5e5" class="bold">
                        <td  colspan="<?= $totalColspan ?>"><?php echo __( "Total" ,true) ?></td>
								<?php if(isset($journal['Journal']['show_loacal_currency']) && $journal['Journal']['show_loacal_currency'] == true){
							?>
						<td style="word-wrap: break-word;" colspan="1"><?php 	echo format_price($currency_totals['debit'],$journal['Journal']['currency_code']) ?></td>

						<td style="word-wrap: break-word;" colspan="1"><?php 	echo format_price($currency_totals['credit'],$journal['Journal']['currency_code']) ?></td>
						<?php
							}
						
						?>
                        <td style="word-wrap: break-word;" ><?php 	echo format_price($journal['Journal']['total_credit'],$journal['Journal']['local_currency_code'] ) ?></td>
                        <td style="word-wrap: break-word;" colspan="1"><?php 	echo format_price($journal['Journal']['total_debit'],$journal['Journal']['local_currency_code'] ) ?></td>
				
                    </tr>
                </table>
<br>
                <?
				if(!$show_cost_centers_summary){
					echo PlaceHolder::getCostCenterTable($cost_transactions);
				}

                if($invoice && $customJournalDescriptionSettings){
                    $placeholders = PlaceHolder::invoice_place_holder($invoice);
                    foreach ($customJournalDescriptionSettings as $customJournalDescriptionSetting){
                        echo $placeholders[$customJournalDescriptionSetting];
                    }
                }
                ?>
            </div>
        </div>
		<script>
			
			if (window.top!=window.self)
				{
					$('body').css('background','rgb(195, 195, 195)');
					$('body').css('padding','20px');
				}

			</script>
		<style>
			body{
				overflow: auto;
			}
		</style>
    </body>
</html>

