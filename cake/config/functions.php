<?php

use App\Helpers\CurrencyHelper;
use Izam\Daftra\Common\Entity\Actions\ShowAction\AppEntityShowAction;
use Izam\Daftra\AppManager\Events\AppEntityEventRegistry;
use Izam\Daftra\AppManager\Events\AppTriggerEventRegistry;
use Izam\Daftra\AppManager\Events\EventRegistryInterface;
use Webmozart\Assert\Assert;
use Izam\Limitation\LimitationService;

if(!function_exists('is_arabic')) {
    /**
     * @param $u
     * @return false|int
     * detect if a rtl charchters and if so its arabic or hebrew or whatever
     */
    function is_arabic($string) {
        $rtl_chars_pattern = '/[\x{0590}-\x{05ff}\x{0600}-\x{06ff}]/u';
        return preg_match($rtl_chars_pattern, $string);
    }

}

function register_app_events()
{
    $eventRegistries = [
        izam_resolve(AppTriggerEventRegistry::class),
        izam_resolve(AppEntityEventRegistry::class),
    ];

    /** @var $eventRegistry EventRegistryInterface */
    foreach ($eventRegistries as $eventRegistry) {
        Assert::isInstanceOf(
            $eventRegistry, EventRegistryInterface::class,
            sprintf("Class %s must implement EventProviderInterface", get_class($eventRegistry))
        );
        try {
            $eventRegistry->register();
        } catch (Throwable $e) {
            \Rollbar\Rollbar::log(\Rollbar\Payload\Level::ERROR , $e->getMessage());
        }
    }
}

function logText($logString)
{
    $site_id = getCurrentSite('id');

    $site_hash = dechex(crc32($site_id));

    $dir = ROOT . DS . APP_DIR . DS . "webroot/files/$site_hash/request-logs/";

    if (!file_exists($dir)) {
        mkdir($dir, 0777, true);
    }

    $file = $dir . date('Y-m-d').".txt";

    $logString = $logString . "\r\n";
    file_put_contents($file, $logString . "\r\n", FILE_APPEND);
}

/** temporary log trying to figure out problems with invoices */
function logInvoiceRequest($logString)
{
    $site_id = getCurrentSite('id');

    if (!in_array($site_id, [3261917])) {
        return;
    }

    $site_hash = dechex(crc32($site_id));

    $dir = ROOT . DS . APP_DIR . DS . "webroot/files/$site_hash/request-logs/";

    if (!file_exists($dir)) {
        mkdir($dir, 0777, true);
    }

    $file = $dir . date('Y-m-d').".txt";
    $logString = $logString . "\r\n###################################################################################################################";
    file_put_contents($file, $logString . "\r\n", FILE_APPEND);
}

if(!function_exists('appFormatPrice')) {
    function appFormatPrice($amount, $currency, $showSymbol = false) {
        return CurrencyHelper::formatPrice($amount, $currency, $showSymbol);
    }
}

if(!function_exists('appFormatDateWithCache')) {
    function appFormatDateWithCache($date, $default_format = false, $isDateTime = false) {
        return \App\Helpers\DateHelper::formatDate($date, $default_format = false, $isDateTime = false);
    }
}

if(!function_exists('db_reconnect')) {
    function db_reconnect($connection, $dbConfig = []) {
        $datasource = ConnectionManager::getDataSource($connection);
        if($datasource->isConnected()) {
            $datasource->reconnect($dbConfig);
        } else {
            $datasource->setConfig($dbConfig);
            $datasource->_sources = null;
            $datasource->connect();
        }
    }
}

if(!function_exists('app_round')) {
    function app_round($amount, $currency) {
        if(!$number_formats = Cache::read('number_formats'))
        {
            clearstatcache('currencies.php');
            $currencies = (include 'config/currencies.php'); //fix
            Cache::write('number_formats', $number_formats);
            Cache::write('currencies', $currencies);
        } else{
            $currencies = Cache::read('currencies');
        }
        // Incase something went wrong just use the 2 decimal rounding
        if (!isset($number_formats[$currency])) { //fix
            $number_formats[$currency] = array(2, '.', ',');
        }
        return round($amount, $number_formats[$currency][0]);
    }
}

if(!function_exists('isApi')) {
    function isApi() {
        return (defined('IS_API') && IS_API);
    }
}

if(!function_exists('getRecordWithEntityStructure')) {
    function getRecordWithEntityStructure($entityKey, $id ,$level = 1) {
        return izam_resolve(AppEntityShowAction::class)->handle($entityKey, $id, $level);
    }
}

if(!function_exists('getRecordWithEntityStructureForActivityLog')) {
    function getRecordWithEntityStructureForActivityLog($entityKey, $id ,$level = 1) {
        return izam_resolve(AppEntityShowAction::class)->handle2($entityKey, $id, $level);
    }
}


if(!function_exists('getEntityBuilder')) {
    /**
     * @return \Izam\Daftra\Common\EntityStructure\IEntityStructureGetter
     */
    function getEntityBuilder() {
        return izam_resolve(\Izam\Daftra\Common\EntityStructure\IEntityStructureGetter::class);
    }
}

if(!function_exists('singular')) {
    function singular($word) {
        return \Inflector::singularize($word);
    }
}


if(!function_exists('getInvoiceNo')) {
 function getInvoiceNo($invoice) {
     if(!empty($invoice) && $invoice['Invoice']['draft'] && empty($invoice['Invoice']['no'])) {
         return sprintf(__('Draft-%s', true), $invoice['Invoice']['id']);
     } else if(!empty($invoice['Invoice']['no'])){
        return $invoice['Invoice']['no'];
     } else {
         return $invoice['Invoice']['id'];
     }
 }
}
if(!function_exists('validateMysqlDate')) {
    function validateMysqlDate( $date ){
        if (preg_match("/^(\d{4})-(\d{2})-(\d{2}) ([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/", $date, $matches)) {
            if (checkdate($matches[2], $matches[3], $matches[1])) {
                return true;
            }
        }
        return false;
    }
}

if(!function_exists('userHavePermissionToAddInvoice')) {
    function userHavePermissionToAddInvoice():bool
    {
        return check_permission([Invoices_Add_New_Invoices, INVOICES_ADD_NEW_TO_HIS_OWN_CLIENTS]);
    }
}

if(!function_exists('userHavePermissionToEditInvoice')) {
    function userHavePermissionToEditInvoice():bool
    {
        return check_permission([Invoices_Edit_All_Invoices, Invoices_Edit_his_own_Invoices]);
    }
}

if(!function_exists('userHavePermissionToAddEstimate')) {
    function userHavePermissionToAddEstimate():bool
    {
        return check_permission([ESTIMATES_ADD_NEW_TO_ALL_CLIENTS, ESTIMATES_ADD_NEW_TO_HIS_OWN_CLIENTS]);
    }
}

if(!function_exists('userHavePermissionToAddDebitNote')) {
    function userHavePermissionToAddDebitNote():bool
    {
        return check_permission([DEBIT_NOTE_ADD_NEW_TO_ALL_CLIENTS, DEBIT_NOTE_ADD_NEW_TO_HIS_OWN_CLIENTS]);
    }
}

if(!function_exists('appendAppButtonsToMultiSelectActions')) {
    function appendAppButtonsToMultiSelectActions(&$multi_select_actions, $appButtons, $entityKey, $entityName, $indexURL): void
    {
        foreach ($appButtons as $key => $button) {
            $multi_select_actions[$key] = [
                'data' => htmlspecialchars(json_encode([
                    'entity_key'=> $entityKey, 
                    'entity_name'=> $entityName, 
                    'target_url' => $button->getUrl(),
                    'title' => $button->getLabel(),
                    'back_url' => $indexURL,
                    'breadcrumbs' => addslashes(json_encode([
                        [
                            'title' => $entityName,
                            'link' => $indexURL,
                        ],
                        ['title' => $button->getLabel()]

                    ]))
                ]), ENT_QUOTES, 'UTF-8'),
                'action' => Router::url(['controller'=>'bulk', 'action' => 'update']),
                'confirm' => true,
                'class' => 'GoBulk',
                'title' => $button->getLabel(),
            ];
        }
    }
}

if (!function_exists('db_format_date')) {
    function db_format_date($date = false, $format = false, $enableHijiriFormat = false) {
        $clientModel = GetObjectOrLoadModel('Client');
        return $clientModel->formatDate($date, $format);
    }
}

if (!function_exists('array_undot')) {
    function array_undot($dottedArray, $initialArray = [])
    {
        $dottedArray = \Illuminate\Support\Arr::dot($dottedArray);
        foreach ($dottedArray as $key => $value) {
            \Illuminate\Support\Arr::set($initialArray, $key, $value);
        }
        return $initialArray;
    }
}

if (!function_exists('db_format_date_time')) {
    function db_format_date_time($date = false)
    {
        $datetime = trim($date );
        $datetime = preg_replace('/\s+/', ' ', $datetime );
        $datetime = explode(' ',$datetime);

        $time=end($datetime);
        $timeM = "";
        if(in_array($time, ['am','pm','AM','PM'])){
            $timeM = array_pop($datetime);
            $time=end($datetime);
        }

        $date=str_replace($time, '', $date);

        $date=db_format_date( trim($date));
        if($date==false){
            return false;
        }

        if($timeM !== ''){
            $parsed = \Illuminate\Support\Carbon::parse("{$date} {$time} {$timeM}")->toDateTimeString();
            $dateArr = explode(' ',$parsed);
            $time=end($dateArr);
        }

        if(preg_match('/\d\d?:\d\d:\d\d/', $time))  $time.='';
        else if(preg_match('/\d\d?:\d\d/', $time))  $time=$time.':00';
        else if(preg_match('/\d\d?/', $time))  $time=$time.':00:00';
        else if(!preg_match('/\d\d?:\d\d:\d\d/', $time))  $time='00:00:00';
        return $date.' '.$time;
    }
}

if (!function_exists('checkSiteLimitV2')) {
    /**
     * @param $site_id
     * @param $limitation_key
     * @param int $new_count
     * @return array
     */
    function checkSiteLimitV2($site_id, $limitation_key, $new_count = 1)
    {
        return LimitationService::checkLimit($site_id, $limitation_key, $new_count);
    }
}

if (!function_exists('checkIfLimitExistsV2')) {
    /**
     * @param $site_id
     * @param $limitation_key
     * @return bool
     */
    function checkIfLimitExistsV2($site_id, $limitation_key)
    {
        return LimitationService::checkIfLimitExists($site_id, $limitation_key);
    }
}

if (!function_exists('singularToPlural')) {
    /**
     * @param $word
     * @return string
     */
    function singularToPlural($word): string
    {
        if (preg_match('/(s|sh|ch|x|z)$/i', $word)) {
            return $word . 'es';
        } elseif (preg_match('/[^aeiou]y$/i', $word)) {
            return preg_replace('/y$/i', 'ies', $word);
        } elseif (preg_match('/(f|fe)$/i', $word)) {
            return preg_replace('/(f|fe)$/i', 'ves', $word);
        } else {
            return $word . 's';
        }
    }
}

function cache_read($name, $closure = null, $config = 'default') {
    $cache = Cache::read($name, $config);
    if(!$cache && $closure) {
        $cache = $closure();
    }
    return $cache;
}

function getCountry($code = "")
{
    $countriesMappedByCode = \Izam\Daftra\Cache\SharedPortalCache::get('countries', function() {
        $CountryModel = GetObjectOrLoadModel('Country');
        $countries = $CountryModel->find('all');
        $countriesMappedByCode = [];
        foreach ($countries as $country) {
            $countriesMappedByCode[$country['Country']['code']] = $country['Country'];
        }
        return $countriesMappedByCode;
    });
    $row = $countriesMappedByCode[$code];
    // warning suppress
    return $row;

}

function getLanguageCode($code, $fieldName = "name") {
    $cacheName = "languages_mapped_by_code";
    $languagesMappedByCode = \Izam\Daftra\Cache\SharedPortalCache::get($cacheName, function() use ($cacheName) {
        $LanguageModel = GetObjectOrLoadModel('Language');
        $languages = $LanguageModel->find('all');
        $languagesMappedByCode = [];
        foreach ($languages as $language) {
            $languagesMappedByCode[$language['Language']['id']] = $language;
        }
        return $languagesMappedByCode;
    });
    $lang = $languagesMappedByCode[$code];
    return $lang["Language"][$fieldName] ?? null;
}

function getCurrenciesFromCache() {
    $cacheName = "currencies_list";
    $currencies = \Izam\Daftra\Cache\SharedPortalCache::get($cacheName, function() use ($cacheName) {
        $CurrencyModel = GetObjectOrLoadModel('Currency');
        $currencies = $CurrencyModel->find('all', array('applyBranchFind'=>false, 'order' => 'Currency.code', 'recursive' => -1));
        return $currencies;
    });
    return $currencies;
}

/**
 * @param $filteredCurrencies
 * @param $short
 * @return array
 * reads currencies from database and cache it
 */
function getCurrenciesList($filteredCurrencies = [], $short = false)
{
    $owner = getAuthOwner();
    $languageCode = getLanguageCode($owner['language_code'], 'code2');
    $currencies = getCurrenciesFromCache();
    $currenciesList = [];
    foreach ($currencies as $currency) {
        $code = $currency['Currency']['code'];
        if(!empty($filteredCurrencies) && !in_array($code, $filteredCurrencies)) {
            continue;
        }
        $codeName = ($languageCode . '_name');
        if(!isset($currency['Currency'][$codeName])) {
            $codeName = 'name';
        }
        if ($short===2) {
            $currenciesList[$code] = $currency['Currency'][$codeName];
        }
        else if ($short) {
            $currenciesList[$code] = up($code);
        } else {
            $currenciesList[$code] = up($code) . ' ' . $currency['Currency'][$codeName];
        }
    }
    return $currenciesList;

}

function getCountriesList($key = 'code', $countriesList = array())
{
    $cacheName = "countries_list";
    $countries = \Izam\Daftra\Cache\SharedPortalCache::get($cacheName, function() use ($cacheName) {
        $CountryModel = GetObjectOrLoadModel('Country');
        $countries = $CountryModel->find('all');
        return $countries;
    });
    $owner = getAuthOwner();
    $languageCode = getLanguageCode($owner['language_code'], 'code2');
    foreach ($countries as $country) {
        $countriesList[$country['Country']['code']] = $country['Country']['country'];
        $fieldName = "country";
        if(isset($country['Country'][$languageCode . '_name'])) {
            $fieldName = $languageCode . '_name';
        }
        $code = $country['Country'][$key];
        if(!empty($countriesList)) {
            if(!in_array($code, $countriesList)) {
                continue;
            }
        }
        $countriesList[$code] = $country['Country'][$fieldName] . ' (' . up($code) . ')';
    }
    return $countriesList;

}


/**
 * This function is a replica of getCountriesList except that it fetchs the country list values dynamically 
 * based on the current language the other one has issue with that
 * @param string $key 
 * @param array $countriesList 
 * @return array 
 * @throws ErrorException 
 * @throws InvalidArgumentException 
 * @throws *30c9e56c 
 * @throws InvalidArgumentException 
 * @throws RandomException 
 */
function getCountriesListBasedOnLang($key = 'code', $countriesList = array())
{
    $cacheName = "countries_list";
    $countries = \Izam\Daftra\Cache\SharedPortalCache::get($cacheName, function() use ($cacheName) {
        $CountryModel = GetObjectOrLoadModel('Country');
        $countries = $CountryModel->find('all');
        return $countries;
    });
    $owner = getAuthOwner();
    $languageCode = getLanguageCode($owner['language_code'], 'code2');
    foreach ($countries as $country) {
        $fieldName = "country";
        if(isset($country['Country'][$languageCode . '_name'])) {
            $fieldName = $languageCode . '_name';
        }
        $countriesList[$country['Country']['code']] = $country['Country'][$fieldName];
        $code = $country['Country'][$key] ?? null;
        if(!empty($countriesList)) {
            if(!in_array($code, $countriesList)) {
                continue;
            }
        }

        $countriesList[$code] = $country['Country'][$fieldName] . ' (' . up($code) . ')';
    }
    return $countriesList;

}


function getSiteLimits($siteId = null) {
    if(!$siteId) {
        $site = getCurrentSite();
        $siteId = $site['id'];
    } else {
        $siteModel = GetObjectOrLoadModel('Site');
        $site = $siteModel->find('first', array('conditions' => array('Site.id' => $siteId), 'recursive' => -1))['Site'];
    }
    $cacheName = "site_limits_$siteId";
    $siteLimits = \Izam\Daftra\Cache\PortalCache::get($cacheName, function() use ($siteId) {
        $SiteLimitModel = GetObjectOrLoadModel('SiteLimit');
        $siteLimits = $SiteLimitModel->find('first', ['conditions' => ['SiteLimit.owner_id' => $siteId]]);
        return $siteLimits;
    });
    return $siteLimits;
}

function check_add_client_limit($site_id = null) {
    $clientModel = GetObjectOrLoadModel('Client');
    if (!$site_id) {
        $site = getCurrentSite();
        $site["session_data"] = true;
    } else {
        $SiteModel = GetObjectOrLoadModel('Site');
        $dbSite = $SiteModel->find(array('Site.id' => $site_id), 'id, created, plan_id , status');
        $site = $dbSite['Site'];
    }
    $site_id = $site['id'];

    $is_suspended = IsSiteSuspended();
    if ($is_suspended) {
        $return['status'] = false;
        $return['message'] = __('You cannot create more clients as your account is suspended.', true);

        return $return;
    }


    if (Site_Full_name_NoSpace != "OnlineInvoices" && $site["created"] > date('Y-m-d', strtotime(Trial_Period))) {
        return array('status' => true, 'message' => '');
    }

    $limits = getSiteLimits();

    if ($limits) {

        $limit = $limits['SiteLimit']['client_limit'];
        if($limits['SiteLimit']['client_limit_type']=="1"){
            $client_count=$clientModel->find('count',array('conditions'=>array('date(Client.created)>=DATE_FORMAT(NOW(), "%Y-%m-01")')));
        }else{
            $client_count=$clientModel->find('count');
        }
    } else {
        $plan = getPlan($site['plan_id']);
        $limit = $plan['Plan']['client_limit'];
    }

    //client_limit_type
    if ($site['plan_id'] == 1) {

        $client_count = $clientModel->find('count');
    }

    $return = array('status' => true, 'message' => '');
    if ($client_count >= $limit) {
        $return['status'] = false;
        $return['message'] = sprintf(__('You can create only %d clients. Please consider the upgrade if you want to add more clients.', true), $limit);
    }

    return $return;
}

function IsSiteSuspended() {
    $site = getCurrentSite();
    if ($site["status"] != SITE_STATUS_STOPPED && $site["status"] != SITE_STATUS_DELETED && $site["status"] != SITE_STATUS_EXPIRED && $site["status"] != SITE_STATUS_SUSPENDED) {
        return false;
    } else {

        if ($site["status"] == SITE_STATUS_STOPPED || $site["status"] != SITE_STATUS_SUSPENDED){
            return true;
        }

        $gp=Grace_Period+(int)$site['tolerant'];

        $str=strtotime($site["expiry_date"]." +$gp day");
        if (date("Y-m-d",$str) > date('Y-m-d') || $site["plan_id"] < 2){
            return false;
        }

    }
    return true;
}

function getPlan($planId) {
    $plansMappedById = \Izam\Daftra\Cache\SharedPortalCache::get('plans', function() {
        $PlanModel = GetObjectOrLoadModel('Plan');
        $plans = $PlanModel->find('all');
        $plansMappedById = [];
        foreach ($plans as $plan) {
            $plansMappedById[$plan['Plan']['id']] = $plan['Plan'];
        }
        return $plansMappedById;
    });
    return $plansMappedById[$planId];
}

function get_country_code($code) {
    $countriesList = getCountriesList();
    return $countriesList[$code]??null;

}

function get_country_label($code) {
    $countriesList = getCountriesListBasedOnLang($code);
    return $countriesList[$code]??null;
}

function getCurrencyMinorUnitList($filteredCurrencies = [], $short = false) {
    $owner = getAuthOwner();
    $languageCode = getLanguageCode($owner['language_code'], 'code2');

    $currencies = getCurrenciesFromCache();
    $currenciesList = [];
    foreach ($currencies as $currency) {
        $code = $currency['Currency']['code'];
        if($filteredCurrencies && !in_array($code, $filteredCurrencies)) {
            continue;
        }
        if(isset($currency['Currency']["{$languageCode}_name"])) {
            $fieldName = $languageCode . '_changes';
        } else {
            $fieldName = "changes";
        }
        $name = $currency['Currency'][$fieldName];
        if ($short===2) {
            $currenciesList[$code] = $name;
        }
        else if ($short) {
            $currenciesList[$code] = up($code);
        } else {
            $currenciesList[$code] = up($code) . ' ' . $name;
        }
    }
    return $currenciesList;
}

function getPrintableLayouts() {
    $cacheName = "printable_layouts";
    $printable_layouts = \Izam\Daftra\Cache\SharedPortalCache::get($cacheName, function() use ($cacheName) {
        $PrintableLayout = GetObjectOrLoadModel("PrintableLayout");
        $printable_layouts = $PrintableLayout->find('all');
        return $printable_layouts;
    });
    return $printable_layouts;
}

function getSite()
{
    $cacheName = "currentSite";
    $site = Izam\Daftra\Cache\PortalCache::get($cacheName, function () use ($cacheName) {
        $siteModel = getSiteModel();
        $site = $siteModel->find(array('Site.id' => getCurrentSite('id')));
        \Izam\Daftra\Cache\PortalCache::set($cacheName, $site);
        return $site;
    });
    return $site;

}

function check_daily_email_limit($site_id = false)
{
    if (!$site_id) {
        $site = getCurrentSite();
        $site["session_data"] = true;
    } else {
        $SiteModel = GetObjectOrLoadModel('Site');
        $dbSite = $SiteModel->find(array('Site.id' => $site_id), 'id, created, plan_id , status');
        $site = $dbSite['Site'];
    }



    $site_id = $site['id'];

    // Temporary fix for daftra admin
    if ($site_id == 2020459) {
        return ['status' => true, 'message' => '', 'left' => '9999'];
    }

    $SiteLimit = GetObjectOrLoadModel("SiteLimit");
    $limits = $SiteLimit->find(array('SiteLimit.owner_id' => $site_id));

    $invoice_limit = $limits['SiteLimit']['invoice_limit'];

    $email_logs = ClassRegistry::init('EmailLog');
    $email_logs->recursive = -1;
    $date = date("Y-m-d");
    $today_email_count = $email_logs->find('all', array('fields' => 'sum(ROUND ((LENGTH(send_to)- LENGTH( REPLACE ( send_to, ",", "") ) ) / LENGTH(","))+1) AS count', 'conditions' => array('Date(EmailLog.sent_date)' => $date)));
    $today_email_count = intval($today_email_count[0][0]['count']);

    if (($site['plan_id'] > 1 && $today_email_count >= 250) ||  ($site['plan_id'] == 1 && $today_email_count >= $invoice_limit)) {
        return array('status' => false, 'message' => sprintf(__('You can only send %d email per day. Please consider the upgrade if you want to send more emails.', true), ($site['plan_id'] == 1 ? $invoice_limit : 250)));
    }
    return array('status' => true, 'message', 'left' => ($invoice_limit - $today_email_count));
}

