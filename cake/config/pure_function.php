<?php
// This File Will Be include  in webroot/index.php at line 0 so never use cake function on this file
/**
 * 
 * @param String $lang_folder_path 
 * @param String $Industry 
 * @param String $country_code 
 * @return String name of translate file
 */
function check_po_file($lang_folder_path, $Industry = "", $country_code = "") {

    if (!empty($Industry)) {
        $DefaultLang=$Industry;
        if (po_exists($lang_folder_path, $DefaultLang)) {
            return $DefaultLang;
        } else {
            return check_po_file($lang_folder_path,"");
        }
    }
}
/**
 * check if translate file exists
 * @param String $lang_folder_path 
 * @param String $file  
 * @return boolean
 */
function po_exists($lang_folder_path, $file) {
    if (file_exists($lang_folder_path . 'LC_MESSAGES' . DIRECTORY_SEPARATOR . 'default-' . $file . '.po')) {
        return true;
    } else {
        return false;
    }
}

function appendFullPath($site_id,$folder="",$file=""){
	if($folder!=""){
	$folder='/'.$folder.'/';
	}
	return '/files/'.SiteHash($site_id).$folder.$file;
}

function SiteHash($string=""){
	
	return dechex(crc32($string));	

}
function isPluginActivePure($pluginId) {
    if(!defined('DB_HOST')) {
        include dirname(dirname(dirname(__FILE__))) . '/configure.php';
    }
    require_once ('database.php');
    $siteID = $_SESSION['CurrentSite']['id']??0;
    if(empty($_SESSION['db_config'])){
        return  false;
    }
    $database = $_SESSION['db_config'];

    $link = mysqli_init();
    mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
     mysqli_real_connect($link,DB_HOST, DB_USER, DB_PASS);
    if (!$link) return false; // todo redirect to specific error page
    mysqli_select_db($link,Portal_Site_DB);
    $response = mysqli_query($link,"select count(*) from site_plugins where plugin_id={$pluginId} and site_id={$siteID} and active=1");
    if (!$response) return false;
    $response = mysqli_fetch_array($response)[0];
    mysqli_close($link);
    return $response == 1;
}
function isBranchPluginActive() {
    return isPluginActivePure(87);
}

function stringContain($string, $needle)
{
    if (strpos($string, $needle) !== false) {
        return true;
    }
    return false;
}

function handleShopFrontRedirection()
{
    if(php_sapi_name()=='cli'){
       return true;
    }
    parse_str($_SERVER['QUERY_STRING'], $queries);
    unset($queries['url']);
    $query = http_build_query($queries);
    if (isPluginActivePure(79) && $_SERVER["REDIRECT_URL"] == "/" && (stringContain($_SERVER["HTTP_REFERER"], "owner") || stringContain($_SERVER["HTTP_REFERER"], "pos"))) {
        $url = "https://" . $_SERVER["HTTP_HOST"] . "/owner/sites/dashboard".(!empty($query) ? ('?'.$query) : "");
        header('Location: ' . $url);
        exit;
    }

    if (isPluginActivePure(79) && $_SERVER["REDIRECT_URL"] == "/" && stringContain($_SERVER["HTTP_REFERER"], "client")) {
        $url = "https://" . $_SERVER["HTTP_HOST"] . "/client/dashboard";
        header('Location: ' . $url);
        exit;
    }
}

function switchToRedis(): void
{

    if (useRedis()) {
        ini_set('session.gc_maxlifetime', 86400);
        ini_set('session.save_handler', 'redis');
        ini_set('session.save_path', REDIS_SERVER);
        if (isset($_COOKIE['CakeCookie']['User_id'])) {

            ini_set('session.gc_maxlifetime', 2630000);
        }
        setRedisCookieFlag();
    }


}
function useRedis(): bool
{
    if ((defined('USEREDIS') && USEREDIS ) ) {
        return true;
    }else{
        return false;
    }
}

function setRedisCookieFlag(){
    if (useRedis() && !isset($_COOKIE['useRedis'])) {
        setcookie('useRedis', '1', strtotime("+1 month"), "/");
    }
}


if(function_exists('print_pre')==false) {
    function print_pre(...$var)
    {
        foreach ($var as $value) {
            if (PHP_SAPI == 'cli') {
                $newline = "\n\r";
                $newlineClose = "\n\r";
                $br_open = "\n\r";
                $br_close = "\n\r";

            } else {
                $br_open = "<br>";
                $br_close = "<br>";
                $newline = "<pre>";
                $newlineClose = "</pre>";
            }
            echo $newline;
            $file = debug_backtrace();
            echo  $file[0]['file'] . " ({$file[0]['line']})".$br_close.$br_open;
            print_r($value);
            echo $newlineClose ;
        }
    }
}

function connectToDatabase($dbConfig) {
    $link = mysqli_init();
    mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
     mysqli_real_connect($link,$dbConfig['host'], $dbConfig['login'], $dbConfig['password'], $dbConfig['database']);

    return $link;
}

function enableDebugging() {
    if(isset($_GET['debug']) && $_GET['debug']==2) {
        ini_set('display_errors',1);
        error_reporting(E_ALL);
    }
}
function Anyip2long($ip){
    if(filter_var($ip, FILTER_VALIDATE_IP,FILTER_FLAG_IPV4)){
        return ip2long($ip);
    }elseif(filter_var($ip,FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)){
        return  gmp_import(inet_pton($ip));
    }
    return  -1;
}
function setStatusInfo(){
    if(!isset($_SERVER['HTTP_HOST']) || php_sapi_name()=='cli'){
        return true;
    }
    if(!CACHE_DISABLE) {
        $memcache_obj = memcache_connect(CACHED_SERVER, 11211, 3);
        if($memcache_obj) {
            $payload=["host"=>$_SERVER['HTTP_HOST'],"user_ip"=>$_SERVER['REMOTE_ADDR'],"referer"=>@$_SERVER['HTTP_REFERER'],"request_with"=>@$_SERVER['HTTP_X_REQUESTED_WITH']??false];
            memcache_set($memcache_obj, "pid_" . getmypid() . "_" . CURRENT_SERVER_NAME, $_SERVER['HTTP_HOST'], 0, 8600);
            memcache_set($memcache_obj, "ip_" . getmypid() . "_" . CURRENT_SERVER_NAME, $_SERVER['REMOTE_ADDR'], 0, 8600);
            memcache_set($memcache_obj, "pid_" . getmypid() . "_" . CURRENT_SERVER_NAME."_".dechex(crc32($_SERVER['REMOTE_ADDR'])), $_SERVER['HTTP_HOST'], 0, 8600);
            memcache_set($memcache_obj, "pid_" . getmypid() . "_" . CURRENT_SERVER_NAME."_".dechex(crc32($_SERVER['REMOTE_ADDR']))."_json", json_encode($payload), 0, 8600);
            memcache_close($memcache_obj);
        }
    }
}

/**
 *  Get client ip address
 * @return string ip address of the client
 */
function getRealIP() {
    foreach (['HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'] as $key) {
        if (!empty($_SERVER[$key])) {
            return trim($_SERVER[$key]);
        }
    }
    return '';
}