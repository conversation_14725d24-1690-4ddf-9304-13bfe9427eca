<?php

//Configure::write('Config.language','ara');
Configure::write('debug',0);
if (PHP_SAPI == 'cli') {
	Configure::write('debug', 0);
}
if(php_sapi_name()=='cli' && strpos($_SERVER['argv'][1], "debug=2")){
    Configure::write('debug', 2);
}

/**
 * Application wide charset encoding
 */
Configure::write('App.encoding', 'UTF-8');
/**
 * To configure CakePHP *not* to use mod_rewrite and to
 * use CakePHP pretty URLs, remove these .htaccess
 * files:
 *
 * /.htaccess
 * /app/.htaccess
 * /app/webroot/.htaccess
 *
 * And uncomment the App.baseUrl below:
 */
//Configure::write('App.baseUrl', env('SCRIPT_NAME'));
/**
 * Uncomment the define below to use CakePHP admin routes.
 *
 * The value of the define determines the name of the route
 * and its associated controller actions:
 *
 * 'admin' 		-> admin_index() and /admin/controller/index
 * 'superuser' -> superuser_index() and /superuser/controller/index
 */
Configure::write('Routing.prefixes', ['owner']);
if (!empty($_SERVER['REQUEST_URI']) && (strpos($_SERVER['REQUEST_URI'], '/owner/') !== false || substr($_SERVER['REQUEST_URI'], -strlen('/owner')) == '/owner')) {
	Configure::write('Routing.prefixes', ['owner']);
}else{
if (!empty($_SERVER['REQUEST_URI']) && (strpos($_SERVER['REQUEST_URI'], '/client/') !== false || substr($_SERVER['REQUEST_URI'], -strlen('/client')) == '/client')) {
	Configure::write('Routing.prefixes', ['client']);
}
if (!empty($_SERVER['REQUEST_URI']) && (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false || substr($_SERVER['REQUEST_URI'], -strlen('/admin')) == '/admin')) {
	Configure::write('Routing.prefixes', ['admin']);
}
}

/**
 * Turn off all caching application-wide.
 *
 */
Configure::write('Cache.disable', CACHE_DISABLE);
/**
 * Enable cache checking.
 *
 * If set to true, for view caching you must still use the controller
 * var $cacheAction inside your controllers to define caching settings.
 * You can either set it controller-wide by setting var $cacheAction = true,
 * or in each action using $this->cacheAction = true.
 *
 */
//Configure::write('Cache.check', true);
/**
 * Defines the default error type when using the log() function. Used for
 * differentiating error logging and debugging. Currently PHP supports LOG_DEBUG.
 */

define('LOG_ERROR', 2);

/**
 * The preferred session handling method. Valid values:
 *
 * 'php'	 		Uses settings defined in your php.ini.
 * 'cake'		Saves session files in CakePHP's /tmp directory.
 * 'database'	Uses CakePHP's database sessions.
 *
 * To define a custom session handler, save it at /app/config/<name>.php.
 * Set the value of 'Session.save' to <name> to utilize it in CakePHP.
 *
 * To use database sessions, execute the SQL file found at /app/config/sql/sessions.sql.
 *
 */

Configure::write('Session.save', 'php');

/**
 * The name of the table used to store CakePHP database sessions.
 *
 * 'Session.save' must be set to 'database' in order to utilize this constant.
 *
 * The table name set here should *not* include any table prefix defined elsewhere.
 */
//Configure::write('Session.table', 'cake_sessions');
/**
 * The DATABASE_CONFIG::$var to use for database session handling.
 *
 * 'Session.save' must be set to 'database' in order to utilize this constant.
 */
//Configure::write('Session.database', 'default');
/**
 * The name of CakePHP's session cookie.
 */
Configure::write('Session.cookie', 'OISystem');
/**
 * Session time out time (in seconds).
 * Actual value depends on 'Security.level' setting.
 */
Configure::write('Session.timeout', 12000000);

/**
 * If set to false, sessions are not automatically started.
 */
Configure::write('Session.start', true);
/**
 * When set to false, HTTP_USER_AGENT will not be checked
 * in the session
 */
Configure::write('Session.checkAgent', true);
/**
 * The level of CakePHP security. The session timeout time defined
 * in 'Session.timeout' is multiplied according to the settings here.
 * Valid values:
 *
 * 'high'	Session timeout in 'Session.timeout' x 10
 * 'medium'	Session timeout in 'Session.timeout' x 100
 * 'low'		Session timeout in 'Session.timeout' x 300
 *
 * CakePHP session IDs are also regenerated between requests if
 * 'Security.level' is set to 'high'.
 */
Configure::write('Security.level', 'low');
/**
 * A random string used in security hashing methods.
 */
if (!defined('DF_SALT')) {
    define('DF_SALT', 'fasd');
    Configure::write('Security.salt', DF_SALT);
}
/**
 * Compress CSS output by removing comments, whitespace, repeating tags, etc.
 * This requires a/var/cache directory to be writable by the web server for caching.
 * and /vendors/csspp/csspp.php
 *
 * To use, prefix the CSS link URL with '/ccss/' instead of '/css/' or use HtmlHelper::css().
 */
//Configure::write('Asset.filter.css', 'css.php');
/**
 * Plug in your own custom JavaScript compressor by dropping a script in your webroot to handle the
 * output, and setting the config below to the name of the script.
 *
 * To use, prefix your JavaScript link URLs with '/cjs/' instead of '/js/' or use JavaScriptHelper::link().
 */
//Configure::write('Asset.filter.js', 'custom_javascript_output_filter.php');
/**
 * The classname and database used in CakePHP's
 * access control lists.
 */
Configure::write('Acl.classname', 'DbAcl');
Configure::write('Acl.database', 'default');
/**
 *
 * Cache Engine Configuration
 * Default settings provided below
 *
 * File storage engine.
 *
 * 	 Cache::config('default', array(
 * 		'engine' => 'File', //[required]
 * 		'duration'=> 3600, //[optional]
 * 		'probability'=> 100, //[optional]
 * 		'path' => CACHE, //[optional] use system tmp directory - remember to use absolute path
 * 		'prefix' => 'cake_', //[optional]  prefix every cache file with this string
 * 		'lock' => false, //[optional]  use file locking
 * 		'serialize' => true, [optional]
 * 	));
 *
 *
 * APC (http://pecl.php.net/package/APC)
 *
 * 	 Cache::config('default', array(
 * 		'engine' => 'Apc', //[required]
 * 		'duration'=> 3600, //[optional]
 * 		'probability'=> 100, //[optional]
 * 		'prefix' => Inflector::slug(APP_DIR) . '_', //[optional]  prefix every cache file with this string
 * 	));
 *
 * Xcache (http://xcache.lighttpd.net/)
 *
 * 	 Cache::config('default', array(
 * 		'engine' => 'Xcache', //[required]
 * 		'duration'=> 3600, //[optional]
 * 		'probability'=> 100, //[optional]
 * 		'prefix' => Inflector::slug(APP_DIR) . '_', //[optional] prefix every cache file with this string
 * 		'user' => 'user', //user from xcache.admin.user settings
 *      'password' => 'password', //plaintext password (xcache.admin.pass)
 * 	));
 *
 *
 * Memcache (http://www.danga.com/memcached/)
 *
 * 	 Cache::config('default', array(
 * 		'engine' => 'Memcache', //[required]
 * 		'duration'=> 3600, //[optional]
 * 		'probability'=> 100, //[optional]
 * 		'prefix' => Inflector::slug(APP_DIR) . '_', //[optional]  prefix every cache file with this string
 * 		'servers' => array(
 * 			'127.0.0.1:11211' // localhost, default port 11211
 * 		), //[optional]
 * 		'compress' => false, // [optional] compress data in Memcache (slower, but uses less memory)
 * 	));
 *
 */

if (defined('CACHED_DRIVER') && CACHED_DRIVER == 'memcached') {
    if (empty(getenv('CACHE_PREFIX'))) {
        $prefix = "";
    } else {
        $prefix = ":";
    }
	Cache::config('default', array(
		'engine' => 'Memcache',
		'duration' => 0, // lifetime
		'probability' => 100,
		'prefix' => getenv('CACHE_PREFIX') . $prefix,
        'servers' => array((defined('CACHED_SERVER') ? CACHED_SERVER : '127.0.0.1') . ':11211'),
		'compress' => false,
	));
    Cache::config('low_time', array(
        'engine' => 'Memcache',
        'duration' => 86400, // 1 Day cache
        'probability' => 100,
        'prefix' => false,
        'servers' => array((defined('CACHED_SERVER') ? CACHED_SERVER : '127.0.0.1') . ':11211'),
        'compress' => false,
    ));
    Cache::config('medium_time', array(
        'engine' => 'Memcache',
        'duration' => 18000, // 5 hour cache
        'probability' => 100,
        'prefix' => false,
        'servers' => array("dev280.memcache.prod.daftra.izam.co:11211"),
        'compress' => false,
    ));
} else {

	Cache::config('default', array('duration' => '+30 days','engine' => 'File'));
}


if (!class_exists('CakeSession')) {
	App::import('Core', 'Session');
}

$session = new CakeSession('/', false);


if (!$session->started()) {
	$session->start();
}

if (isset($_GET['debug'])) {
	$session->write('debug', $_GET['debug']);

	Configure::write('debug', $_GET['debug']);
}


if (isset($_GET['nm'])) {
	$session->write('nm', $_GET['nm']);
	Configure::write('nm', $_GET['nm']);
}

if ($session->check('debug')) {
	Configure::write('debug', $session->read('debug'));
}

//if (isset($_GET['sid'])){
//
//	if (file_exists('/tmp/' . $_GET['sid'])){
//		$sid = @file_get_contents('/tmp/' . $_GET['sid']);
//
//		if ($sid) {
//
//			$session->destroy();
//			$session->id($sid);
//			$session->start();
//			$redirect = preg_replace('/\??sid=([^\&]+)[\&]?/', '', $_SERVER['REQUEST_URI']);
//			header('HTTP / 1.1 302 Redirect Temporarily');
//			header('Location: ' . $redirect);
//			unlink('/tmp/' . $_GET['sid']);
//			exit();
//		}
//	}
//}
