<?php

use \Rollbar\Rollbar;
use \Rollbar\Payload\Level;
function initRollbar() {
    if(defined('ROLLBAR_INIT')) {
        return;
    }
    if(defined('ENVIRONMENT') && (ENVIRONMENT === 'staging' || ENVIRONMENT === 'production'))
    {
        if(empty(getenv('ROLLBAR_ROOT_DIR')) || empty(getenv('ROLLBAR_TOKEN'))) return;
        define('ROLLBAR_INIT', true);
        $config = array(
            // required
            'access_token' => getenv('ROLLBAR_TOKEN'),
            // optional - environment name. any string will do.
            'environment' => ENVIRONMENT,
            // optional - path to directory your code is in. used for linking stack traces.
            'root' => getenv('ROLLBAR_ROOT_DIR'),
            'included_errno' => E_ERROR | E_PARSE | E_CORE_ERROR | E_USER_ERROR | E_RECOVERABLE_ERROR
        );
        Rollbar::init($config);
    }
}