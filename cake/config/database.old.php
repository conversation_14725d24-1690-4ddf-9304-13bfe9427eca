<?php

class DATABASE_CONFIG {

    var $supplier_directory = array(
        'driver' => 'mysql',
        'persistent' => false,
        'host' => '***********',
        'login' => 'root',
        'password' => '',
        'database' => Supplier_Directory_DB,
        'encoding' => 'utf8'
    );
    var $template = array(
        'driver' => 'mysql',
        'persistent' => false,
        'host' => '***********',
        'login' => 'root',
        'password' => '',
        'database' => Portal_Site_DB,
        'encoding' => 'utf8'
    );
    var $df = array(
        'driver' => 'mysql',
        'persistent' => false,
        'host' => '***********',
        'login' => 'root',
        'password' => '',
        'database' => Portal_Site_DB,
        'encoding' => 'utf8'
    );
    var $portal = array(
        'driver' => 'mysql',
        'persistent' => false,
        'host' => '***********',
        'login' => 'root',
        'password' => '',
        'database' => Portal_Site_DB,
        'encoding' => 'utf8'
    );
    var $default = array(
        'driver' => 'mysql',
        'persistent' => false,
        'host' => '***********',
        'login' => 'root',
        'password' => '',
        'database' => Default_DB,
        'encoding' => 'utf8'
    );
    var $static = array(
        'driver' => 'mysql',
        'persistent' => false,
        'host' => '***********',
        'login' => 'root',
        'password' => '',
        'database' => Static_DB,
        'encoding' => 'utf8'
    );

    public function __construct() {

        $link = mysql_connect($this->portal['host'], $this->portal['login'], $this->portal['password']);
        
        if (PHP_SAPI != 'cli') {
   

            $host = $_SERVER['HTTP_HOST'];
            $hostSlug = Inflector::slug($host);
          
            if (strpos($_SERVER['REQUEST_URI'], '/owners/firstlogin') !== false || strpos($_SERVER['REQUEST_URI'], '/owners/first_settings') !== false) {
                
           
                Cache::delete($hostSlug);
                Cache::delete("$hostSlug-currentSite");
            }else{
                $config = Cache::read($hostSlug);
            }



            if (!$config) {
                $site = Cache::read("$hostSlug-currentSite");
                if (!$site) {
					debug($this->portal);
                    
                    mysql_select_db($this->portal['database'], $link);
                    mysql_query('SET NAMES utf8');

                    $query = 'SELECT id, db_config FROM sites WHERE subdomain = "' . mysql_real_escape_string($host, $link) . '"';

                    $result = mysql_query($query, $link);
                    if (mysql_num_rows($result) < 1) {
                        header('HTTP / 1.1 301 Redirect Permanently');
                        header("Location: https://" . Portal_Full_Name . "/register?domain=" . $host);
                        exit();
                    }
                    $site = mysql_fetch_assoc($result);

                    Cache::write("$hostSlug-currentSite", $site['id']);
                }

                $config = json_decode($site['db_config'], true);
                if (empty($config['driver'])) {
                    $config['driver'] = 'mysql';
                }
                $config['encoding'] = 'utf8';

                Cache::write($hostSlug, $config);
            }
           
                $_SESSION['db_config'] = $config;
           
            
            $this->default = $config;
        }
    }

}
?>