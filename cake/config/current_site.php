<?php

use Izam\Daftra\Cache\PortalCache;

$host= str_replace(Beta_Domain, Domain_Short_Name, $_SERVER['HTTP_HOST']);
    $hostSlug = str_replace('.','_',$host);

	$site_id = Cache::read("$hostSlug-currentSite");
        if($site_id){
        $con=array('Site.id' => $site_id);
		
        }else{
        $con=array('Site.subdomain' => $host);    
        }
		if (!class_exists('CakeSession')) {
			App::import('Core', 'Session');
		}
                
    $session = new CakeSession;
    //$SystemIndustryModel = ClassRegistry::init(array('class' => 'SystemIndustry', 'ds' => 'df'));


    if ($session->check('CurrentSite') && $site_id) {

    $site = $session->read('CurrentSite');
    }else if($session->check('CurrentSite') && !$site_id){
        $SiteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));
        $IndustryModel = ClassRegistry::init(array('class' => 'Industry'));
        $site = $SiteModel->find($con);
        Cache::write("$hostSlug-currentSite",$site['Site']['id']);
    } else {
        $SiteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));
        $IndustryModel = ClassRegistry::init(array('class' => 'Industry'));
        $site = $SiteModel->find($con);
        unset($site['Site']['password'], $site['Site']['modified']);
        $session->write('CurrentSite', $site['Site']);
        $site = $site['Site'];
    }

    if ($session->check('timezone')) {
        $timezone = $session->read('timezone');
    } else {
        $SiteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));

        $timeZoneModel = $SiteModel->loadModel('Timezone');
        $timezone = $timeZoneModel->field('zone_name', array('Timezone.id' => $site['timezone']));
        $session->write('timezone', $timezone);
    }

    if ($timezone) {

        date_default_timezone_set($timezone);
    }
                        
    if(!defined('DefaultLang')){
        $lang_folder_path=dirname(dirname(__FILE__)).DIRECTORY_SEPARATOR.'locale'.DIRECTORY_SEPARATOR.  CurrentSiteLang().DIRECTORY_SEPARATOR;
        $df=check_po_file($lang_folder_path,getCurrentSite('Industry'),getCurrentSite('country_code'));
        if($df!=""){
            define('DefaultLang',$df);
        }
    }
    define('SITE_HASH', dechex(crc32($site['id'])));
    // warning suppress
    if(!file_exists(WWW_ROOT . "/files/")){
        mkdir(WWW_ROOT . "/files/");
    }
    // end warning suppress
     if (!file_exists(WWW_ROOT . "/files/" . SITE_HASH . "/")) {
         mkdir(WWW_ROOT . "/files/" . SITE_HASH . "/");
    }
    $limitsCacheName = 'limits';
     $limits = PortalCache::get($limitsCacheName, function() use ($limitsCacheName) {
        $SiteModel = ClassRegistry::init(array('class' => 'Site', 'ds' => 'portal'));
        $limits['document_size_limit'] = $SiteModel->get_document_size_limit();
        $limits['logo_size_limit'] = $SiteModel->get_logo_size_limit();
        $limits['terms_size_limit'] = $SiteModel->get_terms_size_limit();
        $limits['attachment_size_limit'] = $SiteModel->get_attachment_size_limit();
        PortalCache::set($limitsCacheName, $limits);
        return $limits;
    });
    define('DOCUMENT_SIZE', $limits['document_size_limit']);
    define('DOCUMENT_SIZE_MB', (DOCUMENT_SIZE / 1024) . 'MB');
    define('LOGO_SIZE', $limits['logo_size_limit']);
    define('LOGO_SIZE_MB', (LOGO_SIZE / 1024) . 'MB');
    define('TERMS_SIZE', $limits['terms_size_limit']);
    define('TERMS_SIZE_MB', (TERMS_SIZE / 1024) . 'MB');
    define('ATTACHMENT_SIZE', $limits['attachment_size_limit']);
    define('ATTACHMENT_SIZE_MB', (ATTACHMENT_SIZE / 1024) . 'MB');
