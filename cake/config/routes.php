<?php
/**
 *  An example CORS-compliant method.  It will allow any GET, POST, or OPTIONS requests from any
 *  origin.
 *
 *  In a production environment, you probably want to be more restrictive, but this gives you
 *  the general idea of what is involved.  For the nitty-gritty low-down, read:
 *
 *  - https://developer.mozilla.org/en/HTTP_access_control
 *  - https://fetch.spec.whatwg.org/#http-cors-protocol
 *
 */

use Izam\Daftra\Cache\PortalCache;

if(!function_exists("cors")) {
    function cors()
    {

        // Allow from any origin
        if (isset($_SERVER['HTTP_ORIGIN'])) {
            // Decide if the origin in $_SERVER['HTTP_ORIGIN'] is one
            // you want to allow, and if so:
            header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
            header('Access-Control-Allow-Credentials: true');
            header('Access-Control-Max-Age: 86400');    // cache for 1 day
        }

        // Access-Control headers are received during OPTIONS requests
        if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {

            if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
                // may also be using PUT, PATCH, HEAD etc
                header("Access-Control-Allow-Methods: GET, POST, OPTIONS");

            if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
                header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");

            exit(0);
        }

//    echo "You have CORS!";
    }
}
$api_prefix = 'api2/';

$here = Router::url($_SERVER['REQUEST_URI']);
if (low(substr($here, 1, strlen($api_prefix))) === $api_prefix) {

    define("IS_REST", true);
    define('IS_REST2', false);
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Headers: *');
    if ($_SERVER['REQUEST_METHOD'] == "OPTIONS") die();
    $api_prefix = "/${api_prefix}";
    require_once(__DIR__ . "/api_routes.php");
} else {
    if (low(substr($here, 1, strlen($api_prefix))) == 'rest/') {
        define('IS_REST2', true);
    } else {
        define('IS_REST2', false);
    }

    define("IS_REST", false);
}


if (PHP_SAPI != 'cli') {
    require_once dirname(__FILE__) . DS . 'current_site.php';
}


getCurrentSite('Industry');

$config = parse_ini_file(APP . 'app_config.ini');
if (ifPluginActive(WebsiteFrontPlugin) && !isMobileApp()) {
    Router::connect('/home', [ 'controller' => 'contents', 'action' => 'process_content', 'home']);
    Router::connect('/', ['controller' => 'contents', 'action' => 'process_content', 'home']);
    Router::connect('/client/dashboard', ['controller' => 'clients', 'action' => 'dashboard', 'prefix' => 'client', 'client' => true]);
}

Router::connect('/uploaderS3', ['controller' => 'uploader_s3', 'action' => 'add' , 'prefix' => 'owner']);
Router::connect('/s/*', ['controller' => 'shorturls', 'action' => 'redirect_to_full_url']);

if (PHP_SAPI != 'cli') {

    $domainsCacheName = 'domains';
    $cachedDomains = PortalCache::get($domainsCacheName, function() use ($domainsCacheName) {
        $domainModel = ClassRegistry::init('Domain');
        $domains = $domainModel->find('all', ['fields' => ['host', 'default']]);
        $domains = array_column($domains, 'default', 'host');
        return $domains;
    });
    $hostHasDomain = $cachedDomains[$_SERVER['HTTP_HOST']]??false;
    if ($hostHasDomain) {
        Router::connect('/', ['controller' => 'pages', 'action' => 'home']);
    } else {
        $session = new CakeSession(null, false);

        $headers = getallheaders();

        $authorization = null;

        if (isset($headers['Authorization'])) {
            $authorization = $headers['Authorization'];
        }

        if (isset($headers['authorization'])) {
            $authorization = $headers['authorization'];
        }

        if ($_SERVER['REQUEST_METHOD'] == 'POST' && !empty($_POST['access_token'])) {
            $authorization = "Bearer {$_POST['access_token']}";
        }

        /** coule be Basic, used in /admin urls */
        $isBearer = str_contains($authorization, 'Bearer');

        if ($authorization && $isBearer) {
            require_once APP . 'vendors' . DS . 'auth_token.php';
        }

        if ($session->check('CLIENT')) {
            Router::connect('/', ['controller' => 'clients', 'action' => 'dashboard', 'prefix' => 'client', 'client' => true]);
            Router::connect('/owner', ['controller' => 'sites', 'action' => 'dashboard', 'prefix' => 'owner', 'owner' => true]);
        } elseif ($session->check('OWNER')) {
            Router::connect('/', ['controller' => 'sites', 'action' => 'dashboard', 'prefix' => 'owner', 'owner' => true]);
            Router::connect('/client', ['controller' => 'clients', 'action' => 'dashboard', 'prefix' => 'client', 'client' => true]);
        } else {
            Router::connect('/', ['controller' => 'clients', 'action' => 'login']);
        }
    }
}

Router::connect('/bookings/get_staff_time', ['controller' => 'bookings', 'action' => 'get_staff_time']);
Router::connect('/tooltip/*', ['controller' => 'tooltips', 'action' => 'tooltip']);
Router::connect('/pages/*', ['controller' => 'pages', 'action' => 'display']);
Router::connect('/admin/', ['controller' => 'pages', 'action' => 'index', 'prefix' => 'admin', 'admin' => true]);
Router::connect('/contact', ['controller' => 'contacts', 'action' => 'contactus', 'prefix' => 'owner']);
Router::connect('/login', ['controller' => 'clients', 'action' => 'login']);
Router::connect('/renew', ['prefix' => 'owner', 'controller' => 'sites', 'action' => 'renew', 'owner' => true]);
Router::connect('/email_view/*', ['controller' => 'clients', 'action' => 'email_view']);
Router::connect('/content/*', ['controller' => 'pages', 'action' => 'index']);
Router::connect('/owners/:action/*', ['controller' => 'sites']);
Router::connect('/owner/logout', ['controller' => 'sites', 'action' => 'logout', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/settings', ['controller' => 'sites', 'action' => 'change_settings', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/owner/numbering/settings/*', ['controller' => 'settings', 'action' => 'numbering', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/backup', ['controller' => 'sites', 'action' => 'backup', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/plguin_manager', ['controller' => 'sites', 'action' => 'plguin_manager', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/smtp_settings', ['controller' => 'sites', 'action' => 'smtp_settings', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/owner/owners/:action/*', ['controller' => 'sites', 'prefix' => 'owner', 'owner' => true]);
Router::connect('/client/logout', ['controller' => 'clients', 'action' => 'logout', 'prefix' => 'client', 'client' => true]);
Router::connect('/client/clients', ['controller' => 'clients', 'action' => 'change_settings', 'prefix' => 'client', 'client' => true]);
Router::connect('/2co', ['controller' => 'invoice_payments', 'action' => 'two_checkout']);
Router::connect('/paytabs_ipn_listener', ['controller' => 'invoice_payments', 'action' => 'paytabs_ipn_listener']);
Router::connect('/dismiss_snippet', ['controller' => 'snippets', 'action' => 'dismiss_snippet']);

$plugins = App::objects('plugin');
if ($plugins) {
    $pluginMatch = implode('|', array_map(['Inflector', 'underscore'], $plugins));
    Router::connect(
        "/owner/:plugin/:controller/:action/*", ['prefix' => 'owner', 'owner' => true], ['plugin' => $pluginMatch]
    );
}
Router::connect(
    "/rest/clients/login", ['rest' => true, 'controller' => 'clients', 'action' => 'login']
);
Router::connect(
    "/rest/:controller/:action/*", ['prefix' => 'owner', 'rest' => true]
);

Router::connect('/journals/json_find_costCenter', ['prefix' => 'owner' , '[method]' => 'GET','controller' => 'journals', 'action' => 'json_find_costCenter']);

Router::connect('/incomes/reset_filter', ['controller' => 'expenses', 'action' => 'reset_filter']);
Router::connect('/owner/incomes/reset_filter', ['prefix' => 'owner', 'owner' => true, 'controller' => 'expenses', 'action' => 'reset_filter']);
Router::connect('/owner/incomes/:action/*', ['prefix' => 'owner', 'owner' => true, 'controller' => 'expenses', 'is_income' => true]);
Router::connect('/owner/:controller/:action/*', ['prefix' => 'owner', 'owner' => true]);
Router::connect('/client/:controller/:action/*', ['prefix' => 'client', 'client' => true]);
/**
 * ...and connect the rest of 'Pages' controller's urls.
 */
Router::parseExtensions('pdf', 'jpeg', 'csv', 'xlsx', 'xml', 'json');
Router::connect('/api/incomes/*', ['controller' => 'api', 'action' => 'expenses', 'is_income' => true]);
if (PHP_SAPI != 'cli') {
    require_once dirname(__FILE__) . DS . 'current_site.php';
}

/* take care of crons */
$providerPath = __DIR__. DS . '../providers';

\Izam\Entity\Service\ShowLegacyListing::setPlatform('cake');

$providers = [
    '/database_provider.php',
    '/service_provider.php',
    '/database_queue.php',
    '/events.php',
    '/general_provider.php',
    '/limitation_service.php',
    '/rollbar_provider.php',
    '/rollbar_provider.php',
];

foreach($providers as $provider) {
    require_once $providerPath . $provider;
}

Router::connect('/health', ['controller' => 'health', 'action' => 'index']);
Router::connect('/healthz', array('controller' => 'health', 'action' => 'healthz'));
Router::connect('/dynamic/add', ['controller' => 'dynamic', 'action' => 'add']);
Router::connect('/whatismyip', ['controller' => 'tools', 'action' => 'whatismyip']);