<?php
//header("Access-Control-Allow-Origin: *");
//header('Access-Control-Allow-Methods: GET, PUT, POST, DELETE, OPTIONS');
/*
 * Site
 */
Router::connect($api_prefix . 'site_info',	array('controller' => "sites", 'action' => 'me', '[method]' => 'GET', 'prefix' => 'api'));
Router::connect($api_prefix . 'site_local',	array('controller' => "sites", 'action' => 'local', '[method]' => 'GET', 'prefix' => 'api'));
Router::connect($api_prefix . 'first_settings',	array('controller' => "sites", 'action' => 'first_settings', '[method]' => 'POST','rest' => true, 'allowed_models'=>["Site"], 'prefix' => 'owner'));
Router::connect($api_prefix . 'check_first_login',	array('controller' => "sites", 'action' => 'check_first_login', '[method]' => 'GET', 'prefix' => 'api'));
Router::connect($api_prefix . 'layout',	array('controller' => "sites", 'action' => 'get_layout', '[method]' => 'GET', 'prefix' => 'api'));
Router::connect($api_prefix . 'menu_data',	array('controller' => "sites", 'action' => 'get_menu_data', '[method]' => 'GET', 'prefix' => 'api'));
Router::connect($api_prefix . 'pos_shortcuts_test', ['controller' => 'sites', 'action' => 'pos_shortcuts_test', '[method]' => 'GET', 'prefix' => 'api']);
Router::connect($api_prefix . 'site_local_list',	array('controller' => "sites", 'action' => 'local_list', '[method]' => 'GET', 'prefix' => 'api'));
Router::connect($api_prefix . 'inventory/settings', ['controller' => 'settings', 'action' => 'inventory', 'prefix' => 'owner', '[method]' => 'GET', 'rest' => true]);
Router::connect($api_prefix . 'set_report_session', ['controller' => 'reports', 'action' => 'set_report_session', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'array' => true]);

/*
 * Invoice
 */
$controller = $urlName = Inflector::underscore('Invoices');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/list_pos_invoices/:id',	array('controller' => $controller, 'action' => 'list_pos_invoices', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/search/:search',	array('controller' => $controller, 'action' => 'search_invoices', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('pass' => array('search')));
Router::connect($api_prefix . $urlName . '/search',	array('controller' => $controller, 'action' => 'search_invoices', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'array' => true));
Router::connect($api_prefix . $urlName . '/update_draft/:id/:draft_status',	array('controller' => $controller, 'action' => 'update_draft', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('pass' => array('id', 'draft_status')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'invoice'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/assign_work_order/:id',	array('controller' => $controller, 'action' => 'assign_work_order', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/add_shipping/:id',	array('controller' => $controller, 'action' => 'add_shipping', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/add_bulk', ['controller' => $controller, 'action' => 'add_bulk', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'array' => true]);
Router::connect($api_prefix . $urlName . '/subscription',				array('controller' => $controller, 'action' => 'subscriptions', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/view_subscription/:id',	array('controller' => $controller, 'action' => 'view_subscription', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/add_subscription',	array('controller' => $controller, 'action' => 'add_subscription', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/edit_subscription/:id',	array('controller' => $controller, 'action' => 'edit_subscription', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/delete_subscription/:id',	array('controller' => $controller, 'action' => 'delete_subscription', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'invoice'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/change_status/:id/:status_id',array('controller' => $controller, 'action' => 'chnage_bulk_invoice_status', '[method]' => 'GET',  'rest' => true) , array('pass' => array('id','status_id')));
Router::connect($api_prefix . $urlName . '/accessable_client/:clientId',array('controller' => $controller, 'action' => 'accessable_client', '[method]' => 'GET',  'rest' => true) , array('pass' => array('clientId')));
Router::connect($api_prefix . $urlName . '/list_pos_by_type/:type',    array('controller' => $controller, 'action' => 'list_pos_by_type', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),    array('id' => '[0-9]+', 'pass' => array('type')));
Router::connect($api_prefix . $urlName . '/search_by_type/:type',    array('controller' => $controller, 'action' => 'search_by_type', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'array' => true), array('pass' => array('type')));

$controller = $urlName = Inflector::underscore('SalesOrders');
Router::connect($api_prefix . $urlName . '/convert_to_invoice/:id', array('controller' => $controller, 'action' => 'convert_to_invoice', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models' => ["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment"]),    array('id' => '[0-9]+', 'pass' => array('id')));


Router::connect($api_prefix . 'sms/send_message',	array('controller' => 'sms', 'action' => 'send_message', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>[]));

/*
 * Estimate
 */
$urlName = Inflector::underscore('Estimates');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'estimates', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view_estimate', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete_estimate', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add_estimate', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "Estimate"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_estimate', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "Estimate"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_estimate', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "Estimate"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * CreditNote
 */
$urlName = Inflector::underscore('CreditNotes');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'creditnotes', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view_creditnote', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'credit note'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add_creditnote', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "CreditNote"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_creditnote', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "CreditNote"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_creditnote', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "CreditNote"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * RefundReceipt
 */
$urlName = Inflector::underscore('RefundReceipts');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'refund', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view_refund', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'refund reciept'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add_refund', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "RefundReceipt", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "InvoicePayment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_refund', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "RefundReceipt", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "InvoicePayment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_refund', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Invoice", "Client", "InvoiceCustomField", "InvoiceItem", "Deposit", "InvoiceReminder", "DocumentTitle", "Document", "Payment", "InvoicePayment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Supplier
 */
$controller = $urlName = Inflector::underscore('Suppliers');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Supplier", "CustomModel", "SupplierDetail"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Supplier", "CustomModel", "SupplierDetail"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Supplier", "CustomModel", "SupplierDetail"]),	array('id' => '[0-9]+', 'pass' => array('id')));

/*
 * Sales Order
 */
$controller = $urlName = Inflector::underscore('SalesOrders');
Router::connect($api_prefix . $urlName . '/:id', [
    'controller' => $controller,
    'action' => 'delete',
    '[method]' => 'DELETE',
    'prefix' => 'owner',
    'rest' => true
], ['id' => '[0-9]+', 'pass' => ['id']]);

/*
 * WorkOrder
 */
$controller = $urlName = Inflector::underscore('WorkOrders');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['WorkOrder']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['WorkOrder']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['WorkOrder']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * ClientAppointment
 */
$controller = Inflector::underscore('Appointments');
$urlName = Inflector::underscore('ClientAppointments');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 1),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 1),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment', "RecurringAppointment"], null, 1),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment', "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment', "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * InvoiceAppointment
 */
$urlName = Inflector::underscore('InvoiceAppointments');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 2, 'module_name'=>"invoice_appointments"),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 2, 'module_name'=>"invoice_appointments"),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"invoice_appointments"), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"invoice_appointments", 'allowed_models'=>['ClientAppointment'=>"InvoiceAppointments", "RecurringAppointment"], null, 2),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"invoice_appointments", 'allowed_models'=>['ClientAppointment'=>"InvoiceAppointments", "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"invoice_appointments", 'allowed_models'=>['ClientAppointment'=>"InvoiceAppointments", "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * EstimateAppointment
 */
$urlName = Inflector::underscore('EstimateAppointments');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 3),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 3),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment'=>"EstimateAppointment", "RecurringAppointment"], null, 3),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment'=>"EstimateAppointment", "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment'=>"EstimateAppointment", "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * WorkOrderAppointment
 */
$urlName = Inflector::underscore('WorkOrderAppointments');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 8),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 8),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment'=>"WorkOrderAppointment", "RecurringAppointment"], null, 8),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment'=>"WorkOrderAppointment", "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ClientAppointment'=>"WorkOrderAppointment", "RecurringAppointment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Note
 */
$urlName = Inflector::underscore('Notes');
$controller = Inflector::underscore('Posts');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:type/:id',	array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Post'=>'Note', "FollowUpReminder"]),	array('type' => 'client|invoice|estimate|purchase_order|staff|supplier|product|work_order', 'id' => '[0-9]+', 'pass' => array('type', 'id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Post'=>'Note', "FollowUpReminder"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Post'=>'Note', "FollowUpReminder"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * TimeTracking
 */
$controller = $urlName = Inflector::underscore('TimeTracking');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['TimeTracking']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['TimeTracking']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['TimeTracking']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Staff
 */
$controller = Inflector::underscore('Staffs');
$urlName = Inflector::underscore('Staff');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Staff']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Staff']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Staff']),	array('id' => '[0-9]+', 'pass' => array('id')));

Router::connect(
    $api_prefix . $urlName . '/send_login_details/:id',	[
        'controller' => $controller, 
        'action' => 'send_login_details', 
        '[method]' => 'GET', 
        'prefix' => 'owner', 
        'rest' => true, 
        'allowed_models'=>['Staff']
    ],	[
        'id' => '[0-9]+', 
        'pass' => ['id']
    ]
);
/*
 * InvoicePayment
 */
$controller = $urlName = Inflector::underscore('InvoicePayments');
Router::connect($api_prefix . $urlName,			array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"invoice"),array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"invoice"),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'invoice'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,			array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['InvoicePayment']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['InvoicePayment']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['InvoicePayment']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * ClientPayment
 */
$urlName = Inflector::underscore('ClientPayments');
Router::connect($api_prefix . $urlName,			array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"client"),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'module_name'=>"client"),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'client'), array('id' => '[0-9]+', 'pass' => array('id')));

$controller =  Inflector::underscore('Clients');
Router::connect($api_prefix . $urlName, array('controller' => $controller, 'action' => 'add_payment_credit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['InvoicePayment'=>"ClientPayment","CustomModel"]),	array('id' => '[0-9]+', 'pass' => array('id')));

$controller = $urlName = Inflector::underscore('InvoicePayments');
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['InvoicePayment'=>"ClientPayment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['InvoicePayment'=>"ClientPayment"]),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Product
 */
$controller = $urlName = Inflector::underscore('Products');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/brands',	array('controller' => $controller, 'action' => 'list_brands', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Product']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Product']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Product']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . 'is_product_can_be_deleted',	array('controller' => $controller, 'action' => 'is_product_can_be_deleted', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true));
Router::connect($api_prefix . $urlName. '/file_from_url',	array('controller' => $controller, 'action' => 'file_from_url', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true));

/*
 * Journal
 */
$controller = $urlName = Inflector::underscore('Journals');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/accounts/:cat_id',	array('controller' => $controller, 'action' => 'getChildAccounts', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true), array('cat_id' => '-?[0-9]+', 'pass' => array('cat_id')));
Router::connect($api_prefix . $urlName . '/accounts/',	array('controller' => $controller, 'action' => 'getParents', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true), array());
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName, array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Journal','JournalTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName. '/save_auto', array('controller' => $controller, 'action' => 'save_auto_journal', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Journal','JournalTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Journal','JournalTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Journal','JournalTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Store
 */
$controller = $urlName = Inflector::underscore('Stores');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Store']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Store']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Store']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Expense
 */
$controller = $urlName = Inflector::underscore('Expenses');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/update_draft/:id/:status',	array('controller' => $controller, 'action' => 'update_draft', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id', 'status')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Expense', 'JournalTransaction', 'CostCenterTransaction', 'RecurringExpense']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Expense', 'JournalTransaction', 'CostCenterTransaction', 'RecurringExpense']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Expense', 'JournalTransaction', 'CostCenterTransaction', 'RecurringExpense']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Income
 */
$controller = Inflector::underscore('Expenses');
$urlName = Inflector::underscore('Incomes');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'is_income' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 'is_income' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'is_income' => true, ), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/update_draft/:id/:status',	array('controller' => $controller, 'action' => 'update_draft', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'is_income' => true), array('id' => '[0-9]+', 'pass' => array('id', 'status')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'is_income' => true, 'allowed_models'=>['Expense', 'JournalTransaction', 'CostCenterTransaction', 'RecurringExpense']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'is_income' => true, 'allowed_models'=>['Expense', 'JournalTransaction', 'CostCenterTransaction', 'RecurringExpense']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'is_income' => true, 'allowed_models'=>['Expense', 'JournalTransaction', 'CostCenterTransaction', 'RecurringExpense']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Tax
 */
$controller = $urlName = Inflector::underscore('Taxes');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Tax']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Tax']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Tax']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * PurchaseOrder
 */
$controller = $urlName = Inflector::underscore('PurchaseInvoices');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['PurchaseOrder', 'PurchaseOrderItem', 'Payment', 'PurchaseOrderCustomField']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['PurchaseOrder', 'PurchaseOrderItem', 'PurchaseOrderCustomField']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['PurchaseOrder', 'PurchaseOrderItem']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * PurchaseRefund
 */
$urlName = Inflector::underscore('PurchaseRefunds');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'refund', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view_refund', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'purchase_refund'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add_refund', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['PurchaseOrder'=>'PurchaseRefund', 'PurchaseOrderItem']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_refund', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['PurchaseOrder'=>'PurchaseRefund','PurchaseOrderItem']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit_refund', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['PurchaseOrder'=>'PurchaseRefund', 'PurchaseOrderItem']),	array('id' => '[0-9]+', 'pass' => array('id')));

/**
 * Stocktaking
 */
$controller = $urlName = Inflector::underscore('Stocktakings');
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));

/*
 * Client
 */
$controller = $urlName = Inflector::underscore('Clients');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete_client', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/suspend_users/:suspend/:id', array('controller' => $controller, 'action' => 'suspend_users', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'suspend' => '[0-9]+', 'pass' => array('id', 'suspend')));
Router::connect($api_prefix . $urlName . '/search_client',	array('controller' => $controller, 'action' => 'search_client', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'array' => true));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Client', 'ClientDetail', 'le_custom_data_client']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Client', 'ClientDetail', 'le_custom_data_client']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Client', 'ClientDetail', 'le_custom_data_client']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/profile',	array('controller' => $controller, 'action' => 'profile', '[method]' => 'GET', 'prefix' => 'client', 'rest' => true, 'allowed_models'=>['Client', 'ClientDetail']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/assign_staff/:id',	array('controller' => $controller, 'action' => 'assign_staff', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['ItemStaff']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/loyalty-points/:id',	array('controller' => $controller, 'action' => 'loyalty_points', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Client']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/add_bulk', ['controller' => $controller, 'action' => 'add_bulk', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'array' => true]);
Router::connect($api_prefix . $urlName . '/client_has_membership', ['controller' => $controller, 'action' => 'client_has_membership', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'array' => true]);
Router::connect($api_prefix . $urlName . '/client_attendance_barcode', ['controller' => $controller, 'action' => 'client_attendance_by_barcode', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'array' => true]);
/*
 * Address
 */
$controller = $urlName = Inflector::underscore('Address');
Router::connect($api_prefix . $urlName,	array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models' => ['Address']),array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * JournalAccount
 */
$controller = $urlName = Inflector::underscore('JournalAccounts');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName .'/json_find',				array('controller' => $controller, 'action' => 'json_find', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' =>$controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['JournalAccount'], 'journal_account_type'=>1),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['JournalAccount']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['JournalAccount']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * JournalCat
 */
$controller = $urlName = Inflector::underscore('JournalCats');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['JournalCat'], 'journal_account_type'=>0),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['JournalCat']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['JournalCat']),	array('id' => '[0-9]+', 'pass' => array('id')));


/**
 * Branches
 */

$controller = $urlName = Inflector::underscore('Branches');
Router::connect($api_prefix . $urlName . '/all', ['controller' => $controller, 'action' => 'all', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Branch']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName.'/set_report_transactions_branch/:id',				array('controller' => $controller, 'action' => 'set_report_transactions_branch', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array( 'pass' => array('id')));
Router::connect($api_prefix . $urlName.'/set_report_account_branch/:id',	array('controller' => $controller, 'action' => 'set_report_account_branch', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array( 'pass' => array('id')));
Router::connect($api_prefix . 'branches_settings', ['controller' => $controller, 'action' => 'branches_settings', '[method]' => 'GET', 'prefix' => 'api']);
/*
 * Treasury
 */
$controller = $urlName = Inflector::underscore('Treasuries');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/list',				array('controller' => $controller, 'action' => 'list', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Treasury']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Treasury']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Treasury']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * FollowUpAction
 */
$controller = $urlName = Inflector::underscore('FollowUpActions');
Router::connect($api_prefix . $urlName . '/:type',	array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('type' => 'client|invoice|estimate|purchase_order|staff|supplier|product|work_order', 'pass' => array('type')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'api', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * FollowUpStatus
 */
$controller = $urlName = Inflector::underscore('FollowUpStatuses');
Router::connect($api_prefix . $urlName . '/:type',	array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('type' => 'client|invoice|estimate|purchase_order|staff|supplier|product|work_order', 'pass' => array('type')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'api', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * StockTransaction
 */
$controller = $urlName = Inflector::underscore('StockTransactions');
$controller2 = Inflector::underscore('Products');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'api', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller2, 'action' => 'manual_stock_adjust', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['StockTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller2, 'action' => 'edit_stock_transaction', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['StockTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller2, 'action' => 'edit_stock_transaction', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['StockTransaction']),	array('id' => '[0-9]+', 'pass' => array('id')));
/*
 * Category
 */
//TODO Create an endpoint foreach CategoryType for now it supports only ProductCategories
$controller = Inflector::underscore('Categories');
$urlName = Inflector::underscore('ProductCategories');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 1),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['Category']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Category"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Category"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id/:type',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('type' => '[0-9]+','id' => '[0-9]+', 'pass' => array('id', 'type')));

$urlName = Inflector::underscore('Categories');
Router::connect($api_prefix . $urlName . '/update_display_order/:type',	array('controller' => $controller, 'action' => 'update_display_order', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true), array('type' => '[0-9]+', 'pass' => array('type')));
Router::connect($api_prefix . $urlName . '/update_category_items/:type',	array('controller' => $controller, 'action' => 'update_category_items', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true), array('type' => '[0-9]+', 'pass' => array('type')));
/*
 * Setting
 */
$controller = $urlName = Inflector::underscore('Settings');
Router::connect($api_prefix . $urlName . '/pos_interface_settings',	array('controller' => $controller, 'action' => 'pos_interface_settings', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["pos_interface_settings"]));
Router::connect($api_prefix . $urlName . '/:plugin_id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('plugin_id' => 'pos', 'pass' => array('plugin_id')));
/*
 * PosShifts 
 */
$controller = $urlName = Inflector::underscore('PosShifts');
Router::connect($api_prefix . $urlName,	            array('controller' => $controller, 'action' => 'get_session', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true));
Router::connect($api_prefix . $urlName . '/close',  array('controller' => $controller, 'action' => 'close_session', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true));
/*
 * Countries
 */
$controller = $urlName = Inflector::underscore('Countries');
Router::connect($api_prefix . $urlName,	            array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true));
/*
 * CustomForms 
 */
$controller = $urlName = Inflector::underscore('CustomForms');
Router::connect($api_prefix . $urlName . '/get_fields/:form_id',	array('controller' => $controller, 'action' => 'get_fields', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('form_id' => '[0-9]+', 'pass' => array('form_id')));
Router::connect($api_prefix . $urlName . '/:form_id',	array('controller' => $controller, 'action' => 'form_data', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('form_id' => '[0-9]+', 'pass' => array('form_id')));
Router::connect($api_prefix . $urlName . '/:form_id/:id',	array('controller' => $controller, 'action' => 'view_row', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('form_id' => '[0-9]+','id' => '[0-9]+', 'pass' => array('form_id','id')));
Router::connect($api_prefix . $urlName . '/:form_id',	array('controller' => $controller, 'action' => 'save_row', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['CustomModel']),	array('form_id' => '[0-9]+', 'pass' => array('form_id')));
Router::connect($api_prefix . $urlName . '/:form_id/:id',	array('controller' => $controller, 'action' => 'save_row', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>['CustomModel']),	array('form_id' => '[0-9]+','id' => '[0-9]+', 'pass' => array('form_id','id')));
Router::connect($api_prefix . $urlName . '/:form_id/:id',	array('controller' => $controller, 'action' => 'delete_record', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true),	array('form_id' => '[0-9]+','id' => '[0-9]+', 'pass' => array('form_id','id')));
/*
 * General Listing
 */
Router::connect($api_prefix . 'listing' . '/:model',	array('controller' => 'api', 'action' => 'listing', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true),	array('model' => 'Category|InvoiceLayout|GroupPrice|Timezone|DateFormats|Language|Role|Project|TimeActivity', 'pass' => array('model')));
Router::connect($api_prefix . 'execute_sql',	array('controller' => 'api', 'action' => 'execute_sql', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true,'allowed_models'=>['sql']));

//dd('/'.$api_prefix . $urlName . '/:form_id');

$controller = $urlName = Inflector::underscore('ActionLines');
Router::connect($api_prefix . $urlName . '/list_actionline_by_ids',	array('controller' => $controller, 'action' => 'list_actionline_by_ids', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>[]));
Router::connect($api_prefix . $urlName . '/get_actions',	array('controller' => $controller, 'action' => 'get_actions', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>[]));

$controller = $urlName = Inflector::underscore('Bookings');
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'client', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'client', 'rest' => true, 'allowed_models'=>['Invoice', 'InvoiceItem']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName.'/convert/:id', array('controller' => $controller, 'action' => 'convert_booking_to_invoice', '[method]' => 'GET', 'prefix' => 'client', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . 'client'. '/' . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'client', 'rest' => true, 'module_name'=>'invoice'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'invoice'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/add', array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Invoice', 'InvoiceItem']));
Router::connect($api_prefix . $urlName . '/:id', array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Invoice', 'InvoiceItem']),array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/change_status/:id/:status', array('controller' => $controller, 'action' => 'change_status', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),array('id' => '[0-9]+', 'status' => '[0-9]', 'pass' => array('id', 'status')));
Router::connect($api_prefix . $urlName . '/convert_to_invoice/:id', array('controller' => $controller, 'action' => 'convert_booking_to_invoice', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),array('id' => '[0-9]+', 'pass' => array('id')));

$controller = $urlName = Inflector::underscore('Requisitions');
Router::connect($api_prefix . $urlName . '/trigger_update' . '/:id', ['controller' => $controller, 'action' => 'trigger_update', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true], ['id' => '[0-9]+', 'pass' => ['id']]);


/*
 * Cost Center API Routes !
 */
$controller = $urlName = Inflector::underscore('CostCenters');
Router::connect($api_prefix . $urlName, ['controller' => "cost_centers", 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);
Router::connect($api_prefix . $urlName . '/permissions', ['controller' => "cost_centers", 'action' => 'get_permissions', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);
Router::connect($api_prefix . $urlName . '/search', ['controller' => "cost_centers", 'action' => 'find', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);
Router::connect($api_prefix . $urlName . '/:id', ['controller' => "cost_centers", 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true], ['pass' => ['id']]);
Router::connect($api_prefix . 'cost_center_transactions', ['controller' => "cost_center_transactions", 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);

/*
 * Unit Templates Routes !
 */

$controller = $urlName = Inflector::underscore('UnitTemplates');
Router::connect($api_prefix . $urlName, ['controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);

/*
 * POS Insurance and Offers Routes !
 */
Router::connect($api_prefix . 'insurance_agent_classes/ajaxApplyInsurance',array('controller' => 'insurance_agent_classes', 'action' => 'ajaxApplyInsurance', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Invoice', 'InvoiceItem']),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . 'offers/ajaxApplyOffer',array('controller' => 'offers', 'action' => 'ajaxApplyOffer', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>['Invoice', 'InvoiceItem']),	array('id' => '[0-9]+', 'pass' => array('id')));

/*
 * Shipping Options API Routes !
 */

$controller = $urlName = Inflector::underscore('ShippingOptions');
Router::connect($api_prefix . $urlName, ['controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);

/*
 * POS APIs (Clients, Permissions) !
 */
Router::connect($api_prefix . 'get_second_payment_method', ['controller' => 'pos_shifts', 'action' => 'get_second_payment_method', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true]);
Router::connect($api_prefix . 'current_user_permissions', ['controller' => "sites", 'action' => 'get_current_user_permission', '[method]' => 'GET', 'prefix' => 'api']);
Router::connect($api_prefix . 'get_clients', ['controller' => 'clients', 'action' => 'get_clients', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true]);

/*
 * Custom JS Routes !
 */

Router::connect($api_prefix . 'pos_custom_js', ['controller' => "sites", 'action' => 'get_custom_pos_javascript', '[method]' => 'GET', 'prefix' => 'api']);

Router::connect($api_prefix . 'update_plugin/:id/:status', ['controller' => "sites", 'action' => 'update_plugin', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true], ['pass' => ['id', 'status']]);

Router::connect($api_prefix . 'sites/switch_booking_plugin_mode', ['controller' => "sites", 'action' => 'switch_booking_plugin_mode', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true]);

/*
 * Requisition
 */
$controller = $urlName = Inflector::underscore('Requisitions');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName ,        	array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Requisition", "RequisitionItem"]));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["RequisitionItem"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["RequisitionItem"]),	array('id' => '[0-9]+', 'pass' => array('id')));


/*
 * Assets
 */
$controller = $urlName = Inflector::underscore('Assets');
Router::connect($api_prefix . $urlName . '/edit/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Asset"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/edit/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Asset"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true), array('id' => '[0-9]+', 'pass' => array('id')));

/*
 * Placeholder
 */
$controller = $urlName = Inflector::underscore('Placeholder');
Router::connect($api_prefix . $urlName . '/list',	array('controller' => $controller, 'action' => 'list', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Asset"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/label/list',	array('controller' => $controller, 'action' => 'label', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Asset"]),	array('id' => '[0-9]+', 'pass' => array('id')));

$controller = $urlName = Inflector::underscore('ManufacturingOrders');
Router::connect($api_prefix . $urlName . '/update_journal/:id',	array('controller' => $controller, 'action' => 'update_manufacturing_orders_journal', '[method]' => 'GET', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>[]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/update_order_inbound_requisition/:id',	array('controller' => $controller, 'action' => 'update_order_inbound_requisition', '[method]' => 'POST', 'prefix' => 'api', 'rest' => true, 'allowed_models'=>["RequisitionItem"]),	array('id' => '[0-9]+', 'pass' => array('id')));


/*
 * Roles
 */
$controller = $urlName = Inflector::underscore('Roles');
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'view', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName,				array('controller' => $controller, 'action' => 'add', '[method]' => 'POST', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Role","RolesPermission"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'edit', '[method]' => 'PUT', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Role","RolesPermission"]),	array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName . '/:id',	array('controller' => $controller, 'action' => 'delete', '[method]' => 'DELETE', 'prefix' => 'owner', 'rest' => true, 'module_name'=>'invoice'), array('id' => '[0-9]+', 'pass' => array('id')));
Router::connect($api_prefix . $urlName.'/inital',				array('controller' => $controller, 'action' => 'inital', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true, 'allowed_models'=>["Role","RolesPermission"]),	array('id' => '[0-9]+', 'pass' => array('id')));

$controller = $urlName = Inflector::underscore('GroupPrices');
Router::connect($api_prefix . $urlName, ['controller' => $controller, 'action' => 'index', '[method]' => 'GET', 'prefix' => 'owner']);

/*
 * Custom Mobile API Team APIs
 */

// Is Plugin Active API (Can be used to check other plugins as well)
Router::connect($api_prefix . 'is_plugin_active', ['controller' => "sites", 'action' => 'get_plugin_status', '[method]' => 'GET', 'prefix' => 'api']);

Router::connect($api_prefix . 'spelled-currency',	array('controller' => "sites", 'action' => 'spelled_currency', '[method]' => 'GET', 'prefix' => 'api'));
/** PUT YOUR API RULES OVER THIS LINE **/
$controller = $urlName = Inflector::underscore('AutoReminderRules');
Router::connect($api_prefix . $urlName . '/check_auto_reminder_message/:entity_type' , ['controller' => $controller, 'action' => 'check_auto_reminder_message', '[method]' => 'GET', 'prefix' => 'owner', 'rest' => true]);


Router::connect($api_prefix . '*', array('controller' =>"sites", "action"=>'not_found', 'rest' => true));
