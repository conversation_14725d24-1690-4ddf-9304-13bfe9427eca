<?php
// Invoice Actions
define('ACTION_ADD_INVOICE', 1); //tested
//$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'],'param1' => $re_read_invoice['Invoice']['summary_total'],'param2' => $re_read_invoice['Invoice']['payment_status'],'param3' => $re_read_invoice['Invoice']['summary_paid'],'param4' => $re_read_invoice['Invoice']['no'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number']);

define('ACTION_ADD_INVOICE_FROM_ESTIMATE', 10002); //tested
//$arry = array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'],'param1' => $invoice['Invoice']['summary_total'],'param2' => $invoice['Invoice']['payment_status'],'param3' => $invoice['Invoice']['summary_paid'],'param4' => $invoice['Invoice']['no'],'param5' => $invoice['Client']['business_name'],'param6' => $invoice['Client']['client_number'],'param8' =>  $estimate['Invoice']['no'],'param9' =>  $id);
define('ACTION_RECURRING_ADD_INVOICE', 10003); //tested
//$arry = array('primary_id' => $result['Invoice']['id'], 'secondary_id' => $result['Invoice']['client_id'],'param1' => $result['Invoice']['summary_total'],'param2' => $result['Invoice']['payment_status'],'param3' => $result['Invoice']['summary_paid'],'param4' => $result['Invoice']['no'],'param9' => $subscription['Subscription']['id'],'param5' => $result['Client']['business_name'],'param6' => $result['Client']['client_number']);

define('ACTION_UPDATE_INVOICE', 2); //tested
//$arry = array( 'primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => $re_read_invoice['Invoice']['payment_status'],'param3' => $re_read_invoice['Invoice']['summary_paid'], 'param4' => $re_read_invoice['Invoice']['no'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number']);
define('ACTION_SEND_INVOICE', 3); //tested
//$arry = array('param2' => $to, 'param1' => $result, 'primary_id' => $invoice['id'], 'param4' => $invoice['no'], 'secondary_id' => $invoice['client_id']);
define('ACTION_PRINT_INVOICE', 4); //tested
//$this->add_actionline(ACTION_PRINT_INVOICE, array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param4' => $re_read_invoice['Invoice']['no']));
define('ACTION_DELETE_INVOICE', 5); //tested
//$this->add_actionline(ACTION_DELETE_INVOICE, array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'], 'param1' => $invoice['Invoice']['summary_total'], 'param2' => $invoice['Invoice']['payment_status'],'param3' => $invoice['Invoice']['summary_paid'],'param4' => $invoice['Invoice']['no'],'param5' => $invoice['Client']['business_name'],'param6' => $invoice['Client']['client_number']));
define('ACTION_DELETE_INVOICE_PAYMENT', 6); //tested
//CLIENT Actions
define('ACTION_CLIENT_VIEW_INVIOCE', 7); //tested
//$this->add_actionline(ACTION_CLIENT_VIEW_INVIOCE, array('secondary_id' => $invoice['Invoice']['client_id'], 'primary_id' => $invoice['Invoice']['id'], 'param4' => $invoice['Invoice']['no']));
define('ACTION_CLIENT_PRINT_INVIOCE', 8); //tested
define('ACTION_CLIENT_READ_INVOICE_EMAIL', 9); //tested

define('ACTION_ADD_INVOICE_PAYMENT', 10); //tested
//$this->add_actionline(ACTION_ADD_INVOICE_PAYMENT, array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['Payment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Payment']['no'], 'param5' => $invoicePayment['Payment']['id'], 'param6' => $invoicePayment['Invoice']['summary_total'], 'param7' => $invoicePayment['Payment']['status'], 'param8' => $invoicePayment['Payment']['payment_method'], 'param9' => $invoicePayment['Payment']['transaction_id']));

define('ACTION_CLIENT_PAY', 11); //tested
//$this->add_actionline(ACTION_CLIENT_PAY, $x=array('primary_id' => $invoicePayment['Invoice']['id'], 'secondary_id' => $invoicePayment['Invoice']['client_id'], 'param1' => $invoicePayment['InvoicePayment']['amount'], 'param2' => $invoicePayment['Invoice']['payment_status'], 'param3' => $invoicePayment['Invoice']['summary_paid'], 'param4' => $invoicePayment['Invoice']['no'], 'param5' => $invoicePayment['InvoicePayment']['id'], 'param6' =>$invoicePayment['Invoice']['summary_total'] , 'param7' => $invoicePayment['InvoicePayment']['status'], 'param8' => $invoicePayment['InvoicePayment']['payment_method'], 'param9' => $invoicePayment['InvoicePayment']['transaction_id']));


define('ACTION_ADD_ESTIMATE', 12); //tested
//$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => $re_read_invoice['Invoice']['payment_status'],'param4' => $re_read_invoice['Invoice']['no'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number']);
define('ACTION_UPDATE_ESTIMATE', 13); //tested
//$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'],'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => $re_read_invoice['Invoice']['payment_status'], 'param4' => $re_read_invoice['Invoice']['no'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number']);
define ('ACTION_DELETE_ESTIMATE' , 1179);// test;
//$this->add_actionline(ACTION_DELETE_ESTIMATE, array('primary_id' => $invoice['Invoice']['id'], 'secondary_id' => $invoice['Invoice']['client_id'], 'param1' => $invoice['Invoice']['summary_total'], 'param2' => $invoice['Invoice']['payment_status'],'param3' => $invoice['Invoice']['summary_paid'],'param4' => $invoice['Invoice']['no'],'param5' => $invoice['Client']['business_name'],'param6' => $invoice['Client']['client_number']));
define('ACTION_SEND_ESTIMATE', 14); //tested
//$arry = array('param2' => $to, 'param1' => $result, 'param4' => $estimate['no'], 'primary_id' => $estimate['id'], 'secondary_id' => $estimate['client_id']);
define('ACTION_PRINT_ESTIMATE', 15); //tested
//$this->add_actionline(ACTION_PRINT_ESTIMATE, array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param4' => $re_read_invoice['Invoice']['no']));
define('ACTION_CLIENT_VIEW_ESTIMATE', 16); //tested
//$this->add_actionline(ACTION_CLIENT_VIEW_ESTIMATE, array('secondary_id' => $invoice['Invoice']['client_id'], 'param4' => $invoice['Invoice']['no'], 'primary_id' => $invoice['Invoice']['id']));
define('ACTION_CLIENT_PRINT_ESTIMATE', 17); //tested
//$this->add_actionline(ACTION_CLIENT_PRINT_ESTIMATE, array('primary_id' => $id, 'secondary_id' => $invoice['Invoice']['client_id'] , 'param4' => $invoice['Invoice']['no']));
define('ACTION_CLIENT_READ_ESTIMATE_EMAIL', 18);
define('ACTION_CLIENT_ACCEPT_ESTIMATE', 19); //tested
//$this->add_actionline(ACTION_CLIENT_ACCEPT_ESTIMATE, array('primary_id' => $estimate['Invoice']['id'], 'secondary_id' => $estimate['Invoice']['client_id'], 'param1' => $estimate['Invoice']['summary_total'], 'param2' => $estimate['Invoice']['payment_status'], 'param4' => $estimate['Invoice']['no']));
define('ACTION_CONVERT_ESTIMATE_TO_INVOICE', 1901); //tested
//$this->add_actionline(ACTION_CONVERT_ESTIMATE_TO_INVOICE,array('primary_id' => $id, 'secondary_id' => $invoice['Invoice']['client_id'], 'param4' =>  $estimate['Invoice']['no'],  'param5'=> $invoice['Client']['business_name'], 'param6' =>  $invoice['Invoice']['no'], 'param9' =>  $invoice['Invoice']['id']));
define('ACTION_ADD_BOOKING', 2058); 
//$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'], 'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => $re_read_invoice['Invoice']['payment_status'],'param4' => $re_read_invoice['Invoice']['no'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number']);
define('ACTION_UPDATE_BOOKING', 2059); 
//$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'],'param1' => $re_read_invoice['Invoice']['summary_total'], 'param2' => $re_read_invoice['Invoice']['payment_status'], 'param4' => $re_read_invoice['Invoice']['no'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number']);
define('ACTION_UPDATE_INVOICE_PAYMENT', 29); //tested
define('ACTION_PROCCESS_INVOICE_PAYMENT', 291); //tested



define('ACTION_ADD_PRODUCT', 30);
//$this->add_actionline(ACTION_ADD_PRODUCT, array('primary_id' => $this->Product->id,'param4'=>$this->data['Product']['name'],'param2'=>$this->data['Product']['unit_price'],'param3'=>$this->data['Product']['default_quantity']));
define('ACTION_EDIT_PRODUCT', 31);
//$this->add_actionline(ACTION_EDIT_PRODUCT, array('primary_id' => $id,'param4'=>$this->data['Product']['name'],'param2'=>$this->data['Product']['unit_price'],'param3'=>$this->data['Product']['default_quantity']));                
define('ACTION_DELETE_PRODUCT', 32);
//$this->add_actionline(ACTION_DELETE_PRODUCT, array('primary_id' => $product['Product']['id'],'param4'=>$product['Product']['name'],'param2'=>$product['Product']['unit_price'],'param3'=>$product['Product']['default_quantity']));



define('ACTION_ADD_EXPENSE', 20);
//$this->add_actionline($action, array('primary_id' => $this->Expense->id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor']));
define('ACTION_UPDATE_EXPENSE', 21);
//$this->add_actionline($action, array('primary_id' => $id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor']));
define('ACTION_DELETE_EXPENSE', 22);
//$this->add_actionline($action, array('primary_id' => $expense['Expense']['id'], 'param1' => $expense['Expense']['amount'], 'param2' => $expense['Expense']['category'], 'param3' => $expense['Expense']['vendor']));
define('ACTION_ADD_RECURRING_EXPENSE', 201);
//$this->add_actionline($Find[0]['Expense']['is_income']?ACTION_ADD_RECURRING_INCOME:ACTION_ADD_RECURRING_EXPENSE, array('primary_id' => $this->Expense->id, 'param1' => $Find[0]['Expense']['amount'], 'param2' => $Find[0]['Expense']['category'], 'param3' => $Find[0]['Expense']['vendor'], 'param6' => $Find[0]['Expense']['date']));
define('ACTION_CLONE_EXPENSE', 202);

define('ACTION_ADD_MILEAGE', 8700);
define('ACTION_UPDATE_MILEAGE', 8701);
define('ACTION_DELETE_MILEAGE', 8702);
define('ACTION_CLONE_MILEAGE', 8703);


define('ACTION_ADD_INCOME', 23);
//$this->add_actionline($action, array('primary_id' => $this->Expense->id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor']));
define('ACTION_UPDATE_INCOME', 24);
//$this->add_actionline($action, array('primary_id' => $id, 'param1' => $this->data['Expense']['amount'], 'param2' => $this->data['Expense']['category'], 'param3' => $this->data['Expense']['vendor']));
define('ACTION_DELETE_INCOME', 25);
//$this->add_actionline($action, array('primary_id' => $expense['Expense']['id'], 'param1' => $expense['Expense']['amount'], 'param2' => $expense['Expense']['category'], 'param3' => $expense['Expense']['vendor']));
define('ACTION_ADD_RECURRING_INCOME', 231);
//$this->add_actionline($Find[0]['Expense']['is_income']?ACTION_ADD_RECURRING_INCOME:ACTION_ADD_RECURRING_EXPENSE, array('primary_id' => $this->Expense->id, 'param1' => $Find[0]['Expense']['amount'], 'param2' => $Find[0]['Expense']['category'], 'param3' => $Find[0]['Expense']['vendor'], 'param6' => $Find[0]['Expense']['date']));
define('ACTION_CLONE_INCOME', 203);


define('ACTION_ADD_TIME', 26);
//$this->add_actionline(ACTION_ADD_TIME, array('primary_id' => $this->TimeTracking->id, 'param1' => $this->data['TimeTracking']['time'], 'param2' => $this->data['TimeTracking']['project_id'], 'param3' => $this->data['TimeTracking']['activity_id'],'param4' => $projects[$this->data['TimeTracking']['project_id']], 'param5' => $activities[$this->data['TimeTracking']['activity_id']], 'param6' => $this->data['TimeTracking']['date']));                
define('ACTION_EDIT_TIME', 27);
//$this->add_actionline(ACTION_EDIT_TIME, array('primary_id' => $id, 'param1' => $this->data['TimeTracking']['time'], 'param2' => $this->data['TimeTracking']['project_id'], 'param3' => $this->data['TimeTracking']['activity_id'],  'param4' => $projects[$this->data['TimeTracking']['project_id']], 'param5' => $activities[$this->data['TimeTracking']['activity_id'], 'param6' => $this->data['TimeTracking']['date']]));                                
define('ACTION_DELETE_TIME', 28);
//$this->add_actionline(ACTION_DELETE_TIME, array('primary_id' => $TimeTracking['TimeTracking']['id'], 'param1' => $TimeTracking['TimeTracking']['time'], 'param2' => $TimeTracking['TimeTracking']['project_id'], 'param3' => $TimeTracking['TimeTracking']['activity_id']));                                                            



define('ACTION_ADD_PROJECT', 34);
//$this->add_actionline(ACTION_ADD_PROJECT, array('primary_id' => $this->Project->id,'param4'=>$this->data['Project']['name'],'param2'=>$this->data['Project']['status']));
define('ACTION_EDIT_PROJECT', 35);
//$this->add_actionline(ACTION_EDIT_PROJECT, array('primary_id' => $id,'param4'=>$this->data['Project']['name'],'param2'=>$this->data['Project']['status']));                                
define('ACTION_DELETE_PROJECT', 36);
//$this->add_actionline(ACTION_DELETE_PROJECT, array('primary_id' => $project['Project']['id'],'param4'=>$project['Project']['name'],'param2'=>$project['Project']['status']));                                 

define('ACTION_ADD_ACTIVITY', 40);
//$this->add_actionline(ACTION_ADD_ACTIVITY, array('primary_id' => $this->TimeActivity->id,'param4'=>$this->data['TimeActivity']['name'],'param2'=>$this->data['TimeActivity']['active']));                
define('ACTION_EDIT_ACTIVITY', 41);
//$this->add_actionline(ACTION_EDIT_ACTIVITY, array('primary_id' => $this->data['TimeActivity']['id'],'param4'=>$this->data['TimeActivity']['name'],'param2'=>$this->data['TimeActivity']['active']));                
define('ACTION_DELETE_ACTIVITY', 42);
//$this->add_actionline(ACTION_DELETE_ACTIVITY, array('primary_id' => $act['TimeActivity']['id'],'param4'=>$act['TimeActivity']['name']));                

define('ACTION_ADD_STAFF', 37);
//$this->add_actionline(ACTION_ADD_STAFF,array('primary_id'=>$this->Staff->id,'param5'=>$this->data['Staff']['name'],'param2'=>$this->data['Staff']['email_address'],'param3'=>$this->data['Staff']['role_id'],'param4'=>$role['Role']['name']));
define('ACTION_EDIT_STAFF', 38);
//$this->add_actionline(ACTION_EDIT_STAFF,array('primary_id'=>$id,'param5'=>$this->data['Staff']['name'],'param2'=>$this->data['Staff']['email_address'],'param3'=>$this->data['Staff']['role_id'],'param4'=>$role['Role']['name']));
define('ACTION_DELETE_STAFF', 39);
//$this->add_actionline(ACTION_DELETE_STAFF,array('primary_id'=>$id,'param5'=>$staff['Staff']['id'],'param5'=>$staff['Staff']['name'],'param2'=>$staff['Staff']['email_address']));




define('ACTION_SEND_EMAIL_TO_CLIENT', 1001);
define('ACTION_ASSIGN_STAFF_TO_CLIENT', 1002);
define('ACTION_UNASSIGN_STAFF_FROM_CLIENT', 1003);
//$this->add_actionline(ACTION_SEND_EMAIL_TO_CLIENT, array('primary_id'=>$client_id,'secondary_id'=>$client_id,'param2'=>$this->data['Client']['business_name'],'param3'=>$this->data['Client']['email'],'param4'=>$this->data['Client']['client_number'],'param5'=>$this->data['Client']['phone1'].' '.$this->data['Client']['phone2']));
define('ACTION_ADD_CLIENT', 43);
//$this->add_actionline(ACTION_ADD_CLIENT, array('primary_id'=>$client_id,'secondary_id'=>$client_id,'param2'=>$this->data['Client']['business_name'],'param3'=>$this->data['Client']['email'],'param4'=>$this->data['Client']['client_number'],'param5'=>$this->data['Client']['phone1'].' '.$this->data['Client']['phone2']));
define('ACTION_UPDATE_CLIENT', 44);
define('ACTION_SEND_LOGIN_DETAILS', 45);
//$this->add_actionline(ACTION_SEND_LOGIN_DETAILS, array('primary_id'=>$client['Client']['id'],'secondary_id'=>$client['Client']['id'],'param2'=>$client['Client']['business_name'],'param3'=>$client['Client']['email'],'param4'=>$client['Client']['client_number']));
define('ACTION_LOGIN_AS_CLIENT', 46);
//$this->add_actionline(ACTION_LOGIN_AS_CLIENT, array('primary_id'=>$client['Client']['id'],'secondary_id'=>$client['Client']['id'],'param2'=>$client['Client']['business_name'],'param3'=>$client['Client']['email'],'param4'=>$client['Client']['client_number']));
define('ACTION_LOGIN_AS_STAFF', 461);
//$this->add_actionline(ACTION_LOGIN_AS_STAFF, array('primary_id'=>$staff['Staff']['id'],'param2'=>$staff['Staff']['name']));

define('ACTION_DELETE_CLIENT', 47);
//$this->add_actionline(ACTION_DELETE_CLIENT, array('primary_id'=>$client['Client']['id'],'secondary_id'=>$client['Client']['id'],'param2'=>$client['Client']['business_name'],'param3'=>$client['Client']['email'],'param4'=>$client['Client']['client_number'],'param5'=>$client['Client']['phone1'].' '.$client['Client']['phone2']));
define('ACTION_CLIENT_LOGIN', 48);
//$this->add_actionline(ACTION_CLIENT_LOGIN, array('staff_id'=>-1,'primary_id'=>$client['id'],'secondary_id'=>['id'],'param2'=>$client['business_name'],'param3'=>$client['email'],'param4'=>$client['client_number']));


define('ACTION_CLIENT_UPDATE_PROFILE', 811);
//$this->add_actionline(ACTION_CLIENT_UPDATE_PROFILE, array('staff_id' => -1, 'primary_id' => $client['id'],  'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number'],'param5' => json_encode($this->data['Client'])));
define('ACTION_CLIENT_CHNAGE_PASSWORD', 821);
//$this->add_actionline(ACTION_CLIENT_CHNAGE_PASSWORD, array('staff_id' => -1, 'primary_id' => $client['id'],  'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number']));
define('ACTION_CLIENT_CHNAGE_EMAIL', 831);
//$this->add_actionline(ACTION_CHNAGE_EMAIL, array('staff_id' => -1, 'primary_id' => $client['id'],  'param2' => $client['business_name'], 'param3' => $client['email'], 'param4' => $client['client_number'],'param5' => $this->data['Client']['email'],));

define('ACTION_ADD_ROLE', 50);
//$this->add_actionline(ACTION_ADD_ROLE,array('primary_id'=>$this->Role->id,'param4'=>$this->data['Role']['name'],'param3'=>json_encode($this->data['RolesPermission'])));
define('ACTION_EDIT_ROLE', 51);
//$this->add_actionline(ACTION_EDIT_ROLE,array('primary_id'=>$this->Role->id,'param4'=>$this->data['Role']['name'],'param3'=>json_encode($this->data['RolesPermission'])));
define('ACTION_DELETE_ROLE', 52);
//$this->add_actionline(ACTION_DELETE_ROLE,array('primary_id'=>$role['Role']['id'],'param4'=>$role['Role']['name']));	



define('ACTION_IMPORT_DATA', 60);
//$this->add_actionline(ACTION_IMPORT_DATA, array('primary_id'=>0,'secondary_id'=>0,'param2'=>$sucess_record,'param3'=>$updated_record,'param4'=>$this->name,'param5'=>$fail_record,'param6'=>$warning_record));


define('ACTION_ADD_RECURRING_PROFILE', 70);
//$arry = array('primary_id' => $re_read_invoice['Invoice']['id'], 'secondary_id' => $re_read_invoice['Invoice']['client_id'],'param1' => $re_read_invoice['Invoice']['summary_total'],'param2' => $re_read_invoice['Invoice']['name'],'param3' => $re_read_invoice['Invoice']['subscription_unit'].' '.$re_read_invoice['Invoice']['subscription_period'],'param4' => $re_read_invoice['Invoice']['subscription_max_repeat'],'param5' => $re_read_invoice['Client']['business_name'],'param6' => $re_read_invoice['Client']['client_number'],'param7' => $re_read_invoice['Invoice']['date'],'param8' => $re_read_invoice['Invoice']['subscription_issue_before'],'param9' => $re_read_invoice['Invoice']['active']);
define('ACTION_UPDATE_RECURRING_PROFILE', 71);
define('ACTION_DELETE_RECURRING_PROFILE', 72);


define('ACTION_UPDATE_SMTP_SETTINGS', 80);
//$this->add_actionline(ACTION_UPDATE_SMTP_SETTINGS, array( 'param2' => $this->data['Site']['smtp_from_email'],'param3' => 0/1);
define('ACTION_UPDATE_SETTINGS', 81);
//$this->add_actionline(ACTION_UPDATE_SETTINGS,'param4'=>json_encode($this->data['Site']));
define('ACTION_CHNAGE_PASSWORD', 82);
//$this->add_actionline(ACTION_CHNAGE_PASSWORD);
define('ACTION_CHNAGE_EMAIL', 83);
//$this->add_actionline(ACTION_CHNAGE_EMAIL,array('param3'=> $site['email'],'param4'=>$data['Site']['email']));
define('ACTION_UPDATE_SYSTEM_LOGO_COLOR', 84);
//$this->add_actionline(ACTION_UPDATE_SYSTEM_LOGO_COLOR);

define('ACTION_ADD_EMAIL_TEMPLATE', 90);
//$this->add_actionline(ACTION_ADD_EMAIL_TEMPLATE,array('primary_id'=>$this->EmailTemplate->id,'param3'=> $this->data['EmailTemplate']['title'],'param4'=> $this->data['EmailTemplate']['type']));
define('ACTION_UPDATE_EMAIL_TEMPLATE', 91);
//$this->add_actionline(ACTION_UPDATE_EMAIL_TEMPLATE,array('primary_id'=>$id,'param3'=> $this->data['EmailTemplate']['title'],'param4'=> $this->data['EmailTemplate']['type']));
define('ACTION_DELETE_EMAIL_TEMPLATE', 92);
//$this->add_actionline(ACTION_DELETE_EMAIL_TEMPLATE,array('primary_id'=>$template['EmailTemplate']['id'],'param3'=> $template['EmailTemplate']['title'],'param4'=> $template['EmailTemplate']['type']));
define('ACTION_ADD_INVOICE_LAYOUT', 93);
//$this->add_actionline(ACTION_ADD_INVOICE_LAYOUT,array('primary_id'=>$this->InvoiceLayout->id,'param2'=> $this->data['InvoiceLayout']['template_id'],'param3'=> $this->data['InvoiceLayout']['name'],'param4'=> $this->data['InvoiceLayout']['default'],'param5'=> $this->data['InvoiceLayout']['default_estimate'],'param6'=> $this->data['InvoiceLayout']['default_timesheet']));
define('ACTION_UPDATE_INVOICE_LAYOUT', 94);
//$this->add_actionline(ACTION_UPDATE_INVOICE_LAYOUT,array('primary_id'=>$id,'param2'=> $this->data['InvoiceLayout']['template_id'],'param3'=> $this->data['InvoiceLayout']['name'],'param4'=> $this->data['InvoiceLayout']['default'],'param5'=> $this->data['InvoiceLayout']['default_estimate'],'param6'=> $this->data['InvoiceLayout']['default_timesheet']));
define('ACTION_DELETE_INVOICE_LAYOUT', 95);
//$this->add_actionline(ACTION_DELETE_INVOICE_LAYOUT,array('primary_id'=>$id,'param2'=> $this->data['InvoiceLayout']['template_id'],'param3'=> $this->data['InvoiceLayout']['name']));


define('ACTION_LOGIN', 999);
//$this->add_actionline(ACTION_LOGIN);


define('ACTION_STAFF_UPDATE_PROFILE', 101);

define('ACTION_CREATED_THE_ACCOUNT', 1000);


define('ACTION_ADD_PO', 96);

define('ACTION_UPDATE_PO', 97);

define('ACTION_DELETE_PO', 98);

define('ACTION_SEND_PO', 99);

define('ACTION_READ_PO_EMAIL', 100);

define('ACTION_SUPPLIER_VIEW_PO', 102);

define('ACTION_SUPPLIER_PRINT_PO', 103);

//Purchase quotations
define('ACTION_ADD_PQ', 104);
define('ACTION_UPDATE_PQ', 105);
define('ACTION_DELETE_PQ', 106);





define('ACTION_TRANSACTION_PO_ADDED', 204);
//(primary_id => po_id ,  sec_id => product_id,   p1=> quantity,  p2=>transaction_id, p3=>invoice_no, p4=> product_code, p5=>product_name  , p8=> after_stock_balance )

define('ACTION_TRANSACTION_PO_UPDATED', 205);
define('ACTION_TRANSACTION_PO_DELETED', 206);
define('ACTION_TRANSACTION_PO_IGNORED', 207);

define('ACTION_TRANSACTION_INVOICE_ADDED', 214);
define('ACTION_TRANSACTION_INVOICE_UPDATED', 215);
define('ACTION_TRANSACTION_INVOICE_DELETED', 216);
define('ACTION_TRANSACTION_INVOICE_IGNORED', 217);


define('ACTION_TRANSACTION_MANUAL_ADDED', 224);
define('ACTION_TRANSACTION_MANUAL_UPDATED', 225);
define('ACTION_TRANSACTION_MANUAL_DELETED', 226);
define('ACTION_TRANSACTION_MANUAL_IGNORED', 227);

define('STAFF_DOWNLOAD_FILE', 1100);
define('CLIENT_DOWNLOAD_FILE', 1101);

define('ACTION_ADD_NEW_POST', 1102);
define('ACTION_UPDATE_THE_POST', 1103);
define('ACTION_REMOVE_THE_POST', 1104);


define('ACTION_EDIT_CLIENT_APPOINTMENT', 1105);
define('ACTION_UPDATE_CLIENT_STATUS', 1106);
define('ACTION_ADD_CLIENT_APPOINTMENT', 1107);
define('ACTION_REMOVE_POST_FILE', 1108);
define('ACTION_CLIENT_VIEW_POST', 1109);
define('ACTION_DELETE_CLIENT_APPOINTMENT', 1110);
define('ACTION_UPDATE_CLIENT_CATEGORY', 1111);
define('ACTION_ADD_GROUPPRICE', 1112);
define('ACTION_EDIT_GROUPPRICE', 1113);
define('ACTION_DELETE_GROUPPRICE', 1114);
define('ACTION_PRINT_RECEIPT', 1116);
define('ACTION_DOWNLOAD_RECEIPT', 1117);


define ('ACTION_ADD_SUPPLIER' , 1118 );
define ('ACTION_UPDATE_SUPPLIER' , 1119 );
define ('ACTION_DELETE_SUPPLIER' , 1120 );


define('ACTION_ADD_PCN_PAYMENT', 8201);
define('ACTION_DELETE_PCN_PAYMENT', 8202);
define('ACTION_UPDATE_PCN_PAYMENT', 8203);

define('ACTION_ADD_PO_PAYMENT', 1121);
define('ACTION_DELETE_PO_PAYMENT', 1122);
define('ACTION_UPDATE_PO_PAYMENT', 1123);
define('ACTION_PROCCESS_PO_PAYMENT', 1124);

define('ACTION_STORE_TRANSFER', 1125);

define('ACTION_ADD_CREDITNOTE', 1126);
define('ACTION_UPDATE_CREDITNOTE', 1127);
define('ACTION_SEND_CREDITNOTE', 1128);
define('ACTION_TRANSACTION_CREDITNOTE_UPDATED', 1129);
define('ACTION_TRANSACTION_CREDITNOTE_DELETED', 1130);
define('ACTION_TRANSACTION_CREDITNOTE_ADDED', 1131);


define('ACTION_DELETE_CREDITNOTE', 1132);
define('ACTION_ADD_REFUND', 1133);
define('ACTION_UPDATE_REFUND', 1134);
define('ACTION_DELETE_REFUND', 1135);
define('ACTION_SEND_REFUND', 1136);
define('ACTION_TRANSACTION_REFUND_UPDATED', 1137);
define('ACTION_TRANSACTION_REFUND_DELETED', 1138);
define('ACTION_TRANSACTION_REFUND_ADDED', 1139);

define('ACTION_ADD_CLIENT_PAYMENT', 1140); //tested


define('ACTION_ADD_NEW_POST_INVOICE', 1141);
define('ACTION_UPDATE_THE_POST_INVOICE', 1142);
define('ACTION_REMOVE_THE_POST_INVOICE', 1143);

define('ACTION_ADD_NEW_POST_PURCHASE_INVOICE', 6001);
define('ACTION_UPDATE_THE_POST_PURCHASE_INVOICE', 6002);
define('ACTION_REMOVE_THE_POST_PURCHASE_INVOICE', 6003);

define('ACTION_UPDATE_INVOICE_STATUS', 1144);
define('ACTION_UPDATE_ESTIMATE_STATUS', 1145);

define('ACTION_CLIENT_ADD_CLIENT_PAYMENT', 1146); //tested

define('ACTION_EDIT_ESTIMATE_APPOINTMENT', 1147);

define('ACTION_DELETE_INVOICE_APPOINTMENT', 1148);
define('ACTION_DELETE_ESTIMATE_APPOINTMENT', 1149);

define('ACTION_ADD_INVOICE_APPOINTMENT', 1150);
define('ACTION_ADD_ESTIMATE_APPOINTMENT', 1151);
define('ACTION_EDIT_INVOICE_APPOINTMENT', 1152);


define('ACTION_DELETE_CLIENT_PAYMENT', 1153);


define('ACTION_ADD_WORK_ORDER', 1154);
define('ACTION_EDIT_WORK_ORDER', 1155);
define('ACTION_DELETE_WORK_ORDER', 1156);

define('ACTION_UPDATE_WORK_ORDER_STATUS', 1157);

define('ACTION_OWNER_CHNAGE_CLIENT_PASSWORD', 1158);
define('ACTION_OWNER_CHNAGE_STAFF_PASSWORD', 1159);
define('STAFF_DOWNLOAD_INVOICE_DOCUMENT', 1165);
define('CLIENT_DOWNLOAD_INVOICE_DOCUMENT', 1166);

define('ACTION_PRINT_CREDITNOTE', 1167);
define('ACTION_PRINT_REFUND', 1168);

define('ACTION_ADD_EXPENSECATEGORY', 1169);
define('ACTION_UPDATE_EXPENSECATEGORY', 1170);
define('ACTION_DELETE_EXPENSECATEGORY', 1171);

define('ACTION_SEND_EMAIL', 1172);

define('ACTION_JOURNAL_ACCOUNT_EDIT', 1173);
define('ACTION_JOURNAL_ACCOUNT_ADD', 1174);
define('ACTION_JOURNAL_ACCOUNT_DELETE', 1175);

define ( 'ACTION_UPDATED_AVG_PRICE' , 1176);
define ( 'ACTION_UPDATED_TRANSACTION_DATE' , 1177);
define ( 'ACTION_UPDATED_PLUGIN' , 1178);
define('ACTION_SEND_SMS_TO_CLIENT', 2001);
define('ACTION_SEND_SMS_TO_APPOINTMENT', 2002);
define('ACTION_SEND_SMS_TO_APPOINTMENT_STAFF', 2003);
define('ACTION_SEND_SMS_TO_INVOICE', 2039);
define('ACTION_SEND_EMAIL_TO_APPOINTMENT', 2060);
define('ACTION_SEND_EMAIL_TO_APPOINTMENT_STAFF', 2061);
define('ACTION_SEND_SMS_TO_WORK_ORDER', 2097);
define('ACTION_SEND_SMS_TO_WORK_ORDER_STAFF', 2098);
define('ACTION_SEND_EMAIL_TO_WORK_ORDER', 2099);
define('ACTION_SEND_EMAIL_TO_WORK_ORDER_STAFF', 2100);

define ( 'ACTION_ASSET_DELETE'  , 2004 ) ;
define ( 'ACTION_ASSET_MODIFY'  , 2005 ) ;
define ( 'ACTION_ASSET_ADD'  , 2006 ) ;
define ( 'ACTION_ASSET_WRITE_OFF'  , 2007 ) ;
define ( 'ACTION_ASSET_SOLD'  , 2008 ) ;
define ( 'ACTION_ASSET_RE_EVALUATE'  , 2009 ) ;

define ( 'ACTION_ASSET_DEPRECATION_DELETE'  , 2010 ) ;
define ( 'ACTION_ASSET_DEPRECATION_MODIFY'  , 2011 ) ;
define ( 'ACTION_ASSET_DEPRECATION_ADD'  , 2012 ) ;

define ( 'ACTION_ASSET_EDIT_RE_EVALUATE'  , 2013 ) ;
define ( 'ACTION_ASSET_EDIT_SOLD'  , 2014 ) ;

define ( 'ACTION_ASSET_DELETE_RE_EVALUATE'  , 2015 ) ;
define ( 'ACTION_ASSET_DELETE_SOLD'  , 2016 ) ;
define ( 'ACTION_ASSET_DELETE_WRITE_OFF'  , 2017 ) ;
// Opening Balance aka Starting Balance
define ( 'ACTION_EDIT_OPENING_BALANCE'  , 2018 ) ;
// Opening Balance aka Starting Balance
define ( 'ACTION_EDIT_CLIENT_CREDIT'  , 2019 ) ;

define ( 'ACTION_ADD_PR'  , 2020 ) ;
define ( 'ACTION_EDIT_PR'  , 2021 ) ;
define ( 'ACTION_DELETE_PR'  , 2022 ) ;

define ('ACTION_TRANSACTION_PR_ADDED', 2023 ) ;
define('ACTION_TRANSACTION_PR_UPDATED', 2024);
define('ACTION_TRANSACTION_PR_DELETED', 2025);
define('ACTION_TRANSACTION_PR_IGNORED', 2026);
// check pipline master third

//closed_periods to 2029

define ('ACTION_CLOSED_PERIOD_ADD', 2027 ) ;
define('ACTION_CLOSED_PERIOD_EDIT', 2028);
define('ACTION_CLOSED_PERIOD_DELETE', 2029);


//financial_years to 2041

define ('ACTION_FINANCIAL_YEAR_ADD', 2050 ) ;
define('ACTION_FINANCIAL_YEAR_EDIT', 2051);
define('ACTION_FINANCIAL_YEAR_DELETE', 2052);

//cost centers
define('ACTION_COST_CENTER_ADD', 2030);
define('ACTION_COST_CENTER_EDIT', 2031);
define('ACTION_COST_CENTER_DELETE', 2032);
//end cost centers

//journal_account_cost_centers
define('ACTION_JOURNAL_ACCOUNT_COST_CENTER_ADD', 2033);
define('ACTION_JOURNAL_ACCOUNT_COST_CENTER_EDIT', 2034);
define('ACTION_JOURNAL_ACCOUNT_COST_CENTER_DELETE', 2035);
//end journal_accounts_cost_centers

//cost_center_transactions
define('ACTION_COST_CENTER_TRANSACTION_ADD', 2036);
define('ACTION_COST_CENTER_TRANSACTION_EDIT', 2037);
define('ACTION_COST_CENTER_TRANSACTION_DELETE', 2038);
//end_cost_center_transaction

//pos_actions
define('ACTION_POS_SESSION_START', 2053);
define('ACTION_POS_SESSION_OPEN', 2054);
define('ACTION_POS_SESSION_CLOSE', 2055);
define('ACTION_POS_SESSION_VALIDATE', 2056);
define('ACTION_POS_SESSION_TRANSACTION_ADD', 2057);
define('ACTION_POS_SESSION_UN_VALIDATE', 2062);
define('ACTION_POS_SESSION_REOPEN', 2063);
//end_pos_actions


//Requisitions
define('ACTION_REQUISITION_ADD',2064);
define('ACTION_REQUISITION_CHANGE_STORE',2900);
define('ACTION_REQUISITION_UPDATE',2065);
define('ACTION_REQUISITION_DELETE',2066);

//Stocktakings
define('ACTION_STOCKTAKING_ADD',2067);
define('ACTION_STOCKTAKING_UPDATE',2068);
define('ACTION_STOCKTAKING_DELETE',2069);

//Beta
define('ACTION_MOVE_TO_BETA',2070);
define('ACTION_MOVE_TO_LIVE',2071);

// Opening Balance aka Starting Balance
define ( 'ACTION_EDIT_SUPPLIER_OPENING_BALANCE'  , 2072 ) ;
define ( 'ACTION_DELETE_SUPPLIER_OPENING_BALANCE'  , 2073 ) ;
define ( 'ACTION_DELETE_CLIENT_OPENING_BALANCE'  , 2074 ) ;
define ( 'ACTION_ADD_CLIENT_OPENING_BALANCE'  , 2075 ) ;
define ( 'ACTION_ADD_SUPPLIER_OPENING_BALANCE'  , 2076 ) ;

define ( 'ACTION_UPDATE_INVENTORY_SETTINGS'  , 2078 ) ;

define('ACTION_ADD_WAREHOUSE', 2079);
define('ACTION_EDIT_WAREHOUSE', 2080);
define('ACTION_DELETE_WAREHOUSE', 2081);

define('ACTION_DELETE_BOOKING', 2082);
define('ACTION_UPDATE_BOOKING_STATUS', 2083);
define('ACTION_ADD_BOOKING_CLIENT', 2084);
define('ACTION_CONVERT_BOOKING_TO_INVOICE', 2085);
define('ACTION_CLIENT_CANCEL_BOOKING', 2086);

define('ACTION_ADD_TREASURY', 2089);
define('ACTION_EDIT_TREASURY', 2090);
define('ACTION_DELETE_TREASURY', 2091);
define('ACTION_CHANGE_STATUS_TREASURY', 2092);

define('ACTION_ADD_BRANCH', 2093);
define('ACTION_EDIT_BRANCH', 2094);
define('ACTION_DELETE_BRANCH', 2095);
define('ACTION_CHANGE_STATUS_BRANCH', 2096);

//Next Action 2101
define('ACTION_ADD_WORK_ORDER_APPOINTMENT', 3001);
define('ACTION_EDIT_WORK_ORDER_APPOINTMENT', 3002);
define('ACTION_DELETE_WORK_ORDER_APPOINTMENT', 3003);

define('ACTION_ADD_NEW_WORK_ORDER_POST', 3004);
define('ACTION_UPDATE_WORK_ORDER_POST', 3005);
define('ACTION_REMOVE_WORK_ORDER_POST', 3006);

define('ACTION_UPDATE_BRANCH_SETTINGS', 4000);


define('ACTION_INVOICE_PAYMENT_FAIL', 4001);

define ( 'ACTION_UPDATE_POS_ACCOUNTING_SETTINGS'  , 3100 );
define ( 'ACTION_UPDATE_ROUTING_SETTINGS'  , 3102 );
define ( 'ACTION_UPDATE_POS_PARTIAL_PAYMENT'  , 3103 );

define('ACTION_PRINT_PURCHASE_ORDER', 3301);

define ( 'ACTION_UPDATE_AUTONUMBER_SETTINGS'  , 3300 ) ;
define ( 'ACTION_UPDATE_AUTONUMBER_SETTINGS_UNIQUE_STATUS'  , 3302 ) ;
define ( 'ACTION_UPDATE_AUTONUMBER_SETTINGS_PATTERN'  , 3303 ) ;
define ( 'ACTION_UPDATE_AUTONUMBER_SETTINGS_PREFIX'  , 3304 ) ;
define ( 'ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_DIGITS'  , 3305 ) ;
define ( 'ACTION_UPDATE_AUTONUMBER_SETTINGS_NUMBER_OF_CHARACTERS'  , 3306 ) ;
define ( 'ACTION_UPDATE_PURCHASEORDER_PAYMENT'  , 4002 ) ;

define ( 'ACTION_ADD_LOCAL_CURRENCY_RATE'  , 5001 ) ;
define ( 'ACTION_UPDATE_LOCAL_CURRENCY_RATE'  , 5002 ) ;
define ( 'ACTION_DELETE_LOCAL_CURRENCY_RATE'  , 5003 ) ;
define ( 'ACTION_PREVIEW_INVOICE'  , 8001 ) ;
define('ACTION_UPDATE_EMAIL', 7000);

define('ACTION_ADD_UNIT_TEMPLATE', 7005);
define('ACTION_UPDATE_UNIT_TEMPLATE', 7006);
define('ACTION_DELETE_UNIT_TEMPLATE', 7007);

define('ACTION_ADD_SUPPLIER_CREDIT', 5005);
define('ACTION_EDIT_SUPPLIER_CREDIT', 5006 );
define('ACTION_DELETE_SUPPLIER_PAYMENT', 5007 );
define('ACTION_DELETE_PURCHASE_PAYMENT', 5008 );

define('ACTION_ADD_SALES_ORDER_FROM_ESTIMATE', 8000);
define('ACTION_ADD_SALES_ORDER', 8008);
define('ACTION_UPDATE_SALES_ORDER', 8002);
define('ACTION_CONVERT_ESTIMATE_TO_SALES_ORDER', 8003);
define('ACTION_CLIENT_VIEW_SALES_ORDER', 8004);
define('ACTION_PRINT_SALES_ORDER', 8005);
define('ACTION_CLIENT_PRINT_SALES_ORDER', 8006);
define('ACTION_DELETE_SALES_ORDER', 8007);

define('ACTION_ADD_DEBITNOTE', 8110);
define('ACTION_UPDATE_DEBITNOTE', 8111);
define('ACTION_DELETE_DEBITNOTE', 8112);
define('ACTION_SEND_DEBITNOTE', 8113);
define('ACTION_PRINT_DEBITNOTE', 8114);

define('ACTION_PURCHASE_ADD_DEBIT_NOTE', 8120);
define('ACTION_PURCHASE_UPDATE_DEBIT_NOTE', 8121);
define('ACTION_PURCHASE_DELETE_DEBIT_NOTE', 8122);

define('ACTION_ADD_PURCHASE_CREDIT_NOTE', 8130);
define('ACTION_UPDATE_PURCHASE_CREDIT_NOTE', 8131);
define('ACTION_DELETE_PURCHASE_CREDIT_NOTE', 8132);
define('ACTION_SEND_PURCHASE_CREDIT_NOTE', 8133);
define('ACTION_PRINT_PURCHASE_CREDIT_NOTE', 8134);

define('ACTION_DOWNLOAD_PO_PAYMENT', 8135);
define('ACTION_PRINT_PO_PAYMENT', 8136);
define('ACTION_ASSIGN_WORK_ORDER_TRANSACTON', 8137);
define('ACTION_UNASSIGN_WORK_ORDER_TRANSACTON', 8138);


define('ACTION_CONVERT_INVOICE_TO_PRODUCTION_PLAN', 8141);
define('ACTION_CONVERT_SALES_ORDER_TO_PRODUCTION_PLAN', 8142);

define('ACTION_UPDATE_BRANCH', 8143);
define('ACTION_POS_SESSION_REMOVE', 8144);

define('ACTION_ADD_ADVANCE_PAYMENT', 8150);
define('ACTION_UPDATE_ADVANCE_PAYMENT', 8151);
define('ACTION_DELETE_ADVANCE_PAYMENT', 8152);


define('ACTION_UPDATE_ADVANCE_PAYMENT_DISTRIBUTION', 8153);
define('ACTION_DELETE_ADVANCE_PAYMENT_DISTRIBUTION', 8154);
define('ACTION_ATTACH_CREDIT_NOTE_TO_ADVANCE_PAYMENT_ON_CREATION', 8155);

define('ACTION_UPDATE_CLIENT_GROUPPRICE', 8145);

define('ACTION_CHANGE_FOLLOW_UP_STATUS', 8146);
?>
