<?php

class ReportsSchema extends CakeSchema {

	var $name = 'Reports';

	function before($event = array()) {
		return true;
	}

	function after($event = array()) {
		
	}

	var $saved_reports = array(
		'id' => array('type' => 'integer', 'null' => false, 'key' => 'primary'),
		'site_id' => array('type' => 'integer', 'null' => false),
		'title' => array('type' => 'string', 'null' => false),
		'type' => array('type' => 'integer', 'null' => false),
		'data' => array('type' => 'text', 'null' => true, 'default' => ''),
		'created' => array('type' => 'datetime', 'null' => true),
		'modified' => array('type' => 'datetime', 'null' => true),
		'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
	);

}
