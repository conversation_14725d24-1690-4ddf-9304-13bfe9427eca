<?php 
/* SVN FILE: $Id$ */
/* OnlineInvoices schema generated on: 2011-03-27 16:03:28 : 1301234848*/
class OnlineInvoicesSchema extends CakeSchema {
	var $name = 'OnlineInvoices';

	function before($event = array()) {
		return true;
	}

	function after($event = array()) {
	}

	var $blocks = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'block_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'name' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'display_order' => array('type' => 'integer', 'null' => true, 'default' => '0'),
			'close' => array('type' => 'boolean', 'null' => false, 'default' => NULL),
			'position' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'site_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $client_templates = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'subject' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'body' => array('type' => 'text', 'null' => false, 'default' => NULL),
			'invoice_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'client_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'site_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'attachments' => array('type' => 'text', 'null' => false, 'default' => NULL),
			'is_sent' => array('type' => 'boolean', 'null' => true, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $clients = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'client_number' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'unique'),
			'site_id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'index'),
			'business_name' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'first_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'last_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'email' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'password' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'city' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'state' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'postal_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 20),
			'phone1' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'phone2' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'country_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'language_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 10),
			'default_currency_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 5),
			'last_login' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'suspend' => array('type' => 'boolean', 'null' => true, 'default' => NULL),
			'last_ip' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'client_number' => array('column' => 'client_number', 'unique' => 1), 'site_id' => array('column' => 'site_id', 'unique' => 0))
		);
	var $contacts = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'email' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'subject' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'message' => array('type' => 'text', 'null' => false, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $countries = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'country' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'default_currency_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'default_language' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 10),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $country_infos = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'code2' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 2),
			'code3' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 3, 'key' => 'unique'),
			'currency' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 50),
			'language' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 50),
			'postal_code' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 50),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'code3' => array('column' => 'code3', 'unique' => 1))
		);
	var $currencies = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3, 'key' => 'unique'),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'symbol' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 150),
			'format' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'code' => array('column' => 'code', 'unique' => 1))
		);
	var $currencies2 = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'code' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 5),
			'name' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 50),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $email_templates = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'site_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'key' => 'index'),
			'title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'subject' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'body' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'attach_invoice' => array('type' => 'boolean', 'null' => true, 'default' => NULL),
			'attachment' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'site_id' => array('column' => 'site_id', 'unique' => 0))
		);
	var $invoice_custom_fields = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'invoice_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 20),
			'display_order' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'label' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'value' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'placeholder' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $invoice_items = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'invoice_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 20),
			'item' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'unit_price' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'quantity' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'tax' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 4),
			'subtotal' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'product_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 20),
			'display_order' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $invoice_payments = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'invoice_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 20, 'key' => 'index'),
			'payment_method' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'amount' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'transaction_id' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'date' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'status' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 4),
			'notes' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'response_code' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'response_message' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'added_by' => array('type' => 'integer', 'null' => true, 'default' => '0', 'length' => 2),
			'currency_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'first_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'last_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'city' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'state' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'postal_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 10),
			'country_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'phone1' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'phone2' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'ip' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'invoice_id' => array('column' => 'invoice_id', 'unique' => 0))
		);
	var $invoice_reminders = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'invoice_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 20),
			'send_when' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'days' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'email_template_id' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'is_sent' => array('type' => 'boolean', 'null' => true, 'default' => NULL),
			'sent_time' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'display_order' => array('type' => 'integer', 'null' => true, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $invoices = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'site_id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'index'),
			'subscription_id' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'type' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 2, 'key' => 'index'),
			'no' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'po_number' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'client_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 20, 'key' => 'index'),
			'currency_code' => array('type' => 'string', 'null' => false, 'default' => NULL, 'length' => 10),
			'client_business_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'client_first_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'client_last_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'client_address1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'client_address2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'client_postal_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 10),
			'client_city' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'client_state' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'client_country_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'date' => array('type' => 'date', 'null' => true, 'default' => NULL),
			'payment_status' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 4),
			'draft' => array('type' => 'boolean', 'null' => true, 'default' => NULL),
			'issued' => array('type' => 'boolean', 'null' => false, 'default' => NULL),
			'active' => array('type' => 'boolean', 'null' => true, 'default' => '0'),
			'tax1' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'tax2' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'discount' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'deposit' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'due_after' => array('type' => 'integer', 'null' => true, 'default' => '0'),
			'issue_before' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'date_format' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'length' => 2),
			'language_id' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'label_invoice_no' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_date' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_po_no' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_total' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_status' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_due_after' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_due_date' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_deposit' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_paid_amount' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_unpaid_amount' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_subtotal' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_description' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_item' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_tax1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_tax2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_quantity' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_unit_price' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_item_total' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_from_date' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'label_to_date' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'summary_tax1' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'summary_subtotal' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'summary_tax2' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'summary_discount' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'summary_total' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'summary_paid' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'summary_unpaid' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'notes' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'required_terms' => array('type' => 'boolean', 'null' => true, 'default' => NULL),
			'terms' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'subscription_unit' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'subscription_period' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'subscription_repeated' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'subscription_max_repeat' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'from' => array('type' => 'date', 'null' => true, 'default' => NULL),
			'to' => array('type' => 'date', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'site_id' => array('column' => 'site_id', 'unique' => 0), 'type' => array('column' => 'type', 'unique' => 0), 'client_id' => array('column' => 'client_id', 'unique' => 0))
		);
	var $languages = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'code2' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 2),
			'code3' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'parent_id' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'active' => array('type' => 'boolean', 'null' => true, 'default' => '0'),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $pages = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'site_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'key' => 'index'),
			'title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'permalink' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'keywords' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'body' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'show_in_menu' => array('type' => 'boolean', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'site_id' => array('column' => 'site_id', 'unique' => 0))
		);
	var $products = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'site_id' => array('type' => 'integer', 'null' => true, 'default' => NULL, 'key' => 'index'),
			'name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'unit_price' => array('type' => 'float', 'null' => true, 'default' => NULL),
			'default_quantity' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'default_tax' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1), 'site_id' => array('column' => 'site_id', 'unique' => 0))
		);
	var $seo = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'primary'),
			'criteria' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'title' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'keywords' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'description' => array('type' => 'text', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'updated' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $site_payment_gateways = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'site_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'payment_gateway' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'username' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'option1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'option2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'option3' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'default' => array('type' => 'boolean', 'null' => true, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $sites = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 20, 'key' => 'primary'),
			'business_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'first_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'last_name' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'subdomain' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'site_logo' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'invoice_logo' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address1' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'address2' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'city' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'state' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 100),
			'postal_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 20),
			'phone1' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'phone2' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'country_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'timezone' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'date_format' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 50),
			'currency_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 3),
			'language_code' => array('type' => 'string', 'null' => true, 'default' => NULL, 'length' => 10),
			'email' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'password' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'last_login' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'plan_id' => array('type' => 'integer', 'null' => true, 'default' => NULL),
			'expiry_date' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'last_ip' => array('type' => 'string', 'null' => true, 'default' => NULL),
			'created' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => true, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $snippets = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'name' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'content' => array('type' => 'text', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $terms = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'length' => 10, 'key' => 'primary'),
			'title' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'content' => array('type' => 'text', 'null' => false, 'default' => NULL),
			'site_id' => array('type' => 'integer', 'null' => false, 'default' => NULL),
			'active' => array('type' => 'boolean', 'null' => false, 'default' => NULL),
			'display_order' => array('type' => 'integer', 'null' => true, 'default' => '0'),
			'created' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'modified' => array('type' => 'datetime', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
	var $timezones = array(
			'id' => array('type' => 'integer', 'null' => false, 'default' => NULL, 'key' => 'primary'),
			'zone_name' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'offset' => array('type' => 'string', 'null' => false, 'default' => NULL),
			'indexes' => array('PRIMARY' => array('column' => 'id', 'unique' => 1))
		);
}
?>