<?php

class DATABASE_CONFIG {
    
    var $portalWrite = array(
        'driver' => 'mysqli',
        'persistent' => false,
        'host' => DB_HOST,
        'login' => DB_USER,
        'password' => DB_PASS,
        'database' => Portal_Site_DB,
        'encoding' => 'utf8'
    );



    var $portal = array(
        'driver' => 'mysqli',
        'persistent' => false,
        'host' => DB_HOST,
        'login' => DB_READ_USERNAME,
        'password' => DB_READ_PASSWORD,
        'database' => Portal_Site_DB,
        'encoding' => 'utf8'
    );
    var $default = array(
        'driver' => 'mysqli',
        'persistent' => false,
        'host' => DB_HOST,
        'login' => DB_USER,
        'password' => DB_PASS,
        'database' => Default_DB,
        'encoding' => 'utf8'
    );

    var $queue_database = [
        'driver' => 'mysqli',
        'persistent' => false,
        'host' => QUEUE_DB_HOST,
        'login' => QUEUE_DB_USERNAME,
        'password' => QUEUE_DB_PASSWORD,
        'database' => QUEUE_DB_DATABASE,
        'encoding' => 'utf8'
    ];


    private function query_subdomain($host)
    {
        $link = mysqli_init();
        mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
        mysqli_real_connect($link,$this->portal['host'], $this->portal['login'], $this->portal['password']);
        mysqli_select_db($link, $this->portal['database']) or die(mysqli_error($link));
        mysqli_query($link, 'SET NAMES utf8') or die(mysqli_error($link));
        $time=time();
        $query = 'SELECT id, db_config,status,beta_version FROM sites WHERE '.$time.'='.$time.' and  subdomain = "' . mysqli_real_escape_string($link, $host) . '"';
        $cleanDomain = mysqli_real_escape_string($link, str_replace('www.', '', $host));
        $query .= "\nUNION SELECT sites.id, sites.db_config, sites.status, sites.beta_version FROM custom_domains Join sites on (custom_domains.site_id = sites.id) WHERE custom_domains.domain in ('$cleanDomain', 'www.$cleanDomain')";
        $result = mysqli_query($link, $query) or die(mysqli_error($link));
        $site = mysqli_fetch_assoc($result);
        mysqli_close($link);
        return $site;
    }
    public function __construct() {

        TillNow::tillnow('BEFORE CONSTRUCT DATABASE');

        if (PHP_SAPI != 'cli') {
            // warning suppress
            $ip = false;
            if (file_exists('/tmp/myip.log')) {
                $ip=file_get_contents("/tmp/myip.log");
            }
            // end warning suppress
            if (empty($ip)) {
                $ip = CURRENT_SERVER_IP;
            }
            $user_ip = $_SERVER['REMOTE_ADDR'];

        }



        if (PHP_SAPI != 'cli') {
        $beta_domain=Beta_Domain;
            $old_host = $_SERVER['HTTP_HOST'];
            $main_domain= explode('.', $old_host,2);
            $host= str_replace($beta_domain, Domain_Short_Name, $old_host);
            $hostSlug = str_replace('.','_',$host);
//            $hostSlug = Inflector::slug($host).'_'.substr(md5($host),0,6);



            $config = false;
            if (strpos($_SERVER['REQUEST_URI'], '/owners/firstlogin') !== false || strpos($_SERVER['REQUEST_URI'], '/owners/first_settings') !== false || isset($_GET['refresh'])) {
                Cache::delete($hostSlug);
                Cache::delete("$hostSlug-currentSite");
            }else{
                $config = Cache::read($hostSlug);

            }

            if(!is_array($config) || empty($config['host'])){
                $config=false;	
            }else{

                $test_link = mysqli_init();
                mysqli_options($test_link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
                mysqli_real_connect($test_link,$config['host'], $config['login'], $config['password']);

                $db_selected = mysqli_select_db($test_link, $config['database']);
                if(!$db_selected && !isset($_GET['refresh'])){
                    mysqli_close($test_link);
                    debug("<b>Error: The database of this site(" . $config['database'] . ") maybe deleted or archived!</b>");
                    header("Location: ?refresh=true&clean=1");
                    die();
                }
                mysqli_close($test_link);
            }

            if (!$config) {

                $site_id = Cache::read("$hostSlug-currentSite");
                if (!empty($site_id)) {
                    $link = mysqli_init();
                    mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
                   mysqli_real_connect($link,$this->portal['host'], $this->portal['login'], $this->portal['password']);
                    mysqli_select_db($link,$this->portal['database']);
                    mysqli_query($link,'SET NAMES utf8');

                    $query = 'SELECT id, db_config,status,beta_version FROM sites WHERE id = "' . intval($site_id) . '"';

                    $result = mysqli_query($link, $query);

                    if($result!==FALSE) $site = mysqli_fetch_assoc($result);
                }
                if(empty($site)){
                    $link = mysqli_init();
                    mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
                    mysqli_real_connect($link,$this->portal['host'], $this->portal['login'], $this->portal['password']);
                    mysqli_select_db($link,$this->portal['database'])or die(mysqli_error($link));
                    mysqli_query($link,'SET NAMES utf8')or die(mysqli_error($link));

                    $query = 'SELECT id, db_config,status,beta_version FROM sites WHERE subdomain = "' . mysqli_real_escape_string($link, $host) . '"';
                    $cleanDomain = mysqli_real_escape_string($link, str_replace('www.', '', $host));
                    $query .= "\nUNION SELECT sites.id, sites.db_config, sites.status, sites.beta_version FROM custom_domains Join sites on (custom_domains.site_id = sites.id) WHERE 1 = 1 and custom_domains.domain in ('$cleanDomain', 'www.$cleanDomain')";
                    $result = mysqli_query($link, $query)or die(mysqli_error($link));

                    if (mysqli_num_rows($result) < 1) {
//                        header('HTTP / 1.1 301 Redirect Permanently');
                        session_destroy();
                        header("Location: https://" . Portal_Full_Name . "/register?domain=" . $host, true, 301);
                        die();
                    }
                    $site = mysqli_fetch_assoc($result);
                    if ($site['status'] == 5) {
                        header("HTTP/1.1 503 Service Temporarily Unavailable");
                        die('<h1>Site is under maintenance</h1>');
                    }
                    if ($site['status'] == 15) {
                        header("HTTP/1.1 503 Service Temporarily Unavailable");
                    }
                    if ($site['status'] == "9") {

					    $restore_url="https://".Portal_Full_Name."/crons/restore/".$site['id'];
					$ch = curl_init();
					curl_setopt ( $ch , CURLOPT_RETURNTRANSFER , 1 );
					curl_setopt($ch, CURLOPT_URL, $restore_url);
					curl_setopt($ch, CURLOPT_HEADER, 0);
					curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
					curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                            'Referer'.$_SERVER['HTTP_REFERER'],
                            'X-SENDER-URL: '.$_SERVER['REQUEST_URI'],
                            'X-Forwarded-For: '.getRealIP(),
                            'User-Agent: '.$_SERVER['HTTP_USER_AGENT'],
                        ));
					curl_exec($ch);
					curl_close($ch);
                    $site=$this->query_subdomain($host);
					}
                    Cache::write("$hostSlug-currentSite", $site['id']);
                }

                $config = json_decode($site['db_config'], true);
                $config['driver'] = 'mysqli';
                $config['encoding'] = 'utf8';

                Cache::write($hostSlug, $config);
			}


            $test_link = mysqli_init();
            mysqli_options($test_link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);

             mysqli_real_connect($test_link,$config['host'], $config['login'], $config['password']);

            if(!$test_link && !isset($_GET['refresh'])){
                header("Location: ?refresh=true&clean=2");
                die();
            }
            $db_selected = mysqli_select_db($test_link, $config['database']);

            if(!$db_selected && !isset($_GET['refresh'])){
                mysqli_close($test_link);
                header("Location: ?refresh=true&clean=3");
                die();
            }
            if(!$db_selected && isset($_GET['refresh'])){
                header("Location: /error500.php?refresh=yes&clean=4");
                die();
            }
            mysqli_close($test_link);
            $_SESSION['db_config'] = $config;
            if(isset($_SESSION['CurrentSite']['db_config'])){
                $_SESSION['CurrentSite']['db_config']=json_encode($config);
            }

            $this->default = $config;
			WebSiteRedirect($this->portal,$old_host);

            TillNow::tillnow('AFTER CONSTRUCT DATABASE');
        }
			
    }

}
