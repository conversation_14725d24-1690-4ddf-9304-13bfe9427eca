<?php
if(!class_exists('OICurrencies')) {
    class OICurrencies
    {
        static $list = array();
    }
}
//$lang = ClassRegistry::init('Language');
$site_lang_code = substr(CurrentSiteLang('code2'),0,2);

if (file_exists(dirname(__FILE__) . DS . "{$site_lang_code}_currencies.php")) {

    $currenciesz = include dirname(__FILE__) . DS . 'en_currencies.php';


    //clearstatcache(dirname(__FILE__).DS."{$site_lang_code}_currencies.php");
    $all_currencies = include dirname(__FILE__) . DS . "{$site_lang_code}_currencies.php";
    OICurrencies::$list=array_merge($currenciesz, $all_currencies);
    return array_merge($currenciesz, $all_currencies);
} else {
    return include dirname(__FILE__) . DS . 'en_currencies.php';
}