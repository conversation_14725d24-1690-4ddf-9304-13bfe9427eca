<?php
//notifications keys
//define('NOTI_NO_INVOICE_CREATED', 1);
//define('NOTI_NO_SMTP_SETTING', 2);
//define('NOTI_NO_LOGO_UPLOADED', 3);
//define('NOTI_NO_INVOICE_LAYOUT', 4);
//define('NOTI_NO_TAXES_ADDED', 5);
//define('NOTI_NO_ONLINE_PAYMENT_OPTIONS', 6);
//define('NOTI_CLIENT_PENDING_PAYMENT', 7);
//define('NOTI_CLIENT_ACCEPT_ESTIMATE', 8);
//define('NOTI_CLIENT_DECLINED_ESTIMATE', 9);
//define('RECURRING_INVOICE_GENERATED', 10);
//define('Product_Low_Stock', 11);
//define('NOTI_ASSIGN_CLIENT', 12);
//define('NOTI_UPDATE_NOTE', 13);
//define('NOTI_UPDATE_APPOINTMENT', 14);
//define('NOTI_UPDATE_WORK_ORDER', 15);
//
//
////notification status
//define('NOTI_STATUS_CREATED',0);
//define('NOTI_STATUS_VIEWED',1);
//define('NOTI_STATUS_DISMISSED',2);
//define('NOTI_STATUS_DISMISSED_WITHOUT_VIEW',3);
////notification triggers
//define('NOTI_TRIGGER_OWNER',1);
//define('NOTI_TRIGGER_STAFF',2);
//
////notification user_type
//define('NOTI_USER_TYPE_STAFF',0);
//define('NOTI_USER_TYPE_CLIENT',1);
//
////notifiaction actions
//define('NOTI_ACTION_ADD',1);
//define('NOTI_ACTION_UPDATE',2);
//define('NOTI_ACTION_DELETE',3);
//
//define('EXCULDE_TYPE_DONT_ADD',0);
//define('EXCULDE_TYPE_DONT_EMAIL',1);
?>