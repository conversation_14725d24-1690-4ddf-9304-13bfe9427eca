<?php


namespace App\Helpers;


class ModelHelper
{
    public static function recordArrayToObject($record, $modelName = null) {
        return (object) $record[$modelName];
    }

    public static function recordsArrayToObjects($records, $modelName) {
        $objects = [];
        foreach ($records as $k => $record) {
            $objects[] = self::recordArrayToObject($records[$k], $modelName);
        }
        return $objects;
    }
}