<?php

namespace App\Helpers;

class ManufacturingOrderHelper
{
    public static function formatFinishOrderRequisitionsData(array $order, $data = null) : array {
        /** @var \Requisition */
        $Requisition = GetObjectOrLoadModel('Requisition');
        
        $mainProductRequisitionData = [
            [
                "Requisition" => [
                    "staff_id" => getAuthOwner('staff_id'),
                    "type" => $Requisition::TYPE_INBOUND,
                    "order_type" => $Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
                    "order_id" => $order['ManufacturingOrder']['id'],
                    "date" => $data['delivery_date'],
                    "store_id" => $data['main_store_id'],
                    "journal_account_id" => $order['ManufacturingOrder']['journal_account_id'],
                ],
                "RequisitionItem" => [
                    [
                        "item" => $order['Product']['name'],
                        "product_id" => $order['Product']['id'],
                        "org_name" => $order['Product']['name'],
                        "unit_price" => self::calculateMainProductPrice($order),
                        "quantity" => $order['ManufacturingOrder']['quantity'],
                        "requested_quantity" => $order['ManufacturingOrder']['quantity'],
                        "lot" => $data['lot_no'] ?? null,
                        "expiry_date" => $data['expiry_date'] ?? null,
                        "serial" => $data['main_product_serials'] ?? null,
                        "unit_name" => $order['ManufacturingOrder']['unit_name'] ?? null,
                        "unit_small_name" => $order['ManufacturingOrder']['unit_small_name'] ?? null,
                        "unit_factor" => $order['ManufacturingOrder']['factor'] ?? null,
                        "unit_factor_id" => isset($order['ManufacturingOrder']['unit_factor_id']) ? $order['ManufacturingOrder']['unit_factor_id'] : null,    
                    ],
                ],
            ]
        ];

        $scrapProductsRequisitionData = [];

        if(!empty($order['ManufacturingOrderScrap'])){
            $scrapRowsMapById = [];
            foreach ($data['scraps'] as $scrapRow) {
                $scrapRowsMapById[$scrapRow['id']] = $scrapRow;
            }
            $requisitionItems = [];
            foreach ($order['ManufacturingOrderScrap'] as $scrapRow) {
                $requisitionItems[] = [
                    "item" => $scrapRow['Product']['name'],
                    "product_id" => $scrapRow['product_id'],
                    "org_name" => $scrapRow['Product']['name'],
                    "unit_price" => $scrapRow['price'],
                    "quantity" => $scrapRow['quantity'],
                    "lot" => !empty($scrapRowsMapById[$scrapRow['id']]) ? $scrapRowsMapById[$scrapRow['id']]['lot_no'] ?? null : null,
                    "expiry_date" => !empty($scrapRowsMapById[$scrapRow['id']]) ? $scrapRowsMapById[$scrapRow['id']]['expiry_date'] ?? null : null,
                    "serial" => !empty($scrapRowsMapById[$scrapRow['id']]) ? $scrapRowsMapById[$scrapRow['id']]['serials'] ?? null : null,
                    "unit_name" => $scrapRow['unit_name'] ?? null,
                    "unit_small_name" => $scrapRow['unit_small_name'] ?? null,
                    "unit_factor" => $scrapRow['factor'] ?? null,
                    "unit_factor_id" => isset($scrapRow['unit_factor_id']) ? $scrapRow['unit_factor_id'] : null,
                ];
            }
            $scrapProductsRequisitionData[] = [
                "Requisition" => [
                    "staff_id" => getAuthOwner('staff_id'),
                    "type" => $Requisition::TYPE_INBOUND,
                    "order_type" => $Requisition::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP,
                    "order_id" => $order['ManufacturingOrder']['id'],
                    "date" => $data['delivery_date'],
                    "store_id" => $data['scrap_store_id'],
                    "journal_account_id" => $order['ManufacturingOrder']['journal_account_id'],
                ],
                "RequisitionItem" => $requisitionItems,
            ];
        }    

        return array_merge($mainProductRequisitionData, $scrapProductsRequisitionData);
    }

    public static function deleteOrderRequisitions($RequisitionModel, array $requisitions) {
        foreach ($requisitions as $key => $requisition) {
            $RequisitionModel->delete_with_related($requisition['Requisition']['id']);
        }
        return true;
    }

    public static function getOrderRequisitions($RequisitionModel, array $order) : array {
        return $RequisitionModel->find(
			'all', 
			[
				'recursive' => 1, 
				'conditions' => [
                    'Requisition.order_id' => $order['ManufacturingOrder']['id'], 
                    'OR' => [
                        [
                            'Requisition.order_type' => $RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_PRODUCT,
                        ],
                        [
                            'Requisition.order_type' => $RequisitionModel::ORDER_TYPE_MANUFACTURE_ORDER_INBOUND_SCRAP,    
                        ]
                    ]
                ]
			]
		);
    }

    public static function calculateMainProductPrice(array $order) : float {
        // {(All Direct and InDirects Cost for the Manufacturing Order) - (Scrap Items Amount) } / Main Product Quantity
        return self::getOrderDirectAndIndirectCost($order) / $order['ManufacturingOrder']['quantity'];
    }

    public static function getOrderDirectAndIndirectCost(array $order) : float {
        return $order['ManufacturingOrder']['total_cost'] - $order['ManufacturingOrder']['materials_total_cost'] + $order['ManufacturingOrder']['materials_actual_total_cost'] + ($order['ManufacturingOrder']['total_indirect_costs'] ?? 0);
    }

    public static function depreciateOrderOperationsAssets(array $order, $date = null, $cost = null) {
        $operations = $order['ManufacturingOrderOperation'];        
        $WorkstationModel = GetObjectOrLoadModel('Workstation');
        $ManufacturingOrderOperationModel = GetObjectOrLoadModel('ManufacturingOrderOperation');
        $AssetModel = GetObjectOrLoadModel('Asset');
        foreach ($operations as $key => $operation) {
            
            $workstationCurrentValues = $WorkstationModel->findById($operation['workstation_id']);
            $workstationDataArchive = !empty($operation['workstation_data']) ? json_decode($operation['workstation_data'], true) : [];


            if(!$workstationCurrentValues || $workstationCurrentValues['Workstation']['depreciate'] != 1 || empty($workstationDataArchive['asset'])){
                continue;
            }

            $asset = null;
            $AssetModel->disableBranchFindDecorator(function($model) use($workstationDataArchive, &$asset) {
                $asset = $model->findById($workstationDataArchive['asset']['id']);
            });
            $addDepreciationResult = $AssetModel->addDepreciation([
                'AssetDeprecation' => [
                    'asset_id' => $workstationDataArchive['asset']['id'],
                    'cost'=> $cost ?? ($workstationDataArchive['asset_cost'] * $operation['operating_time']),
                    'date'=> $date,
                    'period' => 0,
                    'applyBranchSave' => false,
                    'branch_id' => $asset['Asset']['branch_id'],
                ]
            ], $asset, true);
            $workstationDataArchive['asset_depreciation_id'] = $addDepreciationResult['id'];
            if(! $addDepreciationResult['status']){
                throw new \Exception($addDepreciationResult['errors']);                        
            }

            $ManufacturingOrderOperationModel->update($operation['id'],['workstation_data' => json_encode($workstationDataArchive)]);
        }
    }

    public static function undoDepreciationOrderOperationsAssets(array $order) {
        $operations = $order['ManufacturingOrderOperation'];
        $ManufacturingOrderOperationModel = GetObjectOrLoadModel('ManufacturingOrderOperation');
        $AssetDeprecationModel = GetObjectOrLoadModel('AssetDeprecation');
        foreach ($operations as $operation) {
            if(!empty($operation['workstation_data'])){
                $workstationData = json_decode($operation['workstation_data'], true);
                if(!empty($workstationData['asset_depreciation_id'])){

                    // beforeDelete function in AssetDeprecation Model will reset asset values automatically & use add_action
                    $AssetDeprecationModel->delete($workstationData['asset_depreciation_id']);

                    unset($workstationData['asset_depreciation_id']);
                    $ManufacturingOrderOperationModel->update($operation['id'], ["workstation_data" => json_encode($workstationData)]);
                }
            }
        }
    }
}