<?php


namespace App\Helpers;


class DateHelper
{
    static $cache = [];

    static function formatDate($date, $default_format = false, $isDateTime = false) {
        if(isset(self::$cache[$date]) && isset(self::$cache[$date][$default_format])) {
            return self::$cache[$date][$default_format];
        }
        $result = format_date($date, $default_format, $isDateTime);
        self::$cache[$date][$default_format] = $result;
        return $result;
    }
}