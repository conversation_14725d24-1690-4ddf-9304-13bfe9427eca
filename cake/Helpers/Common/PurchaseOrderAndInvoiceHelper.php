<?php


namespace App\Helpers\Common;


use App\Utils\DiscountUtil;

class PurchaseOrderAndInvoiceHelper
{

    public static function calculate_item_discount($item)
    {

        if ($item['discount_type'] == DiscountUtil::DISCOUNT_TYPE_PERCENTAGE) {
            return  (float)$item['quantity'] * (float)$item['unit_price'] * (float)$item['discount'] / 100;
        }
        if(!isset($item['unit_factor']) || empty($item['unit_factor'])) {
            $item['unit_factor'] = 1;
        }
        return $item['unit_factor'] != 0 ? ((float)$item['discount'] * (float)$item['quantity']) / $item['unit_factor'] : 0;

    }

    public static function getCurrencyFraction($currency_code)
    {
        $currency_fractions=4;
        include dirname(dirname(dirname(__FILE__))) . '/config/en_currencies.php';
        if(isset($number_formats[$currency_code][0]))
            $currency_fractions=$number_formats[$currency_code][0];
        $cent= 1/( pow(10 , $currency_fractions ));
        return [5, $cent];
    }

    public static function invoiceHasMoreThanOneStore($invoiceStore, $items = []) {
        foreach ($items as $item) {
            if(isset($item['store_id']) && $item['store_id'] != $invoiceStore) {
                return true;
            }
        }
        return false;
    }

    public static function getStoreIdQuantitiesInTransactions($defaultStoreId, $transactionItems) {
        $sum = [];
        foreach ($transactionItems as $transactionItem) {
            $storeId = !empty($transactionItem['store_id']) ? $transactionItem['store_id'] : $defaultStoreId;
            $productId = $transactionItem['product_id'];
            if(!isset($sum[$productId][$storeId])) {
                $sum[$productId][$storeId] = 0;
            }
            $sum[$productId][$storeId] += (float)$transactionItem['quantity'];
        }
        return $sum;
    }
}
