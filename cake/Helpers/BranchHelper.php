<?php


namespace App\Helpers;


class BranchHelper
{
    public static function validateRecordInCurrentBranch($branchId, $redirectUrl = "") {
        if(
            $branchId &&
            ifPluginActive(BranchesPlugin) &&
            $branchId != getCurrentBranchID()
        ) {
            return false;
        }
        return true;
    }

    public static function redirectIfRecordNotInCurrentBranch($branchId, $redirectUrl = "/owner/sites/dashboard") {
        if(!self::validateRecordInCurrentBranch($branchId)) {
            $Branch = GetObjectOrLoadModel('Branch');
            $branch = $Branch->findById($branchId);
            CustomValidationFlash([sprintf(__('This record is in branch %s',true), $branch['Branch']['name'])]);
            return redirect($redirectUrl);
        }
        return;
    }
}