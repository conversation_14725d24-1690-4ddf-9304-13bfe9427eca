<?php


namespace App\Helpers;

use Izam\Attachment\Models\Document;
use Izam\Attachment\Service\AttachmentsService;

class PurchaseDocumentsS3Helper
{
    static function prepareAttachments($data)
    {
        $s3Documents = [];
        $normalDocuments = [];

        foreach ($data['PurchaseOrderDocument'] as $item) {
            $documentId = $item['document_id'];

            $document = Document::with('attachments')->find($documentId);

            if ($document) {
                $attachment = $document->attachments->first();

                if ($attachment) {
                    $s3Documents[] = $attachment->id;
                } else {
                    $normalDocuments[] = ['id' => null, 'document_id' => $documentId];
                }
            } else {
                $normalDocuments[] = ['id' => null, 'document_id' => $documentId];
            }
        }

        $data['PurchaseOrderDocument'] = $normalDocuments;

        if (isset($data['purchaseDocuments_s3']) && !empty($data['purchaseDocuments_s3'])) {
            $data['purchaseDocuments_s3'] .= ',' . implode(',', $s3Documents);
        } else {
            $data['purchaseDocuments_s3'] = implode(',', $s3Documents);
        }
        return self::getOldAttachments($data);
    }

    static function getOldAttachments($data)
    {
        if (!empty($data['purchaseDocuments_s3'])) {
            $filesId = explode(',', $data['purchaseDocuments_s3']);
            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
            $data['Attachments'] = $attachment;
        }
        return $data;
    }
}
