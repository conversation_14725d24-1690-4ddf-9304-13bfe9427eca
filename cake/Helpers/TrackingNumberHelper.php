<?php

namespace App\Helpers;

class TrackingNumberHelper
{
    public static function formatTrackingFormData($data, $itemName) {
        if(ifPluginActive(PRODUCT_TRACKING_PLUGIN)) {
            foreach ($data[$itemName] as $k => $item) {
                if(isset($item['lot']) && is_array($item['lot'])) {
                    $data[$itemName][$k]['lot'] = $item['lot'][0];
                }
                if(isset($item['expiry_date']) && is_array($item['expiry_date'])) {
                    $data[$itemName][$k]['expiry_date'] = $item['expiry_date'][0];
                }
            }
        }
        return $data;
    }

    public static function getTrackingDataIdentifier($trackingData) {
        if(is_string($trackingData) && $temp = json_decode($trackingData,true)) {
            $trackingData = json_decode($trackingData,true);
        }
        if(!empty($trackingData['serial'])) {
            return implode('-',$trackingData['serial']);
        }

        if(!empty($trackingData['lot']) && !empty($trackingData['expiry_date'])) {
            return $trackingData['lot'] . '-' . $trackingData['expiry_date'];
        }

        if(!empty($trackingData['lot'])) {
            return $trackingData['lot'];
        }

        if(!empty($trackingData['expiry_date'])) {
            return $trackingData['expiry_date'];
        }

        return false;
    }
}