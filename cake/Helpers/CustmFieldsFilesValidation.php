<?php

namespace App\Helpers;

class CustmFieldsFilesValidation
{
    public static function extractFilesValidation($model)
    {
        $fields = self::getCustomFieldsDetails($model);
        $rules = [];

        foreach ($fields as $key => $filed) {
            if (in_array($filed['CustomFormField']['type'], [160, 165])) {

                if ($filed['CustomFormField']['type'] == 165) {
                    $mimes = self::getValidationRulesStructure("image");
                } else {
                    $validationOptions =  json_decode($filed['CustomFormField']['validation_options'], 1);
                    $mimes = (array_key_exists('allowed_types', $validationOptions)) ? self::getValidationRulesStructure($validationOptions['allowed_types']) : [];
                }

                $rules[$filed['CustomFormField']['id']] = ['required' => ($filed['CustomFormField']['is_required'] == 1) ? true : false, 'mimes' => $mimes];
            }
        }
        return $rules;
    }

    public static function getCustomFieldsDetails($model)
    {
        $model = GetObjectOrLoadModel($model);
        $modelName = $model->name;
        $customForm = GetObjectOrLoadModel('CustomForm');
        $customFormField = GetObjectOrLoadModel('CustomFormField');

        $customTableName = $customForm->get_custom_table_name($modelName);
        $rereadForm = $customForm->findByTableName($customTableName);
        return $customFormField->get_form_fields($rereadForm['CustomForm']['id']);
    }

    private static function getValidationRulesStructure($allowed_types)
    {
        $tmp = [];
        $extensions = [
            "document" => ["docx", "doc", "xls", "xlsx", "pdf", "ppt", "pptx", "rtf"],
            "pdf" => ["pdf"],
            "spreadsheet" => ["xls", "xlsx", "csv", "txt"],
            "text" => ["txt"],
            "image" => ["jpg", "tif", "png", "gif"],
            "html" => ["html"],
            "archive" => ["zip", "rar", "gz", "tar"]
        ];

        foreach (explode(',', $allowed_types) as $type) {
            $tmp = array_merge($tmp, $extensions[$type]);
        }

        return $tmp;
    }

    public static function getCustFieldEntityByModel($model){
        $customEntities = [
            "Client" => "client_custom_field",
            "Supplier" => "supplier_custom_field",
            "Invoice" => "invoice_custom_field",
            "Product" => "product_custom_field",
            "WorkOrder" => "workorders_custom_field" 
        ];
        return $customEntities[$model];
    }
}
