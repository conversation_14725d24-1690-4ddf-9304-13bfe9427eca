<?php
/**
 * Created by PhpStorm.
 * User: belal
 * Date: 26/02/20
 * Time: 09:29 ص
 */
namespace App\Helpers;
\App::import('Vendor', 'settings');

use Configure;
use NumberFormatter;

class CurrencyHelper
{
    static $countryCode = '';
    static $lang;
    //we might need this if we map currency to country code PLEASE DONT REMOVE
//    static $currencyToCountryCode = [
//        'NZD' => 'NZ',
//        'AUD' => 'AU',
//        'EUR' => 'AS',
//        'GBP' => 'GS',
//        'USD' => 'IO',
//        'HKD' => 'HK',
//        'CAD' => 'CA',
//        'JPY' => 'JP',
//        'DZD' => 'DZ',
//        'ARS' => 'AR',
//        'AZN' => 'AZ',
//        'BHD' => 'BH',
//        'BDT' => 'BD',
//        'BYR' => 'BY',
//        'BRL' => 'BR',
//        'BGN' => 'BG',
//        'CLP' => 'CL',
//        'COP' => 'CO',
//        'CRC' => 'CR',
//        'HRK' => 'HR',
//        'CZK' => 'CZ',
//        'DKK' => 'DK',
//        'DOP' => 'DO',
//        'IDR' => 'TP',
//        'EGP' => 'EG',
//        'ETB' => 'ER',
//        'GTQ' => 'GT',
//        'HUF' => 'HU',
//        'IQD' => 'IQ',
//        'ILS' => 'IL',
//        'KZT' => 'KZ',
//        'KES' => 'KE',
//        'KRW' => 'KR',
//        'KWD' => 'KW',
//        'LYD' => 'LY',
//        'CHF' => 'LI',
//        'MYR' => 'MY',
//        'MXN' => 'MX',
//        'MAD' => 'MA',
//        'MMK' => 'MM',
//        'NPR' => 'NP',
//        'NGN' => 'NG',
//        'OMR' => 'OM',
//        'PKR' => 'PK',
//        'PEN' => 'PE',
//        'PHP' => 'PH',
//        'PLN' => 'PL',
//        'QAR' => 'QA',
//        'RON' => 'RO',
//        'RUB' => 'RU',
//        'SAR' => 'SA',
//        'SGD' => 'SG',
//        'ZAR' => 'ZA',
//        'LKR' => 'LK',
//        'SEK' => 'SE',
//        'TWD' => 'TW',
//        'THB' => 'TH',
//        'TND' => 'TN',
//        'TRY' => 'TR',
//        'UAH' => 'UA',
//        'AED' => 'AE',
//        'UYU' => 'UY',
//        'UZS' => 'UZ',
//        'VEF' => 'VE',
//        'VND' => 'VN',
//        'AOA' => 'AO',
//        'GHS' => 'GH',
//        'RSD' => 'RS',
//        //euro is missing
//    ];

    public static function getCountryCode() {
        if (!self::$countryCode) {
            self::$countryCode = getCurrentSite('country_code');
        }
        return self::$countryCode;
    }

    public static function getLang() {
        if (!self::$lang) {
            self::$lang = Configure::read('Config.language');
            if(self::$lang === 'en') {
                $locale = locale_accept_from_http($_SERVER['HTTP_ACCEPT_LANGUAGE']);
                $localeParts = explode('_',$locale);
                self::$lang = $localeParts[0];
            }else if(self::$lang === 'ara') {
                self::$lang = 'ar';
            }
        }
        return self::$lang;
    }

    static function getFraction($currency) {
        $formatter = new NumberFormatter(self::getLang() . '_' . self::getCountryCode() ."@currency=$currency", NumberFormatter::CURRENCY);
        $fractionDigits = $formatter->getAttribute(NumberFormatter::FRACTION_DIGITS);
        return ($fractionDigits===false?2:$fractionDigits);
    }

    static function formatPrice($amount, $currencyCode, $showSymbol = false, $round = true)
    {
        $formatter = new NumberFormatter(self::getLang() . '_' . self::getCountryCode() ."@currency=$currencyCode", NumberFormatter::CURRENCY);
        if($round) {
            $formatter->setAttribute(NumberFormatter::ROUNDING_MODE, NumberFormatter::ROUND_HALFUP);
        }
        if(!$showSymbol){
            $formatter->setSymbol(NumberFormatter::CURRENCY_SYMBOL, '');
        }
        $isNegative = $amount < 0;
        $formattedNumber = $formatter->formatCurrency($amount, $currencyCode);
        $formattedNumber = str_replace(['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '٬', '٫', ' '],['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ',', '.', ' '],$formattedNumber);
        if($isNegative) {
            $negativeFormat = \settings::getValue(0, 'negative_currency_formats');
            if($negativeFormat === \settings::NEGATIVE_CURRENCY_FORMAT_AMOUNT_WITH_PARENTHESIS) {
                $formattedNumber = '('.str_replace('-', '', $formattedNumber).')';
            }
        }
        return $formattedNumber;
    }

    static function testFormats() {
        $Currency = GetObjectOrLoadModel('Currency');
        $currencies = $Currency->find('all');
        $number = 1233451246.123466;
        echo 'Number: '.$number.'</br>';
        foreach ($currencies as $currency)
        {
            echo 'old format: '.format_price($number, $currency['Currency']['code']). '  new format: '. self::formatPrice($number, $currency['Currency']['code']) . '</br>';
        }
        $start = microtime(TRUE);
        foreach ($currencies as $currency)
        {
            format_price($number, $currency['Currency']['code']);
        }
        $end = microtime(TRUE);
        echo "The code took " . ($end - $start) . " seconds to complete.";
        $start = microtime(TRUE);
        foreach ($currencies as $currency)
        {
            self::formatPrice($number, $currency['Currency']['code'], false) ;
        }
        $end = microtime(TRUE);
        echo "The code took " . ($end - $start) . " seconds to complete.";
        die();
    }

}