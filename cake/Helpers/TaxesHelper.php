<?php
namespace App\Helpers;
class TaxesHelper
{
    /**
     * @param $totalTax
     * @param $totalQuantity
     * @param $taxQuantity
     * @return float|int
     * calculates taxes for partial quantity for an item
     * using the item total taxes and total quantity
     * and the partial quantity we want
     * to calculate taxes for
     */
    public static function calculatePartialTax($totalTax, $totalQuantity, $taxQuantity) {
        if($totalTax == 0 || $totalQuantity == 0) {
            return 0;
        }
        $taxPerUnit = $totalTax / $totalQuantity;
        $quantityTaxes = $taxPerUnit * $taxQuantity;
        return $quantityTaxes;
    }

    public static function calculateAmountWithoutTaxes($amount, $taxes) {
        $totalTaxes = 0;
        //calculate inclusive taxes
        $totalInclusiveTax = 0;
        $amountWithoutTaxes = $amount;
        foreach ($taxes as $tax) {
            if($tax['included']) {
                $totalInclusiveTax += $tax['value'];
            }
        }

        if($totalInclusiveTax) {
            $amountWithoutTaxes = $amount / ( ( $totalInclusiveTax / 100 ) + 1);
        }

        return $amountWithoutTaxes;
    }


}
