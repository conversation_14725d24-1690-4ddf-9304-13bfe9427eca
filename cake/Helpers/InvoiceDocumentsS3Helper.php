<?php


namespace App\Helpers;

use Izam\Attachment\Models\Document;
use Izam\Attachment\Service\AttachmentsService;

class InvoiceDocumentsS3Helper
{
    static function prepareAttachments($data)
    {
        $s3Documents = [];
        $normalDocuments = [];

        foreach ($data['InvoiceDocument'] as $item) {
            $documentId = $item['document_id'];

            $document = Document::with('attachments')->find($documentId);

            if ($document) {
                $attachment = $document->attachments->first();

                if ($attachment) {
                    $s3Documents[] = $attachment->id;
                } else {
                    $normalDocuments[] = ['id' => null, 'document_id' => $documentId];
                }
            } else {
                $normalDocuments[] = ['id' => null, 'document_id' => $documentId];
            }
        }

        $data['InvoiceDocument'] = $normalDocuments;

        if (isset($data['invoiceDocuments']) && !empty($data['invoiceDocuments'])) {
            if (!empty($s3Documents)) {

                $data['invoiceDocuments'] .= ',' . implode(',', $s3Documents);
            }
        } else {
            $data['invoiceDocuments'] = implode(',', $s3Documents);
        }

        return self::getOldAttachments($data);
    }

    static function getOldAttachments($data)
    {
        if (!empty($data['invoiceDocuments'])) {
            $filesId = explode(',', $data['invoiceDocuments']);
            $attachment = izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($filesId);
            $data['Attachments'] = $attachment;
        }
        return $data;
    }


    static function getAttachmentsByEntityId($id)
    {
        $attachmentsid = izam_resolve(AttachmentsService::class)->getAttachmentIds('invoice', $id);
        return izam_resolve(AttachmentsService::class)->getFilesDetailsByFileIds($attachmentsid);
    }
}
