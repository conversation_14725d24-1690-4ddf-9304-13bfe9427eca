<?php


namespace App\Helpers;


use App\Utils\StaffTypeUtil;

class UsersHelper
{
    /**
     * @var $instance UsersHelper
     */
   private static $instance;

   public $selectedStaffIds = [];

    /**
     * @var \Staff
     */
    private $StaffModel;
    private function __construct()
    {
        $this->StaffModel = GetObjectOrLoadModel('Staff');
    }

    /**
     * @return UsersHelper
     */
    public static function getInstance() {
        if(!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getList($inlude_admin = true, $conditions = array(),$active_only=false, $include_employees = false) {
        if(!isset($conditions['Staff.id'])) {
            if(!isset($conditions['AND'])) {
                $conditions['AND'] = [];
            }
            if($this->selectedStaffIds != null){
                $conditions['AND'] = ['OR' => ['Staff.id' => $this->selectedStaffIds]];
            }
            if(!$include_employees){
                $conditions['AND'] = ['OR' => ['Staff.type' => \Izam\Daftra\Staff\Util\StaffTypeUtil::USER]];
            }else{
                $conditions['AND'] = ['OR' => ['Staff.type' => [\Izam\Daftra\Staff\Util\StaffTypeUtil::USER, \Izam\Daftra\Staff\Util\StaffTypeUtil::EMPLOYEE]]];
            }
            if(empty($conditions['AND'])){
                unset($conditions['AND']);
            }
            $this->selectedStaffIds = null;
        }
        return $this->StaffModel->getList($inlude_admin, $conditions, $active_only);
    }

    public function find($type, $options = null) {
        if(!isset($options['conditions']['Staff.id']) || is_array($options['conditions']['Staff.id'])) {
            if(!isset($options['conditions']['AND'])) {
                $options['conditions']['AND'] = [];
            }
            $options['conditions']['AND'][] = ['OR' => ['Staff.id' => $this->selectedStaffIds, 'Staff.type' =>  \Izam\Daftra\Staff\Util\StaffTypeUtil::USER]];
            $this->selectedStaffIds = null;
        }
        return $this->StaffModel->find($type, $options);
    }

    public function setSelectedStaff($selectedStaffIds) {
        self::$instance->selectedStaffIds = $selectedStaffIds;
        return self::$instance;
    }
}


