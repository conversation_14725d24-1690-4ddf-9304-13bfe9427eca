<?php

namespace App\Events\Queue\Product;

use Izam\Daftra\Common\Queue\EventTypeUtil;
use Izam\Daftra\Queue\Events\EventAbstract;

class ProductCreatedEvent extends EventAbstract
{
    protected $uniqueOverTransaction = true;

    public function __construct($data)
    {
        if(isset($data['Product']['id'])) {
            $this->setEntityId($data['Product']['id']);
        }
        parent::__construct($data);
    }

    public function getType()
    {
        return EventTypeUtil::PRODUCT_CREATED;
    }
}