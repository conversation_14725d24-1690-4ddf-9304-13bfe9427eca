{"name": "i<PERSON>-da<PERSON>ra", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "concurrently -n BUILD-SASS,SASS,LIVE-SERVER,HELP -c \"bgWhite,bgWhite,bgRed,bgGreen,bgBlue\" \"npm run build:sass\" \"npm run watch:sass\" \"npm run server\" \"npm run help\"", "server": "live-server --open=cake/webroot/frontend/html --port=9090 --watch=cake/webroot/frontend", "watch:sass": "sass --no-source-map --watch cake/webroot/frontend/assets/sass:cake/webroot/frontend/assets/css", "debug:sass": "sass cake/webroot/frontend/assets/sass:cake/webroot/frontend/assets/css", "build:sass": "sass --no-source-map cake/webroot/frontend/assets/sass:cake/webroot/frontend/assets/css", "build:js": "node build-js.js", "build": "concurrently -n BUILD-SASS,BUILD-JS -c \"bgRed,bgWhite\" \"npm run build:sass\" \"npm run build:js\"", "help": "clear; echo '\n🔷------------------------------------------------------------------------------------🔷\n👉 To test css/js changes use the live-server static pages: \nhttp://127.0.0.1:9090/\n\n👉 To test GET/POST requests use the non-live-server static pages: \nhttps://oidev.daftra.local/frontend/html/\n🔷------------------------------------------------------------------------------------🔷'"}, "author": "", "license": "ISC", "dependencies": {"bootstrap": "^5.3.1", "concurrently": "^5.3.0", "live-server": "^1.2.1", "sass": "^1.64.1", "turbodepot-node": "^7.0.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}