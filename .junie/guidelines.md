# Project Guidelines for Izam Daftra/Enerpize

## Project Overview

Izam Daftra/Enerpize is a comprehensive Enterprise Resource Planning (ERP) system designed to manage various business operations. The project has the following key characteristics:

### Architecture

- **Dual-Framework Architecture**: The application uses both CakePHP and Laravel frameworks:
  - **CakePHP**: Handles legacy components and core business logic
  - **Laravel**: Handles newer features and is likely part of a gradual migration strategy

- **Modular Design**: The codebase is organized into numerous modules (packages) that handle specific business functions:
  - Staff/HR management
  - Invoicing and billing
  - Inventory and product management
  - Expense tracking
  - Journal entries and accounting
  - Purchase orders
  - Manufacturing
  - Payroll and payslips
  - Attendance tracking
  - And many others

- **Containerized Deployment**: The application runs in Docker containers with a LAMP stack (Linux, Apache, MySQL, PHP)

### Technology Stack

- **Backend**:
  - PHP 8.0+
  - CakePHP (legacy framework)
  - Laravel 8.x (modern framework)
  - MySQL database
  - Redis for caching
  - RabbitMQ for message queuing

- **Frontend**:
  - Dart Sass for SCSS compilation
  - JavaScript
  - HTML/CSS

- **Infrastructure**:
  - Docker for containerization
  - AWS S3 for file storage
  - Apache web server

- **Third-Party Services**:
  - Rollbar for error tracking
  - Twilio/Plivo for SMS communications
  - Firebase for notifications
  - AWS services

## Development Guidelines

### Project Structure

- **Root Directory**: Contains both CakePHP and Laravel applications, along with Docker configuration
- **cake/**: Contains the CakePHP application with controllers, models, views, and custom components
- **laravel/**: Contains the Laravel application with standard Laravel structure
- **izam-packages/**: Contains shared packages used by both frameworks
- **docker-compose.yaml**: Defines the containerized environment

### Development Environment Setup

1. Use Docker for local development to ensure consistency with production
2. Run `composer update` in both the cake/ and laravel/ directories to install dependencies
3. For frontend development, use `npm run start` to build SCSS files and view documentation

### Code Style and Standards

- Follow PSR standards for PHP code
- Use proper namespacing according to the framework guidelines
- Maintain modular architecture by keeping related functionality in appropriate packages
- Document code changes in appropriate changelog files

### Testing

- Write unit tests for new functionality
- Ensure existing tests pass before submitting changes
- Use PHPUnit for testing both CakePHP and Laravel components

### Deployment Process

1. Build the application using Docker
2. Ensure all tests pass
3. Follow the deployment pipeline defined in Jenkinsfile/bitbucket-pipelines.yml

### Debugging

- Use Xdebug for PHP debugging (configuration available in upgrade_guidelines.md)
- Check logs in the appropriate directories:
  - CakePHP logs: cake/tmp/logs/
  - Laravel logs: laravel/storage/logs/

## Working with Junie

When working with Junie on this project, please provide the following information:

1. **Context**: Explain which part of the application you're working on (CakePHP or Laravel) and which module
2. **Requirements**: Clearly state what needs to be changed or implemented
3. **Testing**: Specify how to test the changes (if applicable)

Junie should:

1. Understand the dual-framework architecture and modular design
2. Make minimal changes to achieve the desired outcome
3. Follow the existing code style and patterns
4. Test changes when possible
5. Document any significant architectural decisions or changes

## Common Tasks

### Adding a New Feature

1. Determine which framework should handle the feature (CakePHP or Laravel)
2. Identify the appropriate module/package
3. Implement the feature following the framework's patterns
4. Add tests
5. Update documentation if necessary

### Fixing Bugs

1. Identify the affected component
2. Reproduce the issue
3. Fix the issue with minimal changes
4. Add tests to prevent regression
5. Document the fix in the appropriate changelog

### Upgrading Dependencies

1. Test the upgrade in a development environment
2. Update the appropriate composer.json file
3. Run composer update
4. Fix any compatibility issues
5. Document the upgrade in the changelog